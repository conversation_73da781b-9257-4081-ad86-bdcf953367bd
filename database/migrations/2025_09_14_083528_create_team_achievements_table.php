<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('team_achievements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained()->onDelete('cascade');
            $table->foreignId('label_id')->constrained()->onDelete('cascade');
            $table->decimal('achieved_amount', 10, 2);
            $table->decimal('bonus_earned', 10, 2)->default(0);
            $table->date('achievement_date');
            $table->text('notes')->nullable();
            $table->boolean('is_paid')->default(false);
            $table->timestamps();

            $table->index(['team_id', 'achievement_date']);
            $table->index(['label_id', 'achievement_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('team_achievements');
    }
};
