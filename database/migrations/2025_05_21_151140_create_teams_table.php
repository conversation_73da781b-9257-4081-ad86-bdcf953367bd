<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teams', function (Blueprint $table) {
            $table->id();

            // Relationships
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('designation_id')->constrained()->onDelete('cascade');
            $table->foreignId('label_id')->constrained()->onDelete('cascade');

            // Team info
            $table->date('joining_date')->nullable();
            $table->string('target')->nullable();

            // Salary / target info
            $table->decimal('target_amount', 10, 2)->default(0);   // Monthly target amount
            $table->decimal('achieved_amount', 10, 2)->default(0); // Current achieved amount
            $table->decimal('base_salary', 10, 2)->default(0);     // Base salary amount

            // Multiple off days (e.g. ["Friday", "Saturday"])
            $table->json('off_days')->nullable();

            // Status
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->enum('leave_status', ['on_leave', 'on_duty'])->default('on_duty');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints for related tables first
        $tables = ['duty_shifts', 'salaries', 'attendances', 'leave_requests', 'project_management'];

        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                try {
                    DB::statement("ALTER TABLE {$table} DROP FOREIGN KEY {$table}_team_id_foreign");
                } catch (\Exception $e) {
                    // ignore if doesn't exist
                }
            }
        }

        Schema::dropIfExists('teams');
    }
};
