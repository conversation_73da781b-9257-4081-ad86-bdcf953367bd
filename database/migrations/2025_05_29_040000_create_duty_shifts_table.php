<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('duty_shifts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained()->onDelete('cascade');
            $table->string('name'); // Morning / Noon / Night
            $table->date('date');   // Specific shift date
            $table->time('start_time');
            $table->time('end_time');
            $table->unsignedSmallInteger('grace_minutes')->default(5);
            $table->timestamps();

            // Ensure one shift per team per date
            $table->unique(['team_id', 'date'], 'duty_shifts_team_date_unique');
        });


    }

    public function down(): void
    {
        Schema::dropIfExists('duty_shifts');
    }
};
