<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('marketplace_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('order_account_name');
            $table->dateTime('created_date')->useCurrent(); // default now
            $table->string('username');
            $table->string('password'); // ⚠️ should be hashed
            $table->string('wifi_ip')->nullable();
            $table->unsignedBigInteger('category_id')->nullable();
            $table->timestamps();

            // Index for performance
            $table->index('category_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('marketplace_accounts');
    }
};
