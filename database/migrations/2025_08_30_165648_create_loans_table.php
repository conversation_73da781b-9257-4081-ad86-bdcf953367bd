<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loans', function (Blueprint $table) {
            $table->id();
            $table->decimal('amount', 10, 2);
            $table->decimal('loan_amount_charge_percentage', 5, 2)->default(0);
            $table->foreignId('marketplace_id')
                  ->constrained('marketplace_accounts')
                  ->onDelete('cascade');
            $table->decimal('due_amounts', 10, 2);
            $table->text('description')->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected', 'paid', 'overdue'])
                  ->default('pending');
            $table->date('paid_date')->nullable();
            $table->decimal('paid_amount', 10, 2)->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loans');
    }
};
