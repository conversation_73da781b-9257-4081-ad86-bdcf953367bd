<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProjectsTable extends Migration
{
    public function up()
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->string('subtitle')->nullable();
            $table->text('description')->nullable();
            $table->json('images')->nullable(); // Store multiple image paths as JSON
            $table->unsignedBigInteger('user_id')->nullable(); // Foreign key to users
            $table->decimal('budget', 15, 2)->nullable();
            $table->date('date')->nullable();
            $table->unsignedBigInteger('service_id')->nullable(); // Changed to unsignedBigInteger for potential FK
            $table->timestamps();

            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_keywords')->nullable();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');

            // Optional: If services is a separate table, uncomment the following line
             $table->foreign('service_id')->references('id')->on('services')->onDelete('set null');
        });
    }

    public function down()
    {
        Schema::dropIfExists('projects');
    }
}


