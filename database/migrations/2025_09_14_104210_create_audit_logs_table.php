<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('action'); // create, update, delete, login, logout, etc.
            $table->string('model_type')->nullable(); // App\Models\User, App\Models\Blog, etc.
            $table->unsignedBigInteger('model_id')->nullable(); // ID of the affected record
            $table->string('model_name')->nullable(); // Human readable name
            $table->json('old_values')->nullable(); // Previous values before change
            $table->json('new_values')->nullable(); // New values after change
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->string('url')->nullable();
            $table->string('method')->nullable(); // GET, POST, PUT, DELETE
            $table->text('description')->nullable(); // Human readable description
            $table->json('metadata')->nullable(); // Additional data
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'created_at']);
            $table->index(['model_type', 'model_id']);
            $table->index(['action', 'created_at']);
            $table->index('created_at');
            $table->index(['created_at', 'action'], 'idx_audit_logs_created_action');
            $table->index(['user_id', 'action', 'created_at'], 'idx_audit_logs_user_action_created');

            // Foreign key
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');

            // Table comment
            $table->comment('Audit logs table - Individual records cannot be deleted for security and compliance');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};
