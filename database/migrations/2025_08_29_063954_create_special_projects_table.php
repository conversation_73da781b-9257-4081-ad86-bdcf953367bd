<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('special_projects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('marketplace_id')->constrained('marketplace_accounts')->onDelete('cascade');
            $table->decimal('order_amount', 10, 2);
            $table->decimal('delivery_amount', 10, 2)->nullable();
            $table->decimal('order_amount_rate', 10, 2)->default(152);
            $table->decimal('withdrawn_amount_rate', 10, 2)->default(124);
            $table->decimal('in_hand_amount', 10, 2)->nullable();
            $table->foreignId('assigned_by')->constrained('teams')->onDelete('cascade');
            $table->date('order_date');
            $table->date('delivery_date')->nullable();
            $table->string('project_name');
            $table->text('description')->nullable();
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending');
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('special_projects');
    }
};
