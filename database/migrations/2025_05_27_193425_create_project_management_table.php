<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('project_managements', function (Blueprint $table) {$table->id();

    // Foreign Keys
    $table->foreignId('team_id')->nullable()->constrained()->nullOnDelete(); // Possibly creator/lead team
    $table->foreignId('order_account_id')->constrained('marketplace_accounts')->onDelete('cascade');
    $table->foreignId('assigned_by')->nullable()->constrained('teams')->nullOnDelete();
    $table->foreignId('delivered_by')->nullable()->constrained('teams')->nullOnDelete();
    $table->foreignId('responsible_team')->constrained('teams')->onDelete('cascade');

    // Project Details
    $table->string('project_name');
    $table->datetime('delivered_date')->nullable();
    $table->decimal('delivered_amount', 10, 2)->nullable();
    $table->unsignedTinyInteger('progress')->nullable();
    // Client Communication
    $table->text('instruction_from_client')->nullable();
    $table->text('login_credentials')->nullable();

    // Tech Stack
    $table->string('theme_name')->nullable();
    $table->string('page_builder_name')->nullable();
    $table->string('reference_website')->nullable();
    $table->string('special_plugins')->nullable();
    $table->string('special_requirements')->nullable();

    // Additional Notes
    $table->text('notes')->nullable();

    // Status Tracking
    $table->enum('status', ['pending','in_progress','completed','in_revision','on_hold','cancelled'])->nullable();

    // Order & Client Info
    $table->string('client_name');
    $table->decimal('amount', 10, 2);
    $table->string('order_page_url');
    $table->datetime('incoming_date')->nullable();
    $table->string('cms');

    // File Uploads
    $table->string('order_page_screenshot')->nullable();
    $table->string('conversation_page_screenshot')->nullable();
    $table->string('files')->nullable();

    $table->timestamps();
});

    }

    public function down(): void
    {
        Schema::dropIfExists('project_managements');
    }
};

