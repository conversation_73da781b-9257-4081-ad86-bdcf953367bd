<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition()
    {
        return [
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'number' => $this->faker->phoneNumber,
            'address' => $this->faker->address,
            'role' => 'user',
            'permission' => 'read',
            'email_verified_at' => now(),
            'password' => bcrypt('password'), // or use Hash::make('password')
        ];
    }
}
