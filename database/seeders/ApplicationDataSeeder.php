<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Team;
use App\Models\Label;
use App\Models\Designation;
use App\Models\BlogCategory;
use App\Models\Service;
use App\Models\Project;
use App\Models\MarketplaceAccountCategory;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class ApplicationDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default designations
        $designations = [
            ['name' => 'CEO', 'base_amount' => 10000.00],
            ['name' => 'CTO', 'base_amount' => 8000.00],
            ['name' => 'Developer', 'base_amount' => 5000.00],
            ['name' => 'Designer', 'base_amount' => 4500.00],
            ['name' => 'Manager', 'base_amount' => 6000.00],
            ['name' => 'Marketing', 'base_amount' => 4000.00],
            ['name' => 'HR', 'base_amount' => 3500.00],
            ['name' => 'Accountant', 'base_amount' => 4200.00],
        ];

        foreach ($designations as $designation) {
            Designation::firstOrCreate(
                ['name' => $designation['name']],
                $designation
            );
        }

        // Create default labels with bonus targets
        $labels = [
            [
                'name' => 'Bronze',
                'bonus_target' => 999.99,
                'bonus_amount' => 100.00,
                'description' => 'Bronze level achievement',
                'is_active' => true,
            ],
            [
                'name' => 'Silver',
                'bonus_target' => 999.99,
                'bonus_amount' => 250.00,
                'description' => 'Silver level achievement',
                'is_active' => true,
            ],
            [
                'name' => 'Gold',
                'bonus_target' => 999.99,
                'bonus_amount' => 500.00,
                'description' => 'Gold level achievement',
                'is_active' => true,
            ],
            [
                'name' => 'Platinum',
                'bonus_target' => 999.99,
                'bonus_amount' => 1000.00,
                'description' => 'Platinum level achievement',
                'is_active' => true,
            ],
        ];

        foreach ($labels as $label) {
            Label::firstOrCreate(
                ['name' => $label['name']],
                $label
            );
        }

        // Create default blog categories
        $blogCategories = [
            ['name' => 'Technology', 'slug' => 'technology'],
            ['name' => 'Business', 'slug' => 'business'],
            ['name' => 'Design', 'slug' => 'design'],
            ['name' => 'Marketing', 'slug' => 'marketing'],
            ['name' => 'News', 'slug' => 'news'],
        ];

        foreach ($blogCategories as $category) {
            BlogCategory::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }

        // Create default services
        $services = [
            [
                'title' => 'Web Development',
                'slug' => 'web-development',
                'description' => 'Custom web application development',
                'icon' => 'ri-code-s-slash-line',
            ],
            [
                'title' => 'Mobile App Development',
                'slug' => 'mobile-app-development',
                'description' => 'iOS and Android app development',
                'icon' => 'ri-smartphone-line',
            ],
            [
                'title' => 'UI/UX Design',
                'slug' => 'ui-ux-design',
                'description' => 'User interface and experience design',
                'icon' => 'ri-palette-line',
            ],
            [
                'title' => 'Digital Marketing',
                'slug' => 'digital-marketing',
                'description' => 'Online marketing and SEO services',
                'icon' => 'ri-marketing-line',
            ],
        ];

        foreach ($services as $service) {
            Service::firstOrCreate(
                ['title' => $service['title']],
                $service
            );
        }

        // Create default projects
        $projects = [
            [
                'title' => 'E-commerce Platform',
                'slug' => 'ecommerce-platform',
                'description' => 'Full-featured e-commerce solution',
                'budget' => 50000.00,
                'date' => now()->subMonths(2),
            ],
            [
                'title' => 'CRM System',
                'slug' => 'crm-system',
                'description' => 'Customer relationship management system',
                'budget' => 30000.00,
                'date' => now()->subMonths(1),
            ],
            [
                'title' => 'Mobile Banking App',
                'slug' => 'mobile-banking-app',
                'description' => 'Secure mobile banking application',
                'budget' => 75000.00,
                'date' => now()->subMonths(6),
            ],
        ];

        foreach ($projects as $project) {
            Project::firstOrCreate(
                ['title' => $project['title']],
                $project
            );
        }

        // Create marketplace account categories
        $marketplaceCategories = [
            ['name' => 'Amazon', 'description' => 'Amazon marketplace accounts'],
            ['name' => 'eBay', 'description' => 'eBay marketplace accounts'],
            ['name' => 'Shopify', 'description' => 'Shopify store accounts'],
            ['name' => 'Etsy', 'description' => 'Etsy marketplace accounts'],
        ];

        foreach ($marketplaceCategories as $category) {
            MarketplaceAccountCategory::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }

        // Create default users with different roles
        $users = [
            [
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'super_admin',
                'permission' => 'full',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'admin',
                'permission' => 'full',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Developer',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'developer',
                'permission' => 'full',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Editor',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'editor',
                'permission' => 'read_write',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Marketer',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'marketer',
                'permission' => 'read_write',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Regular User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'user',
                'permission' => 'read',
                'email_verified_at' => now(),
            ],
        ];

        foreach ($users as $userData) {
            $user = User::firstOrCreate(
                ['email' => $userData['email']],
                $userData
            );

            // Assign Spatie role
            $user->assignRole($userData['role']);
        }

        // Create some team members
        $teamMembers = [
            [
                'user_id' => User::where('email', '<EMAIL>')->first()->id,
                'designation_id' => Designation::where('name', 'Developer')->first()->id,
                'label_id' => Label::where('name', 'Gold')->first()->id,
                'joining_date' => now()->subMonths(6),
                'target' => 5000.00,
                'base_salary' => 3000.00,
                'status' => 'active',
                'leave_status' => 'on_duty',
            ],
            [
                'user_id' => User::where('email', '<EMAIL>')->first()->id,
                'designation_id' => Designation::where('name', 'Designer')->first()->id,
                'label_id' => Label::where('name', 'Silver')->first()->id,
                'joining_date' => now()->subMonths(3),
                'target' => 2500.00,
                'base_salary' => 2500.00,
                'status' => 'active',
                'leave_status' => 'on_duty',
            ],
            [
                'user_id' => User::where('email', '<EMAIL>')->first()->id,
                'designation_id' => Designation::where('name', 'Marketing')->first()->id,
                'label_id' => Label::where('name', 'Bronze')->first()->id,
                'joining_date' => now()->subMonths(1),
                'target' => 1000.00,
                'base_salary' => 2000.00,
                'status' => 'active',
                'leave_status' => 'on_duty',
            ],
        ];

        foreach ($teamMembers as $teamData) {
            Team::firstOrCreate(
                ['user_id' => $teamData['user_id']],
                $teamData
            );
        }
    }
}
