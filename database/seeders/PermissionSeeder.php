<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $groups = [
            // User Management
            'user' => ['manage user', 'create user', 'edit user', 'delete user', 'show user', 'update user profile'],

            // Team Management
            'team' => ['manage team', 'create team', 'edit team', 'delete team', 'show team'],
            'team achievement' => ['manage team achievement', 'create team achievement', 'edit team achievement', 'delete team achievement', 'show team achievement', 'mark team achievement paid', 'mark team achievement unpaid'],

            // HR Management
            'salary' => ['manage salary', 'create salary', 'edit salary', 'delete salary', 'show salary', 'view bonus dashboard', 'view my payslips', 'view target achievement'],
            'attendance' => ['manage attendance', 'create attendance', 'edit attendance', 'delete attendance', 'show attendance', 'view my attendance', 'check in', 'check out', 'view attendance status'],
            'leave request' => ['manage leave request', 'create leave request', 'edit leave request', 'delete leave request', 'show leave request', 'approve leave request', 'reject leave request'],
            'duty shift' => ['manage duty shift', 'create duty shift', 'edit duty shift', 'delete duty shift', 'show duty shift', 'view my duty schedule'],
            'designation' => ['manage designation', 'create designation', 'edit designation', 'delete designation', 'show designation'],

            // Financial Management
            'expense' => ['manage expense', 'create expense', 'edit expense', 'delete expense', 'show expense', 'update expense status', 'view my expenses'],
            'loan' => ['manage loan', 'create loan', 'edit loan', 'delete loan', 'show loan', 'update loan status', 'mark loan paid', 'add loan deduction'],
            'profit' => ['manage profit', 'view profit table'],

            // Project Management
            'project' => ['manage project', 'create project', 'edit project', 'delete project', 'show project'],
            'project management' => ['manage project management', 'create project management', 'edit project management', 'delete project management', 'show project management', 'view my projects'],
            'special project' => ['manage special project', 'create special project', 'edit special project', 'delete special project', 'show special project', 'update special project status', 'view my special projects'],

            // Content Management
            'blog' => ['manage blog', 'create blog', 'edit blog', 'delete blog', 'show blog'],
            'blog category' => ['manage blog category', 'create blog category', 'edit blog category', 'delete blog category', 'show blog category'],
            'service' => ['manage service', 'create service', 'edit service', 'delete service', 'show service'],
            'contact' => ['manage contact', 'create contact', 'edit contact', 'delete contact', 'show contact', 'reply contact', 'send contact reply'],

            // Business Management
            'marketplace account' => ['manage marketplace account', 'create marketplace account', 'edit marketplace account', 'delete marketplace account', 'show marketplace account'],
            'marketplace account category' => ['manage marketplace account category', 'create marketplace account category', 'edit marketplace account category', 'delete marketplace account category', 'show marketplace account category'],
            'label' => ['manage label', 'create label', 'edit label', 'delete label', 'show label'],

            // Reports
            'report' => [
                'manage income report',
                'manage expense report',
                'manage profit and loss report',
                'manage team performance report',
                'manage attendance report',
                'manage salary report',
                'manage project report',
            ],

            // Settings & Configuration
            'settings' => [
                'manage account settings', 'manage password settings', 'manage general settings', 'manage company settings',
                'manage email settings', 'manage payment settings', 'manage seo settings', 'manage google recaptcha settings',
                'manage 2FA settings', 'manage storage settings', 'manage twilio settings',
            ],

            // System
            'dashboard' => ['view dashboard', 'view admin dashboard', 'view user dashboard'],
            'profile' => ['view profile', 'edit profile', 'update profile'],

            // Audit Logs
            'audit log' => ['show audit log', 'manage audit log', 'export audit log', 'clean audit log'],
        ];

        foreach ($groups as $group => $names) {
            foreach ($names as $name) {
                Permission::firstOrCreate([
                    'name' => $name,
                    'guard_name' => 'web',
                ]);
            }
        }
    }
}
