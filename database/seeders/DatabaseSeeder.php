<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Run Spatie Permission seeders first
        $this->call([
            PermissionSeeder::class,
            RolePermissionManagementSeeder::class,
            AssignPermissionsToRolesSeeder::class,
            ApplicationDataSeeder::class,
            AssignLegacyRolesToUsersSeeder::class,
        ]);
    }
}
