<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;

class AssignLegacyRolesToUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure base roles exist
        $roles = ['super_admin', 'admin', 'editor', 'developer', 'marketer', 'tenant', 'user'];
        foreach ($roles as $r) {
            Role::firstOrCreate(['name' => $r, 'guard_name' => 'web']);
        }

        User::query()->chunkById(200, function ($users) {
            foreach ($users as $user) {
                // clear inconsistent roles first
                $user->syncRoles([]);
                $legacy = $user->role ?: 'user';
                // map unknowns to 'user'
                $roleName = in_array($legacy, ['super_admin','admin','editor','developer','marketer','user'], true)
                    ? $legacy
                    : 'user';
                $user->assignRole($roleName);
            }
        });
    }
}
