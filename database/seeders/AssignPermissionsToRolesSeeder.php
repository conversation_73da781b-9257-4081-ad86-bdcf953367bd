<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AssignPermissionsToRolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $allPermissions = Permission::pluck('name')->toArray();

        // super_admin: ALL permissions - complete system access
        $superAdminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'web']);
        $superAdminRole->syncPermissions($allPermissions);

        // Ensure super admin always has all permissions, even if new ones are added later
        $this->command->info('Super Admin has been granted ALL ' . count($allPermissions) . ' permissions:');
        foreach ($allPermissions as $permission) {
            $this->command->line("  ✓ {$permission}");
        }

        // admin: all permissions except some system-level ones
        Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web'])
            ->syncPermissions($allPermissions);

        // developer: full access to all features
        Role::firstOrCreate(['name' => 'developer', 'guard_name' => 'web'])
            ->syncPermissions($allPermissions);

        // editor: content management + limited HR access
        $editorPerms = [
            // Content Management
            'manage blog','create blog','edit blog','delete blog','show blog',
            'manage blog category','create blog category','edit blog category','delete blog category','show blog category',
            'manage service','create service','edit service','delete service','show service',
            'manage contact','create contact','edit contact','delete contact','show contact','reply contact','send contact reply',
            'manage project','create project','edit project','delete project','show project',

            // Limited HR Access
            'view my attendance','check in','check out','view attendance status',
            'create leave request','show leave request',
            'view my duty schedule',
            'view my payslips','view target achievement',
            'view my expenses','view my special projects','view my projects',

            // Basic System Access
            'view dashboard','view user dashboard',
            'view profile','edit profile','update profile',
        ];
        Role::firstOrCreate(['name' => 'editor', 'guard_name' => 'web'])
            ->syncPermissions(array_values(array_intersect($allPermissions, $editorPerms)));

        // marketer: marketing and content focused
        $marketerPerms = [
            // Content & Marketing
            'manage blog','create blog','edit blog','delete blog','show blog',
            'manage blog category','create blog category','edit blog category','delete blog category','show blog category',
            'manage service','create service','edit service','delete service','show service',
            'manage contact','create contact','edit contact','delete contact','show contact','reply contact','send contact reply',
            'manage seo settings',

            // Limited Project Access
            'manage project','create project','edit project','delete project','show project',
            'view my projects',

            // Basic System Access
            'view dashboard','view user dashboard',
            'view profile','edit profile','update profile',
        ];
        Role::firstOrCreate(['name' => 'marketer', 'guard_name' => 'web'])
            ->syncPermissions(array_values(array_intersect($allPermissions, $marketerPerms)));

        // tenant: very limited access (if this role is used for external users)
        $tenantPerms = [
            'view my attendance','check in','check out','view attendance status',
            'create leave request','show leave request',
            'view my duty schedule',
            'view my payslips',
            'view my expenses',
            'view dashboard','view user dashboard',
            'view profile','edit profile','update profile',
        ];
        Role::firstOrCreate(['name' => 'tenant', 'guard_name' => 'web'])
            ->syncPermissions(array_values(array_intersect($allPermissions, $tenantPerms)));

        // user: basic employee access
        $userPerms = [
            // Personal HR Access
            'view my attendance','check in','check out','view attendance status',
            'create leave request','show leave request',
            'view my duty schedule',
            'view my payslips','view target achievement',
            'view my expenses','view my special projects','view my projects',

            // Basic System Access
            'view dashboard','view user dashboard',
            'view profile','edit profile','update profile',
        ];
        Role::firstOrCreate(['name' => 'user', 'guard_name' => 'web'])
            ->syncPermissions(array_values(array_intersect($allPermissions, $userPerms)));
    }
}
