<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create role and permission management permissions
        $rolePermissionPermissions = [
            'manage role',
            'create role',
            'edit role',
            'delete role',
            'show role',
            'assign role to user',
            'remove role from user',
            'sync role permissions',

            'manage permission',
            'create permission',
            'edit permission',
            'delete permission',
            'show permission',
            'assign permission to role',
            'remove permission from role',
            'assign permission to user',
            'remove permission from user',
        ];

        foreach ($rolePermissionPermissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }

        // Assign role and permission management permissions to appropriate roles
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $adminRole = Role::where('name', 'admin')->first();
        $developerRole = Role::where('name', 'developer')->first();

        if ($superAdminRole) {
            $superAdminRole->givePermissionTo($rolePermissionPermissions);
        }

        if ($adminRole) {
            $adminRole->givePermissionTo($rolePermissionPermissions);
        }

        if ($developerRole) {
            $developerRole->givePermissionTo($rolePermissionPermissions);
        }
    }
}
