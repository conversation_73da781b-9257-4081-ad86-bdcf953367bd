<?php

namespace App\Providers;

use App\Models\User;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Share roles and permissions with all views (only if not already set)
        View::composer('*', function ($view) {
            if (!$view->offsetExists('roles')) {
                $view->with('roles', User::ROLES);
            }
            if (!$view->offsetExists('permissions')) {
                $view->with('permissions', User::PERMISSIONS);
            }
        });

        // Custom Blade directive for admin check
        Blade::if('admin', function () {
            return auth()->check() && in_array(auth()->user()->role, ['admin', 'super_admin']);
        });

        // You can add more Blade directives here if needed
        Blade::if('manager', function () {
            return auth()->check() && auth()->user()->role === 'manager';
        });
    }
}
