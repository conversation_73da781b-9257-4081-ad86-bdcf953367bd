<?php

namespace App\Traits;

use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

trait AuditLoggable
{
    /**
     * Log an audit event
     */
    protected function logAudit(
        string $action,
        string $modelType = null,
        int $modelId = null,
        string $modelName = null,
        array $oldValues = null,
        array $newValues = null,
        string $description = null,
        array $metadata = null
    ): void {
        $request = request();

        AuditLog::create([
            'user_id' => Auth::id(),
            'action' => $action,
            'model_type' => $modelType,
            'model_id' => $modelId,
            'model_name' => $modelName,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'description' => $description,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Log model creation
     */
    protected function logCreate($model, string $description = null): void
    {
        $this->logAudit(
            action: 'create',
            modelType: get_class($model),
            modelId: $model->id,
            modelName: $this->getModelName($model),
            newValues: $model->getAttributes(),
            description: $description ?: "Created " . class_basename($model)
        );
    }

    /**
     * Log model update
     */
    protected function logUpdate($model, array $oldValues, string $description = null): void
    {
        $this->logAudit(
            action: 'update',
            modelType: get_class($model),
            modelId: $model->id,
            modelName: $this->getModelName($model),
            oldValues: $oldValues,
            newValues: $model->getChanges(),
            description: $description ?: "Updated " . class_basename($model)
        );
    }

    /**
     * Log model deletion
     */
    protected function logDelete($model, string $description = null): void
    {
        $this->logAudit(
            action: 'delete',
            modelType: get_class($model),
            modelId: $model->id,
            modelName: $this->getModelName($model),
            oldValues: $model->getAttributes(),
            description: $description ?: "Deleted " . class_basename($model)
        );
    }

    /**
     * Log view action
     */
    protected function logView($model, string $description = null): void
    {
        $this->logAudit(
            action: 'view',
            modelType: get_class($model),
            modelId: $model->id,
            modelName: $this->getModelName($model),
            description: $description ?: "Viewed " . class_basename($model)
        );
    }

    /**
     * Log login
     */
    protected function logLogin($user = null): void
    {
        $this->logAudit(
            action: 'login',
            modelType: $user ? get_class($user) : null,
            modelId: $user ? $user->id : null,
            modelName: $user ? $user->name : null,
            description: "User logged in"
        );
    }

    /**
     * Log logout
     */
    protected function logLogout($user = null): void
    {
        $this->logAudit(
            action: 'logout',
            modelType: $user ? get_class($user) : null,
            modelId: $user ? $user->id : null,
            modelName: $user ? $user->name : null,
            description: "User logged out"
        );
    }

    /**
     * Log custom action
     */
    protected function logCustomAction(
        string $action,
        string $description,
        $model = null,
        array $metadata = null
    ): void {
        $this->logAudit(
            action: $action,
            modelType: $model ? get_class($model) : null,
            modelId: $model ? $model->id : null,
            modelName: $model ? $this->getModelName($model) : null,
            description: $description,
            metadata: $metadata
        );
    }

    /**
     * Get model name for display
     */
    private function getModelName($model): string
    {
        // Try common name fields
        $nameFields = ['name', 'title', 'email', 'username'];

        foreach ($nameFields as $field) {
            if (isset($model->$field)) {
                return $model->$field;
            }
        }

        // Fallback to class name with ID
        return class_basename($model) . ' #' . $model->id;
    }
}
