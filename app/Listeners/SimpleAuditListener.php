<?php

namespace App\Listeners;

use App\Models\AuditLog;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Http\Request;

class SimpleAuditListener
{
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function handleLogin(Login $event): void
    {
        // Check if we already logged this login event recently (within 1 second)
        $recentLogin = AuditLog::where('user_id', $event->user->id)
            ->where('action', 'login')
            ->where('created_at', '>=', now()->subSeconds(1))
            ->first();

        if ($recentLogin) {
            return; // Skip duplicate login log
        }

        AuditLog::create([
            'user_id' => $event->user->id,
            'action' => 'login',
            'model_type' => get_class($event->user),
            'model_id' => $event->user->id,
            'model_name' => $event->user->name,
            'ip_address' => $this->request->ip(),
            'user_agent' => $this->request->userAgent(),
            'url' => $this->request->fullUrl(),
            'method' => $this->request->method(),
            'description' => "User {$event->user->name} logged in successfully",
            'metadata' => [
                'login_time' => now()->toISOString(),
                'remember' => $event->remember,
                'guard' => $event->guard,
            ],
        ]);
    }

    public function handleLogout(Logout $event): void
    {
        if ($event->user) {
            // Check if we already logged this logout event recently (within 1 second)
            $recentLogout = AuditLog::where('user_id', $event->user->id)
                ->where('action', 'logout')
                ->where('created_at', '>=', now()->subSeconds(1))
                ->first();

            if ($recentLogout) {
                return; // Skip duplicate logout log
            }

            AuditLog::create([
                'user_id' => $event->user->id,
                'action' => 'logout',
                'model_type' => get_class($event->user),
                'model_id' => $event->user->id,
                'model_name' => $event->user->name,
                'ip_address' => $this->request->ip(),
                'user_agent' => $this->request->userAgent(),
                'url' => $this->request->fullUrl(),
                'method' => $this->request->method(),
                'description' => "User {$event->user->name} logged out",
                'metadata' => [
                    'logout_time' => now()->toISOString(),
                    'guard' => $event->guard,
                ],
            ]);
        }
    }
}
