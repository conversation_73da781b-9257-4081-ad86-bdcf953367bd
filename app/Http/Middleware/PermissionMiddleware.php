<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PermissionMiddleware
{
    /**
     * Handle an incoming request and ensure user has one of the specified permissions.
     * This middleware works with both legacy permission field and Spatie permissions
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  mixed  ...$permissions
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ...$permissions)
    {
        if (!Auth::check()) {
            return redirect('login');
        }

        /** @var \App\Models\User $user */
        $user = Auth::user();

        // Super admin bypasses all permission checks - has complete access
        if ($user->hasRole('super_admin')) {
            \Log::info('Super admin bypassing permission check for: ' . implode(', ', $permissions) . ' on route: ' . $request->path());
            return $next($request);
        }

        // Check if user has any of the required permissions
        foreach ($permissions as $permission) {
            if ($user->hasPermission($permission)) {
                \Log::info('User has permission: ' . $permission . ' for route: ' . $request->path());
                return $next($request);
            }
        }

        \Log::warning('User does not have any of the required permissions: ' . implode(', ', $permissions) . ' for route: ' . $request->path());
        abort(403, 'Unauthorized action.');
    }
}
