<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\AuditLog;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AuditLogMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only log for authenticated users and successful responses
        if (Auth::check() && $response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
            $this->logRequest($request, $response);
        }

        return $response;
    }

    /**
     * Log the request details
     */
    private function logRequest(Request $request, Response $response): void
    {
        $method = $request->method();
        $path = $request->path();
        $action = $this->determineAction($method, $path);

        // Skip logging for certain routes
        if ($this->shouldSkipLogging($path, $action)) {
            return;
        }

        $description = $this->generateDescription($method, $path, $request);

        AuditLog::create([
            'user_id' => Auth::id(),
            'action' => $action,
            'model_type' => $this->getModelType($path),
            'model_id' => $this->getModelId($request),
            'model_name' => $this->getModelName($request, $path),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $method,
            'description' => $description,
            'metadata' => $this->getMetadata($request, $response),
        ]);
    }

    /**
     * Determine the action based on method and path
     */
    private function determineAction(string $method, string $path): string
    {
        // Special cases for specific routes
        if (str_contains($path, 'login')) {
            return 'login';
        }

        if (str_contains($path, 'logout')) {
            return 'logout';
        }

        if (str_contains($path, 'register')) {
            return 'register';
        }

        if (str_contains($path, 'check-in')) {
            return 'check_in';
        }

        if (str_contains($path, 'check-out')) {
            return 'check_out';
        }

        // Default actions based on HTTP method
        return match ($method) {
            'GET' => 'view',
            'POST' => 'create',
            'PUT', 'PATCH' => 'update',
            'DELETE' => 'delete',
            default => 'access',
        };
    }

    /**
     * Check if we should skip logging for this request
     */
    private function shouldSkipLogging(string $path, string $action): bool
    {
        $skipPaths = [
            'audit-logs',
            'api/',
            'css/',
            'js/',
            'images/',
            'assets/',
            'favicon.ico',
            'login', // Skip login route - handled by event listener
            'logout', // Skip logout route - handled by event listener
            'register', // Skip register route - handled by event listener
        ];

        $skipActions = [
            'view', // Skip GET requests to reduce noise
            'login', // Skip login action - handled by event listener
            'logout', // Skip logout action - handled by event listener
        ];

        foreach ($skipPaths as $skipPath) {
            if (str_starts_with($path, $skipPath)) {
                return true;
            }
        }

        return in_array($action, $skipActions);
    }

    /**
     * Generate a human-readable description
     */
    private function generateDescription(string $method, string $path, Request $request): string
    {
        $user = Auth::user();
        $userName = $user ? $user->name : 'Unknown User';

        // Special cases
        if (str_contains($path, 'login')) {
            return "{$userName} logged in";
        }

        if (str_contains($path, 'logout')) {
            return "{$userName} logged out";
        }

        if (str_contains($path, 'register')) {
            return "New user registration";
        }

        if (str_contains($path, 'check-in')) {
            return "{$userName} checked in";
        }

        if (str_contains($path, 'check-out')) {
            return "{$userName} checked out";
        }

        // Generic descriptions
        $resource = $this->getResourceName($path);

        return match ($method) {
            'GET' => "{$userName} viewed {$resource}",
            'POST' => "{$userName} created {$resource}",
            'PUT', 'PATCH' => "{$userName} updated {$resource}",
            'DELETE' => "{$userName} deleted {$resource}",
            default => "{$userName} accessed {$resource}",
        };
    }

    /**
     * Get the model type from the path
     */
    private function getModelType(string $path): ?string
    {
        $segments = explode('/', $path);

        // Skip common prefixes
        $skipPrefixes = ['admin', 'api', 'dashboard'];
        foreach ($segments as $segment) {
            if (!in_array($segment, $skipPrefixes) && !is_numeric($segment)) {
                $modelName = str_replace('-', '', ucwords($segment, '-'));
                return "App\\Models\\{$modelName}";
            }
        }

        return null;
    }

    /**
     * Get the model ID from the request
     */
    private function getModelId(Request $request): ?int
    {
        $route = $request->route();
        if ($route && isset($route->parameters()['id'])) {
            return (int) $route->parameters()['id'];
        }

        return null;
    }

    /**
     * Get the model name for display
     */
    private function getModelName(Request $request, string $path): ?string
    {
        $route = $request->route();
        if ($route) {
            $parameters = $route->parameters();

            // Try to get a model instance
            foreach ($parameters as $key => $value) {
                if (is_object($value) && method_exists($value, 'getKey')) {
                    // Try common name fields
                    $nameFields = ['name', 'title', 'email', 'username'];
                    foreach ($nameFields as $field) {
                        if (isset($value->$field)) {
                            return $value->$field;
                        }
                    }
                    return class_basename($value) . ' #' . $value->getKey();
                }
            }
        }

        return null;
    }

    /**
     * Get the resource name from the path
     */
    private function getResourceName(string $path): string
    {
        $segments = explode('/', $path);
        $resource = end($segments);

        // Clean up the resource name
        $resource = str_replace('-', ' ', $resource);
        $resource = ucwords($resource);

        return $resource ?: 'page';
    }

    /**
     * Get additional metadata
     */
    private function getMetadata(Request $request, Response $response): array
    {
        return [
            'status_code' => $response->getStatusCode(),
            'content_type' => $response->headers->get('Content-Type'),
            'request_size' => strlen($request->getContent()),
            'response_size' => strlen($response->getContent()),
            'execution_time' => microtime(true) - LARAVEL_START,
        ];
    }
}
