<?php

namespace App\Http\Controllers;

use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AuditLogController extends Controller
{
    /**
     * Display a listing of the audit logs
     */
    public function index(Request $request)
    {
        $query = AuditLog::with('user')->latest();

        // Filter by action
        if ($request->filled('action')) {
            $query->where('action', $request->action);
        }

        // Filter by user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by model type
        if ($request->filled('model_type')) {
            $query->where('model_type', $request->model_type);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Search in description
        if ($request->filled('search')) {
            $query->where('description', 'like', '%' . $request->search . '%');
        }

        $logs = $query->paginate(20);

        // Get filter options
        $actions = AuditLog::distinct()->pluck('action')->sort();
        $modelTypes = AuditLog::distinct()->pluck('model_type')->filter()->sort();
        $users = User::whereIn('id', AuditLog::distinct()->pluck('user_id')->filter())->get();

        return view('admin.audit-logs.index', compact('logs', 'actions', 'modelTypes', 'users'));
    }

    /**
     * Display the specified audit log
     */
    public function show(AuditLog $auditLog)
    {
        $auditLog->load('user');

        return view('admin.audit-logs.show', compact('auditLog'));
    }

    /**
     * Get audit log statistics
     */
    public function statistics(Request $request)
    {
        $days = $request->get('days', 30);
        $startDate = now()->subDays($days);

        $stats = [
            'total_logs' => AuditLog::where('created_at', '>=', $startDate)->count(),
            'actions' => AuditLog::where('created_at', '>=', $startDate)
                ->select('action', DB::raw('count(*) as count'))
                ->groupBy('action')
                ->orderBy('count', 'desc')
                ->get(),
            'users' => AuditLog::where('created_at', '>=', $startDate)
                ->with('user')
                ->select('user_id', DB::raw('count(*) as count'))
                ->groupBy('user_id')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get(),
            'models' => AuditLog::where('created_at', '>=', $startDate)
                ->whereNotNull('model_type')
                ->select('model_type', DB::raw('count(*) as count'))
                ->groupBy('model_type')
                ->orderBy('count', 'desc')
                ->get(),
            'daily_activity' => AuditLog::where('created_at', '>=', $startDate)
                ->select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
        ];

        return view('admin.audit-logs.statistics', compact('stats', 'days'));
    }

    /**
     * Export audit logs
     */
    public function export(Request $request)
    {
        $query = AuditLog::with('user')->latest();

        // Apply same filters as index
        if ($request->filled('action')) {
            $query->where('action', $request->action);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('model_type')) {
            $query->where('model_type', $request->model_type);
        }

        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        if ($request->filled('search')) {
            $query->where('description', 'like', '%' . $request->search . '%');
        }

        $logs = $query->limit(1000)->get();

        $filename = 'audit_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($logs) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'User',
                'Action',
                'Model Type',
                'Model ID',
                'Model Name',
                'Description',
                'IP Address',
                'User Agent',
                'URL',
                'Method',
                'Created At'
            ]);

            // CSV data
            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->user ? $log->user->name : 'System',
                    $log->action,
                    $log->model_type,
                    $log->model_id,
                    $log->model_name,
                    $log->formatted_description,
                    $log->ip_address,
                    $log->user_agent,
                    $log->url,
                    $log->method,
                    $log->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Clean old audit logs (only super admin can do this)
     */
    public function clean(Request $request)
    {
        // Only super admin can clean audit logs
        if (!auth()->user()->hasRole('super_admin')) {
            return redirect()->route('audit-logs.index')
                ->with('error', 'Only super admin can clean audit logs.');
        }

        $days = $request->get('days', 90);

        // Prevent cleaning logs newer than 30 days
        if ($days < 30) {
            return redirect()->route('audit-logs.index')
                ->with('error', 'Cannot clean audit logs newer than 30 days.');
        }

        $deletedCount = AuditLog::where('created_at', '<', now()->subDays($days))->delete();

        // Log this critical action
        \App\Models\AuditLog::create([
            'user_id' => auth()->id(),
            'action' => 'clean_audit_logs',
            'description' => "Super admin cleaned {$deletedCount} audit logs older than {$days} days",
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'metadata' => [
                'deleted_count' => $deletedCount,
                'days_threshold' => $days,
                'action_type' => 'system_maintenance'
            ],
        ]);

        return redirect()->route('audit-logs.index')
            ->with('success', "Cleaned {$deletedCount} audit logs older than {$days} days.");
    }

    /**
     * Prevent deletion of individual audit logs
     */
    public function destroy(AuditLog $auditLog)
    {
        return redirect()->route('audit-logs.index')
            ->with('error', 'Individual audit logs cannot be deleted for security and compliance reasons.');
    }
}
