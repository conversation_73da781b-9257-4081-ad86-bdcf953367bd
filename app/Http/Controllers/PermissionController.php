<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;

class PermissionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:full');
    }

    /**
     * Display a listing of permissions
     */
    public function index()
    {
        $permissions = Permission::orderBy('name')->get();
        $roles = Role::with('permissions')->get();
        $users = User::all(); // Remove the with('roles') to avoid conflicts

        return view('permissions.index', compact('permissions', 'roles', 'users'));
    }

    /**
     * Show the form for creating a new permission
     */
    public function create()
    {
        return view('permissions.create');
    }

    /**
     * Store a newly created permission
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name',
            'guard_name' => 'required|string|max:255'
        ]);

        Permission::create([
            'name' => $request->name,
            'guard_name' => $request->guard_name
        ]);

        return redirect()->route('permissions.index')
            ->with('success', 'Permission created successfully.');
    }

    /**
     * Display the specified permission
     */
    public function show(Permission $permission)
    {
        $roles = Role::all();
        $users = User::all();

        return view('permissions.show', compact('permission', 'roles', 'users'));
    }

    /**
     * Show the form for editing the specified permission
     */
    public function edit(Permission $permission)
    {
        return view('permissions.edit', compact('permission'));
    }

    /**
     * Update the specified permission
     */
    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name,' . $permission->id,
            'guard_name' => 'required|string|max:255'
        ]);

        $permission->update([
            'name' => $request->name,
            'guard_name' => $request->guard_name
        ]);

        return redirect()->route('permissions.index')
            ->with('success', 'Permission updated successfully.');
    }

    /**
     * Remove the specified permission
     */
    public function destroy(Permission $permission)
    {
        $permission->delete();

        return redirect()->route('permissions.index')
            ->with('success', 'Permission deleted successfully.');
    }

    /**
     * Assign permission to role
     */
    public function assignToRole(Request $request)
    {
        $request->validate([
            'permission_id' => 'required|exists:permissions,id',
            'role_id' => 'required|exists:roles,id'
        ]);

        $permission = Permission::findOrFail($request->permission_id);
        $role = Role::findOrFail($request->role_id);

        if (!$role->hasPermissionTo($permission)) {
            $role->givePermissionTo($permission);
            return response()->json(['success' => true, 'message' => 'Permission assigned to role successfully.']);
        }

        return response()->json(['success' => false, 'message' => 'Role already has this permission.']);
    }

    /**
     * Remove permission from role
     */
    public function removeFromRole(Request $request)
    {
        $request->validate([
            'permission_id' => 'required|exists:permissions,id',
            'role_id' => 'required|exists:roles,id'
        ]);

        $permission = Permission::findOrFail($request->permission_id);
        $role = Role::findOrFail($request->role_id);

        if ($role->hasPermissionTo($permission)) {
            $role->revokePermissionTo($permission);
            return response()->json(['success' => true, 'message' => 'Permission removed from role successfully.']);
        }

        return response()->json(['success' => false, 'message' => 'Role does not have this permission.']);
    }

    /**
     * Assign permission to user
     */
    public function assignToUser(Request $request)
    {
        $request->validate([
            'permission_id' => 'required|exists:permissions,id',
            'user_id' => 'required|exists:users,id'
        ]);

        $permission = Permission::findOrFail($request->permission_id);
        $user = User::findOrFail($request->user_id);

        if (!$user->hasPermissionTo($permission)) {
            $user->givePermissionTo($permission);
            return response()->json(['success' => true, 'message' => 'Permission assigned to user successfully.']);
        }

        return response()->json(['success' => false, 'message' => 'User already has this permission.']);
    }

    /**
     * Remove permission from user
     */
    public function removeFromUser(Request $request)
    {
        $request->validate([
            'permission_id' => 'required|exists:permissions,id',
            'user_id' => 'required|exists:users,id'
        ]);

        $permission = Permission::findOrFail($request->permission_id);
        $user = User::findOrFail($request->user_id);

        if ($user->hasPermissionTo($permission)) {
            $user->revokePermissionTo($permission);
            return response()->json(['success' => true, 'message' => 'Permission removed from user successfully.']);
        }

        return response()->json(['success' => false, 'message' => 'User does not have this permission.']);
    }
}
