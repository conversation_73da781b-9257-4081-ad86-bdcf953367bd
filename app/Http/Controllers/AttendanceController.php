<?php

namespace App\Http\Controllers;

use App\Models\Attendance;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\LeaveRequest;
use App\Traits\AuditLoggable;

class AttendanceController extends Controller
{
    use AuditLoggable;
    // Member: view own
    public function myIndex()
    {
        $user = Auth::user();
        $team = Team::where('user_id', $user->id)->first();
        $attendances = $team ? Attendance::where('team_id', $team->id)->orderByDesc('date')->paginate(30) : collect();
        return view('attendance.my', compact('attendances', 'team'));
    }

    public function myStore(Request $request)
    {
        $user = Auth::user();
        $team = Team::where('user_id', $user->id)->firstOrFail();

        $validated = $request->validate([
            'date' => 'required|date',
            'status' => 'nullable|in:present,absent,leave,half_day',
            'check_in' => 'nullable|date_format:H:i',
            'check_out' => 'nullable|date_format:H:i|after_or_equal:check_in',
            'notes' => 'nullable|string',
        ]);

        // Block edits on days auto-marked as leave
        $existing = Attendance::where('team_id', $team->id)
            ->where('date', $validated['date'])
            ->first();

        if ($existing && $existing->status === 'leave') {
            return back()->with('error', 'This date is marked as approved leave and cannot be changed.');
        }

        // If user is checking in/out, force status to present
        if (!empty($validated['check_in']) || !empty($validated['check_out'])) {
            $validated['status'] = 'present';
        }

        // Default status when none provided
        if (empty($validated['status'])) {
            $validated['status'] = 'present';
        }

        // Only create leave request if user manually changes status (not from check-in/out) and adds notes
        // Check-in/out actions should NEVER create leave requests
        if (!empty($validated['notes']) && $validated['status'] !== 'present' && empty($validated['check_in']) && empty($validated['check_out'])) {
            $leaveType = 'casual'; // Default leave type

            // Map attendance status to leave type
            if ($validated['status'] === 'absent') {
                $leaveType = 'unpaid';
            } elseif ($validated['status'] === 'half_day') {
                $leaveType = 'half_day';
            } elseif ($validated['status'] === 'leave') {
                $leaveType = 'casual';
            }

            LeaveRequest::updateOrCreate(
                [
                    'team_id' => $team->id,
                    'start_date' => $validated['date'],
                    'end_date' => $validated['date'],
                ],
                [
                    'leave_type' => $leaveType,
                    'reason' => $validated['notes'],
                    'status' => 'pending',
                    'approved_by' => null,
                ]
            );
        }

        Attendance::updateOrCreate(
            ['team_id' => $team->id, 'date' => $validated['date']],
            [
                'status' => $validated['status'],
                'check_in' => $validated['check_in'] ?? null,
                'check_out' => $validated['check_out'] ?? null,
                'notes' => $validated['notes'] ?? null,
            ]
        );

        $message = 'Attendance saved.';
        if (!empty($validated['notes']) && $validated['status'] !== 'present' && empty($validated['check_in']) && empty($validated['check_out'])) {
            $message .= ' Leave request created and pending approval.';
        }

        return back()->with('success', $message);
    }

    // Admin: CRUD
    public function index()
    {
        $attendances = Attendance::with('team.user')->latest('date')->paginate(10);
        return view('attendance.index', compact('attendances'));
    }

    public function show(Attendance $attendance)
    {
        return view('attendance.show', compact('attendance'));
    }

    public function create()
    {
        $teams = Team::with('user')->get();
        return view('attendance.create', compact('teams'));
    }

    public function edit(Attendance $attendance)
    {
        $teams = Team::with('user')->get();
        return view('attendance.edit', compact('attendance', 'teams'));
    }

    public function update(Request $request, Attendance $attendance)
    {
        $validated = $request->validate([
            'team_id' => 'required|exists:teams,id',
            'date' => 'required|date',
            'status' => 'required|in:present,absent,leave,half_day',
            'check_in' => 'nullable|date_format:H:i',
            'check_out' => 'nullable|date_format:H:i|after_or_equal:check_in',
            'notes' => 'nullable|string',
        ]);

        $attendance->update($validated);
        return redirect()->route('attendance.index')->with('success', 'Attendance updated.');
    }

    public function destroy(Attendance $attendance)
    {
        $attendance->delete();
        return back()->with('success', 'Attendance deleted.');
    }

    // Check-in functionality
    public function checkIn()
    {
        $user = Auth::user();
        $team = Team::where('user_id', $user->id)->first();

        if (!$team) {
            return back()->with('error', 'You are not assigned to any team.');
        }

        $today = now()->toDateString();
        $currentTime = now()->format('H:i');

        // Check if already checked in today
        $existingAttendance = Attendance::where('team_id', $team->id)
            ->where('date', $today)
            ->first();

        if ($existingAttendance && $existingAttendance->check_in) {
            return back()->with('error', 'You have already checked in today.');
        }

        // Create or update attendance record
        $attendance = Attendance::updateOrCreate(
            [
                'team_id' => $team->id,
                'date' => $today
            ],
            [
                'status' => 'present',
                'check_in' => $currentTime,
                'check_out' => null
            ]
        );

        // Log the check-in action
        $this->logCustomAction('check_in', "User {$user->name} checked in at {$currentTime}", $attendance, [
            'check_in_time' => $currentTime,
            'date' => $today,
            'team_id' => $team->id
        ]);

        return back()->with('success', 'Successfully checked in at ' . $currentTime);
    }

    // Check-out functionality
    public function checkOut()
    {
        $user = Auth::user();
        $team = Team::where('user_id', $user->id)->first();

        if (!$team) {
            return back()->with('error', 'You are not assigned to any team.');
        }

        $today = now()->toDateString();
        $currentTime = now()->format('H:i');

        // Check if checked in today
        $existingAttendance = Attendance::where('team_id', $team->id)
            ->where('date', $today)
            ->first();

        if (!$existingAttendance || !$existingAttendance->check_in) {
            return back()->with('error', 'You must check in before checking out.');
        }

        if ($existingAttendance->check_out) {
            return back()->with('error', 'You have already checked out today.');
        }

        // Update attendance record with check-out time
        $existingAttendance->update([
            'check_out' => $currentTime
        ]);

        // Log the check-out action
        $this->logCustomAction('check_out', "User {$user->name} checked out at {$currentTime}", $existingAttendance, [
            'check_out_time' => $currentTime,
            'check_in_time' => $existingAttendance->check_in,
            'date' => $today,
            'team_id' => $team->id
        ]);

        return back()->with('success', 'Successfully checked out at ' . $currentTime);
    }

    // Get current attendance status for dashboard
    public function getCurrentStatus()
    {
        $user = Auth::user();
        $team = Team::where('user_id', $user->id)->first();

        if (!$team) {
            return response()->json(['error' => 'No team assigned']);
        }

        $today = now()->toDateString();
        $attendance = Attendance::where('team_id', $team->id)
            ->where('date', $today)
            ->first();

        return response()->json([
            'checked_in' => $attendance && $attendance->check_in ? true : false,
            'checked_out' => $attendance && $attendance->check_out ? true : false,
            'check_in_time' => $attendance && $attendance->check_in ? $attendance->check_in : null,
            'check_out_time' => $attendance && $attendance->check_out ? $attendance->check_out : null,
        ]);
    }
}


