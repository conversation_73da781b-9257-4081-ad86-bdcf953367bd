<?php

namespace App\Http\Controllers;

use App\Models\Salary;
use App\Models\Team;
use App\Models\ProjectManagement;
use App\Models\Attendance;
use App\Models\Expense;
use App\Models\Label;
use App\Models\TeamAchievement;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SalaryController extends Controller
{
    protected function sumDeliveredAmounts(Team $team, Carbon $startOfMonth, Carbon $endOfMonth): float
    {
        $idCandidates = [$team->id, $team->user_id];

        // Sum delivered amounts for projects that have been delivered (have delivered_amount)
        // or are completed, within the specified month range
        $sum = ProjectManagement::where(function($q) use ($startOfMonth, $endOfMonth) {
                // If delivered_date exists, use it for date filtering
                $q->where(function($qq) use ($startOfMonth, $endOfMonth) {
                    $qq->whereNotNull('delivered_date')
                       ->whereBetween('delivered_date', [$startOfMonth, $endOfMonth]);
                })
                // If no delivered_date, use updated_at for date filtering
                ->orWhere(function($qq) use ($startOfMonth, $endOfMonth) {
                    $qq->whereNull('delivered_date')
                       ->whereBetween('updated_at', [$startOfMonth, $endOfMonth]);
                });
            })
            ->where('assigned_by', $team->id) // Only count projects assigned to this team
            ->where(function($q) {
                // Include projects that are completed OR have delivered_amount
                $q->where('status', 'completed')
                  ->orWhereNotNull('delivered_amount');
            })
            ->selectRaw('COALESCE(SUM(delivered_amount), 0) as s1, COALESCE(SUM(CASE WHEN delivered_amount IS NULL THEN amount ELSE 0 END), 0) as s2')
            ->first();

        return (float) (($sum->s1 ?? 0) + ($sum->s2 ?? 0));
    }

    protected function calculateAttendanceBonus(Team $team, Carbon $startOfMonth, Carbon $endOfMonth): float
    {
        $bonusPerHour = 10.00; // $10 per overtime hour
        $regularHoursPerDay = 8; // Standard 8-hour workday
        $totalBonus = 0.00;

        // Get all attendance records for the team in the specified month
        $attendances = Attendance::where('team_id', $team->id)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('status', 'present')
            ->whereNotNull('check_in')
            ->whereNotNull('check_out')
            ->get();

        foreach ($attendances as $attendance) {
            if ($attendance->check_in && $attendance->check_out) {
                // Calculate hours worked using the time difference
                $checkInHour = (int) $attendance->check_in->format('H');
                $checkOutHour = (int) $attendance->check_out->format('H');

                // Calculate total hours worked
                $totalHoursWorked = $checkOutHour - $checkInHour;

                // Calculate overtime hours (hours beyond regular workday)
                $overtimeHours = max(0, $totalHoursWorked - $regularHoursPerDay);

                // Add bonus only for overtime hours
                $totalBonus += $overtimeHours * $bonusPerHour;
            }
        }

        return $totalBonus;
    }

    protected function calculateExpenseDeduction(Team $team, Carbon $startOfMonth, Carbon $endOfMonth): float
    {
        // Get all approved expenses for the team in the specified month
        $expenses = Expense::where('user_id', $team->user_id)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('status', 'approved')
            ->get();

        $totalExpenseAmount = 0.00;

        foreach ($expenses as $expense) {
            // Use accepted_amount if set, otherwise use given_amount
            $expenseAmount = $expense->accepted_amount ?? $expense->given_amount;
            $totalExpenseAmount += $expenseAmount;
        }

        return $totalExpenseAmount;
    }

    public function index()
    {
        $teams = Team::with(['user', 'designation', 'label'])->get();
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();
        $currentYear = (int) Carbon::now()->year;
        $currentMonth = (int) Carbon::now()->month;
        $generatedSalaries = [];
        $totalPaid = 0;

        foreach ($teams as $team) {
            $userId = $team->user_id;
            $base = $team->designation->base_amount ?? 0;
            $bonusPercent = $team->label->bonus_percent ?? 0;
            $bonusAmountFull = ($base * $bonusPercent) / 100;

            $workingDeliveryAmount = $this->sumDeliveredAmounts($team, $startOfMonth, $endOfMonth);

            $target = floatval($team->target ?? 0);
            $achievedTarget = max(0, $workingDeliveryAmount);

            $achievementRatio = ($target > 0) ? max(0, min(1, $workingDeliveryAmount / $target)) : 1;
            $amount = $base * $achievementRatio;

            $existingSalary = Salary::where('team_id', $team->id)
                ->where('year', $currentYear)
                ->where('month', $currentMonth)
                ->first();

            $computedBonus = ($bonusAmountFull * $achievementRatio);
            $bonus = ($existingSalary && $existingSalary->bonus > 0) ? $existingSalary->bonus : $computedBonus;

            // Calculate attendance bonus ($10 per hour)
            $attendanceBonus = $this->calculateAttendanceBonus($team, $startOfMonth, $endOfMonth);

            $total = $amount + $bonus + $attendanceBonus;

            $generatedSalaries[] = (object)[
                'team' => $team,
                'user_name' => $team->user->name ?? 'N/A',
                'target' => $target,
                'delivered' => $workingDeliveryAmount,
                'amount' => $amount,
                'bonus' => $bonus,
                'attendance_bonus' => $attendanceBonus,
                'total_paid' => $total,
                'achieved_target' => $achievedTarget,
                'created_at' => $existingSalary?->created_at ?? now(),
            ];

            $totalPaid += $total;
        }

        $projectSalaries = Salary::with('team.user')
            ->where('year', $currentYear)
            ->where('month', $currentMonth)
            ->get();

        return view('salaries.index', [
            'salaries' => $generatedSalaries,
            'totalPaid' => $totalPaid,
            'projectSalaries' => $projectSalaries,
            'periodYear' => $currentYear,
            'periodMonth' => $currentMonth,
        ]);
    }

    public function create()
    {
        $teams = Team::with('user')->get();
        return view('salaries.create', compact('teams'));
    }

    public function getSalaryComponents($team_id)
    {
        $team = Team::with(['designation', 'label'])->findOrFail($team_id);

        $base = $team->designation->base_amount ?? 0;
        $bonusPercent = $team->label->bonus_percent ?? 0;
        $bonusAmount = ($base * $bonusPercent) / 100;
        $totalAmount = $base + $bonusAmount;

        return response()->json([
            'base' => $base,
            'bonus' => $bonusAmount,
            'total' => $totalAmount,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $this->validateSalary($request, true);

        $team = Team::with(['designation', 'label', 'user'])->findOrFail($validated['team_id']);
        $userId = $team->user_id;

        $currentYear = (int) Carbon::now()->year;
        $currentMonth = (int) Carbon::now()->month;

        $base = $team->designation->base_amount ?? 0;
        $bonusPercent = $team->label->bonus_percent ?? 0;

        $startOfMonth = Carbon::create($currentYear, $currentMonth, 1)->startOfMonth();
        $endOfMonth = Carbon::create($currentYear, $currentMonth, 1)->endOfMonth();

        $workingDeliveryAmount = $this->sumDeliveredAmounts($team, $startOfMonth, $endOfMonth);

        $target = floatval($team->target ?? 0);
        $achievedTarget = max(0, $workingDeliveryAmount);

        $achievementRatio = ($target > 0) ? max(0, min(1, $workingDeliveryAmount / $target)) : 1;
        $amount = $base * $achievementRatio;

        // Calculate label bonus based on achieved target amount
        $labelBonus = ($achievedTarget * $bonusPercent) / 100;

        // Get extra bonus from form (if provided)
        $extraBonus = $validated['bonus'] ?? 0;

        // Total bonus = label bonus + extra bonus
        $totalBonus = $labelBonus + $extraBonus;

        // Calculate attendance bonus
        $attendanceBonus = $this->calculateAttendanceBonus($team, $startOfMonth, $endOfMonth);

        // Calculate expense deduction
        $expenseDeduction = $this->calculateExpenseDeduction($team, $startOfMonth, $endOfMonth);

        $payAmount = $amount + $totalBonus + $attendanceBonus - $expenseDeduction;

        $salaryData = [
            'team_id' => $validated['team_id'],
            'year' => $currentYear,
            'month' => $currentMonth,
            'amount' => $payAmount,
            'bonus' => $totalBonus, // Total bonus (label + extra)
            'attendance_bonus' => $attendanceBonus,
            'achieved_target' => $achievedTarget,
            'expense_deduction' => $expenseDeduction,
        ];

        Salary::create($salaryData);

        return redirect()->route('salaries.index')->with('success', 'Salary record created successfully.');
    }

    public function edit(Salary $salary)
    {
        $teams = Team::with('user')->get();
        return view('salaries.edit', compact('salary', 'teams'));
    }

        public function update(Request $request, Salary $salary)
    {
        $validated = $this->validateSalary($request, false);

        // Only update the extra bonus amount
        $extraBonus = $validated['bonus'] ?? 0;

        // Get the existing salary data
        $existingAttendanceBonus = $salary->attendance_bonus;
        $existingAchievedTarget = $salary->achieved_target;

        // Calculate the base amount (performance-based salary)
        $team = Team::with(['designation', 'label'])->findOrFail($salary->team_id);
        $base = $team->designation->base_amount ?? 0;
        $target = floatval($team->target ?? 0);
        $achievementRatio = ($target > 0) ? max(0, min(1, $existingAchievedTarget / $target)) : 1;
        $baseAmount = $base * $achievementRatio;

        // Calculate the label bonus from existing achieved target
        $bonusPercent = $team->label->bonus_percent ?? 0;
        $labelBonus = ($existingAchievedTarget * $bonusPercent) / 100;

        // Total bonus = label bonus + extra bonus
        $totalBonus = $labelBonus + $extraBonus;

        // Recalculate total pay amount
        $payAmount = $baseAmount + $totalBonus + $existingAttendanceBonus;

        $salary->update([
            'bonus' => $totalBonus, // Total bonus (label + extra)
            'amount' => $payAmount, // Updated total amount
        ]);

        return redirect()->route('salaries.index')->with('success', 'Extra bonus updated successfully.');
    }

    public function destroy(Salary $salary)
    {
        $salary->delete();
        return redirect()->route('salaries.index')->with('success', 'Salary record deleted successfully.');
    }

    /**
     * Validate salary data
     */
    protected function validateSalary(Request $request, bool $isCreate): array
    {
        $rules = [
            'year' => 'nullable|integer|min:2000|max:2100',
            'month' => 'nullable|integer|min:1|max:12',
            'amount' => 'nullable|numeric|min:0',
            'bonus' => 'nullable|numeric|min:0',
            'attendance_bonus' => 'nullable|numeric|min:0',
            'achieved_target' => 'nullable|numeric|min:0',
        ];

        // Additional validation for create operation
        if ($isCreate) {
            $rules['team_id'] = 'required|exists:teams,id';

            // Check for duplicate salary for the same team and period
            $year = $request->get('year', Carbon::now()->year);
            $month = $request->get('month', Carbon::now()->month);

            $duplicateExists = Salary::where('team_id', $request->team_id)
                ->where('year', $year)
                ->where('month', $month)
                ->exists();

            if ($duplicateExists) {
                throw new \Illuminate\Validation\ValidationException(
                    validator([], []),
                    'Salary for this team and month already exists.'
                );
            }
        }

        return $request->validate($rules);
    }

    public function myPayslips(Request $request)
    {
        $user = auth()->user();
        $team = Team::where('user_id', $user->id)->first();
        if (!$team) {
            return view('salaries.my', [
                'payslips' => collect(),
                'team' => null,
            ]);
        }

        $payslips = Salary::where('team_id', $team->id)
            ->orderByDesc('year')
            ->orderByDesc('month')
            ->get();

        return view('salaries.my', compact('payslips', 'team'));
    }

    public function myPayslip(int $year, int $month)
    {
        $user = auth()->user();
        $team = Team::where('user_id', $user->id)->firstOrFail();

        $summary = Salary::where('team_id', $team->id)
            ->where('year', $year)
            ->where('month', $month)
            ->first();

        return view('salaries.payslip', [
            'team' => $team,
            'year' => $year,
            'month' => $month,
            'summary' => $summary,
        ]);
    }

    public function myTargetAchievement(Request $request)
    {
        $user = auth()->user();
        $team = Team::where('user_id', $user->id)->first();

        if (!$team) {
            return view('salaries.target_achievement', [
                'targetData' => collect(),
                'team' => null,
                'currentYear' => Carbon::now()->year,
                'currentMonth' => Carbon::now()->month,
            ]);
        }

        $year = $request->get('year', Carbon::now()->year);
        $month = $request->get('month', Carbon::now()->month);

        $startOfMonth = Carbon::create($year, $month, 1)->startOfMonth();
        $endOfMonth = Carbon::create($year, $month, 1)->endOfMonth();

        // Get target vs achievement data for the last 12 months (latest first)
        $targetData = collect();
        for ($i = 0; $i <= 11; $i++) {
            $checkDate = Carbon::now()->subMonths($i);
            $checkStart = $checkDate->copy()->startOfMonth();
            $checkEnd = $checkDate->copy()->endOfMonth();

            $target = floatval($team->target ?? 0);
            $achieved = $this->sumDeliveredAmounts($team, $checkStart, $checkEnd);
            $achievementRatio = ($target > 0) ? min(1, $achieved / $target) : 0;
            $achievementPercent = round($achievementRatio * 100, 2);

            // Get salary record for this month to get achieved_target
            $salaryRecord = Salary::where('team_id', $team->id)
                ->where('year', $checkDate->year)
                ->where('month', $checkDate->month)
                ->first();

            $targetData->push([
                'month' => $checkDate->format('M Y'),
                'year' => $checkDate->year,
                'month_num' => $checkDate->month,
                'target' => $target,
                'achieved' => $achieved,
                'achieved_target' => $salaryRecord->achieved_target ?? $achieved,
                'achievement_percent' => $achievementPercent,
                'achievement_ratio' => $achievementRatio,
                'status' => $achievementPercent >= 100 ? 'exceeded' : ($achievementPercent >= 80 ? 'good' : ($achievementPercent >= 60 ? 'average' : 'poor')),
            ]);
        }

        // Get current month detailed data
        $currentTarget = floatval($team->target ?? 0);
        $currentAchieved = $this->sumDeliveredAmounts($team, $startOfMonth, $endOfMonth);
        $currentRatio = ($currentTarget > 0) ? min(1, $currentAchieved / $currentTarget) : 0;
        $currentPercent = round($currentRatio * 100, 2);

        // Get current month salary record for achieved_target
        $currentSalaryRecord = Salary::where('team_id', $team->id)
            ->where('year', $year)
            ->where('month', $month)
            ->first();
        $currentAchievedTarget = $currentSalaryRecord->achieved_target ?? $currentAchieved;

        return view('salaries.target_achievement', [
            'targetData' => $targetData,
            'team' => $team,
            'currentYear' => $year,
            'currentMonth' => $month,
            'currentTarget' => $currentTarget,
            'currentAchieved' => $currentAchieved,
            'currentAchievedTarget' => $currentAchievedTarget,
            'currentPercent' => $currentPercent,
            'currentRatio' => $currentRatio,
        ]);
    }

    /**
     * Show the bonus dashboard
     */
    public function bonusDashboard()
    {
        try {
            $teams = Team::with(['user', 'label', 'achievements'])->get();
            $labels = Label::all();
            $recentAchievements = TeamAchievement::with(['team.user', 'label'])
                ->latest('achievement_date')
                ->limit(10)
                ->get();

            return view('salaries.bonus_dashboard', compact('teams', 'labels', 'recentAchievements'));
        } catch (\Exception $e) {
            // If there's an error, return a simple view for debugging
            return view('salaries.bonus_dashboard', [
                'teams' => collect(),
                'labels' => collect(),
                'recentAchievements' => collect(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Display the specified salary record
     */
    public function show(Salary $salary)
    {
        $salary->load(['team.user', 'team.label']);
        return view('salaries.show', compact('salary'));
    }


}
