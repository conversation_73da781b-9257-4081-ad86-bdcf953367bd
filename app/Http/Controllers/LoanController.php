<?php

namespace App\Http\Controllers;

use App\Models\Loan;
use App\Models\MarketplaceAccount;
use Illuminate\Http\Request;
use Carbon\Carbon;

class LoanController extends Controller
{
    public function index(Request $request)
    {
        $query = Loan::with('marketplace');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by marketplace
        if ($request->filled('marketplace_id')) {
            $query->where('marketplace_id', $request->marketplace_id);
        }

        // Filter by amount range
        if ($request->filled('amount_from')) {
            $query->where('due_amounts', '>=', $request->amount_from);
        }
        if ($request->filled('amount_to')) {
            $query->where('due_amounts', '<=', $request->amount_to);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%")
                  ->orWhereHas('marketplace', function($mq) use ($search) {
                      $mq->where('order_account_name', 'like', "%{$search}%");
                  });
            });
        }

        $loans = $query->latest()->paginate(15);
        $marketplaces = MarketplaceAccount::orderBy('order_account_name')->get();

        // Calculate summary statistics
        $totalLoans = Loan::count();
        $pendingLoans = Loan::pending()->count();
        $approvedLoans = Loan::approved()->count();
        $paidLoans = Loan::paid()->count();
        $overdueLoans = Loan::overdue()->count();
        $totalAmount = Loan::sum('amount');
        $totalPaid = Loan::whereNotNull('paid_amount')->sum('paid_amount');

        return view('loans.index', compact(
            'loans',
            'marketplaces',
            'totalLoans',
            'pendingLoans',
            'approvedLoans',
            'paidLoans',
            'overdueLoans',
            'totalAmount',
            'totalPaid'
        ));
    }

    public function create()
    {
        $marketplaces = MarketplaceAccount::orderBy('order_account_name')->get();
        return view('loans.create', compact('marketplaces'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:0',
            'loan_amount_charge_percentage' => 'nullable|numeric|min:0|max:100',
            'marketplace_id' => 'required|exists:marketplace_accounts,id',
            'due_amounts' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        Loan::create($validated);

        return redirect()->route('loans.index')->with('success', 'Loan created successfully.');
    }

    public function show(Loan $loan)
    {
        $loan->load('marketplace');
        return view('loans.show', compact('loan'));
    }

    public function edit(Loan $loan)
    {
        $marketplaces = MarketplaceAccount::orderBy('order_account_name')->get();
        return view('loans.edit', compact('loan', 'marketplaces'));
    }

    public function update(Request $request, Loan $loan)
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:0',
            'loan_amount_charge_percentage' => 'nullable|numeric|min:0|max:100',
            'marketplace_id' => 'required|exists:marketplace_accounts,id',
            'due_amounts' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'status' => 'required|in:pending,approved,rejected,paid,overdue',
            'notes' => 'nullable|string',
        ]);

        $loan->update($validated);

        return redirect()->route('loans.index')->with('success', 'Loan updated successfully.');
    }

    public function destroy(Loan $loan)
    {
        $loan->delete();
        return redirect()->route('loans.index')->with('success', 'Loan deleted successfully.');
    }

    public function updateStatus(Request $request, Loan $loan)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,approved,rejected,paid,overdue',
            'paid_amount' => 'nullable|numeric|min:0',
            'paid_date' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        // If status is being set to paid, set paid_date if not provided
        if ($validated['status'] === 'paid' && !isset($validated['paid_date'])) {
            $validated['paid_date'] = Carbon::now();
        }

        $loan->update($validated);

        return redirect()->back()->with('success', 'Loan status updated successfully.');
    }

    public function markAsPaid(Request $request, Loan $loan)
    {
        $validated = $request->validate([
            'paid_amount' => 'required|numeric|min:0|max:' . $loan->total_amount,
            'paid_date' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        $validated['status'] = 'paid';
        $validated['paid_date'] = $validated['paid_date'] ?? Carbon::now();

        $loan->update($validated);

        return redirect()->back()->with('success', 'Loan marked as paid successfully.');
    }

    /**
     * Add a deduction to the loan (when money is taken from the loan)
     */
    public function addDeduction(Request $request, Loan $loan)
    {
        $validated = $request->validate([
            'deduction_amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255',
            'notes' => 'nullable|string',
        ]);

        // Check if loan has available balance
        if (!$loan->hasAvailableBalance($validated['deduction_amount'])) {
            return redirect()->back()->with('error', 'Insufficient loan balance for this deduction.');
        }

        // Add the deduction
        $loan->addDeduction($validated['deduction_amount'], $validated['description']);

        // Add additional notes if provided
        if (!empty($validated['notes'])) {
            $currentNotes = $loan->notes ? $loan->notes . "\n" : '';
            $loan->notes = $currentNotes . "Additional Notes: " . $validated['notes'];
            $loan->save();
        }

        return redirect()->back()->with('success', 'Deduction added successfully. New due amount: $' . number_format($loan->due_amounts, 2));
    }

    /**
     * Show loan deduction form
     */
    public function showDeductionForm(Loan $loan)
    {
        $loan->load('marketplace');
        return view('loans.deduction', compact('loan'));
    }
}
