<?php

namespace App\Http\Controllers;

use App\Models\LeaveRequest;
use App\Models\Attendance;
use App\Models\Team;
use Illuminate\Http\Request;

class LeaveRequestController extends Controller
{
    public function index()
    {
        $leaveRequests = LeaveRequest::with('team', 'approver')->latest()->paginate(10);
        return view('leave_requests.index', compact('leaveRequests'));
    }

    public function create()
    {
        $teams = Team::with('user')->get();
        return view('leave_requests.create', compact('teams'));
    }

    public function store(Request $request)
    {
        // If you want to set team_id as logged in user's team, ensure user has team relation.
        $request->merge([
            'team_id' => auth()->user()->team->id ?? null,
        ]);

        $validated = $request->validate([
            'team_id' => 'required|exists:teams,id',
            'leave_type' => 'required|in:sick,casual,earned,maternity,paternity,unpaid',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'reason' => 'nullable|string',
            'status' => 'nullable|in:pending,approved,rejected',
            'approved_by' => 'nullable|exists:teams,id',
        ]);

        // Default status to pending on create if not provided from UI
        if (empty($validated['status'])) {
            $validated['status'] = 'pending';
        }

        LeaveRequest::create($validated);

        return redirect()->route('leave_requests.index')->with('success', 'Leave request created successfully.');
    }

    public function show(LeaveRequest $leaveRequest)
    {
        $leaveRequest->load('team', 'approver');
        return view('leave_requests.show', compact('leaveRequest'));
    }

    public function edit(LeaveRequest $leaveRequest)
    {
        $teams = Team::with('user')->get();
        return view('leave_requests.edit', compact('leaveRequest', 'teams'));
    }

    public function update(Request $request, LeaveRequest $leaveRequest)
    {
        $validated = $request->validate([
            'team_id' => 'required|exists:teams,id',
            'leave_type' => 'required|in:half_day,sick,casual,earned,maternity,paternity,unpaid',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'reason' => 'nullable|string',
            'status' => 'required|in:pending,approved,rejected',
            'approved_by' => 'nullable|exists:teams,id',
        ]);

        $leaveRequest->update($validated);

        // Update the related team's leave_status based on leave request status
        if ($leaveRequest->team_id) {
            $team = Team::find($leaveRequest->team_id);
            if ($team) {
                $team->leave_status = $validated['status'] === 'approved' ? 'on_leave' : 'on_duty';
                $team->save();
            }
        }

        return redirect()->route('leave_requests.index')->with('success', 'Leave request updated successfully.');
    }

    public function destroy(LeaveRequest $leaveRequest)
    {
        $leaveRequest->delete();

        return redirect()->route('leave_requests.index')->with('success', 'Leave request deleted successfully.');
    }

    public function approve(LeaveRequest $leaveRequest)
    {
        $leaveRequest->update([
            'status' => 'approved',
            'approved_by' => auth()->user()->team->id ?? null,
        ]);

        // Sync team leave status
        if ($leaveRequest->team_id) {
            $team = Team::find($leaveRequest->team_id);
            if ($team) {
                $team->leave_status = 'on_leave';
                $team->save();
            }
        }

        // Create attendance entries for the approved leave period
        if ($leaveRequest->team_id && $leaveRequest->start_date && $leaveRequest->end_date) {
            $start = new \Carbon\Carbon($leaveRequest->start_date);
            $end = new \Carbon\Carbon($leaveRequest->end_date);

            // Map leave type to attendance status
            $attendanceStatus = 'leave'; // default
            if ($leaveRequest->leave_type === 'half_day') {
                $attendanceStatus = 'half_day';
            } elseif ($leaveRequest->leave_type === 'unpaid') {
                $attendanceStatus = 'absent';
            }

            for ($date = $start->copy(); $date->lte($end); $date->addDay()) {
                Attendance::updateOrCreate(
                    [
                        'team_id' => $leaveRequest->team_id,
                        'date' => $date->toDateString(),
                    ],
                    [
                        'status' => $attendanceStatus,
                        'check_in' => null,
                        'check_out' => null,
                        'notes' => $leaveRequest->reason ?? 'Approved leave',
                    ]
                );
            }
        }

        return redirect()->route('leave_requests.index')->with('success', 'Leave request approved successfully.');
    }

    public function reject(LeaveRequest $leaveRequest)
    {
        $leaveRequest->update([
            'status' => 'rejected',
            'approved_by' => auth()->user()->team->id ?? null,
        ]);

        // Sync team leave status
        if ($leaveRequest->team_id) {
            $team = Team::find($leaveRequest->team_id);
            if ($team) {
                $team->leave_status = 'on_duty';
                $team->save();
            }
        }

        // Update attendance to show as absent when leave request is rejected
        if ($leaveRequest->team_id && $leaveRequest->start_date && $leaveRequest->end_date) {
            $start = new \Carbon\Carbon($leaveRequest->start_date);
            $end = new \Carbon\Carbon($leaveRequest->end_date);

            for ($date = $start->copy(); $date->lte($end); $date->addDay()) {
                Attendance::updateOrCreate(
                    [
                        'team_id' => $leaveRequest->team_id,
                        'date' => $date->toDateString(),
                    ],
                    [
                        'status' => 'absent',
                        'check_in' => null,
                        'check_out' => null,
                        'notes' => 'Leave request rejected',
                    ]
                );
            }
        }

        return redirect()->route('leave_requests.index')->with('success', 'Leave request rejected successfully.');
    }
}
