<?php

namespace App\Http\Controllers;

use App\Models\BlogCategory;
use Illuminate\Http\Request;

class BlogCategoryController extends Controller
{
    public function index()
    {
        $categories = BlogCategory::latest()->paginate(10);
        return view('blog_categories.index', compact('categories'));
    }

    public function show(BlogCategory $blog_category)
    {
        return view('blog_categories.show', compact('blog_category'));
    }

    public function create()
    {
        // Pass existing categories to select parent category (optional)
        $categories = BlogCategory::all();
        return view('blog_categories.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:blog_categories,id',
        ]);

        BlogCategory::create($request->only('name', 'parent_id'));

        return redirect()->route('blog-categories.index')->with('success', 'Category created successfully.');
    }

    public function edit(BlogCategory $blog_category)
    {
        $categories = BlogCategory::where('id', '!=', $blog_category->id)->get(); // exclude self from parent list
        return view('blog_categories.edit', compact('blog_category', 'categories'));
    }

    public function update(Request $request, BlogCategory $blog_category)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:blog_categories,id|not_in:' . $blog_category->id,
        ]);

        $blog_category->update($request->only('name', 'parent_id'));

        return redirect()->route('blog-categories.index')->with('success', 'Category updated successfully.');
    }

    public function destroy(BlogCategory $blog_category)
    {
        $blog_category->delete();

        return redirect()->route('blog-categories.index')->with('success', 'Category deleted successfully.');
    }
}
