<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    public function index()
    {
        $contacts = Contact::with('service')->latest()->paginate(10);
        return view('contacts.index', compact('contacts'));
    }

    public function show(Contact $contact)
    {
        return view('contacts.show', compact('contact'));
    }

    public function create()
    {
        $services = \App\Models\Service::all();
        return view('contacts.create', compact('services'));
    }

public function store(Request $request)
{
    $data = $request->validate([
        'name' => 'required|string|max:255',
        'email' => 'required|email|unique:contacts,email',
        'number' => 'nullable|string|max:20',
        'service_id' => 'required|exists:services,id',
        'message' => 'nullable|string',
    ]);

    $duplicate = Contact::where('name', $data['name'])
        ->where('email', $data['email'])
        ->where('service_id', $data['service_id'])
        ->where('created_at', '>=', now()->subMinutes(5))
        ->exists();

    if ($duplicate) {
        return response()->json([
            'success' => false,
            'message' => 'You already submitted a message recently. Please wait before trying again.'
        ]);
    }

    Contact::create($data);

    return response()->json([
        'success' => true,
        'message' => 'Message sent successfully.'
    ]);
}





    public function destroy(Contact $contact)
    {
        $contact->delete();
        return redirect()->route('contacts.index')->with('success', 'Contact deleted successfully.');
    }

    public function reply(Contact $contact)
    {
        return view('contacts.reply', compact('contact'));
    }

    public function sendReply(Request $request, Contact $contact)
    {
        $request->validate([
            'subject' => 'required|string|max:255',
            'body' => 'required|string',
        ]);

        if ($contact->email) {
            Mail::raw($request->body, function ($message) use ($contact, $request) {
                $message->to($contact->email)
                        ->subject($request->subject);
            });

            return redirect()->route('contacts.index')->with('success', 'Reply sent successfully.');
        }

        return redirect()->route('contacts.index')->with('error', 'This contact has no email address.');
    }
}


