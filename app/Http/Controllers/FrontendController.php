<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use App\Models\Team;
use App\Models\Project;
use App\Models\Service;
use App\Models\BlogCategory;
use Illuminate\Http\Request;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class FrontendController extends Controller
{
    protected function createThumbnail($imagePath, $width = 300, $height = null)
    {
        if (!file_exists(public_path($imagePath))) {
            return null;
        }

        $manager = new ImageManager(new Driver());
        $img = $manager->read(public_path($imagePath));

        if ($height) {
            $img->resize($width, $height);
        } else {
            $img->resize($width, null, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }

        $thumbnailPath = 'uploads/thumbnails/' . basename($imagePath);
        $img->save(public_path($thumbnailPath));

        return $thumbnailPath;
    }

    public function index()
    {
        $services = Service::all();
        $latestServices = Service::latest()->take(5)->get();
        $blogs = Blog::latest()->take(3)->get();
        $recentBlogs = Blog::latest()->take(2)->get();
        $projects = Project::all();
        $teamMembers = Team::with('user')->get();

        return view('frontend.home', compact('services', 'blogs', 'teamMembers', 'latestServices', 'recentBlogs', 'projects'));
    }

    public function about()
    {
        $latestServices = Service::latest()->take(5)->get();
        $recentBlogs = Blog::latest()->take(2)->get();

        return view('frontend.pages.about', compact('latestServices', 'recentBlogs'));
    }

    public function blog()
    {
        $blogs = Blog::with(['category', 'author'])->latest()->paginate(5);
        $allTags = Blog::pluck('meta_keywords')
            ->flatMap(fn($keywords) => explode(',', $keywords))
            ->map(fn($tag) => trim($tag))
            ->filter()
            ->countBy()
            ->sortDesc()
            ->take(10);

        $latestServices = Service::latest()->take(5)->get();
        $recentBlogs = Blog::latest()->take(2)->get();

        return view('frontend.pages.blog', compact('blogs', 'allTags', 'latestServices', 'recentBlogs'));
    }

    public function team()
    {
        $teamMembers = Team::with('user')->get();
        $latestServices = Service::latest()->take(5)->get();
        $recentBlogs = Blog::latest()->take(2)->get();

        return view('frontend.pages.team', compact('teamMembers', 'latestServices', 'recentBlogs'));
    }

    public function blogDetails($slug)
    {
        $blog = Blog::with(['category', 'author'])->where('slug', $slug)->firstOrFail();
        $latestServices = Service::latest()->take(5)->get();
        $recentBlogs = Blog::latest()->take(2)->get();

        return view('frontend.pages.blog-details', compact('blog', 'latestServices', 'recentBlogs'));
    }

    public function service()
    {
        $services = Service::latest()->paginate(9);
        $latestServices = Service::latest()->take(5)->get();
        $recentBlogs = Blog::latest()->take(2)->get();

        return view('frontend.pages.services', compact('services', 'latestServices', 'recentBlogs'));
    }

    public function serviceDetails($slug)
    {
        $service = Service::where('slug', $slug)->firstOrFail();
        $allServices = Service::all();
        $latestServices = Service::latest()->take(5)->get();
        $recentBlogs = Blog::latest()->take(2)->get();

        return view('frontend.pages.service-details', compact('service', 'allServices', 'latestServices', 'recentBlogs'));
    }

    public function contact()
    {
        $services = Service::all();
        $latestServices = Service::latest()->take(5)->get();
        $recentBlogs = Blog::latest()->take(2)->get();

        return view('frontend.pages.contact', compact('services', 'latestServices', 'recentBlogs'));
    }

    public function project()
    {
        $projects = Project::latest()->paginate(12);
        $latestServices = Service::latest()->take(5)->get();
        $recentBlogs = Blog::latest()->take(2)->get();

        return view('frontend.pages.project', compact('projects', 'latestServices', 'recentBlogs'));
    }
    public function projectDetails($slug)
    {
        $project = Project::where('slug', $slug)->firstOrFail();
        $latestServices = Service::latest()->take(5)->get();
        $recentBlogs = Blog::latest()->take(2)->get();

        return view('frontend.pages.projectDetails', compact('project', 'latestServices', 'recentBlogs'));
    }

}
