<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Project;
use App\Models\Service;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ProjectController extends Controller
{
    public function index()
    {
        $projects = Project::latest()->paginate(10);
        return view('projects.index', compact('projects'));
    }

    public function create()
    {
        $users = User::where('role', 'user')->get();
        $services = Service::all();
        return view('projects.create', compact('users', 'services'));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:9120',
            'user_id' => 'nullable|exists:users,id',
            'budget' => 'nullable|numeric',
            'date' => 'nullable|date',
            'service_id' => 'nullable|exists:services,id',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string|max:255',
        ]);
         if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }
        if (empty($data['meta_title'])) {
            $data['meta_title'] = $data['title'];
        }
        if (empty($data['meta_description']) && !empty($data['description'])) {
            $data['meta_description'] = substr(strip_tags($data['description']), 0, 150);
        }
        if (empty($data['meta_keywords'])) {
            $data['meta_keywords'] = $data['title'];
        }
        $projectData = $data;


        // Handle image upload
        if ($request->hasFile('images')) {
            $imagePaths = [];
            foreach ($request->file('images') as $imageFile) {
                $filename = time() . '_' . uniqid() . '.' . $imageFile->getClientOriginalExtension();
                $fullPath = 'uploads/projects/' . $filename;

                $manager = new ImageManager(new Driver());
                $img = $manager->read($imageFile->getRealPath());
                $img->resize(800, null, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

                $img->save(public_path($fullPath));

                $imagePaths[] = $fullPath;
            }

            $projectData['images'] = $imagePaths;
        }

        Project::create($projectData);


        return redirect()->route('projects.index')->with('success', 'Project created successfully!');
    }

    public function show(Project $project)
    {
        return view('projects.show', compact('project'));
    }

    public function edit(Project $project)
    {
        $users = User::where('role', 'user')->get();
        $services = Service::all();
        return view('projects.edit', compact('project', 'users', 'services'));
    }



public function update(Request $request, Project $project)
{
    $data = $request->validate([
        'title' => 'required|string|max:255',
        'slug' => 'nullable|string|max:255|unique:projects,slug,' . $project->id,
        'subtitle' => 'nullable|string|max:255',
        'description' => 'nullable|string',
        'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:9120',
        'user_id' => 'nullable|exists:users,id',
        'budget' => 'nullable|numeric',
        'date' => 'nullable|date',
        'service_id' => 'nullable|exists:services,id',
        'meta_title' => 'nullable|string|max:255',
        'meta_description' => 'nullable|string',
        'meta_keywords' => 'nullable|string|max:255',
        'removed_images' => 'nullable|string', // JSON-encoded array
    ]);

    $data['slug'] = $data['slug'] ?? Str::slug($data['title']);
    $data['meta_title'] = $data['meta_title'] ?? $data['title'];
    if (empty($data['meta_description']) && !empty($data['description'])) {
        $data['meta_description'] = substr(strip_tags($data['description']), 0, 150);
    }
    $data['meta_keywords'] = $data['meta_keywords'] ?? $data['title'];

    $existingImages = is_array($project->images) ? $project->images : [];

    // Handle removed images
    $removedImages = json_decode($request->input('removed_images'), true) ?? [];
    $updatedImages = array_filter($existingImages, function ($img) use ($removedImages) {
        return !in_array($img, $removedImages);
    });

    foreach ($removedImages as $imgPath) {
        $fullPath = public_path($imgPath);
        if (file_exists($fullPath)) {
            unlink($fullPath);
        }
    }

    // Handle uploaded new images
    if ($request->hasFile('images')) {
        $manager = new ImageManager(new Driver());

        foreach ($request->file('images') as $image) {
            $filename = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
            $img = $manager->read($image->getRealPath());

            $img->resize(800, null, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });

            $path = 'uploads/projects/' . $filename;
            $img->save(public_path($path));

            $updatedImages[] = $path;
        }
    }

    $data['images'] = array_values($updatedImages); // reset keys

    $project->update($data);

    return redirect()->route('projects.index')->with('success', 'Project updated successfully!');
}


    public function destroy(Project $project)
    {
        // Delete all project images from storage if exists
        if (is_array($project->images)) {
            foreach ($project->images as $imagePath) {
                $fullPath = public_path($imagePath);
                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }
            }
        }

        $project->delete();

        return redirect()->route('projects.index')->with('success', 'Project deleted successfully.');
    }
}
