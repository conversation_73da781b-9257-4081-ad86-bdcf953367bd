<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Illuminate\Validation\Rule;
use App\Traits\AuditLoggable;

class UserController extends Controller
{
    use AuditLoggable;
    /**
     * Display a listing of the users.
     */
    public function index()
    {
        $query = \App\Models\User::query();

        // If logged in user is admin, hide super_admins
        if (auth()->user()->hasRole('admin')) {
            $query->where('role', '!=', 'super_admin');
        }

        $users = $query->latest()->paginate(15);
        return view('admin.users.index', compact('users'));
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        // Log the view action
        $this->logView($user, "Viewed user profile: {$user->name}");

        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $roles = ['user', 'admin', 'super_admin', 'editor', 'developer', 'marketer', 'tenant'];
        $permissions = ['read', 'write', 'read_write', 'none', 'delete', 'full']; // Legacy permissions for backward compatibility
        return view('admin.users.create', compact('roles', 'permissions'));
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => ['required', Rule::in(['user', 'admin', 'super_admin', 'editor', 'developer', 'marketer', 'tenant'])],
            'permission' => ['required', Rule::in(['read', 'write', 'read_write', 'none', 'delete', 'full'])],
            'picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:9048',
        ]);

        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'permission' => $request->permission,
        ];

        // Handle image upload
        if ($request->hasFile('picture')) {
            $image = $request->file('picture');
            $filename = time() . '.' . $image->getClientOriginalExtension();

            // Create image manager with driver
            $manager = new ImageManager(new Driver());

            // Process the image
            $img = $manager->read($image->getRealPath());
            $img->resize(300, 300);

            // Save the image
            $path = public_path('uploads/users/' . $filename);
            $img->save($path);

            $userData['picture'] = 'uploads/users/' . $filename;
        }

        $user = User::create($userData);

        // Assign Spatie role
        $user->assignRole($request->role);

        // Log the creation
        $this->logCreate($user, "Created new user: {$user->name}");

        return redirect()->route('users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        // Prevent editing super admin users
        if ($user->hasRole('super_admin')) {
            return redirect()->route('users.index')
                ->with('error', 'Super admin users cannot be edited.');
        }

        $roles = ['user', 'admin', 'super_admin', 'editor', 'developer', 'marketer', 'tenant'];
        $permissions = ['read', 'write', 'read_write', 'none', 'delete', 'full']; // Legacy permissions
        return view('admin.users.edit', compact('user', 'roles', 'permissions'));
    }
    public function profile()
    {
        $user = auth()->user();
        return view('auth.profile', compact('user'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user)
    {
        // Prevent updating super admin users
        if ($user->hasRole('super_admin')) {
            return redirect()->route('users.index')
                ->with('error', 'Super admin users cannot be updated.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'number' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'role' => ['required', Rule::in(['user', 'admin', 'super_admin', 'editor', 'developer', 'marketer', 'tenant'])],
            'permission' => ['required', Rule::in(['read', 'write', 'read_write', 'none', 'delete', 'full'])],
            'picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:9048',
        ]);

        $userData = [
            'name' => $request->name,
            'number' => $request->number,
            'address' => $request->address,
            'email' => $request->email,
            'role' => $request->role,
            'permission' => $request->permission,
        ];

        // Prevent changing super admin role
        if ($user->hasRole('super_admin') && $request->role !== 'super_admin') {
            return redirect()->route('users.index')
                ->with('error', 'Super admin role cannot be changed.');
        }

        // Update password if provided
        if ($request->filled('password')) {
            $request->validate([
                'password' => 'required|string|min:8|confirmed',
            ]);
            $userData['password'] = Hash::make($request->password);
        }

        // Handle image upload
        if ($request->hasFile('picture')) {
            $image = $request->file('picture');
            $filename = time() . '.' . $image->getClientOriginalExtension();

            // Create image manager with driver
            $manager = new ImageManager(new Driver());

            // Process the image
            $img = $manager->read($image->getRealPath());
            $img->resize(300, 300);

            // Save the image
            $path = public_path('uploads/users/' . $filename);
            $img->save($path);

            // Delete old image if exists
            if ($user->picture && file_exists(public_path($user->picture))) {
                unlink(public_path($user->picture));
            }

            $userData['picture'] = 'uploads/users/' . $filename;
        }

        // Store old values for audit log
        $oldValues = $user->getAttributes();

        $user->update($userData);

        // Sync Spatie role
        $user->syncRoles([$request->role]);

        // Log the update
        $this->logUpdate($user, $oldValues, "Updated user: {$user->name}");

        return redirect()->route('users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        // Prevent deleting super admin users
        if ($user->hasRole('super_admin')) {
            return redirect()->route('users.index')
                ->with('error', 'Super admin users cannot be deleted.');
        }

        // Prevent users from deleting themselves
        if ($user->id === auth()->id()) {
            return redirect()->route('users.index')
                ->with('error', 'You cannot delete your own account.');
        }

        // Log the deletion before deleting
        $this->logDelete($user, "Deleted user: {$user->name}");

        $user->delete();
        return redirect()->route('users.index')->with('success', 'User deleted successfully.');
    }

    /**
     * Update the user's profile.
     */
   public function updateProfile(Request $request)
{
    $user = auth()->user();

    $request->validate([
        'name' => 'required|string|max:255',
        'number' => 'nullable|string|max:20',
        'address' => 'nullable|string|max:255',
        'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
        'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg,svg|max:5048',
    ]);

    $userData = [
        'name' => $request->name,
        'number' => $request->number,
        'address' => $request->address,
        'email' => $request->email,
    ];

    // Handle image upload using Intervention Image
    if ($request->hasFile('profile_picture')) {
        $image = $request->file('profile_picture');
        $filename = time() . '.' . $image->getClientOriginalExtension();

        $manager = new \Intervention\Image\ImageManager(new \Intervention\Image\Drivers\Gd\Driver());
        $img = $manager->read($image->getRealPath());
        $img->resize(300, 300);

        $path = public_path('uploads/users/' . $filename);
        $img->save($path);

        // Delete old picture if exists
        if ($user->picture && file_exists(public_path($user->picture))) {
            unlink(public_path($user->picture));
        }

        $userData['picture'] = 'uploads/users/' . $filename;
    }

    $user->update($userData);

    return redirect()->route('profile')->with('success', 'Profile updated successfully!');
}

}
