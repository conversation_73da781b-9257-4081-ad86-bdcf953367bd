<?php

namespace App\Http\Controllers;

use App\Models\Label;
use Illuminate\Http\Request;

class LabelController extends Controller
{
    public function index()
    {
        $labels = Label::paginate(10);
        return view('labels.index', compact('labels'));
    }

    public function show(Label $label)
    {
        return view('labels.show', compact('label'));
    }

    public function create()
    {
        return view('labels.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|unique:labels,name',
            'bonus_target' => 'required|numeric|min:0',
            'bonus_amount' => 'required|numeric|min:0',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        Label::create($validated);
        return redirect()->route('labels.index')->with('success', 'Label created successfully.');
    }

    public function edit(Label $label)
    {
        return view('labels.edit', compact('label'));
    }

    public function update(Request $request, Label $label)
    {
        $validated = $request->validate([
            'name' => 'required|unique:labels,name,' . $label->id,
            'bonus_target' => 'required|numeric|min:0',
            'bonus_amount' => 'required|numeric|min:0',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $label->update($validated);
        return redirect()->route('labels.index')->with('success', 'Label updated successfully.');
    }

    public function destroy(Label $label)
    {
        $label->delete();
        return redirect()->route('labels.index')->with('success', 'Label deleted successfully.');
    }
}

