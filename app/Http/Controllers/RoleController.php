<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RoleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:full');
    }

    /**
     * Display a listing of roles
     */
    public function index()
    {
        $roles = Role::with('permissions')->get();
        $permissions = Permission::orderBy('name')->get();
        $users = User::all(); // Remove the with('roles') to avoid conflicts

        return view('roles.index', compact('roles', 'permissions', 'users'));
    }

    /**
     * Show the form for creating a new role
     */
    public function create()
    {
        $permissions = Permission::orderBy('name')->get();
        return view('roles.create', compact('permissions'));
    }

    /**
     * Store a newly created role
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name',
            'guard_name' => 'required|string|max:255',
            'permissions' => 'array'
        ]);

        $role = Role::create([
            'name' => $request->name,
            'guard_name' => $request->guard_name
        ]);

        if ($request->has('permissions')) {
            $role->syncPermissions($request->permissions);
        }

        return redirect()->route('roles.index')
            ->with('success', 'Role created successfully.');
    }

    /**
     * Display the specified role
     */
    public function show(Role $role)
    {
        $permissions = Permission::orderBy('name')->get();
        $users = User::all();

        return view('roles.show', compact('role', 'permissions', 'users'));
    }

    /**
     * Show the form for editing the specified role
     */
    public function edit(Role $role)
    {
        // Prevent editing super_admin role
        if ($role->name === 'super_admin') {
            return redirect()->route('roles.index')
                ->with('error', 'Super admin role cannot be edited.');
        }

        $permissions = Permission::orderBy('name')->get();
        return view('roles.edit', compact('role', 'permissions'));
    }

    /**
     * Update the specified role
     */
    public function update(Request $request, Role $role)
    {
        // Prevent updating super_admin role
        if ($role->name === 'super_admin') {
            return redirect()->route('roles.index')
                ->with('error', 'Super admin role cannot be updated.');
        }

        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'guard_name' => 'required|string|max:255',
            'permissions' => 'array'
        ]);

        $role->update([
            'name' => $request->name,
            'guard_name' => $request->guard_name
        ]);

        if ($request->has('permissions')) {
            $role->syncPermissions($request->permissions);
        } else {
            $role->syncPermissions([]);
        }

        return redirect()->route('roles.index')
            ->with('success', 'Role updated successfully.');
    }

    /**
     * Remove the specified role
     */
    public function destroy(Role $role)
    {
        // Prevent deleting super_admin role
        if ($role->name === 'super_admin') {
            return redirect()->route('roles.index')
                ->with('error', 'Super admin role cannot be deleted.');
        }

        // Check if role has users assigned
        if ($role->users()->count() > 0) {
            return redirect()->route('roles.index')
                ->with('error', 'Cannot delete role that has users assigned to it.');
        }

        $role->delete();

        return redirect()->route('roles.index')
            ->with('success', 'Role deleted successfully.');
    }

    /**
     * Assign role to user
     */
    public function assignToUser(Request $request)
    {
        $request->validate([
            'role_id' => 'required|exists:roles,id',
            'user_id' => 'required|exists:users,id'
        ]);

        $role = Role::findOrFail($request->role_id);
        $user = User::findOrFail($request->user_id);

        if (!$user->hasRole($role)) {
            $user->assignRole($role);
            return response()->json(['success' => true, 'message' => 'Role assigned to user successfully.']);
        }

        return response()->json(['success' => false, 'message' => 'User already has this role.']);
    }

    /**
     * Remove role from user
     */
    public function removeFromUser(Request $request)
    {
        $request->validate([
            'role_id' => 'required|exists:roles,id',
            'user_id' => 'required|exists:users,id'
        ]);

        $role = Role::findOrFail($request->role_id);
        $user = User::findOrFail($request->user_id);

        if ($user->hasRole($role)) {
            $user->removeRole($role);
            return response()->json(['success' => true, 'message' => 'Role removed from user successfully.']);
        }

        return response()->json(['success' => false, 'message' => 'User does not have this role.']);
    }

    /**
     * Sync permissions for a role
     */
    public function syncPermissions(Request $request, Role $role)
    {
        $request->validate([
            'permissions' => 'array'
        ]);

        $role->syncPermissions($request->permissions ?? []);

        return response()->json(['success' => true, 'message' => 'Role permissions updated successfully.']);
    }
}
