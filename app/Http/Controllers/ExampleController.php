<?php

namespace App\Http\Controllers;

use App\Helpers\ImageHelper;
use Illuminate\Http\Request;

class ExampleController extends Controller
{
    /**
     * Example of using the ImageHelper
     */
    public function uploadImage(Request $request)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($request->hasFile('image')) {
            // Basic image resize and save
            $imagePath = ImageHelper::saveImage(
                $request->file('image'),
                'uploads/images',
                800
            );
            
            // Create a thumbnail
            $thumbnailPath = ImageHelper::createThumbnail(
                $imagePath,
                'uploads/thumbnails/' . basename($imagePath),
                300
            );
            
            // Crop and resize for profile picture
            $profilePicPath = ImageHelper::cropAndResizeImage(
                $request->file('image'),
                'uploads/profiles',
                300,
                300
            );
            
            // Add watermark (if you have a watermark image)
            // $watermarkedPath = ImageHelper::addWatermark(
            //     $imagePath,
            //     'assets/images/watermark.png',
            //     'bottom-right'
            // );
            
            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully',
                'data' => [
                    'original' => $imagePath,
                    'thumbnail' => $thumbnailPath,
                    'profile' => $profilePicPath,
                ]
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'No image uploaded',
        ]);
    }
}