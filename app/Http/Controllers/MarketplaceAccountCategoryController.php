<?php

namespace App\Http\Controllers;

use App\Models\MarketplaceAccountCategory;
use Illuminate\Http\Request;

class MarketplaceAccountCategoryController extends Controller
{
    public function index()
    {
        $categories = MarketplaceAccountCategory::withCount('marketplaceAccounts')->latest()->get();
        return view('marketplace_account_categories.index', compact('categories'));
    }

    public function create()
    {
        return view('marketplace_account_categories.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:marketplace_account_categories',
            'delivery_amount_charge_percentage' => 'required|numeric|min:0|max:100',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        MarketplaceAccountCategory::create($validated);
        return redirect()->route('marketplace-account-categories.index')->with('success', 'Category created successfully.');
    }

    public function show(MarketplaceAccountCategory $marketplaceAccountCategory)
    {
        $category = $marketplaceAccountCategory->load('marketplaceAccounts');
        return view('marketplace_account_categories.show', compact('category'));
    }

    public function edit(MarketplaceAccountCategory $marketplaceAccountCategory)
    {
        return view('marketplace_account_categories.edit', compact('marketplaceAccountCategory'));
    }

    public function update(Request $request, MarketplaceAccountCategory $marketplaceAccountCategory)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:marketplace_account_categories,name,' . $marketplaceAccountCategory->id,
            'delivery_amount_charge_percentage' => 'required|numeric|min:0|max:100',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $marketplaceAccountCategory->update($validated);
        return redirect()->route('marketplace-account-categories.index')->with('success', 'Category updated successfully.');
    }

    public function destroy(MarketplaceAccountCategory $marketplaceAccountCategory)
    {
        // Check if category has associated marketplace accounts
        if ($marketplaceAccountCategory->marketplaceAccounts()->count() > 0) {
            return back()->with('error', 'Cannot delete category. It has associated marketplace accounts.');
        }

        $marketplaceAccountCategory->delete();
        return redirect()->route('marketplace-account-categories.index')->with('success', 'Category deleted successfully.');
    }
}
