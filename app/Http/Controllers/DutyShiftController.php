<?php

namespace App\Http\Controllers;

use App\Models\DutyShift;
use App\Models\Team;
use Illuminate\Http\Request;

class DutyShiftController extends Controller
{
    // Admin CRUD
    public function index(Request $request)
    {
        $query = DutyShift::with('team.user')->orderByDesc('date')->orderByDesc('id');
        if ($request->filled('team_id')) {
            $query->where('team_id', $request->team_id);
        }
        if ($request->filled('from')) {
            $query->whereDate('date', '>=', $request->from);
        }
        if ($request->filled('to')) {
            $query->whereDate('date', '<=', $request->to);
        }
        $shifts = $query->latest()->paginate(10)->appends($request->query());
        $teams = Team::with('user')->get();
        return view('duty_shift.index', compact('shifts', 'teams'));
    }

    public function show(DutyShift $dutyShift)
    {
        return view('duty_shift.show', compact('dutyShift'));
    }

    public function create()
    {
        $teams = Team::with('user')->get();
        return view('duty_shift.create', compact('teams'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'team_id' => 'required|exists:teams,id',
            'dates' => 'required|string', // comma-separated dates
            'name' => 'required|in:Morning,Noon,Night',
            'grace_minutes' => 'nullable|integer|min:0|max:120',

        ]);

        // If setting effective, clear any existing effective for this team
        // Map name to default times
        $map = [ 'Morning' => ['06:00', '14:00'], 'Noon' => ['14:00', '22:00'], 'Night' => ['22:00', '06:00'] ];
        [$start, $end] = $map[$validated['name']];

        $dates = array_filter(array_map('trim', explode(',', $validated['dates'])));
        foreach ($dates as $date) {
            // accept YYYY-MM-DD only
            $d = \DateTime::createFromFormat('Y-m-d', $date);
            if (!$d || $d->format('Y-m-d') !== $date) { continue; }
            DutyShift::updateOrCreate(
                [
                    'team_id' => $validated['team_id'],
                    'date' => $date,
                ],
                [
                    'name' => $validated['name'],
                    'start_time' => $start,
                    'end_time' => $end,
                    'grace_minutes' => $validated['grace_minutes'] ?? 5,

                ]
            );
        }

        return redirect()->route('duty_shift.index')->with('success', 'Shift created.');
    }

    public function edit(DutyShift $dutyShift)
    {
        $teams = Team::with('user')->get();
        return view('duty_shift.edit', ['shift' => $dutyShift, 'teams' => $teams]);
    }

    public function update(Request $request, DutyShift $dutyShift)
    {
        $validated = $request->validate([
            'team_id' => 'required|exists:teams,id',
            'date' => 'required|date',
            'name' => 'required|in:Morning,Noon,Night',
            'grace_minutes' => 'nullable|integer|min:0|max:120',

        ]);

        if (($validated['effective'] ?? false)) {
            DutyShift::where('team_id', $validated['team_id'])->where('id', '!=', $dutyShift->id)->update(['effective' => false]);
        }

        $map = [ 'Morning' => ['06:00', '14:00'], 'Noon' => ['14:00', '22:00'], 'Night' => ['22:00', '06:00'] ];
        [$start, $end] = $map[$validated['name']];
        $dutyShift->update([
            'team_id' => $validated['team_id'],
            'date' => $validated['date'],
            'name' => $validated['name'],
            'start_time' => $start,
            'end_time' => $end,
            'grace_minutes' => $validated['grace_minutes'] ?? 5,

        ]);

        return redirect()->route('duty_shift.index')->with('success', 'Shift updated.');
    }

    public function destroy(DutyShift $dutyShift)
    {
        $dutyShift->delete();
        return back()->with('success', 'Shift deleted.');
    }

    // Member schedule view (next 30 days)
    public function mySchedule()
{
    $user = auth()->user();
    $team = Team::where('user_id', $user->id)->firstOrFail();

    $days = [];

    // Generate schedule for next 30 days
    for ($i = 0; $i < 30; $i++) {
        $date = now()->startOfDay()->addDays($i)->toDateString();

        // Get shift for this team and date
        $shift = DutyShift::where('team_id', $team->id)
                    ->where('date', $date)
                    ->first();

                    $days[] = [
                        'date' => $date,
                        'name' => $shift?->name ?? '-',
                        'start_time' => $shift?->start_time,
                        'end_time' => $shift?->end_time,
                        'grace_minutes' => $shift?->grace_minutes ?? 5,
                    ];

    }

    return view('duty_shift.my_schedule', compact('days'));
}

}


