<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\MarketplaceAccount;

class MarketplaceAccountController extends Controller
{

    public function index()
    {
        $accounts = MarketplaceAccount::with('category')->latest()->get();
        return view('marketplace_accounts.index', compact('accounts'));
    }

    public function show(MarketplaceAccount $marketplace_account)
    {
        return view('marketplace_accounts.show', compact('marketplace_account'));
    }

    public function create()
    {
        return view('marketplace_accounts.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'order_account_name' => 'required|string',
            'created_date' => 'required|date',
            'username' => 'required|string',
            'password' => 'required|string',
            'wifi_ip' => 'nullable|ip',
            'category_id' => 'required|exists:marketplace_account_categories,id',
        ]);

        MarketplaceAccount::create($validated);
        return redirect()->route('marketplace_accounts.index')->with('success', 'Account created.');
    }

    public function edit(MarketplaceAccount $marketplace_account)
    {
        return view('marketplace_accounts.edit', compact('marketplace_account'));
    }

    public function update(Request $request, MarketplaceAccount $marketplace_account)
    {
        $validated = $request->validate([
            'order_account_name' => 'required|string',
            'created_date' => 'required|date',
            'username' => 'required|string',
            'password' => 'required|string',
            'wifi_ip' => 'nullable|ip',
            'category_id' => 'required|exists:marketplace_account_categories,id',
        ]);

        $marketplace_account->update($validated);
        return redirect()->route('marketplace_accounts.index')->with('success', 'Account updated.');
    }

    public function destroy(MarketplaceAccount $marketplace_account)
    {
        $marketplace_account->delete();
        return back()->with('success', 'Account deleted.');
    }


}
