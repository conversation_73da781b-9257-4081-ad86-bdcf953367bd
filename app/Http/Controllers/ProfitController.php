<?php

namespace App\Http\Controllers;

use App\Models\MarketplaceAccount;
use App\Models\ProjectManagement;
use App\Models\SpecialProject;
use App\Models\Salary;
use App\Models\Expense;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ProfitController extends Controller
{
    public function index(Request $request)
    {
        // Get selected month and year (default to current month)
        $selectedMonth = (int) $request->get('month', now()->format('m'));
        $selectedYear = (int) $request->get('year', now()->format('Y'));

        // Validate month range
        if ($selectedMonth < 1 || $selectedMonth > 12) {
            $selectedMonth = (int) now()->format('m');
        }

        // Get all marketplace accounts
        $marketplaces = DB::table('marketplace_accounts')->orderBy('order_account_name')->get();

                $profitData = [];
        $summaryData = [];

        foreach ($marketplaces as $marketplace) {
            // Get project management data for the selected month
            $projectManagementData = DB::table('project_managements')
                ->where('order_account_id', $marketplace->id)
                ->where('status', 'completed')
                ->whereYear('delivered_date', $selectedYear)
                ->whereMonth('delivered_date', $selectedMonth)
                ->select(
                    DB::raw('COUNT(*) as total_orders'),
                    DB::raw('SUM(amount) as total_order_amount'),
                    DB::raw('SUM(delivered_amount) as total_delivery_amount'),
                    DB::raw('SUM(amount) as total_payable_amount')
                )
                ->first();

            // Get special projects data for the selected month
            $specialProjectsData = DB::table('special_projects')
                ->where('marketplace_id', $marketplace->id)
                ->where('status', 'completed')
                ->whereYear('delivery_date', $selectedYear)
                ->whereMonth('delivery_date', $selectedMonth)
                ->select(
                    DB::raw('COUNT(*) as total_orders'),
                    DB::raw('SUM(order_amount) as total_order_amount'),
                    DB::raw('SUM(delivery_amount) as total_delivery_amount'),
                    DB::raw('SUM((order_amount * order_amount_rate) - (delivery_amount * withdrawn_amount_rate)) as total_profit')
                )
                ->first();

                        // Get pending orders data (all time, not just selected month)
            $pendingProjectManagement = DB::table('project_managements')
                ->where('order_account_id', $marketplace->id)
                ->whereNotIn('status', ['completed'])
                ->count();

            $pendingSpecialProjects = DB::table('special_projects')
                ->where('marketplace_id', $marketplace->id)
                ->whereNotIn('status', ['completed'])
                ->count();

            // Calculate totals
            $totalOrders = ($projectManagementData->total_orders ?? 0) + ($specialProjectsData->total_orders ?? 0);
            $totalOrderAmount = ($projectManagementData->total_order_amount ?? 0) + ($specialProjectsData->total_order_amount ?? 0);
            $totalDeliveryAmount = ($projectManagementData->total_delivery_amount ?? 0) + ($specialProjectsData->total_delivery_amount ?? 0);
            // For in_hand_amount, only use special projects profit (not project management payable amount)
            $totalProfit = ($specialProjectsData->total_profit ?? 0);
            $totalPendingOrders = $pendingProjectManagement + $pendingSpecialProjects;

            // Calculate available balance (total delivery amount from both project types in $)
            $availableBalance = ($projectManagementData->total_delivery_amount ?? 0) + ($specialProjectsData->total_delivery_amount ?? 0);

            $profitData[] = [
                'marketplace' => $marketplace,
                'project_management' => [
                    'orders' => $projectManagementData->total_orders ?? 0,
                    'order_amount' => $projectManagementData->total_order_amount ?? 0,
                    'delivery_amount' => $projectManagementData->total_delivery_amount ?? 0,
                    'payable_amount' => $projectManagementData->total_payable_amount ?? 0,
                ],
                'special_projects' => [
                    'orders' => $specialProjectsData->total_orders ?? 0,
                    'order_amount' => $specialProjectsData->total_order_amount ?? 0,
                    'delivery_amount' => $specialProjectsData->total_delivery_amount ?? 0,
                    'profit' => $specialProjectsData->total_profit ?? 0,
                ],
                'totals' => [
                    'orders' => $totalOrders,
                    'order_amount' => $totalOrderAmount,
                    'delivery_amount' => $totalDeliveryAmount,
                    'profit' => $totalProfit,
                ]
            ];

            // Summary data for the new table
            $summaryData[] = [
                'marketplace' => $marketplace,
                'real_orders' => $projectManagementData->total_orders ?? 0,
                'special_projects_orders' => $specialProjectsData->total_orders ?? 0,
                'pending_orders' => $totalPendingOrders,
                'completed_orders' => $totalOrders,
                'available_balance' => $availableBalance,
            ];
        }

        // Calculate grand totals
        $grandTotals = [
            'orders' => collect($profitData)->sum('totals.orders'),
            'order_amount' => collect($profitData)->sum('totals.order_amount'),
            'delivery_amount' => collect($profitData)->sum('totals.delivery_amount'),
            'profit' => collect($profitData)->sum('totals.profit'),
        ];

        // Calculate total salary for the selected month
        $totalSalary = Salary::where('year', $selectedYear)
            ->where('month', $selectedMonth)
            ->sum('amount');

        // Calculate total expenses for the selected month
        $totalExpenses = Expense::where('status', 'approved')
            ->whereYear('date', $selectedYear)
            ->whereMonth('date', $selectedMonth)
            ->sum(DB::raw('COALESCE(accepted_amount, given_amount)'));

        // Get available months and years for filter
        $availableMonths = [];
        for ($i = 1; $i <= 12; $i++) {
            $availableMonths[$i] = Carbon::create()->month($i)->format('F');
        }

        $availableYears = DB::table('project_managements')
            ->selectRaw('YEAR(delivered_date) as year')
            ->whereNotNull('delivered_date')
            ->union(
                DB::table('special_projects')
                    ->selectRaw('YEAR(delivery_date) as year')
                    ->whereNotNull('delivery_date')
            )
            ->distinct()
            ->pluck('year')
            ->sort()
            ->toArray();

        return view('profits.index', compact(
            'profitData',
            'summaryData',
            'grandTotals',
            'selectedMonth',
            'selectedYear',
            'availableMonths',
            'availableYears',
            'totalSalary',
            'totalExpenses'
        ));
    }
}
