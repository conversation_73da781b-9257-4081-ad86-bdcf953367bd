<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Team;
use App\Models\Salary;
use App\Models\Expense;
use App\Models\Project;
use App\Models\Contact;
use App\Models\Service;
use App\Models\Blog;
use App\Models\ProjectManagement;
use App\Models\SpecialProject;
use App\Models\MarketplaceAccount;
use App\Models\Loan;
use App\Models\LeaveRequest;
use App\Models\Attendance;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();

        // Check if user is admin or super_admin
        if (in_array($user->role, ['admin', 'super_admin'])) {
            return $this->adminDashboard();
        } else {
            return $this->userDashboard();
        }
    }

    /**
     * Admin Dashboard with comprehensive statistics
     */
    private function adminDashboard()
    {
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;

        // User Statistics
        $totalUsers = User::count();
        $activeUsers = User::whereNotNull('email_verified_at')->count();
        $newUsersThisMonth = User::whereYear('created_at', $currentYear)
            ->whereMonth('created_at', $currentMonth)->count();

        // Team Statistics
        $totalTeams = Team::count();
        $teamsWithMembers = Team::whereHas('user')->count();

        // Project Statistics (Portfolio Projects)
        $totalProjects = Project::count();
        $completedProjects = 0; // Portfolio projects don't have status
        $pendingProjects = 0; // Portfolio projects don't have status

        // Project Management Statistics
        $totalProjectManagements = ProjectManagement::count();
        $completedProjectManagements = ProjectManagement::where('status', 'completed')->count();
        $pendingProjectManagements = ProjectManagement::where('status', 'pending')->count();
        $inProgressProjectManagements = ProjectManagement::where('status', 'in_progress')->count();

        // Special Projects Statistics
        $totalSpecialProjects = SpecialProject::count();
        $completedSpecialProjects = SpecialProject::where('status', 'completed')->count();
        $pendingSpecialProjects = SpecialProject::where('status', 'pending')->count();

        // Financial Statistics
        $monthlySalaries = Salary::where('year', $currentYear)
            ->where('month', $currentMonth)->get();
        $totalSalary = $monthlySalaries->sum(function($salary) {
            return $salary->amount + $salary->bonus + $salary->attendance_bonus - $salary->expense_deduction + $salary->achieved_target;
        });
        $monthlyExpenses = Expense::whereYear('created_at', $currentYear)
            ->whereMonth('created_at', $currentMonth)->get();
        $totalExpenses = $monthlyExpenses->sum(function($expense) {
            return $expense->accepted_amount ?? $expense->given_amount;
        });
        $totalLoans = Loan::where('status', 'approved')->sum('amount');
        $pendingLoans = Loan::where('status', 'pending')->count();

        // Marketplace Statistics
        $totalMarketplaceAccounts = MarketplaceAccount::count();
        $activeMarketplaceAccounts = MarketplaceAccount::whereHas('category')->count();

        // Leave Request Statistics
        $pendingLeaveRequests = LeaveRequest::where('status', 'pending')->count();
        $approvedLeaveRequests = LeaveRequest::where('status', 'approved')->count();

        // Attendance Statistics
        $todayAttendance = Attendance::whereDate('date', Carbon::today())->count();
        $thisMonthAttendance = Attendance::whereYear('date', $currentYear)
            ->whereMonth('date', $currentMonth)->count();

        // Content Statistics
        $totalBlogs = Blog::count();
        $publishedBlogs = $totalBlogs; // All blogs are considered published
        $totalServices = Service::count();
        $totalContacts = Contact::count();
        $unreadContacts = 0; // Contacts don't have read status

        // Monthly Target vs Achievement Statistics
        /** @var \Illuminate\Database\Eloquent\Collection $teams */
        $teams = Team::with(['user'])->get();
        $totalTargetAmount = $teams->sum('target_amount');
        $totalAchievedAmount = $teams->sum('achieved_amount');
        $achievementPercentage = $totalTargetAmount > 0 ? ($totalAchievedAmount / $totalTargetAmount) * 100 : 0;

        // Monthly target vs achievement data for the last 6 months
        $monthlyTargetData = [];
        $monthlyAchievementData = [];
        $monthlyLabels = [];

        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $month = $date->month;
            $year = $date->year;

            $monthlyLabels[] = $date->format('M Y');

            // Get target amounts for this month (assuming targets are set monthly)
            $monthlyTarget = $teams->sum('target_amount');
            $monthlyTargetData[] = $monthlyTarget;

            // Get achieved amounts for this month from achievements - use direct query instead of relationship
            $monthlyAchieved = \App\Models\TeamAchievement::whereMonth('achievement_date', $month)
                ->whereYear('achievement_date', $year)
                ->sum('achieved_amount');
            $monthlyAchievementData[] = $monthlyAchieved;
        }

        // Recent Activities
        $recentProjectManagements = ProjectManagement::with('assignedBy.user')
            ->latest()->take(5)->get();
        $recentSpecialProjects = SpecialProject::with('marketplace')
            ->latest()->take(5)->get();
        $recentLoans = Loan::with('marketplace')
            ->latest()->take(5)->get();

        return view('dashboard.admin', compact(
            'totalUsers', 'activeUsers', 'newUsersThisMonth',
            'totalTeams', 'teamsWithMembers',
            'totalProjects', 'completedProjects', 'pendingProjects',
            'totalProjectManagements', 'completedProjectManagements', 'pendingProjectManagements', 'inProgressProjectManagements',
            'totalSpecialProjects', 'completedSpecialProjects', 'pendingSpecialProjects',
            'totalSalary', 'totalExpenses', 'totalLoans', 'pendingLoans',
            'totalMarketplaceAccounts', 'activeMarketplaceAccounts',
            'pendingLeaveRequests', 'approvedLeaveRequests',
            'todayAttendance', 'thisMonthAttendance',
            'totalBlogs', 'publishedBlogs', 'totalServices', 'totalContacts', 'unreadContacts',
            'recentProjectManagements', 'recentSpecialProjects', 'recentLoans',
            'totalTargetAmount', 'totalAchievedAmount', 'achievementPercentage',
            'monthlyTargetData', 'monthlyAchievementData', 'monthlyLabels'
        ));
    }

    /**
     * User Dashboard with personal statistics
     */
    private function userDashboard()
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        /** @var \App\Models\Team|null $team */
        $team = $user->team;
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;

        // Personal Project Statistics
        $myProjectManagements = collect();
        $mySpecialProjects = collect();
        $myCompletedProjects = 0;
        $myPendingProjects = 0;

        if ($team) {
            $myProjectManagements = ProjectManagement::where('assigned_by', $team->id)
                ->orWhere('delivered_by', $team->id)
                ->orWhere('responsible_team', $team->id)
                ->latest()->take(5)->get();

            $mySpecialProjects = SpecialProject::where('assigned_by', $team->id)
                ->latest()->take(5)->get();

            $myCompletedProjects = ProjectManagement::where(function($q) use ($team) {
                $q->where('assigned_by', $team->id)
                  ->orWhere('delivered_by', $team->id)
                  ->orWhere('responsible_team', $team->id);
            })->where('status', 'completed')->count();

            $myPendingProjects = ProjectManagement::where(function($q) use ($team) {
                $q->where('assigned_by', $team->id)
                  ->orWhere('delivered_by', $team->id)
                  ->orWhere('responsible_team', $team->id);
            })->whereIn('status', ['pending', 'in_progress'])->count();
        }

                // Personal Salary Statistics
        $mySalary = $team ? Salary::where('team_id', $team->id)
            ->where('year', $currentYear)
            ->where('month', $currentMonth)->first() : null;

        $myTotalSalary = $mySalary ? ($mySalary->amount + $mySalary->bonus + $mySalary->attendance_bonus - $mySalary->expense_deduction + $mySalary->achieved_target) : 0;

        // Personal Attendance Statistics
        $myTodayAttendance = $team ? Attendance::where('team_id', $team->id)
            ->whereDate('date', Carbon::today())->first() : null;

        $myThisMonthAttendance = $team ? Attendance::where('team_id', $team->id)
            ->whereYear('date', $currentYear)
            ->whereMonth('date', $currentMonth)->count() : 0;

        // Personal Leave Requests
        $myLeaveRequests = $team ? LeaveRequest::where('team_id', $team->id)
            ->latest()->take(5)->get() : collect();

        $myPendingLeaveRequests = $team ? LeaveRequest::where('team_id', $team->id)
            ->where('status', 'pending')->count() : 0;

        // Personal Expenses
        $myMonthlyExpenses = Expense::where('user_id', $user->id)
            ->whereYear('created_at', $currentYear)
            ->whereMonth('created_at', $currentMonth)->get();
        $myExpenses = $myMonthlyExpenses->sum(function($expense) {
            return $expense->accepted_amount ?? $expense->given_amount;
        });

        // Personal Target vs Achievement Statistics
        $myTargetAmount = $team ? $team->target_amount : 0;
        $myAchievedAmount = $team ? $team->achieved_amount : 0;
        $myAchievementPercentage = $myTargetAmount > 0 ? ($myAchievedAmount / $myTargetAmount) * 100 : 0;

        // Personal monthly target vs achievement data for the last 6 months
        $myMonthlyTargetData = [];
        $myMonthlyAchievementData = [];
        $myMonthlyLabels = [];

        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $month = $date->month;
            $year = $date->year;

            $myMonthlyLabels[] = $date->format('M Y');

            // Get target amount for this month
            $myMonthlyTarget = $myTargetAmount;
            $myMonthlyTargetData[] = $myMonthlyTarget;

            // Get achieved amount for this month from achievements - use direct query
            $myMonthlyAchieved = $team ? \App\Models\TeamAchievement::where('team_id', $team->id)
                ->whereMonth('achievement_date', $month)
                ->whereYear('achievement_date', $year)
                ->sum('achieved_amount') : 0;
            $myMonthlyAchievementData[] = $myMonthlyAchieved;
        }

        // Recent Activities
        $recentActivities = collect();

        return view('dashboard.user', compact(
            'myProjectManagements', 'mySpecialProjects', 'myCompletedProjects', 'myPendingProjects',
            'myTotalSalary', 'myTodayAttendance', 'myThisMonthAttendance',
            'myLeaveRequests', 'myPendingLeaveRequests', 'myExpenses',
            'recentActivities', 'myTargetAmount', 'myAchievedAmount', 'myAchievementPercentage',
            'myMonthlyTargetData', 'myMonthlyAchievementData', 'myMonthlyLabels'
        ));
    }
}
