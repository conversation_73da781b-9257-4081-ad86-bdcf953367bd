<?php

namespace App\Http\Controllers;

use App\Models\SpecialProject;
use App\Models\MarketplaceAccount;
use App\Models\Team;
use App\Models\MarketplaceAccountCategory;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SpecialProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = SpecialProject::with(['marketplace', 'assignedBy.user'])->latest();

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Filter by marketplace
        if ($request->has('marketplace_id') && $request->marketplace_id !== '') {
            $query->where('marketplace_id', $request->marketplace_id);
        }

        // Filter by assigned by
        if ($request->has('assigned_by') && $request->assigned_by !== '') {
            $query->where('assigned_by', $request->assigned_by);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from) {
            $query->where('order_date', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->where('order_date', '<=', $request->date_to);
        }

        // Search by project name or description
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('project_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $specialProjects = $query->paginate(10);
        $marketplaces = MarketplaceAccount::orderBy('order_account_name')->get();
        $teams = Team::with('user')->get()->sortBy('user.name');

        return view('special_projects.index', compact('specialProjects', 'marketplaces', 'teams'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $marketplaces = MarketplaceAccount::with('category')->orderBy('order_account_name')->get();
        $teams = Team::with('user')->get()->sortBy('user.name');
        return view('special_projects.create', compact('marketplaces', 'teams'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'marketplace_id' => 'required|exists:marketplace_accounts,id',
            'order_amount' => 'required|numeric|min:0',
            'delivery_amount' => 'nullable|numeric|min:0',
            'order_amount_rate' => 'required|numeric|min:0.01',
            'withdrawn_amount_rate' => 'required|numeric|min:0.01',
            'assigned_by' => 'required|exists:teams,id',
            'order_date' => 'required|date',
            'delivery_date' => 'nullable|date|after_or_equal:order_date',
            'project_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'notes' => 'nullable|string',
        ]);

        // Get marketplace account with category and active loans for delivery charge calculation
        $marketplaceAccount = MarketplaceAccount::with(['category', 'activeLoans'])->find($validated['marketplace_id']);

        if (!$marketplaceAccount->category) {
            return redirect()->back()->withErrors(['marketplace_id' => 'Marketplace account must have a category assigned.'])->withInput();
        }

        // Auto-calculate delivery amount based on category delivery charge and loan charge
        $validated['delivery_amount'] = $marketplaceAccount->calculateFinalDeliveredAmount($validated['order_amount']);

        SpecialProject::create($validated);

        return redirect()->route('special-projects.index')->with('success', 'Special project created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(SpecialProject $specialProject)
    {
        $specialProject->load(['marketplace', 'assignedBy.user']);
        return view('special_projects.show', compact('specialProject'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SpecialProject $specialProject)
    {
        $marketplaces = MarketplaceAccount::with('category')->orderBy('order_account_name')->get();
        $teams = Team::with('user')->get()->sortBy('user.name');
        return view('special_projects.edit', compact('specialProject', 'marketplaces', 'teams'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SpecialProject $specialProject)
    {
        $validated = $request->validate([
            'marketplace_id' => 'required|exists:marketplace_accounts,id',
            'order_amount' => 'required|numeric|min:0',
            'delivery_amount' => 'nullable|numeric|min:0',
            'order_amount_rate' => 'required|numeric|min:0.01',
            'withdrawn_amount_rate' => 'required|numeric|min:0.01',
            'assigned_by' => 'required|exists:teams,id',
            'order_date' => 'required|date',
            'delivery_date' => 'nullable|date|after_or_equal:order_date',
            'project_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'notes' => 'nullable|string',
        ]);

        // Get marketplace account with category and active loans for delivery charge calculation
        $marketplaceAccount = MarketplaceAccount::with(['category', 'activeLoans'])->find($validated['marketplace_id']);

        if (!$marketplaceAccount->category) {
            return redirect()->back()->withErrors(['marketplace_id' => 'Marketplace account must have a category assigned.'])->withInput();
        }

        // Auto-calculate delivery amount based on category delivery charge and loan charge
        $validated['delivery_amount'] = $marketplaceAccount->calculateFinalDeliveredAmount($validated['order_amount']);

        $specialProject->update($validated);

        return redirect()->route('special-projects.index')->with('success', 'Special project updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SpecialProject $specialProject)
    {
        $specialProject->delete();

        return redirect()->route('special-projects.index')->with('success', 'Special project deleted successfully.');
    }

    /**
     * Show user's own special projects.
     */
    public function myProjects(Request $request)
    {
        $user = auth()->user();
        $team = Team::where('user_id', $user->id)->first();

        $query = SpecialProject::with(['marketplace', 'assignedBy.user']);

        if ($team) {
            $query->where('assigned_by', $team->id);
        } else {
            // If user has no team, return empty result set
            $query->whereRaw('1 = 0'); // This ensures no results are returned
        }

        $query->latest();

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Filter by marketplace
        if ($request->has('marketplace_id') && $request->marketplace_id !== '') {
            $query->where('marketplace_id', $request->marketplace_id);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from) {
            $query->where('order_date', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->where('order_date', '<=', $request->date_to);
        }

        // Search by project name or description
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('project_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $specialProjects = $query->paginate(10);
        $marketplaces = MarketplaceAccount::orderBy('order_account_name')->get();
        $teams = Team::with('user')->get()->sortBy('user.name');

        return view('special_projects.my', compact('specialProjects', 'marketplaces', 'teams'));
    }

    /**
     * Update project status.
     */
    public function updateStatus(Request $request, SpecialProject $specialProject)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'delivery_amount' => 'nullable|numeric|min:0',
            'delivery_date' => 'nullable|date|after_or_equal:order_date',
            'notes' => 'nullable|string',
        ]);

        // If order amount is being updated, recalculate delivery amount
        if ($request->has('order_amount') && $request->order_amount) {
            $marketplaceAccount = MarketplaceAccount::with('category')->find($specialProject->marketplace_id);

            if (!$marketplaceAccount->category) {
                return redirect()->back()->withErrors(['marketplace_id' => 'Marketplace account must have a category assigned.'])->withInput();
            }

            $deliveryChargePercentage = $marketplaceAccount->category->delivery_amount_charge_percentage;
            $deliveryChargeMultiplier = (100 - $deliveryChargePercentage) / 100; // Convert percentage to multiplier
            $validated['delivery_amount'] = $request->order_amount * $deliveryChargeMultiplier;
        }

        $specialProject->update($validated);

        return redirect()->back()->with('success', 'Project status updated successfully.');
    }
}
