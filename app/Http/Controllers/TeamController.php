<?php

namespace App\Http\Controllers;

use App\Models\Team;
use App\Models\User;
use App\Models\Designation;
use App\Models\Label;
use Illuminate\Http\Request;

class TeamController extends Controller
{
    public function index(Request $request)
    {
        $query = Team::with(['user', 'designation', 'label']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by designation
        if ($request->filled('designation_id')) {
            $query->where('designation_id', $request->designation_id);
        }

        // Filter by label
        if ($request->filled('label_id')) {
            $query->where('label_id', $request->label_id);
        }

        $teams = $query->latest()->paginate(10);
        $designations = Designation::all();
        $labels = Label::all();

        return view('teams.index', compact('teams', 'designations', 'labels'));
    }

    public function create()
    {
        $users = User::all();
        $designations = Designation::all();
        $labels = Label::all();
        return view('teams.create', compact('users', 'designations', 'labels'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id|unique:teams,user_id',
            'designation_id' => 'required|exists:designations,id',
            'label_id' => 'required|exists:labels,id',
            'joining_date' => 'nullable|date|before_or_equal:today',
            'target' => 'nullable|numeric|min:0',
            'target_amount' => 'required|numeric|min:0',
            'base_salary' => 'required|numeric|min:0',
            'off_days' => 'nullable|array|max:2',
            'off_days.*' => 'required|string|in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
            'status' => 'required|in:active,inactive',
            'leave_status' => 'required|in:on_leave,on_duty',
        ]);

        // Ensure no duplicate off days
        if (isset($validated['off_days'])) {
            $validated['off_days'] = array_unique($validated['off_days']);
        }

        // Convert target to numeric if provided
        if (isset($validated['target'])) {
            $validated['target'] = floatval($validated['target']);
        }

        Team::create($validated);

        return redirect()->route('teams.index')->with('success', 'Team member created successfully.');
    }

    public function show(Team $team)
    {
        $team->load(['user', 'designation', 'label']);
        return view('teams.show', compact('team'));
    }

    public function edit(Team $team)
    {
        $users = User::all();
        $designations = Designation::all();
        $labels = Label::all();
        return view('teams.edit', compact('team', 'users', 'designations', 'labels'));
    }

    public function update(Request $request, Team $team)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id|unique:teams,user_id,' . $team->id,
            'designation_id' => 'required|exists:designations,id',
            'label_id' => 'required|exists:labels,id',
            'joining_date' => 'nullable|date|before_or_equal:today',
            'target' => 'nullable|numeric|min:0',
            'target_amount' => 'required|numeric|min:0',
            'base_salary' => 'required|numeric|min:0',
            'off_days' => 'nullable|array|max:2',
            'off_days.*' => 'required|string|in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
            'status' => 'required|in:active,inactive',
            'leave_status' => 'required|in:on_leave,on_duty',
        ]);

        // Ensure no duplicate off days
        if (isset($validated['off_days'])) {
            $validated['off_days'] = array_unique($validated['off_days']);
        }

        // Convert target to numeric if provided
        if (isset($validated['target'])) {
            $validated['target'] = floatval($validated['target']);
        }

        $team->update($validated);

        return redirect()->route('teams.index')->with('success', 'Team member updated successfully.');
    }

    public function destroy(Team $team)
    {
        $team->delete();

        return redirect()->route('teams.index')->with('success', 'Team member deleted successfully.');
    }
}

