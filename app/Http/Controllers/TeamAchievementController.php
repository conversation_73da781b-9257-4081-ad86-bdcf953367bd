<?php

namespace App\Http\Controllers;

use App\Models\TeamAchievement;
use App\Models\Team;
use App\Models\Label;
use Illuminate\Http\Request;

class TeamAchievementController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = TeamAchievement::with(['team.user', 'label']);

        // Filter by team
        if ($request->filled('team_id')) {
            $query->where('team_id', $request->team_id);
        }

        // Filter by label
        if ($request->filled('label_id')) {
            $query->where('label_id', $request->label_id);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->where('achievement_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('achievement_date', '<=', $request->end_date);
        }

        // Filter by payment status
        if ($request->filled('is_paid')) {
            $query->where('is_paid', $request->is_paid);
        }

        $achievements = $query->latest('achievement_date')->paginate(15);
        $teams = Team::with('user')->get();
        $labels = Label::active()->get();

        return view('team_achievements.index', compact('achievements', 'teams', 'labels'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $teams = Team::with('user')->get();
        $labels = Label::active()->get();
        return view('team_achievements.create', compact('teams', 'labels'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'team_id' => 'required|exists:teams,id',
            'label_id' => 'required|exists:labels,id',
            'achieved_amount' => 'required|numeric|min:0',
            'achievement_date' => 'required|date',
            'notes' => 'nullable|string|max:500',
        ]);

        // Calculate total bonus based on all labels that have been achieved
        $totalBonus = $this->calculateTotalBonusForAchievement($validated['team_id'], $validated['achieved_amount']);
        $validated['bonus_earned'] = $totalBonus;

        TeamAchievement::create($validated);

        // Update team's achieved amount
        $team = Team::findOrFail($validated['team_id']);
        $team->increment('achieved_amount', $validated['achieved_amount']);

        return redirect()->route('team-achievements.index')->with('success', 'Achievement recorded successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(TeamAchievement $teamAchievement)
    {
        $teamAchievement->load(['team.user', 'label']);
        return view('team_achievements.show', compact('teamAchievement'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TeamAchievement $teamAchievement)
    {
        $teams = Team::with('user')->get();
        $labels = Label::active()->get();
        return view('team_achievements.edit', compact('teamAchievement', 'teams', 'labels'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TeamAchievement $teamAchievement)
    {
        $validated = $request->validate([
            'team_id' => 'required|exists:teams,id',
            'label_id' => 'required|exists:labels,id',
            'achieved_amount' => 'required|numeric|min:0',
            'achievement_date' => 'required|date',
            'notes' => 'nullable|string|max:500',
            'is_paid' => 'boolean',
        ]);

        // Calculate total bonus based on all labels that have been achieved
        $totalBonus = $this->calculateTotalBonusForAchievement($validated['team_id'], $validated['achieved_amount']);
        $validated['bonus_earned'] = $totalBonus;

        // Update team's achieved amount if the amount changed
        if ($teamAchievement->achieved_amount != $validated['achieved_amount']) {
            $team = Team::findOrFail($validated['team_id']);
            $difference = $validated['achieved_amount'] - $teamAchievement->achieved_amount;
            $team->increment('achieved_amount', $difference);
        }

        $teamAchievement->update($validated);

        return redirect()->route('team-achievements.index')->with('success', 'Achievement updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TeamAchievement $teamAchievement)
    {
        // Update team's achieved amount
        $team = $teamAchievement->team;
        $team->decrement('achieved_amount', $teamAchievement->achieved_amount);

        $teamAchievement->delete();

        return redirect()->route('team-achievements.index')->with('success', 'Achievement deleted successfully.');
    }

    /**
     * Mark achievement as paid
     */
    public function markAsPaid(TeamAchievement $teamAchievement)
    {
        $teamAchievement->update(['is_paid' => true]);
        return redirect()->back()->with('success', 'Achievement marked as paid.');
    }

    /**
     * Mark achievement as unpaid
     */
    public function markAsUnpaid(TeamAchievement $teamAchievement)
    {
        $teamAchievement->update(['is_paid' => false]);
        return redirect()->back()->with('success', 'Achievement marked as unpaid.');
    }

    /**
     * Calculate total bonus for a team member based on all labels they've achieved
     */
    private function calculateTotalBonusForAchievement($teamId, $achievedAmount)
    {
        $team = Team::findOrFail($teamId);
        $totalBonus = 0;

        // Get all active labels
        $labels = Label::active()->get();

        foreach ($labels as $label) {
            // Check if the achieved amount meets this label's target
            if ($achievedAmount >= $label->bonus_target) {
                $totalBonus += $label->bonus_amount;
            }
        }

        return $totalBonus;
    }
}
