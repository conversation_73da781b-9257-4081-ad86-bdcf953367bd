<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Team;
use App\Models\User;
use Illuminate\View\View;
use Illuminate\Http\Request;
use App\Models\ProjectManagement;
use App\Models\MarketplaceAccount;
use App\Models\MarketplaceAccountCategory;
use Intervention\Image\ImageManager;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Drivers\Gd\Driver;

class ProjectManagementController extends Controller
{
    public function index(Request $request): View
    {
        $query = ProjectManagement::with('assignedBy');

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('assigned_by')) {
            $query->where('assigned_by', $request->assigned_by);
        }

        if ($request->filled('incoming_date')) {
            $query->whereDate('incoming_date', '>=', $request->incoming_date);
        }

        if ($request->filled('delivered_date')) {
            $query->whereDate('delivered_date', '<=', $request->delivered_date);
        }


        if ($request->filled('q')) {
            $search = $request->q;
            $query->where(function ($q) use ($search) {
                $q->where('client_name', 'LIKE', "%{$search}%")
                    ->orWhere('order_page_url', 'LIKE', "%{$search}%")
                    ->orWhere('amount', 'LIKE', "%{$search}%");
            });
        }

        $projectManagements = $query->latest()->paginate(config('app.pagination_per_page', 10));

        $orderAccounts = MarketplaceAccount::with('category')->get();
        $teams = Team::with('user')->get();

        return view('project_management.index', compact('projectManagements', 'teams', 'orderAccounts'));
    }

    // public function myProjects(Request $request): View
    // {
    //     $user = auth()->user();
    //     $team = $user->team;

    //     if (!$team) {
    //         return view('project_management.my', compact('projectManagements'))->with('error', 'No team assigned to you.');
    //     }

    //     $query = ProjectManagement::with(['assignedBy', 'deliveredBy', 'responsibleTeam'])
    //         ->where(function ($q) use ($team, $user) {
    //             $q->where('assigned_by', $team->id)
    //               ->orWhere('delivered_by', $team->id)
    //               ->orWhere('responsible_team', $team->id);
    //         });

    //     if ($request->filled('status')) {
    //         $query->where('status', $request->status);
    //     }

    //     if ($request->filled('q')) {
    //         $search = $request->q;
    //         $query->where(function ($q) use ($search) {
    //             $q->where('client_name', 'LIKE', "%{$search}%")
    //                 ->orWhere('project_name', 'LIKE', "%{$search}%")
    //                 ->orWhere('order_page_url', 'LIKE', "%{$search}%");
    //         });
    //     }

    //     $projectManagements = $query->latest()->paginate(10);

    //     return view('project_management.my', compact('projectManagements'));
    // }
public function myProjects(Request $request)
{
    $user = auth()->user();
    $team = $user->team ?? null;

    $query = ProjectManagement::query();

    if ($team) {
        // Filter by team role - show all projects where team is involved
        $query->where(function($q) use ($team) {
            $q->where('assigned_by', $team->id)
              ->orWhere('delivered_by', $team->id)
              ->orWhere('responsible_team', $team->id);
        });
    } else {
        // If user has no team, return empty result set
        $query->whereRaw('1 = 0'); // This ensures no results are returned
    }

    // Optional filters
    if ($request->filled('q')) {
        $q = $request->q;
        $query->where(function($sub) use ($q) {
            $sub->where('client_name', 'like', "%{$q}%")
                ->orWhere('project_name', 'like', "%{$q}%")
                ->orWhere('project_url', 'like', "%{$q}%");
        });
    }

    if ($request->filled('status')) {
        $query->where('status', $request->status);
    }

    $projectManagements = $query->latest()->paginate(10);
    $teams = Team::with('user')->get();

    return view('project_management.my', compact('projectManagements', 'teams'));
}



    public function create(): View
    {
        $teams = Team::with('user')->get();
        $orderAccounts = MarketplaceAccount::with('category')->get();

        $projectManagement = new ProjectManagement();
        $statuses = ['pending', 'in_progress', 'completed', 'on_hold', 'cancelled', 'in_revision'];
        $defaultStatus = 'in_progress';

        return view('project_management.create', compact('teams', 'statuses', 'orderAccounts', 'projectManagement'));
    }

    public function store(Request $request): RedirectResponse
    {
        $validated = $this->validateProject($request, true);

        // Convert and calculate amounts
        $validated['amount'] = $this->parseAmount($validated['amount'] ?? 0);

        // Get marketplace account with category and active loans for delivery charge calculation
        $marketplaceAccount = MarketplaceAccount::with(['category', 'activeLoans'])->find($validated['order_account_id']);

        if (!$marketplaceAccount->category) {
            return redirect()->back()->withErrors(['order_account_id' => 'Marketplace account must have a category assigned.'])->withInput();
        }

        // Calculate final delivered amount considering both delivery charge and loan charge
        $validated['delivered_amount'] = $this->parseAmount($validated['delivered_amount'] ?? $marketplaceAccount->calculateFinalDeliveredAmount($validated['amount']));

        // Handle dates
        if ($request->filled('delivered_date')) {
            $validated['delivered_date'] = Carbon::parse($request->input('delivered_date'));
        } elseif (array_key_exists('delivered_date', $validated)) {
            $validated['delivered_date'] = null;
        }

        // Set authenticated user's team as deliverer if not specified
        $user = auth()->user();
        $validated['delivered_by'] = $validated['delivered_by'] ?? $user->team_id;

        // Handle file uploads
        $validated = $this->handleFileUploads($request, $validated);

        ProjectManagement::create($validated);

        return redirect()->route('project-managements.index')
            ->with('success', 'Project created successfully.');
    }

    public function update(Request $request, ProjectManagement $projectManagement): RedirectResponse
    {
        if ($projectManagement->status === 'completed') {
            return redirect()->route('project-managements.index')
                ->with('error', 'Completed projects cannot be updated.');
        }

        if ($projectManagement->status === 'completed' && $request->status === 'in_revision') {
            return redirect()->route('project-managements.edit', $projectManagement)
                ->with('error', 'Cannot revert a completed project to in_revision.');
        }

        $validated = $this->validateProject($request, false);

        // Handle amounts if provided
        if ($request->has('amount')) {
            $validated['amount'] = $this->parseAmount($validated['amount']);

            // Recalculate delivered_amount based on category delivery charge and loan charge if amount changed
            if ($request->has('amount') && !$request->has('delivered_amount')) {
                $marketplaceAccount = MarketplaceAccount::with(['category', 'activeLoans'])->find($projectManagement->order_account_id);

                if (!$marketplaceAccount->category) {
                    return redirect()->back()->withErrors(['order_account_id' => 'Marketplace account must have a category assigned.'])->withInput();
                }

                $validated['delivered_amount'] = $marketplaceAccount->calculateFinalDeliveredAmount($validated['amount']);
            }
        }
        if ($request->has('delivered_amount')) {
            $validated['delivered_amount'] = $this->parseAmount($validated['delivered_amount']);
        }

        if ($request->filled('delivered_date')) {
            $validated['delivered_date'] = Carbon::parse($request->input('delivered_date'));
        } elseif (array_key_exists('delivered_date', $validated)) {
            $validated['delivered_date'] = null;
        }

        $validated = $this->handleFileUploads($request, $validated, $projectManagement);

        $projectManagement->update($validated);

        return redirect()->route('project-managements.index')
            ->with('success', 'Project updated successfully.');
    }


    public function show(ProjectManagement $projectManagement): View
    {
        $projectManagement->load(['assignedBy', 'deliveredBy', 'responsibleTeam']);
        $orderAccounts = MarketplaceAccount::with('category')->get();
        $teams = Team::with('user')->get();

        return view('project_management.show', compact('projectManagement', 'teams', 'orderAccounts'));
    }

    public function edit(ProjectManagement $projectManagement): View
    {

        $teams = Team::with('user')->get();
        $statuses = ['pending', 'in_progress', 'completed', 'on_hold', 'cancelled', 'in_revision'];
        $orderAccounts = MarketplaceAccount::with('category')->get();

    // Format dates for form inputs
    $projectManagement->incoming_date = $projectManagement->incoming_date?->format('Y-m-d');
    $projectManagement->delivered_date = $projectManagement->delivered_date?->format('Y-m-d\TH:i');

    // Calculate Remaining Days if project is not completed
    $remainingDays = null;
    if ($projectManagement->status !== 'completed' && $projectManagement->incoming_date) {
        $deadline = Carbon::parse($projectManagement->incoming_date)->addDays(30); // Assuming 30 days deadline from incoming_date
        $remainingDays = $deadline->diffInDays(Carbon::now(), false); // false to get negative if past deadline
    }

    return view('project_management.edit', compact('projectManagement', 'statuses', 'teams', 'orderAccounts', 'remainingDays'));
}


    public function destroy(ProjectManagement $projectManagement): RedirectResponse
    {
        $this->deleteProjectFiles($projectManagement);

        $projectManagement->delete();

        return redirect()->route('project-managements.index')
            ->with('success', 'Project deleted successfully.');
    }

    protected function validateProject(Request $request, bool $isCreate): array
    {
        $rules = [
            'team_id' => 'nullable|exists:teams,id',
            'order_account_id' => 'required|exists:marketplace_accounts,id',
            'project_name' => 'required|string|max:255',
            'delivered_date' => 'nullable|date',
            'delivered_amount' => 'nullable|numeric|min:0',
            'progress' => 'nullable|integer|min:0|max:100',
            'instruction_from_client' => 'nullable|string',
            'order_page_screenshot.*' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:9120',
            'conversation_page_screenshot.*' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:9120',
            'files.*' => 'nullable|file|mimes:zip,rar,pdf,doc,docx,jpeg,png,jpg|max:9120',
            'login_credentials' => 'nullable|string',
            'theme_name' => 'nullable|string|max:255',
            'page_builder_name' => 'nullable|string|max:255',
            'reference_website' => 'nullable|url',
            'special_plugins' => 'nullable|string',
            'special_requirements' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,in_progress,completed,in_revision,on_hold,cancelled',
            'assigned_by' => 'nullable|exists:teams,id',
            'delivered_by' => 'nullable|exists:teams,id',
            'client_name' => 'required|string|max:255',
            'amount' => 'required|numeric',
            'order_page_url' => 'required|url',
            'responsible_team' => 'required|exists:teams,id',
            'incoming_date' => 'required|date',
            'cms' => 'required|string|max:255',
        ];

        if (!$isCreate) {
            $rules['amount'] = 'nullable|numeric';
            $rules['order_page_url'] = 'nullable|url';
            $rules['client_name'] = 'nullable|string|max:255';
            $rules['incoming_date'] = 'nullable|date';
            $rules['cms'] = 'nullable|string|max:255';
            $rules['order_account_id'] = 'nullable|exists:marketplace_accounts,id';
            $rules['project_name'] = 'nullable|string|max:255';
            $rules['responsible_team'] = 'nullable|exists:teams,id';
        }

        return $request->validate($rules);
    }

    protected function handleFileUploads(Request $request, array $validated, ProjectManagement $projectManagement = null): array
    {
        $fileFields = [
            'order_page_screenshot' => 'screenshots',
            'conversation_page_screenshot' => 'screenshots',
            'files' => 'files'
        ];

        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        $manager = new ImageManager(new Driver());

        foreach ($fileFields as $field => $folder) {
            // Handle file deletions first
            if ($projectManagement && $request->has("delete_{$field}")) {
                $this->deleteFiles((array) $request->input("delete_{$field}"));
                $validated[$field] = null;
            }

            // Handle new file uploads
            if ($request->hasFile($field)) {
                $uploadedFiles = [];

                foreach ($request->file($field) as $file) {
                    $filename = time() . '_' . $file->getClientOriginalName();
                    $extension = strtolower($file->getClientOriginalExtension());
                    $path = "project_management/{$folder}/{$filename}";

                    if (in_array($extension, $imageExtensions)) {
                        // Process and compress the image
                        $image = $manager->read($file);
                        $image->resize(1200, null, function ($constraint) {
                            $constraint->aspectRatio();
                            $constraint->upsize();
                        });

                        Storage::disk('public')->put($path, (string) $image->toJpeg(85));
                    } else {
                        // Store non-image files as-is
                        $file->storeAs("project_management/{$folder}", $filename, 'public');
                    }

                    $uploadedFiles[] = $path;
                }

                // For multiple files, store as JSON
                $validated[$field] = count($uploadedFiles) > 1 ? json_encode($uploadedFiles) : ($uploadedFiles[0] ?? null);
            } elseif ($projectManagement) {
                // Keep existing files if not updating
                $validated[$field] = $projectManagement->$field;
            }
        }

        return $validated;
    }
    protected function deleteFiles(array $files): void
    {
        foreach ($files as $file) {
            Storage::disk('public')->delete($file);
        }
    }

    protected function deleteProjectFiles(ProjectManagement $projectManagement): void
    {
        $fileFields = ['order_page_screenshot', 'conversation_page_screenshot', 'files'];

        foreach ($fileFields as $field) {
            $files = json_decode($projectManagement->{$field}, true);
            if ($files) {
                $this->deleteFiles($files);
            }
        }
    }

    protected function parseAmount($amount)
    {
        // If amount is string with commas, e.g. "1,200", remove commas
        if (is_string($amount)) {
            $amount = str_replace(',', '', $amount);
        }

        return (float) $amount;
    }
     protected function isJson($string): bool
    {
        json_decode($string);
        return (json_last_error() === JSON_ERROR_NONE);
    }
}




