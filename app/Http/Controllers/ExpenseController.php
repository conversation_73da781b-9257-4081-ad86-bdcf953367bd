<?php

namespace App\Http\Controllers;

use App\Models\Expense;
use App\Models\User;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class ExpenseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Expense::with('user')->latest();

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from) {
            $query->where('date', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->where('date', '<=', $request->date_to);
        }

        // Search by name or purpose
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('purpose', 'like', "%{$search}%");
            });
        }

        // Filter by user role
        if (!auth()->user()->hasRole(['super_admin', 'admin'])) {
            $query->where('user_id', auth()->id());
        }

        $expenses = $query->paginate(10);

        return view('expenses.index', compact('expenses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Only show user selection for admins, regular users will be auto-assigned
        if (auth()->user()->hasRole(['super_admin', 'admin'])) {
            $users = User::orderBy('name')->get();
        } else {
            $users = collect();
        }
        return view('expenses.create', compact('users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'date' => 'required|date',
            'picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'purpose' => 'required|string',
            'given_amount' => 'required|numeric|min:0',
            'accepted_amount' => 'nullable|numeric|min:0',
            'user_id' => 'nullable|exists:users,id',
            'notes' => 'nullable|string',
        ]);

        // Handle file upload using ImageHelper
        if ($request->hasFile('picture')) {
            $validated['picture'] = ImageHelper::saveImage($request->file('picture'), 'uploads/expenses', 800);
        }

        // Set default status
        $validated['status'] = 'pending';

        // Set user_id to current user if not admin/super_admin
        if (!auth()->user()->hasRole(['super_admin', 'admin'])) {
            $validated['user_id'] = auth()->id();
        }

        Expense::create($validated);

        return redirect()->route('expenses.index')->with('success', 'Expense created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Expense $expense)
    {
        // Check if user can view this expense
        if (!auth()->user()->hasRole(['super_admin', 'admin']) && $expense->user_id !== auth()->id()) {
            abort(403);
        }

        return view('expenses.show', compact('expense'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Expense $expense)
    {
        // Check if user can edit this expense
        if (!auth()->user()->hasRole(['super_admin', 'admin']) && $expense->user_id !== auth()->id()) {
            abort(403);
        }

        // Only show user selection for admins, regular users will be auto-assigned
        if (auth()->user()->hasRole(['super_admin', 'admin'])) {
            $users = User::orderBy('name')->get();
        } else {
            $users = collect();
        }
        return view('expenses.edit', compact('expense', 'users'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Expense $expense)
    {
        // Check if user can update this expense
        if (!auth()->user()->hasRole(['super_admin', 'admin']) && $expense->user_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'date' => 'required|date',
            'picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'purpose' => 'required|string',
            'given_amount' => 'required|numeric|min:0',
            'accepted_amount' => 'nullable|numeric|min:0',
            'user_id' => 'nullable|exists:users,id',
            'notes' => 'nullable|string',
        ]);

        // Handle file upload using ImageHelper
        if ($request->hasFile('picture')) {
            // Delete old file if exists
            if ($expense->picture) {
                $oldFile = public_path($expense->picture);
                if (file_exists($oldFile)) {
                    unlink($oldFile);
                }
            }

            $validated['picture'] = ImageHelper::saveImage($request->file('picture'), 'uploads/expenses', 800);
        }

        // Set user_id to current user if not admin/super_admin
        if (!auth()->user()->hasRole(['super_admin', 'admin'])) {
            $validated['user_id'] = auth()->id();
        }

        $expense->update($validated);

        return redirect()->route('expenses.index')->with('success', 'Expense updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Expense $expense)
    {
        // Check if user can delete this expense
        if (!auth()->user()->hasRole(['super_admin', 'admin']) && $expense->user_id !== auth()->id()) {
            abort(403);
        }

        // Delete picture file if exists
        if ($expense->picture) {
            $file = public_path($expense->picture);
            if (file_exists($file)) {
                unlink($file);
            }
        }

        $expense->delete();

        return redirect()->route('expenses.index')->with('success', 'Expense deleted successfully.');
    }

    /**
     * Approve or reject an expense (admin only).
     */
    public function updateStatus(Request $request, Expense $expense)
    {
        if (!auth()->user()->hasRole(['super_admin', 'admin'])) {
            abort(403);
        }

        $validated = $request->validate([
            'status' => 'required|in:approved,rejected',
            'accepted_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        $expense->update($validated);

        $statusText = $validated['status'] === 'approved' ? 'approved' : 'rejected';
        return redirect()->route('expenses.index')->with('success', "Expense {$statusText} successfully.");
    }

    /**
     * Show user's own expenses.
     */
    public function myExpenses(Request $request)
    {
        $query = Expense::where('user_id', auth()->id())->latest();

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from) {
            $query->where('date', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->where('date', '<=', $request->date_to);
        }

        $expenses = $query->paginate(10);

        return view('expenses.my', compact('expenses'));
    }
}
