<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Service extends Model
{
    protected $fillable = [
        'title', 'slug', 'subtitle', 'description', 'image', 'icon',
        'meta_title', 'meta_description', 'meta_keywords',
    ];
// Auto-generate slug if not provided
    public static function boot()
    {
        parent::boot();

        static::saving(function ($service) {
            if (empty($service->slug)) {
                $service->slug = Str::slug($service->title);
            }
            if (empty($service->meta_title)) {
                $service->meta_title = $service->title;
            }
            if (empty($service->meta_description) && !empty($service->description)) {
                $service->meta_description = substr(strip_tags($service->description), 0, 150);
            }
        });
    }
    protected static function booted()
    {
        static::creating(function ($service) {
            if (empty($service->slug)) {
                $service->slug = Str::slug($service->title);
            }
        });

        static::updating(function ($service) {
            if (empty($service->slug)) {
                $service->slug = Str::slug($service->title);
            }
        });
    }
}

