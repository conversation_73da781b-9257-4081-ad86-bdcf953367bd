<?php

namespace App\Models;

use App\Models\Team;
use App\Models\MarketplaceAccount;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProjectManagement extends Model
{
    use HasFactory;

    protected $table = 'project_managements';

    protected $fillable = [
        'team_id',
        'order_account_id',
        'project_name',
        'delivered_date',
        'delivered_amount',
        'progress',
        'instruction_from_client',
        'order_page_screenshot',
        'conversation_page_screenshot',
        'files',
        'login_credentials',
        'theme_name',
        'page_builder_name',
        'reference_website',
        'special_plugins',
        'special_requirements',
        'notes',
        'status',
        'assigned_by',
        'delivered_by',
        'client_name',
        'amount',
        'order_page_url',
        'responsible_team',
        'incoming_date',
        'cms',
    ];
protected $casts = [
    'delivered_date' => 'datetime',
    'incoming_date' => 'datetime',
    'order_page_screenshot' => 'array',
    'conversation_page_screenshot' => 'array',
    'files' => 'array',
];
    // Relationships
    public function assignedBy()
    {
        return $this->belongsTo(Team::class, 'assigned_by');
    }

    public function deliveredBy()
    {
        return $this->belongsTo(Team::class, 'delivered_by');
    }

    public function responsibleTeam()
    {
        return $this->belongsTo(Team::class, 'responsible_team');
    }

    public function orderAccount()
    {
        return $this->belongsTo(MarketplaceAccount::class, 'order_account_id');
    }
}

