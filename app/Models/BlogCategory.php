<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class BlogCategory extends Model
{
    protected $fillable = ['name', 'slug', 'parent_id'];

    // Generate slug automatically when creating/updating
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($category) {
            if (empty($category->slug)) {
                $category->slug = Str::slug($category->name);
            }
        });
    }

    // Define parent category relationship
    public function parent()
    {
        return $this->belongsTo(BlogCategory::class, 'parent_id');
    }

    // Define subcategories relationship
    public function children()
    {
        return $this->hasMany(BlogCategory::class, 'parent_id');
    }
}


