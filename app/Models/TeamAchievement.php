<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TeamAchievement extends Model
{
    protected $fillable = [
        'team_id',
        'label_id',
        'achieved_amount',
        'bonus_earned',
        'achievement_date',
        'notes',
        'is_paid'
    ];

    protected $casts = [
        'achieved_amount' => 'decimal:2',
        'bonus_earned' => 'decimal:2',
        'achievement_date' => 'date',
        'is_paid' => 'boolean',
    ];

    /**
     * Get the team that owns this achievement
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the label for this achievement
     */
    public function label(): BelongsTo
    {
        return $this->belongsTo(Label::class);
    }

    /**
     * Scope for paid achievements
     */
    public function scopePaid($query)
    {
        return $query->where('is_paid', true);
    }

    /**
     * Scope for unpaid achievements
     */
    public function scopeUnpaid($query)
    {
        return $query->where('is_paid', false);
    }

    /**
     * Scope for achievements in a date range
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('achievement_date', [$startDate, $endDate]);
    }
}
