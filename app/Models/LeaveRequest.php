<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LeaveRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'team_id',
        'leave_type',
        'start_date',
        'end_date',
        'reason',
        'status',
        'approved_by',
    ];

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function approver()
    {
        return $this->belongsTo(Team::class, 'approved_by');
    }
}
