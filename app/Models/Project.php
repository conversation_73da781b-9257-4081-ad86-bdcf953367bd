<?php

namespace App\Models;

use App\Models\User;
use App\Models\Service;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;

class Project extends Model
{
    protected $fillable = [
        'title', 'subtitle', 'images', 'description', 'user_id', 'budget', 'date', 'service_id', 'meta_title', 'meta_description', 'meta_keywords',
    ];

    protected $casts = [
        'images' => 'array',
        'date' => 'date',
        'budget' => 'decimal:2',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    public static function boot()
    {
        parent::boot();

        static::saving(function ($service) {
            if (empty($service->slug)) {
                $service->slug = Str::slug($service->title);
            }
            if (empty($service->meta_title)) {
                $service->meta_title = $service->title;
            }
            if (empty($service->meta_description) && !empty($service->description)) {
                $service->meta_description = substr(strip_tags($service->description), 0, 150);
            }
        });
    }
    protected static function booted()
    {
        static::creating(function ($service) {
            if (empty($service->slug)) {
                $service->slug = Str::slug($service->title);
            }
        });

        static::updating(function ($service) {
            if (empty($service->slug)) {
                $service->slug = Str::slug($service->title);
            }
        });
    }
}


