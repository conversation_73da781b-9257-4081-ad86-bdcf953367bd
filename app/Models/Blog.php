<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Blog extends Model
{
    use HasFactory;

    protected $table = 'blogs';

    protected $fillable = [
        'category_id',
        'title',
        'slug',
        'image',
        'description',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    public function category()
    {
        return $this->belongsTo(BlogCategory::class, 'category_id');
    }
    public function author()
{
    return $this->belongsTo(User::class, 'author_id'); // adjust if different key
}
// Blog.php
public function getTagsAttribute()
{
    return collect(explode(',', $this->meta_keywords))
        ->map(fn($tag) => trim($tag))
        ->filter()
        ->unique();
}


    // Automatically generate slug on creating or updating
    protected static function booted()
    {
        static::saving(function ($blog) {
            // Only generate slug if title is set and slug is empty or title changed
            if ($blog->isDirty('title')) {
                $slug = Str::slug($blog->title);
                $originalSlug = $slug;
                $count = 1;

                // Check uniqueness of slug
                while (
                    static::where('slug', $slug)
                        ->where('id', '!=', $blog->id ?? 0)
                        ->exists()
                ) {
                    $slug = $originalSlug . '-' . $count++;
                }

                $blog->slug = $slug;
            }
        });
    }
}

