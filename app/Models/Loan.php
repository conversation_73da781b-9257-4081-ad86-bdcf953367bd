<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Loan extends Model
{
    use HasFactory;

    protected $fillable = [
        'amount',
        'loan_amount_charge_percentage',
        'marketplace_id',
        'due_amounts',
        'description',
        'status',
        'paid_date',
        'paid_amount',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'loan_amount_charge_percentage' => 'decimal:2',
        'due_amounts' => 'decimal:2',
        'paid_date' => 'date',
        'paid_amount' => 'decimal:2',
    ];

    /**
     * Get the marketplace that owns the loan.
     */
    public function marketplace()
    {
        return $this->belongsTo(MarketplaceAccount::class, 'marketplace_id');
    }

    /**
     * Scope a query to only include pending loans.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include approved loans.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include paid loans.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope a query to only include overdue loans.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue');
    }

    /**
     * Get the total amount (amount + loan charge).
     */
    public function getTotalAmountAttribute()
    {
        $chargeAmount = ($this->amount * $this->loan_amount_charge_percentage) / 100;
        return $this->amount + $chargeAmount;
    }

    /**
     * Get the remaining amount to be paid.
     */
    public function getRemainingAmountAttribute()
    {
        return $this->due_amounts - ($this->paid_amount ?? 0);
    }

    /**
     * Add a deducted amount to the due amounts (track loan usage)
     */
    public function addDeduction($amount, $description = null)
    {
        $this->due_amounts += $amount;

        // Add to notes if description provided
        if ($description) {
            $currentNotes = $this->notes ? $this->notes . "\n" : '';
            $this->notes = $currentNotes . date('Y-m-d H:i:s') . ": Deducted $" . number_format($amount, 2) . " - " . $description;
        }

        $this->save();

        return $this;
    }

    /**
     * Get the original loan amount (before any deductions)
     */
    public function getOriginalAmountAttribute()
    {
        return $this->amount;
    }

    /**
     * Get the total amount deducted from this loan
     */
    public function getTotalDeductedAttribute()
    {
        return $this->due_amounts - $this->amount;
    }

    /**
     * Check if loan has available balance for deduction
     */
    public function hasAvailableBalance($amount)
    {
        $availableBalance = $this->due_amounts - ($this->paid_amount ?? 0);
        return $availableBalance >= $amount;
    }

    /**
     * Check if the loan is overdue.
     */
    public function getIsOverdueAttribute()
    {
        // Since due_amounts is now an amount, we'll consider it overdue if status is overdue
        return $this->status === 'overdue';
    }

    /**
     * Check if the loan is active (approved and not paid).
     */
    public function getIsActiveAttribute()
    {
        return $this->status === 'approved' && $this->status !== 'paid' && $this->status !== 'rejected';
    }

    /**
     * Get the total loan charge amount for a given project amount.
     */
    public function getLoanChargeAmount($projectAmount)
    {
        if (!$this->is_active) {
            return 0;
        }
        return ($projectAmount * $this->loan_amount_charge_percentage) / 100;
    }

    /**
     * Get the status badge class.
     */
    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'pending' => 'badge bg-warning',
            'approved' => 'badge bg-info',
            'rejected' => 'badge bg-danger',
            'paid' => 'badge bg-success',
            'overdue' => 'badge bg-danger',
            default => 'badge bg-secondary',
        };
    }

    /**
     * Get the formatted due amount.
     */
    public function getFormattedDueAmountAttribute()
    {
        return '$' . number_format($this->due_amounts, 2);
    }

    /**
     * Get the formatted paid date.
     */
    public function getFormattedPaidDateAttribute()
    {
        return $this->paid_date ? $this->paid_date->format('M d, Y') : 'Not paid';
    }

    /**
     * Boot method to automatically update status to overdue
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            // Since due_amounts is now an amount, we'll need manual status management
            // The overdue status should be set manually or through business logic
        });
    }
}
