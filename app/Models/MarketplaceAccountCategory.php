<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MarketplaceAccountCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'delivery_amount_charge_percentage',
        'description',
        'is_active',
    ];

    protected $casts = [
        'delivery_amount_charge_percentage' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the marketplace accounts for this category.
     */
    public function marketplaceAccounts()
    {
        return $this->hasMany(MarketplaceAccount::class, 'category_id');
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
