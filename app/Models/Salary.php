<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Salary extends Model
{
    protected $fillable = [
        'team_id', 'year', 'month', 'amount', 'bonus', 'attendance_bonus', 'expense_deduction', 'achieved_target'
    ];

    public function team()
    {
        return $this->belongsTo(Team::class);
    }
    public function project()
    {
        return $this->belongsTo(ProjectManagement::class, 'project_id');
    }
}

