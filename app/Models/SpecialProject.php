<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SpecialProject extends Model
{
    use HasFactory;

    protected $fillable = [
        'marketplace_id',
        'order_amount',
        'delivery_amount',
        'order_amount_rate',
        'withdrawn_amount_rate',
        'in_hand_amount',
        'assigned_by',
        'order_date',
        'delivery_date',
        'project_name',
        'description',
        'status',
        'notes',
    ];

    protected $casts = [
        'order_date' => 'date',
        'delivery_date' => 'date',
        'order_amount' => 'decimal:2',
        'delivery_amount' => 'decimal:2',
        'order_amount_rate' => 'decimal:2',
        'withdrawn_amount_rate' => 'decimal:2',
        'in_hand_amount' => 'decimal:2',
    ];

    /**
     * Boot method to automatically calculate in_hand_amount
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            if ($model->order_amount_rate && $model->withdrawn_amount_rate && $model->order_amount && $model->delivery_amount) {
                // In Hand Amount is the final amount in BDT (delivery amount converted to BDT)
                $model->in_hand_amount = $model->delivery_amount * $model->withdrawn_amount_rate;
            }
        });
    }

    /**
     * Get the marketplace that owns the special project.
     */
    public function marketplace()
    {
        return $this->belongsTo(MarketplaceAccount::class, 'marketplace_id');
    }

    /**
     * Get the team member assigned to this project.
     */
    public function assignedBy()
    {
        return $this->belongsTo(Team::class, 'assigned_by');
    }

    /**
     * Scope a query to only include pending projects.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include in progress projects.
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * Scope a query to only include completed projects.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include cancelled projects.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Get the formatted order date attribute.
     */
    public function getFormattedOrderDateAttribute()
    {
        return $this->order_date->format('M d, Y');
    }

    /**
     * Get the formatted delivery date attribute.
     */
    public function getFormattedDeliveryDateAttribute()
    {
        return $this->delivery_date ? $this->delivery_date->format('M d, Y') : 'Not set';
    }

    /**
     * Get the formatted date attribute (for general use).
     */
    public function getFormattedDateAttribute()
    {
        return $this->order_date->format('M d, Y');
    }

    /**
     * Get the status badge class.
     */
    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'pending' => 'badge bg-warning',
            'in_progress' => 'badge bg-info',
            'completed' => 'badge bg-success',
            'cancelled' => 'badge bg-danger',
            default => 'badge bg-secondary',
        };
    }

    /**
     * Calculate the profit margin.
     */
    public function getProfitMarginAttribute()
    {
        if ($this->delivery_amount && $this->order_amount) {
            return (($this->delivery_amount - $this->order_amount) / $this->order_amount) * 100;
        }
        return 0;
    }

    /**
     * Calculate the profit amount.
     */
    public function getProfitAmountAttribute()
    {
        if ($this->delivery_amount && $this->order_amount) {
            return $this->delivery_amount - $this->order_amount;
        }
        return 0;
    }

    /**
     * Get the order amount in local currency.
     */
    public function getOrderAmountLocalAttribute()
    {
        return $this->order_amount * $this->order_amount_rate;
    }

    /**
     * Get the delivery amount in local currency.
     */
    public function getDeliveryAmountLocalAttribute()
    {
        if ($this->delivery_amount) {
            return $this->delivery_amount * $this->withdrawn_amount_rate;
        }
        return 0;
    }

    /**
     * Get the total profit in local currency.
     */
    public function getProfitAmountLocalAttribute()
    {
        return $this->delivery_amount_local - $this->order_amount_local;
    }

    /**
     * Get the calculated in hand amount.
     */
    public function getCalculatedInHandAmountAttribute()
    {
        return $this->order_amount_rate - $this->withdrawn_amount_rate;
    }

    /**
     * Check if project is overdue.
     */
    public function getIsOverdueAttribute()
    {
        if ($this->delivery_date && $this->status !== 'completed') {
            return $this->delivery_date->isPast();
        }
        return false;
    }

    /**
     * Get the project duration in days.
     */
    public function getDurationDaysAttribute()
    {
        if ($this->delivery_date && $this->order_date) {
            return $this->order_date->diffInDays($this->delivery_date);
        }
        return 0;
    }
}
