<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Label extends Model
{
    protected $fillable = [
        'name',
        'bonus_target',
        'bonus_amount',
        'description',
        'is_active'
    ];

    protected $casts = [
        'bonus_target' => 'decimal:2',
        'bonus_amount' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get all teams with this label
     */
    public function teams(): HasMany
    {
        return $this->hasMany(Team::class);
    }

    /**
     * Get all achievements for this label
     */
    public function achievements(): HasMany
    {
        return $this->hasMany(TeamAchievement::class);
    }

    /**
     * Calculate bonus amount - returns the bonus amount if label target is achieved
     */
    public function calculateBonus($achievedAmount): float
    {
        // Check if the achieved amount meets or exceeds the label's target
        if ($achievedAmount >= $this->bonus_target) {
            return $this->bonus_amount;
        }

        return 0; // No bonus if target not achieved
    }

    /**
     * Check if label target is achieved
     */
    public function isTargetAchieved($achievedAmount): bool
    {
        return $achievedAmount >= $this->bonus_target;
    }

    /**
     * Get the remaining amount needed to achieve this label's target
     */
    public function getRemainingAmount($achievedAmount): float
    {
        return max(0, $this->bonus_target - $achievedAmount);
    }

    /**
     * Scope for active labels
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}

