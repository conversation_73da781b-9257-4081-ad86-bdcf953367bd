<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    /**
     * User roles
     */
    const ROLES = [
        'user', 'affiliate', 'admin', 'super_admin', 'editor', 'developer', 'marketer'
    ];

    /**
     * User permissions
     */
    const PERMISSIONS = [
        'read', 'write', 'read_write', 'none', 'delete', 'full'
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'number',
        'address',
        'picture',
        'role',
        'permission',
        'email_verified_at',

    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',

    ];

    /**
     * Check if the user has a specific role.
     * This method works with both legacy role field and Spatie permissions
     *
     * @param string|array $role
     * @return bool
     */
    public function hasRole($role)
    {
        // Use Spatie roles directly (avoiding infinite loop)
        if (is_array($role)) {
            return $this->roles()->whereIn('name', $role)->exists();
        }

        return $this->roles()->where('name', $role)->exists();
    }

    /**
     * Check if the user has a specific permission.
     * This method works with both legacy permission field and Spatie permissions
     *
     * @param string $permission
     * @return bool
     */
    public function hasPermission($permission)
    {
        // Super admin has all permissions
        if ($this->roles()->where('name', 'super_admin')->exists()) {
            return true;
        }

        // Use Spatie permissions directly (avoiding infinite loop)
        return $this->permissions()->where('name', $permission)->exists() ||
               $this->roles()->whereHas('permissions', function($query) use ($permission) {
                   $query->where('name', $permission);
               })->exists();
    }

    public function team()
    {
        return $this->hasOne(Team::class);
    }
}
