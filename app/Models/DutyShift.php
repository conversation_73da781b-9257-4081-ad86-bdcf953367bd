<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DutyShift extends Model
{
    protected $fillable = [
        'team_id', 'date', 'name', 'start_time', 'end_time', 'grace_minutes', 'effective'
    ];

    protected $casts = [
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'effective' => 'boolean',
        'grace_minutes' => 'integer',
    ];

    public function team()
    {
        return $this->belongsTo(Team::class);
    }
}


