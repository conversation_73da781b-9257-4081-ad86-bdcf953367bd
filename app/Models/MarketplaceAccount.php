<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MarketplaceAccount extends Model
{
    protected $fillable = [
        'order_account_name',
        'created_date',
        'username',
        'password',
        'wifi_ip',
        'category_id',
    ];

    /**
     * Get the category that owns the marketplace account.
     */
    public function category()
    {
        return $this->belongsTo(MarketplaceAccountCategory::class, 'category_id');
    }

    /**
     * Get the loans for this marketplace account.
     */
    public function loans()
    {
        return $this->hasMany(Loan::class, 'marketplace_id');
    }

    /**
     * Get the active loans for this marketplace account.
     */
    public function activeLoans()
    {
        return $this->hasMany(Loan::class, 'marketplace_id')->where('status', 'approved');
    }

    /**
     * Get the total active loan charge percentage for this marketplace account.
     */
    public function getTotalActiveLoanChargePercentage()
    {
        return $this->activeLoans()->sum('loan_amount_charge_percentage');
    }

    /**
     * Calculate the final delivered amount considering both delivery charge and loan charge.
     */
    public function calculateFinalDeliveredAmount($projectAmount)
    {
        $deliveryChargePercentage = $this->category ? $this->category->delivery_amount_charge_percentage : 0;
        $loanChargePercentage = $this->getTotalActiveLoanChargePercentage();

        // Calculate delivery charge first
        $deliveryChargeAmount = ($projectAmount * $deliveryChargePercentage) / 100;
        $afterDelivery = $projectAmount - $deliveryChargeAmount;

        // Calculate loan charge on the amount after delivery
        $loanChargeAmount = ($afterDelivery * $loanChargePercentage) / 100;

        // Final amount after both charges
        $finalAmount = $projectAmount - $deliveryChargeAmount - $loanChargeAmount;

        return $finalAmount;
    }
}

