<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'model_type',
        'model_id',
        'model_name',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
        'url',
        'method',
        'description',
        'metadata',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Prevent deletion of audit logs
     */
    public function delete()
    {
        throw new \Exception('Audit logs cannot be deleted for security and compliance reasons. Use the clean method for bulk cleanup.');
    }

    /**
     * Prevent force deletion of audit logs
     */
    public function forceDelete()
    {
        throw new \Exception('Audit logs cannot be force deleted for security and compliance reasons.');
    }

    /**
     * Get the user that performed the action
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the model that was affected
     */
    public function model()
    {
        if ($this->model_type && $this->model_id) {
            return $this->model_type::find($this->model_id);
        }
        return null;
    }

    /**
     * Get the action badge color
     */
    public function getActionBadgeColorAttribute(): string
    {
        return match ($this->action) {
            'create' => 'success',
            'update' => 'warning',
            'delete' => 'danger',
            'login' => 'info',
            'logout' => 'secondary',
            'view' => 'primary',
            default => 'secondary',
        };
    }

    /**
     * Get the action icon
     */
    public function getActionIconAttribute(): string
    {
        return match ($this->action) {
            'create' => 'ri-add-line',
            'update' => 'ri-edit-line',
            'delete' => 'ri-delete-bin-line',
            'login' => 'ri-login-box-line',
            'logout' => 'ri-logout-box-line',
            'view' => 'ri-eye-line',
            default => 'ri-information-line',
        };
    }

    /**
     * Get formatted description
     */
    public function getFormattedDescriptionAttribute(): string
    {
        if ($this->description) {
            return $this->description;
        }

        $userName = $this->user ? $this->user->name : 'System';
        $modelName = $this->model_name ?: class_basename($this->model_type);

        return match ($this->action) {
            'create' => "{$userName} created {$modelName}",
            'update' => "{$userName} updated {$modelName}",
            'delete' => "{$userName} deleted {$modelName}",
            'login' => "{$userName} logged in",
            'logout' => "{$userName} logged out",
            'view' => "{$userName} viewed {$modelName}",
            default => "{$userName} performed {$this->action} on {$modelName}",
        };
    }

    /**
     * Scope for filtering by action
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for filtering by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for filtering by model
     */
    public function scopeByModel($query, $modelType, $modelId = null)
    {
        $query = $query->where('model_type', $modelType);

        if ($modelId) {
            $query->where('model_id', $modelId);
        }

        return $query;
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for recent logs
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
