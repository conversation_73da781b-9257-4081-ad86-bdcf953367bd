<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Team extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'designation_id',
        'label_id',
        'joining_date',
        'target',
        'target_amount',
        'achieved_amount',
        'base_salary',
        'off_days',
        'status',
        'leave_status',
    ];

    protected $casts = [
        'joining_date' => 'date',
        'off_days' => 'array',
        'target_amount' => 'decimal:2',
        'achieved_amount' => 'decimal:2',
        'base_salary' => 'decimal:2',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function designation()
    {
        return $this->belongsTo(Designation::class);
    }

    public function label()
    {
        return $this->belongsTo(Label::class);
    }

    /**
     * Get all achievements for this team member
     */
    public function achievements()
    {
        return $this->hasMany(TeamAchievement::class);
    }

    /**
     * Calculate total salary including base salary and bonuses
     */
    public function calculateTotalSalary($month = null, $year = null)
    {
        $baseSalary = $this->base_salary;
        $bonusAmount = 0;

        if ($month && $year) {
            $achievements = $this->achievements()
                ->whereMonth('achievement_date', $month)
                ->whereYear('achievement_date', $year)
                ->where('is_paid', false)
                ->get();
        } else {
            $achievements = $this->achievements()->where('is_paid', false)->get();
        }

        foreach ($achievements as $achievement) {
            $bonusAmount += $achievement->bonus_earned;
        }

        return $baseSalary + $bonusAmount;
    }

    /**
     * Calculate total bonus based on current achieved amount and all labels
     */
    public function calculateCurrentBonus()
    {
        $totalBonus = 0;
        $labels = \App\Models\Label::active()->get();

        foreach ($labels as $label) {
            if ($this->achieved_amount >= $label->bonus_target) {
                $totalBonus += $label->bonus_amount;
            }
        }

        return $totalBonus;
    }

    /**
     * Get all labels that have been achieved by this team member
     */
    public function getAchievedLabels()
    {
        $achievedLabels = [];
        $labels = \App\Models\Label::active()->get();

        foreach ($labels as $label) {
            if ($this->achieved_amount >= $label->bonus_target) {
                $achievedLabels[] = $label;
            }
        }

        return collect($achievedLabels);
    }

    /**
     * Check if team member has achieved their target
     */
    public function hasAchievedTarget($month = null, $year = null)
    {
        if ($month && $year) {
            $totalAchieved = $this->achievements()
                ->whereMonth('achievement_date', $month)
                ->whereYear('achievement_date', $year)
                ->sum('achieved_amount');
        } else {
            $totalAchieved = $this->achieved_amount;
        }

        return $totalAchieved >= $this->target_amount;
    }

    /**
     * Get achievement percentage
     */
    public function getAchievementPercentage($month = null, $year = null)
    {
        if ($this->target_amount == 0) {
            return 0;
        }

        if ($month && $year) {
            $totalAchieved = $this->achievements()
                ->whereMonth('achievement_date', $month)
                ->whereYear('achievement_date', $year)
                ->sum('achieved_amount');
        } else {
            $totalAchieved = $this->achieved_amount;
        }

        return ($totalAchieved / $this->target_amount) * 100;
    }

    /**
     * Get formatted off days string
     */
    public function getOffDaysFormattedAttribute()
    {
        if (!$this->off_days || empty($this->off_days)) {
            return 'None';
        }

        return implode(', ', $this->off_days);
    }

    /**
     * Check if a given day is an off day
     */
    public function isOffDay($day)
    {
        if (!$this->off_days || empty($this->off_days)) {
            return false;
        }

        return in_array($day, $this->off_days);
    }
}
