<?php

// app/Models/Contact.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Contact extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'number',
        'service_id',
        'message',
    ];
protected $dates = ['created_at', 'updated_at'];

    public function service()
    {
        return $this->belongsTo(Service::class);
    }
}

