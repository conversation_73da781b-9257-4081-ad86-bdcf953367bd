<?php

namespace App\Helpers;

use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageHelper
{
    /**
     * Process and save an uploaded image
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param string $path
     * @param int $width
     * @param int|null $height
     * @return string The path to the saved image
     */
    public static function saveImage($file, $path, $width = 800, $height = null)
    {
        // Create directory if it doesn't exist
        $directory = public_path($path);
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
        $fullPath = $path . '/' . $filename;

        // Create image manager with driver
        $manager = new ImageManager(new Driver());

        // Process the image
        $img = $manager->read($file->getRealPath());

        // Resize the image
        if ($height) {
            $img->resize($width, $height);
        } else {
            $img->resize($width, null, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }

        // Save the image
        $img->save(public_path($fullPath));

        return $fullPath;
    }

    /**
     * Create a thumbnail from an existing image
     *
     * @param string $imagePath Path to the original image
     * @param string $thumbnailPath Path to save the thumbnail
     * @param int $width Thumbnail width
     * @param int|null $height Thumbnail height (null for auto)
     * @return string|null The path to the thumbnail or null if original image doesn't exist
     */
    public static function createThumbnail($imagePath, $thumbnailPath, $width = 300, $height = null)
    {
        if (!file_exists(public_path($imagePath))) {
            return null;
        }

        // Create directory if it doesn't exist
        $directory = dirname(public_path($thumbnailPath));
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        // Create image manager with driver
        $manager = new ImageManager(new Driver());

        // Process the image
        $img = $manager->read(public_path($imagePath));

        // Resize the image
        if ($height) {
            $img->resize($width, $height);
        } else {
            $img->resize($width, null, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }

        // Save the thumbnail
        $img->save(public_path($thumbnailPath));

        return $thumbnailPath;
    }

    /**
     * Crop and resize an image to fit specific dimensions
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param string $path
     * @param int $width
     * @param int $height
     * @return string The path to the saved image
     */
    public static function cropAndResizeImage($file, $path, $width, $height)
    {
        // Create directory if it doesn't exist
        $directory = public_path($path);
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
        $fullPath = $path . '/' . $filename;

        // Create image manager with driver
        $manager = new ImageManager(new Driver());

        // Process the image
        $img = $manager->read($file->getRealPath());

        // Crop and resize the image
        $img->cover($width, $height);

        // Save the image
        $img->save(public_path($fullPath));

        return $fullPath;
    }

    /**
     * Add a watermark to an image
     *
     * @param string $imagePath Path to the original image
     * @param string $watermarkPath Path to the watermark image
     * @param string $position Position of the watermark (top-left, top-right, bottom-left, bottom-right, center)
     * @return string The path to the watermarked image
     */
    public static function addWatermark($imagePath, $watermarkPath, $position = 'bottom-right')
    {
        // Create image manager with driver
        $manager = new ImageManager(new Driver());

        // Process the image
        $img = $manager->read(public_path($imagePath));
        $watermark = $manager->read(public_path($watermarkPath));

        // Resize watermark if needed (optional)
        $watermark->resize(null, 100, function ($constraint) {
            $constraint->aspectRatio();
        });

        // Calculate position
        $x = 10;
        $y = 10;

        switch ($position) {
            case 'top-left':
                $x = 10;
                $y = 10;
                break;
            case 'top-right':
                $x = $img->width() - $watermark->width() - 10;
                $y = 10;
                break;
            case 'bottom-left':
                $x = 10;
                $y = $img->height() - $watermark->height() - 10;
                break;
            case 'bottom-right':
                $x = $img->width() - $watermark->width() - 10;
                $y = $img->height() - $watermark->height() - 10;
                break;
            case 'center':
                $x = ($img->width() - $watermark->width()) / 2;
                $y = ($img->height() - $watermark->height()) / 2;
                break;
        }

        // Add watermark
        $img->place($watermark, 'top-left', $x, $y);

        // Save the image
        $img->save(public_path($imagePath));

        return $imagePath;
    }
}
