<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class GiveSuperAdminAllPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'super-admin:give-all-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Give super admin role all permissions in the system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔐 Granting Super Admin ALL permissions...');

        // Get all permissions
        $allPermissions = Permission::pluck('name')->toArray();

        if (empty($allPermissions)) {
            $this->warn('No permissions found in the system. Run PermissionSeeder first.');
            return 1;
        }

        // Get or create super admin role
        $superAdminRole = Role::firstOrCreate([
            'name' => 'super_admin',
            'guard_name' => 'web'
        ]);

        // Give super admin all permissions
        $superAdminRole->syncPermissions($allPermissions);

        $this->info("✅ Super Admin has been granted ALL " . count($allPermissions) . " permissions:");

        // Display all permissions
        $this->table(
            ['Permission'],
            array_map(fn($permission) => [$permission], $allPermissions)
        );

        $this->info('🎉 Super Admin now has complete system access!');

        return 0;
    }
}
