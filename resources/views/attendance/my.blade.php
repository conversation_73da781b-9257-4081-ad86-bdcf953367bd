@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h2>My Attendance</h2>
                    @if(!$team)
                        <div class="alert alert-warning">You are not assigned to a team.</div>
                    @else
                        <form id="my-attendance-form" action="{{ route('attendance.my.store') }}" method="POST" class="row g-2 mb-3">
                            @csrf
                            <div class="col-auto">
                                <input type="date" name="date" class="form-control" value="{{ now()->toDateString() }}" required>
                            </div>
                            <div class="col-auto">
                                <select name="status" class="form-select" required>
                                    <option value="present">Present</option>
                                    <option value="absent">Absent</option>
                                    <option value="leave">Leave</option>
                                    <option value="half_day">Half Day</option>
                                </select>
                            </div>
                            <div class="col-auto d-none">
                                <input type="time" name="check_in" class="form-control" placeholder="Check In">
                            </div>
                            <div class="col-auto d-none">
                                <input type="time" name="check_out" class="form-control" placeholder="Check Out">
                            </div>
                            <div class="col-auto ">
                                <input type="text" name="notes" class="form-control" placeholder="Notes">
                            </div>
                            <div class="col-auto">
                                <button class="btn btn-success" type="submit">Save</button>
                            </div>
                            <div class="col-auto">
                                <button class="btn btn-primary" type="button" id="btn-check-in-now">Check In Now</button>
                            </div>
                            <div class="col-auto">
                                <button class="btn btn-danger" type="button" id="btn-check-out-now">Check Out Now</button>
                            </div>
                        </form>

                        <script>
                            document.addEventListener('DOMContentLoaded', function () {
                                const form = document.getElementById('my-attendance-form');
                                const statusEl = form.querySelector('select[name="status"]');
                                const dateEl = form.querySelector('input[name="date"]');
                                const inEl = form.querySelector('input[name="check_in"]');
                                const outEl = form.querySelector('input[name="check_out"]');

                                function todayStr() {
                                    const d = new Date();
                                    const m = (d.getMonth() + 1).toString().padStart(2, '0');
                                    const day = d.getDate().toString().padStart(2, '0');
                                    return `${d.getFullYear()}-${m}-${day}`;
                                }

                                function nowHm() {
                                    const d = new Date();
                                    const h = d.getHours().toString().padStart(2, '0');
                                    const mi = d.getMinutes().toString().padStart(2, '0');
                                    return `${h}:${mi}`;
                                }

                                document.getElementById('btn-check-in-now').addEventListener('click', function () {
                                    statusEl.value = 'present';
                                    dateEl.value = todayStr();
                                    inEl.value = nowHm();
                                    // Clear notes for check-in/out actions
                                    form.querySelector('input[name="notes"]').value = '';
                                    form.submit();
                                });

                                document.getElementById('btn-check-out-now').addEventListener('click', function () {
                                    statusEl.value = 'present';
                                    dateEl.value = todayStr();
                                    // Ensure check-in exists and is not after now
                                    if (!inEl.value) {
                                        inEl.value = nowHm();
                                    }
                                    outEl.value = nowHm();
                                    // Clear notes for check-in/out actions
                                    form.querySelector('input[name="notes"]').value = '';
                                    form.submit();
                                });
                            });
                        </script>

                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Check In</th>
                                    <th>Check Out</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($attendances as $a)
                                    <tr>
                                        <td>{{ \Carbon\Carbon::parse($a['date'])->format('l, d F Y') }}</td>
                                        <td>{{ ucfirst($a->status) }}</td>
                                        <td>
                                            {{ $a->check_in ? \Carbon\Carbon::parse($a->check_in)->format('h:i A') : '-' }}
                                        </td>
                                        <td>
                                            {{ $a->check_out ? \Carbon\Carbon::parse($a->check_out)->format('h:i A') : '-' }}
                                        </td>

                                        <td>{{ $a->notes }}</td>
                                    </tr>
                                @empty
                                    <tr><td colspan="5" class="text-center">No records.</td></tr>
                                @endforelse
                            </tbody>
                        </table>
                        {{ method_exists($attendances, 'links') ? $attendances->links() : '' }}
                    @endif
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection


