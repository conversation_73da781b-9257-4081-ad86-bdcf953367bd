@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h2>Edit Attendance</h2>
                    <form action="{{ route('attendance.update', $attendance->id) }}" method="POST" class="row g-3">
                        @csrf @method('PUT')
                        <div class="col-md-4">
                            <label class="form-label">Team Member</label>
                            <select name="team_id" class="form-select">
                                @foreach($teams as $team)
                                    <option value="{{ $team->id }}" {{ $attendance->team_id == $team->id ? 'selected' : '' }}>{{ $team->user->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date</label>
                            <input type="date" name="date" class="form-control" value="{{ $attendance->date->format('Y-m-d') }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-select">
                                @foreach(['present','absent','leave','half_day'] as $st)
                                    <option value="{{ $st }}" {{ $attendance->status === $st ? 'selected' : '' }}>{{ ucfirst($st) }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Check In</label>
                            <input type="time" name="check_in" class="form-control" value="{{ $attendance->check_in }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Check Out</label>
                            <input type="time" name="check_out" class="form-control" value="{{ $attendance->check_out }}">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Notes</label>
                            <input type="text" name="notes" class="form-control" value="{{ $attendance->notes }}">
                        </div>
                        <div class="col-12">
                            <button class="btn btn-primary" type="submit">Save</button>
                        </div>
                    </form>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection


