@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h2>Attendance (Admin)</h2>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>SL</th>
                                <th>Date</th>
                                <th>User</th>
                                <th>Status</th>
                                <th>Check In</th>
                                <th>Check Out</th>
                                <th>Notes</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($attendances as $a)
                                <tr>
                                    <td>{{ $loop->iteration + ($attendances->currentPage() - 1) * $attendances->perPage() }}</td>
                                    <td>{{ \Carbon\Carbon::parse($a->date)->format('Y-m-d') }}</td>
                                    <td>{{ $a->team->user->name ?? 'N/A' }}</td>
                                    <td>{{ ucfirst($a->status) }}</td>
                                    <td>{{ $a->check_in }}</td>
                                    <td>{{ $a->check_out }}</td>
                                    <td>{{ $a->notes }}</td>
                                    <td>
                                        <a href="{{ route('attendance.edit', $a->id) }}" class="btn btn-sm btn-warning">Edit</a>
                                        <form action="{{ route('attendance.destroy', $a->id) }}" method="POST" style="display:inline-block" onsubmit="return confirm('Are you sure?');">
                                            @csrf @method('DELETE')
                                            <button class="btn btn-sm btn-danger" type="submit">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    {{ $attendances->onEachSide(1)->links('pagination::bootstrap-5') }}
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection


