@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h2>Edit Blog</h2>
                    <form action="{{ route('blogs.update', $blog->id) }}" method="POST" enctype="multipart/form-data"
                        id="blogForm">
                        @csrf
                        @method('PUT')
                        @include('blogs.form')
                        <button type="submit" class="btn btn-primary">Update</button>
                    </form>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing TinyMCE in edit form...');

            const titleInput = document.getElementById('title');
            const slugInput = document.getElementById('slug');
            const metaTitleInput = document.getElementById('meta_title');
            const metaDescInput = document.getElementById('meta_description');

            let userEditedMetaDesc = false;
            let userEditedMetaTitle = false;

            // Detect manual edit in meta description
            metaDescInput.addEventListener('input', function() {
                userEditedMetaDesc = true;
            });

            // Detect manual edit in meta title
            metaTitleInput.addEventListener('input', function() {
                userEditedMetaTitle = true;
            });

            function slugify(text) {
                return text.toString().toLowerCase()
                    .trim()
                    .replace(/\s+/g, '-') // Replace spaces with -
                    .replace(/[^\w\-]+/g, '') // Remove all non-word chars
                    .replace(/\-\-+/g, '-') // Replace multiple - with single -
                    .replace(/^-+/, '') // Trim - from start
                    .replace(/-+$/, ''); // Trim - from end
            }

            tinymce.init({
                selector: '#description',
                height: 300,
                plugins: [
                    'advlist autolink lists link image charmap print preview anchor',
                    'searchreplace visualblocks code fullscreen',
                    'insertdatetime media table paste code help wordcount'
                ],
                toolbar: 'undo redo | formatselect | ' +
                    'bold italic backcolor | alignleft aligncenter ' +
                    'alignright alignjustify | bullist numlist outdent indent | ' +
                    'removeformat | help',
                content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                setup: function(editor) {
                    editor.on('Change KeyUp', function() {
                        if (!userEditedMetaDesc) {
                            const plainText = editor.getContent({
                                format: 'text'
                            }).substring(0, 160);
                            metaDescInput.value = plainText;
                        }
                    });
                }
            });

            // Update slug and meta title on title input, only if meta title not manually edited
            titleInput.addEventListener('input', function() {
                const titleVal = titleInput.value;
                slugInput.value = slugify(titleVal);

                if (!userEditedMetaTitle) {
                    metaTitleInput.value = titleVal;
                }
            });

            // Form submit handler to sync TinyMCE content and validate description
            const form = document.getElementById('blogForm');
            form.addEventListener('submit', function(e) {
                tinymce.triggerSave();

                const description = document.getElementById('description').value.trim();
                if (description === '') {
                    e.preventDefault();
                    alert('The description field is required.');
                    tinymce.get('description').focus();
                }
            });
        });
    </script>
@endpush
