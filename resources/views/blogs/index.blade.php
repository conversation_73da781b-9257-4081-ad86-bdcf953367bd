@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">

                <div class="container-fluid">
                    <h1>Blogs</h1>

                    {{-- Show "Add New Blog" button only if user has "create" permission --}}
                    @if (auth()->user()->hasPermission('create'))
                        <a href="{{ route('blogs.create') }}" class="btn btn-primary mb-3">Add New Blog</a>
                    @endif

                    @if (session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif

                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Title</th>
                                <th>Category</th>
                                <th>SEO Score</th>
                                <th>Picture</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($blogs as $blog)
                                <tr>
                                    <td>{{ $blog->id }}</td>
                                    <td>{{ $blog->title }}</td>
                                    <td>{{ $blog->category->name ?? 'N/A' }}</td>
                                    <td>
                                        @php
                                            $score = 0;
                                            $metaTitleLength = strlen(trim($blog->meta_title ?? ''));
                                            $metaDescLength = strlen(trim($blog->meta_description ?? ''));

                                            if ($metaTitleLength >= 50 && $metaTitleLength <= 60) {
                                                $score += 50;
                                            } elseif ($metaTitleLength >= 40 && $metaTitleLength <= 70) {
                                                $score += 30;
                                            }

                                            if ($metaDescLength >= 120 && $metaDescLength <= 160) {
                                                $score += 50;
                                            } elseif ($metaDescLength >= 100 && $metaDescLength <= 180) {
                                                $score += 30;
                                            }

                                            $score = min($score, 100);

                                            $badgeClass = 'bg-secondary';
                                            $label = 'Very Poor';

                                            if ($score >= 90) {
                                                $badgeClass = 'bg-success';
                                                $label = 'Excellent';
                                            } elseif ($score >= 75) {
                                                $badgeClass = 'bg-success text-dark';
                                                $label = 'Very Good';
                                            } elseif ($score >= 60) {
                                                $badgeClass = 'bg-info text-dark';
                                                $label = 'Good';
                                            } elseif ($score >= 45) {
                                                $badgeClass = 'bg-warning text-dark';
                                                $label = 'Fair';
                                            } elseif ($score >= 30) {
                                                $badgeClass = 'bg-danger';
                                                $label = 'Poor';
                                            } else {
                                                $badgeClass = 'bg-dark';
                                                $label = 'Very Poor';
                                            }
                                        @endphp
                                        <span class="badge {{ $badgeClass }}">{{ $score }}%
                                            ({{ $label }})</span>
                                    </td>
                                    <td>
                                        @if ($blog->image)
                                            <img src="{{ asset($blog->image) }}" width="100" alt="{{ $blog->title }}">
                                        @endif
                                    </td>
                                    <td>
                                        {{-- Edit button only if user has 'edit' permission --}}
                                        @if (auth()->user()->hasPermission('edit'))
                                            <a href="{{ route('blogs.edit', $blog->id) }}"
                                                class="btn btn-warning btn-sm">Edit</a>
                                        @endif

                                        {{-- Delete button only if user has 'delete' permission --}}
                                        @if (auth()->user()->hasPermission('delete'))
                                            <form action="{{ route('blogs.destroy', $blog->id) }}" method="POST"
                                                style="display:inline-block">
                                                @csrf
                                                @method('DELETE')
                                                <button class="btn btn-danger btn-sm"
                                                    onclick="return confirm('Are you sure?')">Delete</button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>

                    {{ $blogs->onEachSide(1)->links('pagination::bootstrap-5') }}

                </div>

            </div>

            @include('layouts.sections.footer.footer')

        </div>

    </div>

    @include('layouts.sections.menu.rightsidebar')
@endsection
