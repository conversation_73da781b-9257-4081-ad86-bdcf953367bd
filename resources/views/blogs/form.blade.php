<div class="row">
    <div class="col-md-6 mb-3">
        <label for="category_id" class="form-label">Category</label>
        <select name="category_id" id="category_id" class="form-control" required>
            <option value="">-- Select Category --</option>
            @foreach ($categories as $category)
                <option value="{{ $category->id }}"
                    {{ old('category_id', $blog->category_id ?? null) == $category->id ? 'selected' : '' }}>
                    {{ $category->name }}
                </option>
            @endforeach
        </select>
    </div>

    <div class="col-md-6 mb-3">
        <label for="slug" class="form-label">Slug</label>
        <input type="text" id="slug" name="slug" class="form-control"
            value="{{ old('slug', $blog->slug ?? '') }}" readonly>
    </div>

</div>

<div class="mb-3">
    <label for="title" class="form-label">Title</label>
    <input type="text" id="title" name="title" class="form-control"
        value="{{ old('title', $blog->title ?? '') }}" required>
</div>

<div class="mb-3">

</div>

<div class="mb-3">
    <label for="image" class="form-label">Picture</label>
    <input type="file" id="image" name="image" class="form-control" accept="image/*"
        @if (!isset($blog)) required @endif>
    @if (isset($blog) && $blog->image)
        <div class="mt-2">
            <img src="{{ asset($blog->image) }}" alt="{{ $blog->title }}" width="100">
        </div>
    @endif
</div>

<div class="mb-3">
    <label for="description" class="form-label">Description</label>
    <textarea id="description" name="description" class="form-control tinymce-editor" rows="5">{{ old('description', $blog->description ?? '') }}</textarea>
</div>

<div class="mb-3">
    <label for="meta_title" class="form-label">Meta Title</label>
    <input type="text" id="meta_title" name="meta_title" class="form-control"
        value="{{ old('meta_title', $blog->meta_title ?? '') }}">
</div>

<div class="mb-3">
    <label for="meta_description" class="form-label">Meta Description</label>
    <input type="text" id="meta_description" name="meta_description" class="form-control "
        value="{{ old('meta_description', $blog->meta_description ?? '') }}">
</div>

<div class="mb-3">
    <label for="meta_keywords" class="form-label">Meta Keywords</label>
    <input type="text" id="meta_keywords" name="meta_keywords" class="form-control"
        value="{{ old('meta_keywords', $blog->meta_keywords ?? '') }}">
</div>
