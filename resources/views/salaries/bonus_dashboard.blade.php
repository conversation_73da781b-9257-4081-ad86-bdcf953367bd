@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Salary & Bonus Dashboard</h4>
                    <a href="{{ route('team-achievements.create') }}" class="btn btn-primary">Add Achievement</a>
                </div>

                @if(isset($error))
                    <div class="alert alert-danger">
                        <strong>Error:</strong> {{ $error }}
                    </div>
                @endif

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">Total Team Members</h5>
                                <h3>{{ $teams->count() }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">Target Achievers</h5>
                                <h3>{{ $teams->filter(function($team) { return $team->hasAchievedTarget(); })->count() }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h5 class="card-title">Total Bonuses Earned</h5>
                                <h3>${{ number_format($teams->sum(function($team) { return $team->achievements()->sum('bonus_earned'); }), 2) }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5 class="card-title">Active Labels</h5>
                                <h3>{{ $labels->where('is_active', true)->count() }}</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Team Performance Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Team Performance Overview</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Team Member</th>
                                        <th>Label</th>
                                        <th>Target Amount</th>
                                        <th>Achieved Amount</th>
                                        <th>Achievement %</th>
                                        <th>Base Salary</th>
                                        <th>Total Bonuses</th>
                                        <th>Total Salary</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($teams as $team)
                                        <tr>
                                            <td>{{ $team->user->name }}</td>
                                            <td>{{ $team->label->name }}</td>
                                            <td>${{ number_format($team->target_amount, 2) }}</td>
                                            <td>${{ number_format($team->achieved_amount, 2) }}</td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar {{ $team->getAchievementPercentage() >= 100 ? 'bg-success' : ($team->getAchievementPercentage() >= 50 ? 'bg-warning' : 'bg-danger') }}"
                                                         style="width: {{ min($team->getAchievementPercentage(), 100) }}%">
                                                        {{ number_format($team->getAchievementPercentage(), 1) }}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>${{ number_format($team->base_salary, 2) }}</td>
                                            <td>${{ number_format($team->calculateCurrentBonus(), 2) }}</td>
                                            <td>${{ number_format($team->base_salary + $team->calculateCurrentBonus(), 2) }}</td>
                                            <td>
                                                @if($team->hasAchievedTarget())
                                                    <span class="badge bg-success">Target Achieved</span>
                                                    <br>
                                                    <small class="text-muted">
                                                        Achieved Labels:
                                                        @foreach($team->getAchievedLabels() as $label)
                                                            <span class="badge bg-info">{{ $label->name }}</span>
                                                        @endforeach
                                                    </small>
                                                @else
                                                    <span class="badge bg-warning">In Progress</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Recent Achievements -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Recent Achievements</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Team Member</th>
                                        <th>Label</th>
                                        <th>Achieved Amount</th>
                                        <th>Bonus Earned</th>
                                        <th>Date</th>
                                        <th>Payment Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($recentAchievements as $achievement)
                                        <tr>
                                            <td>{{ $achievement->team->user->name }}</td>
                                            <td>{{ $achievement->label->name }}</td>
                                            <td>${{ number_format($achievement->achieved_amount, 2) }}</td>
                                            <td>${{ number_format($achievement->bonus_earned, 2) }}</td>
                                            <td>{{ $achievement->achievement_date->format('M d, Y') }}</td>
                                            <td>
                                                <span class="badge {{ $achievement->is_paid ? 'bg-success' : 'bg-warning' }}">
                                                    {{ $achievement->is_paid ? 'Paid' : 'Unpaid' }}
                                                </span>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="6">No recent achievements found.</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
