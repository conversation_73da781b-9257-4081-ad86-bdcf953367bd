@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <h1>Target vs Achievement</h1>

                @if (session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                @if (session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif

                @if(!$team)
                    <div class="alert alert-warning">
                        <i class="ri-alert-line me-2"></i>
                        No team assigned to you. Please contact your administrator.
                    </div>
                @else
                    <!-- Current Month Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="ri-target-line me-2 text-primary"></i>
                                        Current Month ({{ \Carbon\Carbon::create($currentYear, $currentMonth, 1)->format('F Y') }})
                                    </h5>
                                    <div class="row mt-3">
                                        <div class="col-4">
                                            <div class="text-center">
                                                <h3 class="text-primary mb-1">${{ number_format($currentTarget, 2) }}</h3>
                                                <small class="text-muted">Target</small>
                                            </div>
                                        </div>

                                        <div class="col-4">
                                            <div class="text-center">
                                                <h3 class="text-info mb-1">${{ number_format($currentAchievedTarget, 2) }}</h3>
                                                <small class="text-muted">Achieved Target</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <div class="d-flex justify-content-between mb-1">
                                            <span>Progress</span>
                                            <span>{{ $currentPercent }}%</span>
                                        </div>
                                        <div class="progress" style="height: 25px;">
                                            <div class="progress-bar
                                                @if($currentPercent >= 100) bg-success
                                                @elseif($currentPercent >= 80) bg-primary
                                                @elseif($currentPercent >= 60) bg-warning
                                                @else bg-danger @endif"
                                                role="progressbar"
                                                style="width: {{ min(100, $currentPercent) }}%"
                                                aria-valuenow="{{ $currentPercent }}"
                                                aria-valuemin="0"
                                                aria-valuemax="100">
                                                {{ $currentPercent }}%
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="ri-bar-chart-line me-2 text-info"></i>
                                        Performance Summary
                                    </h5>
                                    <div class="mt-3">
                                        @php
                                            $exceeded = $targetData->where('status', 'exceeded')->count();
                                            $good = $targetData->where('status', 'good')->count();
                                            $average = $targetData->where('status', 'average')->count();
                                            $poor = $targetData->where('status', 'poor')->count();
                                        @endphp
                                        <div class="row text-center">
                                            <div class="col-3">
                                                <div class="border rounded p-2">
                                                    <h4 class="text-success mb-1">{{ $exceeded }}</h4>
                                                    <small class="text-muted">Exceeded</small>
                                                </div>
                                            </div>
                                            <div class="col-3">
                                                <div class="border rounded p-2">
                                                    <h4 class="text-primary mb-1">{{ $good }}</h4>
                                                    <small class="text-muted">Good</small>
                                                </div>
                                            </div>
                                            <div class="col-3">
                                                <div class="border rounded p-2">
                                                    <h4 class="text-warning mb-1">{{ $average }}</h4>
                                                    <small class="text-muted">Average</small>
                                                </div>
                                            </div>
                                            <div class="col-3">
                                                <div class="border rounded p-2">
                                                    <h4 class="text-danger mb-1">{{ $poor }}</h4>
                                                    <small class="text-muted">Poor</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Performance Chart -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="ri-line-chart-line me-2"></i>
                                Last 12 Months Performance
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Month</th>
                                            <th>Target</th>
                                            <th>Attendance Bonus</th>
                                            <th>Achieved Target</th>
                                            <th>Achievement %</th>
                                            <th>Status</th>
                                            <th>Progress Bar</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($targetData as $data)
                                            <tr>
                                                <td>
                                                    <strong>{{ $data['month'] }}</strong>
                                                </td>
                                                <td>${{ number_format($data['target'], 2) }}</td>
                                                <td>${{ number_format($data['attendance_bonus'] ?? 0, 2) }}</td>
                                                <td>${{ number_format($data['achieved_target'], 2) }}</td>
                                                <td>
                                                    <span class="fw-bold
                                                        @if($data['achievement_percent'] >= 100) text-success
                                                        @elseif($data['achievement_percent'] >= 80) text-primary
                                                        @elseif($data['achievement_percent'] >= 60) text-warning
                                                        @else text-danger @endif">
                                                        {{ $data['achievement_percent'] }}%
                                                    </span>
                                                </td>
                                                <td>
                                                    @if($data['status'] === 'exceeded')
                                                        <span class="badge bg-success">
                                                            <i class="ri-check-double-line me-1"></i>Exceeded
                                                        </span>
                                                    @elseif($data['status'] === 'good')
                                                        <span class="badge bg-primary">
                                                            <i class="ri-check-line me-1"></i>Good
                                                        </span>
                                                    @elseif($data['status'] === 'average')
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="ri-time-line me-1"></i>Average
                                                        </span>
                                                    @else
                                                        <span class="badge bg-danger">
                                                            <i class="ri-close-line me-1"></i>Poor
                                                        </span>
                                                    @endif
                                                </td>
                                                <td style="width: 200px;">
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar
                                                            @if($data['achievement_percent'] >= 100) bg-success
                                                            @elseif($data['achievement_percent'] >= 80) bg-primary
                                                            @elseif($data['achievement_percent'] >= 60) bg-warning
                                                            @else bg-danger @endif"
                                                            role="progressbar"
                                                            style="width: {{ min(100, $data['achievement_percent']) }}%"
                                                            aria-valuenow="{{ $data['achievement_percent'] }}"
                                                            aria-valuemin="0"
                                                            aria-valuemax="100">
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Insights -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card shadow-sm">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="ri-lightbulb-line me-2 text-warning"></i>
                                        Performance Insights
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @php
                                        $bestMonth = $targetData->sortByDesc('achievement_percent')->first();
                                        $worstMonth = $targetData->sortBy('achievement_percent')->first();
                                        $avgAchievement = $targetData->avg('achievement_percent');
                                    @endphp
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="ri-trophy-line text-success me-2"></i>
                                            <strong>Best Month:</strong> {{ $bestMonth['month'] }} ({{ $bestMonth['achievement_percent'] }}%)
                                        </li>
                                        <li class="mb-2">
                                            <i class="ri-arrow-down-line text-danger me-2"></i>
                                            <strong>Challenging Month:</strong> {{ $worstMonth['month'] }} ({{ $worstMonth['achievement_percent'] }}%)
                                        </li>
                                        <li class="mb-2">
                                            <i class="ri-bar-chart-line text-info me-2"></i>
                                            <strong>Average Achievement:</strong> {{ round($avgAchievement, 1) }}%
                                        </li>
                                        @if($currentPercent < 80)
                                            <li class="mb-2">
                                                <i class="ri-alert-line text-warning me-2"></i>
                                                <strong>Current Month:</strong> Need {{ round(80 - $currentPercent, 1) }}% more to reach good performance
                                            </li>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card shadow-sm">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="ri-target-line me-2 text-primary"></i>
                                        Target Information
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-primary mb-1">${{ number_format($team->target ?? 0, 2) }}</h4>
                                                <small class="text-muted">Monthly Target</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-info mb-1">{{ $team->designation->name ?? 'N/A' }}</h4>
                                                <small class="text-muted">Designation</small>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-success mb-1">{{ $team->label->name ?? 'N/A' }}</h4>
                                                <small class="text-muted">Label</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-warning mb-1">{{ $team->label->bonus_percent ?? 0 }}%</h4>
                                                <small class="text-muted">Bonus %</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>
@include('layouts.sections.menu.rightsidebar')
@endsection
