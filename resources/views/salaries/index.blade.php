@extends('layouts.master')

@section('maincontent')
    <!-- <body data-layout="horizontal" data-topbar="dark"> -->
    <!-- Begin page -->
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        <!-- ========== Left Sidebar Start ========== -->
        @include('layouts.sections.menu.sidebar')
        <!-- Left Sidebar End -->
        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">
            <!-- start Page-content -->
            <div class="page-content">

                <div class="container-fluid">
                    <h2>Salaries</h2>
                    <div class="d-flex gap-2 mb-3">
                        <a href="{{ route('salaries.create') }}" class="btn btn-primary">Create Salary</a>
                    </div>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Sl</th>

                                <th>User</th>
                                <th>Total Paid</th>
                                <th>Amount</th>
                                <th>Total Bonus</th>
                                <th>Attendance Bonus</th>
                                <th>Expense Deduction</th>
                                <th>Target</th>
                                <th>Achieved Target</th>
                                <th>Created At</th>
                                <th>Status</th>
                                <th>Actions</th>
                                <th>Month</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($salaries as $index => $salary)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>{{ $salary->user_name }}</td>
                                    <td>{{ number_format($salary->total_paid, 2) }}</td>
                                    <td>{{ number_format($salary->amount, 2) }}</td>
                                    <td>{{ number_format($salary->bonus, 2) }}</td>
                                    <td>{{ number_format($salary->attendance_bonus ?? 0, 2) }}</td>
                                    <td>{{ number_format($salary->expense_deduction ?? 0, 2) }}</td>
                                    <td>{{ number_format($salary->target, 2) }}</td>
                                    <td>{{ number_format($salary->achieved_target, 2) }}</td>
                                    <td>{{ \Carbon\Carbon::parse($salary->created_at)->format('Y-m-d') }}</td>
                                    <td>
                                        {{-- Optionally show edit if existingSalary exists --}}
                                        @php
                                            $teamId = $salary->team->id;
                                            $existing = \App\Models\Salary::where('team_id', $teamId)
                                                ->where('year', now()->year)
                                                ->where('month', now()->month)
                                                ->first();
                                        @endphp
                                        @if ($existing)
                                            <span class="badge bg-success">Paid</span>
                                        @else
                                            <span class="badge bg-secondary">Unpaid</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if (isset($existing) && $existing)
                                            <a href="{{ route('salaries.edit', $existing->id) }}"
                                                class="btn btn-sm btn-warning">Edit</a>
                                            <form action="{{ route('salaries.destroy', $existing->id) }}" method="POST"
                                                style="display:inline-block;" onsubmit="return confirm('Are you sure?');">
                                                @csrf
                                                @method('DELETE')
                                                <button class="btn btn-sm btn-danger" type="submit">Delete</button>
                                            </form>
                                        @else
                                            <a href="{{ route('salaries.create') }}" class="btn btn-sm btn-primary">Add</a>
                                        @endif
                                    </td>
                                    <td>{{ isset($periodYear, $periodMonth) ? \Carbon\Carbon::create($periodYear, $periodMonth)->format('F Y') : \Carbon\Carbon::parse($salary->created_at)->format('F Y') }}
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="13" class="text-center">No salary data found.</td>
                                </tr>
                            @endforelse
                        </tbody>

                    </table>
                    {{-- {{ $salaries->links() }} --}}


                </div>

            </div>

            <!-- End Page-content -->
            @include('layouts.sections.footer.footer')

        </div>
        <!-- end main content-->

    </div>
    <!-- END layout-wrapper -->

    @include('layouts.sections.menu.rightsidebar')
@endsection
