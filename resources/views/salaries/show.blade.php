@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Salary Details</h4>
                    <div>
                        <a href="{{ route('salaries.edit', $salary) }}" class="btn btn-warning">Edit</a>
                        <a href="{{ route('salaries.index') }}" class="btn btn-secondary">Back to List</a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Salary Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Team Member:</strong> {{ $salary->team->user->name }}</p>
                                        <p><strong>Designation:</strong> {{ $salary->team->designation->name ?? 'N/A' }}</p>
                                        <p><strong>Label:</strong> {{ $salary->team->label->name ?? 'N/A' }}</p>
                                        <p><strong>Base Salary:</strong> ${{ number_format($salary->team->base_salary, 2) }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Target Amount:</strong> ${{ number_format($salary->team->target_amount, 2) }}</p>
                                        <p><strong>Achieved Amount:</strong> ${{ number_format($salary->team->achieved_amount, 2) }}</p>
                                        <p><strong>Salary Month:</strong> {{ $salary->month }}/{{ $salary->year }}</p>
                                        <p><strong>Status:</strong>
                                            <span class="badge {{ $salary->status === 'paid' ? 'bg-success' : 'bg-warning' }}">
                                                {{ ucfirst($salary->status) }}
                                            </span>
                                        </p>
                                    </div>
                                </div>

                                @if($salary->notes)
                                    <div class="mt-3">
                                        <strong>Notes:</strong>
                                        <p class="mt-2">{{ $salary->notes }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Salary Breakdown</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Base Salary:</span>
                                    <span>${{ number_format($salary->team->base_salary, 2) }}</span>
                                </div>

                                @if($salary->team->achievements()->count() > 0)
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Total Bonuses:</span>
                                        <span>${{ number_format($salary->team->calculateCurrentBonus(), 2) }}</span>
                                    </div>

                                    <hr>

                                    <div class="d-flex justify-content-between mb-2">
                                        <span><strong>Total Salary:</strong></span>
                                        <span><strong>${{ number_format($salary->team->base_salary + $salary->team->calculateCurrentBonus(), 2) }}</strong></span>
                                    </div>
                                @else
                                    <div class="d-flex justify-content-between mb-2">
                                        <span><strong>Total Salary:</strong></span>
                                        <span><strong>${{ number_format($salary->team->base_salary, 2) }}</strong></span>
                                    </div>
                                @endif

                                <div class="mt-3">
                                    <strong>Achievement Status:</strong>
                                    @if($salary->team->hasAchievedTarget())
                                        <div class="alert alert-success mt-2">
                                            <i class="fas fa-check-circle"></i> Target Achieved!
                                            <br>
                                            <small>Achievement: {{ number_format($salary->team->getAchievementPercentage(), 1) }}%</small>
                                        </div>
                                    @else
                                        <div class="alert alert-warning mt-2">
                                            <i class="fas fa-clock"></i> Target Not Yet Achieved
                                            <br>
                                            <small>Achievement: {{ number_format($salary->team->getAchievementPercentage(), 1) }}%</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        @if($salary->team->getAchievedLabels()->count() > 0)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Achieved Labels</h5>
                                </div>
                                <div class="card-body">
                                    @foreach($salary->team->getAchievedLabels() as $label)
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="badge bg-info">{{ $label->name }}</span>
                                            <span>${{ number_format($label->bonus_amount, 2) }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
