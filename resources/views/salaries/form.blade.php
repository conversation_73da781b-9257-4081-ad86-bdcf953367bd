@if(!isset($salary) || !$salary->id)
{{-- Team Member Dropdown (only for create) --}}
<div class="mb-3">
    <label for="team_id" class="form-label">Team Member</label>
    <select name="team_id" id="team_id" class="form-select" required>
        <option value="">-- Select Team Member --</option>
        @foreach($teams as $team)
            <option value="{{ $team->id }}"
                {{ old('team_id', $salary->team_id ?? '') == $team->id ? 'selected' : '' }}>
                {{ $team->user->name }}
            </option>
        @endforeach
    </select>
    @error('team_id') <small class="text-danger">{{ $message }}</small> @enderror
</div>

{{-- Auto-filled Amount (only for create) --}}
<div class="mb-3">
    <label for="amount" class="form-label">Amount (auto-filled)</label>
    <input type="number" name="amount" id="amount" class="form-control" step="0.01"
           value="{{ old('amount', $salary->amount ?? '') }}" readonly>
    @error('amount') <small class="text-danger">{{ $message }}</small> @enderror
</div>
@else
{{-- Read-only fields for edit --}}
<div class="mb-3">
    <label class="form-label">Team Member</label>
    <input type="text" class="form-control" value="{{ $salary->team->user->name ?? 'N/A' }}" readonly>
</div>

{{-- Hidden field for team_id in edit mode --}}
<input type="hidden" name="team_id" value="{{ $salary->team_id }}">

@endif



{{-- Extra Bonus --}}
<div class="mb-3">
    <label for="bonus" class="form-label">Extra Bonus (Additional to Label Bonus)</label>
    <input type="number" name="bonus" class="form-control" step="0.01" min="0"
           value="{{ old('bonus', isset($salary) && $salary->id ? ($salary->bonus - (($salary->achieved_target * ($salary->team->label->bonus_percent ?? 0)) / 100)) : ($salary->bonus ?? '')) }}"
           placeholder="Enter extra bonus amount">
    <small class="text-muted">
        @if(isset($salary) && $salary->id)
            Current extra bonus: ${{ number_format($salary->bonus - (($salary->achieved_target * ($salary->team->label->bonus_percent ?? 0)) / 100), 2) }}
        @else
            This bonus will be added to the automatic label bonus calculated from achieved target
        @endif
    </small>
    @error('bonus') <small class="text-danger">{{ $message }}</small> @enderror
</div>

{{-- Expense Deduction (Read-only) --}}
<div class="mb-3">
    <label class="form-label">Expense Deduction (Auto-calculated)</label>
    <input type="text" class="form-control"
           value="${{ number_format($salary->expense_deduction ?? 0, 2) }}" readonly>
    <small class="text-muted">Automatically calculated from approved expenses for this month</small>
</div>
@push('scripts')
<script>
document.getElementById('team_id').addEventListener('change', function () {
    const teamId = this.value;

    if (!teamId) return;

    fetch(`/get-salary-components/${teamId}`)
        .then(res => res.json())
        .then(data => {
            document.getElementById('amount').value = data.total.toFixed(2);
        })
        .catch(err => console.error('Salary component fetch error', err));
});
</script>
@endpush
