@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h2>Payslip - {{ $team->user->name }} - {{ \Carbon\Carbon::create($year, $month, 1)->format('F Y') }}</h2>

                    @if(!$summary)
                        <div class="alert alert-warning">No summary salary record for this month.</div>
                    @else
                        <div class="card mb-4">
                            <div class="card-body">
                                <div><strong>Base/Performance Amount:</strong> {{ number_format($summary->amount, 2) }}</div>

                                <div><strong>Total Bonus:</strong> {{ number_format($summary->bonus, 2) }}</div>
                                <div><strong>Overtime Bonus:</strong> {{ number_format($summary->attendance_bonus ?? 0, 2) }}</div>
                                <div><strong>Expense Deduction:</strong> {{ number_format($summary->expense_deduction ?? 0, 2) }}</div>
                                <div><strong>Total Paid:</strong> {{ number_format($summary->amount + $summary->bonus + ($summary->attendance_bonus ?? 0) - ($summary->expense_deduction ?? 0), 2) }}</div>
                                <div><strong>Achieved vs Target:</strong> {{ number_format($summary->achieved_target, 2) }}</div>
                            </div>
                        </div>
                    @endif

                    <h4>Monthly Summary</h4>
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Salary Components</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Base/Performance Amount:</strong><br>
                                ${{ number_format($summary->amount - ($summary->bonus ?? 0) - ($summary->attendance_bonus ?? 0), 2) }}
                                <small class="text-muted d-block">Performance-based salary</small>
                            </div>
                            <div class="col-md-3">
                                <strong>Label Bonus:</strong><br>
                                ${{ number_format(($summary->achieved_target * ($team->label->bonus_percent ?? 0)) / 100, 2) }}
                                <small class="text-muted d-block">Automatic bonus ({{ $team->label->bonus_percent ?? 0 }}% of achieved target)</small>
                            </div>
                            <div class="col-md-3">
                                <strong>Extra Bonus:</strong><br>
                                ${{ number_format(($summary->bonus ?? 0) - (($summary->achieved_target * ($team->label->bonus_percent ?? 0)) / 100), 2) }}
                                <small class="text-muted d-block">Additional discretionary bonus</small>
                            </div>
                            <div class="col-md-3">
                                <strong>Expense Deduction:</strong><br>
                                ${{ number_format($summary->expense_deduction ?? 0, 2) }}
                                <small class="text-muted d-block">Approved expenses deducted</small>
                            </div>
                            <div class="col-md-3">
                                <strong>Attendance Bonus:</strong><br>
                                ${{ number_format($summary->attendance_bonus ?? 0, 2) }}
                                <small class="text-muted d-block">Overtime bonus</small>
                            </div>
                            <div class="col-md-3">
                                <strong>Expense Deduction:</strong><br>
                                ${{ number_format($summary->expense_deduction ?? 0, 2) }}
                                <small class="text-muted d-block">Approved expenses</small>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <h6>Performance Summary</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>Monthly Target:</strong> ${{ number_format($team->target ?? 0, 2) }}
                                </div>
                                <div class="col-md-4">
                                    <strong>Achieved Target:</strong> ${{ number_format($summary->achieved_target ?? 0, 2) }}
                                </div>
                                <div class="col-md-4">
                                    <strong>Achievement Ratio:</strong> {{ number_format((($summary->achieved_target ?? 0) / max(1, $team->target ?? 1)) * 100, 1) }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection


