@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h2>My Payslips</h2>
                    @if(!$team)
                        <div class="alert alert-warning">You are not assigned to a team.</div>
                    @else
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Period</th>
                                    <th>Amount</th>
                                    <th>Bonus</th>
                                    <th>Total</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($payslips as $p)
                                    <tr>
                                        <td>{{ \Carbon\Carbon::create($p->year, $p->month, 1)->format('F Y') }}</td>
                                        <td>{{ number_format($p->amount, 2) }}</td>
                                        <td>{{ number_format($p->bonus, 2) }}</td>
                                        <td>{{ number_format($p->amount + $p->bonus, 2) }}</td>
                                        <td><a class="btn btn-sm btn-primary" href="{{ route('salaries.my.show', [$p->year, $p->month]) }}">View</a></td>
                                    </tr>
                                @empty
                                    <tr><td colspan="5" class="text-center">No payslips yet.</td></tr>
                                @endforelse
                            </tbody>
                        </table>
                    @endif
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection


