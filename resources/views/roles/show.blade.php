@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Role Details: {{ $role->name }}</h4>
                    <div class="d-flex gap-2">
                        <a href="{{ route('roles.index') }}" class="btn btn-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Roles
                        </a>
                        <a href="{{ route('roles.edit', $role) }}" class="btn btn-primary">
                            <i class="ri-edit-line me-1"></i>Edit Role
                        </a>
                    </div>
                </div>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <div class="row">
                    <div class="col-xl-8">
                        <!-- Role Information -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Role Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Role Name:</label>
                                            <div class="fw-bold fs-5">
                                                <span class="badge bg-primary fs-6">{{ $role->name }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Guard:</label>
                                            <div>
                                                <span class="badge bg-secondary">{{ $role->guard_name }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Created:</label>
                                            <div class="small text-muted">{{ $role->created_at->format('M d, Y H:i') }}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Last Updated:</label>
                                            <div class="small text-muted">{{ $role->updated_at->format('M d, Y H:i') }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Role Permissions -->
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">Assigned Permissions ({{ $role->permissions->count() }})</h5>
                                <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#assignPermissionModal">
                                    <i class="ri-links-line me-1"></i>Assign Permissions
                                </button>
                            </div>
                            <div class="card-body">
                                @if($role->permissions->count() > 0)
                                    @php
                                        $permissionsByGroup = $role->permissions->groupBy(function($permission) {
                                            $parts = explode(' ', $permission->name);
                                            return $parts[0] ?? 'other';
                                        });
                                    @endphp
                                    @foreach($permissionsByGroup as $group => $groupPermissions)
                                        <div class="mb-3">
                                            <h6 class="text-primary">{{ ucfirst($group) }} Permissions</h6>
                                            <div class="d-flex flex-wrap gap-2">
                                                @foreach($groupPermissions as $permission)
                                                    <span class="badge bg-success fs-6">
                                                        {{ ucfirst(str_replace('_', ' ', $permission->name)) }}
                                                    </span>
                                                @endforeach
                                            </div>
                                            <hr>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="ri-shield-line fs-1"></i>
                                            <p class="mt-2">No permissions assigned to this role</p>
                                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#assignPermissionModal">
                                                <i class="ri-links-line me-1"></i>Assign Permissions
                                            </button>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Role Users -->
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">Assigned Users ({{ $role->users()->count() }})</h5>
                                <button class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#assignUserModal">
                                    <i class="ri-user-add-line me-1"></i>Assign Users
                                </button>
                            </div>
                            <div class="card-body">
                                @if($role->users()->count() > 0)
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>ID</th>
                                                    <th>User</th>
                                                    <th>Email</th>
                                                    <th>Assigned Date</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($role->users as $user)
                                                    <tr>
                                                        <td>{{ $user->id }}</td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                @if($user->picture)
                                                                    <img src="{{ asset($user->picture) }}" alt="User Picture" width="32" height="32"
                                                                         style="border-radius: 50%; object-fit: cover; margin-right: 8px;">
                                                                @else
                                                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2"
                                                                         style="width: 32px; height: 32px;">
                                                                        <i class="ri-user-line text-white"></i>
                                                                    </div>
                                                                @endif
                                                                <div>
                                                                    <div class="fw-bold">{{ $user->name }}</div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>{{ $user->email }}</td>
                                                        <td>
                                                            <span class="text-muted small">
                                                                {{ $user->pivot->created_at ? $user->pivot->created_at->format('M d, Y') : 'N/A' }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <form action="{{ route('roles.remove-from-user', $role) }}" method="POST" class="d-inline">
                                                                @csrf
                                                                <input type="hidden" name="user_id" value="{{ $user->id }}">
                                                                <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                        onclick="return confirm('Are you sure you want to remove this user from the role?')"
                                                                        title="Remove from Role">
                                                                    <i class="ri-user-unfollow-line"></i>
                                                                </button>
                                                            </form>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <div class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="ri-user-line fs-1"></i>
                                            <p class="mt-2">No users assigned to this role</p>
                                            <button class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#assignUserModal">
                                                <i class="ri-user-add-line me-1"></i>Assign Users
                                            </button>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4">
                        <!-- Quick Actions -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="{{ route('roles.edit', $role) }}" class="btn btn-primary">
                                        <i class="ri-edit-line me-1"></i>Edit Role
                                    </a>
                                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#assignPermissionModal">
                                        <i class="ri-links-line me-1"></i>Assign Permissions
                                    </button>
                                    <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#assignUserModal">
                                        <i class="ri-user-add-line me-1"></i>Assign Users
                                    </button>
                                    <a href="{{ route('roles.index') }}" class="btn btn-secondary">
                                        <i class="ri-arrow-left-line me-1"></i>Back to Roles
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Role Statistics -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Role Statistics</h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h4 class="text-primary mb-1">{{ $role->permissions->count() }}</h4>
                                            <small class="text-muted">Permissions</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-info mb-1">{{ $role->users()->count() }}</h4>
                                        <small class="text-muted">Users</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Permission Summary -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Permission Summary</h5>
                            </div>
                            <div class="card-body">
                                @php
                                    $permissionTypes = [
                                        'manage' => $role->permissions->filter(fn($p) => str_contains($p->name, 'manage'))->count(),
                                        'create' => $role->permissions->filter(fn($p) => str_contains($p->name, 'create'))->count(),
                                        'edit' => $role->permissions->filter(fn($p) => str_contains($p->name, 'edit'))->count(),
                                        'delete' => $role->permissions->filter(fn($p) => str_contains($p->name, 'delete'))->count(),
                                        'show' => $role->permissions->filter(fn($p) => str_contains($p->name, 'show'))->count(),
                                    ];
                                @endphp

                                @foreach($permissionTypes as $type => $count)
                                    @if($count > 0)
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="text-capitalize">{{ $type }} permissions:</span>
                                            <span class="badge bg-primary">{{ $count }}</span>
                                        </div>
                                    @endif
                                @endforeach

                                @if(array_sum($permissionTypes) == 0)
                                    <div class="text-center text-muted">
                                        <i class="ri-shield-line fs-3"></i>
                                        <p class="mt-2 small">No permissions assigned</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')

<!-- Assign Permission Modal -->
<div class="modal fade" id="assignPermissionModal" tabindex="-1" aria-labelledby="assignPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignPermissionModalLabel">Assign Permissions to Role</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('roles.sync-permissions', $role) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        @foreach($permissions as $permission)
                            <div class="col-md-6 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           name="permissions[]" value="{{ $permission->name }}"
                                           id="assign_permission_{{ $permission->id }}"
                                           {{ $role->hasPermissionTo($permission->name) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="assign_permission_{{ $permission->id }}">
                                        {{ ucfirst(str_replace('_', ' ', $permission->name)) }}
                                    </label>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign Permissions</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Assign User Modal -->
<div class="modal fade" id="assignUserModal" tabindex="-1" aria-labelledby="assignUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignUserModalLabel">Assign Users to Role</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('roles.assign-to-user', $role) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="user_id" class="form-label">Select User</label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            <option value="">Choose a user...</option>
                            @foreach($users as $user)
                                @if(!$user->hasRole($role->name))
                                    <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign User</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
