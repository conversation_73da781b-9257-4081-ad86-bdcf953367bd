@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Edit Role: {{ $role->name }}</h4>
                    <div class="d-flex gap-2">
                        <a href="{{ route('roles.index') }}" class="btn btn-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Roles
                        </a>
                        <a href="{{ route('roles.show', $role) }}" class="btn btn-info">
                            <i class="ri-eye-line me-1"></i>View Role
                        </a>
                    </div>
                </div>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <div class="row">
                    <div class="col-xl-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Role Details</h5>
                            </div>
                            <div class="card-body">
                                <form action="{{ route('roles.update', $role) }}" method="POST">
                                    @csrf
                                    @method('PUT')

                                    <div class="mb-3">
                                        <label for="name" class="form-label">Role Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror"
                                               id="name" name="name" value="{{ old('name', $role->name) }}"
                                               placeholder="e.g., manager, editor, viewer" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">Use descriptive names like "manager", "editor", "viewer", etc.</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="guard_name" class="form-label">Guard Name <span class="text-danger">*</span></label>
                                        <select class="form-select @error('guard_name') is-invalid @enderror"
                                                id="guard_name" name="guard_name" required>
                                            <option value="">Select Guard</option>
                                            <option value="web" {{ old('guard_name', $role->guard_name) == 'web' ? 'selected' : '' }}>Web</option>
                                            <option value="api" {{ old('guard_name', $role->guard_name) == 'api' ? 'selected' : '' }}>API</option>
                                        </select>
                                        @error('guard_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Legacy Permission Levels -->
                                    <div class="mb-4">
                                        <label class="form-label">Legacy Permission Levels</label>
                                        <div class="row">
                                            @php
                                                $selectedPermissions = old('permissions', $role->permissions->pluck('name')->toArray());
                                                $hasRead = false;
                                                $hasWrite = false;
                                                $hasDelete = false;
                                                $hasFull = false;
                                                $hasReadWrite = false;

                                                foreach($selectedPermissions as $perm) {
                                                    if(str_contains($perm, 'show') || str_contains($perm, 'view')) $hasRead = true;
                                                    if(str_contains($perm, 'create') || str_contains($perm, 'edit')) $hasWrite = true;
                                                    if(str_contains($perm, 'delete')) $hasDelete = true;
                                                    if(str_contains($perm, 'manage')) $hasFull = true;
                                                }

                                                if($hasRead && $hasWrite) $hasReadWrite = true;
                                            @endphp
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" id="legacy_full" name="legacy_permissions[]" type="checkbox" value="full" {{ $hasFull ? 'checked' : '' }}>
                                                    <label for="legacy_full" class="form-check-label">Full</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" id="legacy_read" name="legacy_permissions[]" type="checkbox" value="read" {{ $hasRead ? 'checked' : '' }}>
                                                    <label for="legacy_read" class="form-check-label">Read</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" id="legacy_write" name="legacy_permissions[]" type="checkbox" value="write" {{ $hasWrite ? 'checked' : '' }}>
                                                    <label for="legacy_write" class="form-check-label">Write</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" id="legacy_read_write" name="legacy_permissions[]" type="checkbox" value="read_write" {{ $hasReadWrite ? 'checked' : '' }}>
                                                    <label for="legacy_read_write" class="form-check-label">Read Write</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" id="legacy_delete" name="legacy_permissions[]" type="checkbox" value="delete" {{ $hasDelete ? 'checked' : '' }}>
                                                    <label for="legacy_delete" class="form-check-label">Delete</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" id="legacy_none" name="legacy_permissions[]" type="checkbox" value="none" {{ count($selectedPermissions) == 0 ? 'checked' : '' }}>
                                                    <label for="legacy_none" class="form-check-label">None</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Granular Permissions -->
                                    <div class="mb-3">
                                        <label class="form-label">Granular Permissions</label>
                                        <div class="row">
                                            @php
                                                $permissionsByGroup = $permissions->groupBy(function($permission) {
                                                    $parts = explode(' ', $permission->name);
                                                    return $parts[0] ?? 'other';
                                                });
                                            @endphp
                                            @foreach($permissionsByGroup as $group => $groupPermissions)
                                                <div class="col-12 mb-3">
                                                    <h6 class="text-primary">{{ ucfirst($group) }} Permissions</h6>
                                                    <div class="row">
                                                        @foreach($groupPermissions as $permission)
                                                            <div class="col-md-6 col-lg-4 mb-2">
                                                                <div class="form-check">
                                                                    <input class="form-check-input" type="checkbox"
                                                                           name="permissions[]" value="{{ $permission->name }}"
                                                                           id="permission_{{ $permission->id }}"
                                                                           {{ in_array($permission->name, $selectedPermissions) ? 'checked' : '' }}>
                                                                    <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                                        {{ ucfirst(str_replace('_', ' ', $permission->name)) }}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                    <hr>
                                                </div>
                                            @endforeach
                                        </div>
                                        @error('permissions')
                                            <div class="text-danger small">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ri-save-line me-1"></i>Update Role
                                        </button>
                                        <a href="{{ route('roles.index') }}" class="btn btn-secondary">
                                            <i class="ri-arrow-left-line me-1"></i>Back to Roles
                                        </a>
                                        <a href="{{ route('roles.show', $role) }}" class="btn btn-info">
                                            <i class="ri-eye-line me-1"></i>View Role
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Current Role Info</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Role Name:</label>
                                    <div class="fw-bold">{{ $role->name }}</div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted">Guard:</label>
                                    <div>
                                        <span class="badge bg-secondary">{{ $role->guard_name }}</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted">Current Permissions ({{ $role->permissions->count() }}):</label>
                                    <div class="d-flex flex-wrap gap-1">
                                        @forelse($role->permissions as $permission)
                                            <span class="badge bg-success">{{ $permission->name }}</span>
                                        @empty
                                            <span class="text-muted small">No permissions assigned</span>
                                        @endforelse
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted">Assigned Users ({{ $role->users()->count() }}):</label>
                                    <div class="d-flex flex-wrap gap-1">
                                        @forelse($role->users as $user)
                                            <span class="badge bg-info">{{ $user->name }}</span>
                                        @empty
                                            <span class="text-muted small">No users assigned</span>
                                        @endforelse
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted">Created:</label>
                                    <div class="small text-muted">{{ $role->created_at->format('M d, Y H:i') }}</div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted">Last Updated:</label>
                                    <div class="small text-muted">{{ $role->updated_at->format('M d, Y H:i') }}</div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Role Guidelines</h5>
                            </div>
                            <div class="card-body">
                                <h6>Common Role Types:</h6>
                                <ul class="list-unstyled">
                                    <li><span class="badge bg-primary me-1">admin</span> Full system access</li>
                                    <li><span class="badge bg-primary me-1">manager</span> Management level access</li>
                                    <li><span class="badge bg-primary me-1">editor</span> Content editing access</li>
                                    <li><span class="badge bg-primary me-1">viewer</span> Read-only access</li>
                                    <li><span class="badge bg-primary me-1">user</span> Basic user access</li>
                                </ul>

                                <h6 class="mt-3">Permission Groups:</h6>
                                <ul class="list-unstyled">
                                    <li>• <strong>manage</strong> - Full CRUD operations</li>
                                    <li>• <strong>create</strong> - Create new records</li>
                                    <li>• <strong>edit</strong> - Edit existing records</li>
                                    <li>• <strong>delete</strong> - Delete records</li>
                                    <li>• <strong>show</strong> - View records</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Legacy permission checkboxes
    const legacyRead = document.getElementById('legacy_read');
    const legacyWrite = document.getElementById('legacy_write');
    const legacyDelete = document.getElementById('legacy_delete');
    const legacyFull = document.getElementById('legacy_full');
    const legacyReadWrite = document.getElementById('legacy_read_write');
    const legacyNone = document.getElementById('legacy_none');

    // Get all granular permission checkboxes
    const allPermissions = document.querySelectorAll('input[name="permissions[]"]');

    // Function to get permissions by type
    function getPermissionsByType(type) {
        const permissions = [];
        allPermissions.forEach(checkbox => {
            const permName = checkbox.value;
            if (type === 'read' && (permName.includes('show') || permName.includes('view'))) {
                permissions.push(checkbox);
            } else if (type === 'write' && (permName.includes('create') || permName.includes('edit'))) {
                permissions.push(checkbox);
            } else if (type === 'delete' && permName.includes('delete')) {
                permissions.push(checkbox);
            } else if (type === 'manage' && permName.includes('manage')) {
                permissions.push(checkbox);
            }
        });
        return permissions;
    }

    // Function to update legacy checkboxes based on granular permissions
    function updateLegacyCheckboxes() {
        const checkedPermissions = Array.from(allPermissions).filter(cb => cb.checked);
        const hasRead = checkedPermissions.some(cb => cb.value.includes('show') || cb.value.includes('view'));
        const hasWrite = checkedPermissions.some(cb => cb.value.includes('create') || cb.value.includes('edit'));
        const hasDelete = checkedPermissions.some(cb => cb.value.includes('delete'));
        const hasManage = checkedPermissions.some(cb => cb.value.includes('manage'));
        const hasReadWrite = hasRead && hasWrite;

        if (legacyRead) legacyRead.checked = hasRead;
        if (legacyWrite) legacyWrite.checked = hasWrite;
        if (legacyDelete) legacyDelete.checked = hasDelete;
        if (legacyFull) legacyFull.checked = hasManage;
        if (legacyReadWrite) legacyReadWrite.checked = hasReadWrite;
        if (legacyNone) legacyNone.checked = checkedPermissions.length === 0;
    }

    // Read permissions (show actions)
    if (legacyRead) {
        legacyRead.addEventListener('change', function() {
            if (this.checked) {
                const readPermissions = getPermissionsByType('read');
                readPermissions.forEach(checkbox => {
                    checkbox.checked = true;
                });
            }
            updateLegacyCheckboxes();
        });
    }

    // Write permissions (create/edit actions)
    if (legacyWrite) {
        legacyWrite.addEventListener('change', function() {
            if (this.checked) {
                const writePermissions = getPermissionsByType('write');
                writePermissions.forEach(checkbox => {
                    checkbox.checked = true;
                });
            }
            updateLegacyCheckboxes();
        });
    }

    // Delete permissions (delete actions)
    if (legacyDelete) {
        legacyDelete.addEventListener('change', function() {
            if (this.checked) {
                const deletePermissions = getPermissionsByType('delete');
                deletePermissions.forEach(checkbox => {
                    checkbox.checked = true;
                });
            }
            updateLegacyCheckboxes();
        });
    }

    // Full permissions (check all)
    if (legacyFull) {
        legacyFull.addEventListener('change', function() {
            if (this.checked) {
                allPermissions.forEach(checkbox => {
                    checkbox.checked = true;
                });
                // Also check all legacy permissions
                [legacyRead, legacyWrite, legacyDelete, legacyReadWrite].forEach(checkbox => {
                    if (checkbox) checkbox.checked = true;
                });
                if (legacyNone) legacyNone.checked = false;
            }
            updateLegacyCheckboxes();
        });
    }

    // Read-Write permissions (show + create + edit)
    if (legacyReadWrite) {
        legacyReadWrite.addEventListener('change', function() {
            if (this.checked) {
                const readPermissions = getPermissionsByType('read');
                const writePermissions = getPermissionsByType('write');
                [...readPermissions, ...writePermissions].forEach(checkbox => {
                    checkbox.checked = true;
                });
                // Check read and write legacy permissions
                if (legacyRead) legacyRead.checked = true;
                if (legacyWrite) legacyWrite.checked = true;
                if (legacyNone) legacyNone.checked = false;
            }
            updateLegacyCheckboxes();
        });
    }

    // None permissions (uncheck all)
    if (legacyNone) {
        legacyNone.addEventListener('change', function() {
            if (this.checked) {
                allPermissions.forEach(checkbox => {
                    checkbox.checked = false;
                });
                // Uncheck all other legacy permissions
                [legacyRead, legacyWrite, legacyDelete, legacyFull, legacyReadWrite].forEach(checkbox => {
                    if (checkbox) checkbox.checked = false;
                });
            }
        });
    }

    // Update legacy checkboxes when granular permissions change
    allPermissions.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateLegacyCheckboxes();
        });
    });

    // Initialize legacy checkboxes on page load
    updateLegacyCheckboxes();
});
</script>
@endpush
