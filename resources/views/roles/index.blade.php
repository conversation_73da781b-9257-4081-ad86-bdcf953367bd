@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Role Management</h4>
                    <div class="d-flex gap-2">
                        <a href="{{ route('roles.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add Role
                        </a>
                        <a href="{{ route('permissions.index') }}" class="btn btn-info">
                            <i class="ri-shield-line me-1"></i>Manage Permissions
                        </a>
                    </div>
                </div>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif

                <!-- Search Form -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="roleSearch" class="form-label">Search Roles</label>
                                <input type="text" id="roleSearch" class="form-control" placeholder="Search roles...">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Roles Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">All Roles ({{ $roles->count() }})</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="rolesTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>Role</th>
                                        <th>Guard</th>
                                        <th>Permissions</th>
                                        <th>Users</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($roles as $role)
                                        <tr>
                                            <td>{{ $role->id }}</td>
                                            <td>
                                                <span class="badge bg-primary fs-6">{{ $role->name }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ $role->guard_name }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">{{ $role->permissions->count() }} permissions</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $role->users()->count() }} users</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('roles.show', $role) }}" class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="ri-eye-line"></i>
                                                    </a>
                                                    @if ($role->name !== 'super_admin')
                                                        <a href="{{ route('roles.edit', $role) }}" class="btn btn-sm btn-outline-primary" title="Edit">
                                                            <i class="ri-edit-line"></i>
                                                        </a>
                                                        @if ($role->users()->count() === 0)
                                                            <form action="{{ route('roles.destroy', $role) }}" method="POST" class="d-inline">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                        onclick="return confirm('Are you sure you want to delete this role?')"
                                                                        title="Delete">
                                                                    <i class="ri-delete-bin-line"></i>
                                                                </button>
                                                            </form>
                                                        @else
                                                            <span class="btn btn-sm btn-outline-secondary" title="Cannot delete - has users assigned">
                                                                <i class="ri-lock-line"></i>
                                                            </span>
                                                        @endif
                                                    @else
                                                        <span class="btn btn-sm btn-outline-secondary" title="Super Admin - Protected">
                                                            <i class="ri-shield-line"></i>
                                                        </span>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="ri-shield-user-line fs-1"></i>
                                                    <p class="mt-2">No roles found</p>
                                                    <a href="{{ route('roles.create') }}" class="btn btn-primary btn-sm">Create First Role</a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection

@push('styles')
<style>
.permission-list, .user-list {
    max-height: 100px;
    overflow-y: auto;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Role search functionality
    $('#roleSearch').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        $('#rolesTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
});
</script>
@endpush
