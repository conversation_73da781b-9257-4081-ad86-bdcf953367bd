@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h1>My Projects</h1>

                    {{-- <div class="alert alert-info">
                        <i class="ri-information-line me-2"></i>
                        <strong>Salary Calculation Note:</strong> Only projects where you are the <strong>"Assigned By"</strong> will count towards your achieved target and salary calculation. Projects where you are "Delivered By" or "Responsible Team" are shown for reference but do not affect your salary.
                    </div> --}}

                    @if (session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif
                    @if (session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif

                    <form method="GET" action="{{ route('project-managements.my') }}" class="row mb-3 g-2">
                        <div class="col-md-4">
                            <label>Search</label>
                            <input type="text" name="q" class="form-control"
                                placeholder="Client, Project Name or URL" value="{{ request('q') }}">
                        </div>
                        <div class="col-md-3">
                            <label>Status</label>
                            <select name="status" class="form-control">
                                <option value="">-- All Statuses --</option>
                                @foreach (['pending', 'in_progress', 'completed', 'on_hold', 'cancelled', 'in_revision'] as $status)
                                    <option value="{{ $status }}"
                                        {{ request('status') == $status ? 'selected' : '' }}>
                                        {{ ucfirst(str_replace('_', ' ', $status)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">Filter</button>
                            <a href="{{ route('project-managements.my') }}" class="btn btn-secondary">Reset</a>
                        </div>
                    </form>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>SL</th>
                                    <th>Client</th>
                                    <th>Project Name</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                    <th>My Role</th>
                                    <th>Assigned By</th>
                                    <th>Responsible Team</th>
                                    <th>Deadline</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($projectManagements as $projectManagement)
                                    @php
                                        $isCompleted = in_array($projectManagement->status, [
                                            'completed',
                                            'in_revision',
                                        ]);
                                        $now = now();
                                        $deadline = $projectManagement->incoming_date
                                            ? Carbon\Carbon::parse($projectManagement->incoming_date)->addDays(30)
                                            : null;

                                        // Determine user's role in this project
$user = auth()->user();
$team = $user->team ?? null;
$role = '';
if ($team && $projectManagement->assigned_by == $team->id) {
    $role = 'Assigned By';
} elseif ($team && $projectManagement->delivered_by == $team->id) {
    $role = 'Delivered By';
} elseif ($team && $projectManagement->responsible_team == $team->id) {
    $role = 'Responsible Team';
}

if ($deadline) {
    $isPast = $now->gt($deadline);
    $diff = $now->diff($deadline);
    $timeString = sprintf('%d days, %02d:%02d', $diff->d, $diff->h, $diff->i);
                                        }
                                    @endphp

                                    <tr class="{{ $isCompleted ? 'table-success' : '' }}">
                                        <td>{{ $loop->iteration + ($projectManagements->currentPage() - 1) * $projectManagements->perPage() }}
                                        </td>
                                        <td>{{ $projectManagement->client_name ?? 'N/A' }}</td>
                                        <td>{{ $projectManagement->project_name ?? 'N/A' }}</td>
                                        <td>${{ number_format($projectManagement->delivered_amount ?? ($projectManagement->amount ?? 0), 2) }}
                                        </td>

                                        <!-- Status Column -->
                                        <td>
                                            <form
                                                action="{{ route('project-managements.update', $projectManagement->id) }}"
                                                method="POST" class="d-flex align-items-center">
                                                @csrf
                                                @method('PUT')

                                                @if (!$isCompleted)
                                                    <select name="status" class="form-select form-select-sm me-2"
                                                        onchange="this.form.submit()">
                                                        @foreach (['pending', 'in_progress', 'completed', 'on_hold', 'cancelled', 'in_revision'] as $status)
                                                            <option value="{{ $status }}"
                                                                {{ $projectManagement->status === $status ? 'selected' : '' }}>
                                                                {{ ucfirst(str_replace('_', ' ', $status)) }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                @else
                                                    <span
                                                        class="badge bg-{{ $projectManagement->status === 'completed' ? 'success' : 'info' }}">
                                                        {{ ucfirst(str_replace('_', ' ', $projectManagement->status)) }}
                                                    </span>
                                                @endif
                                        </td>

                                        <!-- Progress Column -->
                                        <td>
                                            @if (!$isCompleted)
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar
                                                    @if ($projectManagement->progress >= 90) bg-success
                                                    @elseif($projectManagement->progress >= 50) bg-primary
                                                    @else bg-warning @endif"
                                                        role="progressbar"
                                                        style="width: {{ $projectManagement->progress ?? 0 }}%"
                                                        aria-valuenow="{{ $projectManagement->progress ?? 0 }}"
                                                        aria-valuemin="0" aria-valuemax="100">
                                                        {{ $projectManagement->progress ?? 0 }}%
                                                    </div>
                                                </div>
                                            @else
                                                <span class="text-muted">100%</span>
                                            @endif
                                        </td>

                                        <!-- My Role Column -->
                                        <td>
                                            @if ($role === 'Assigned By')
                                                <span class="badge bg-success">
                                                    <i class="ri-check-line me-1"></i>
                                                    {{ $role }} (Counts for Salary)
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">
                                                    <i class="ri-info-line me-1"></i>
                                                    {{ $role }}
                                                </span>
                                            @endif
                                        </td>

                                        <!-- Assigned By Column -->
                                        <td>
                                            @if (auth()->check() &&
                                                    in_array(auth()->user()->permission, ['read', 'read_write', 'full']) &&
                                                    auth()->user()->role !== 'user' &&
                                                    isset($teams))
                                                <form
                                                    action="{{ route('project-managements.update', $projectManagement->id) }}"
                                                    method="POST">
                                                    @csrf
                                                    @method('PUT')

                                                    <select name="assigned_by" class="form-select form-select-sm"
                                                        onchange="this.form.submit()">
                                                        <option value="">-- Select --</option>
                                                        @foreach ($teams as $team)
                                                            <option value="{{ $team->id }}"
                                                                {{ $projectManagement->assigned_by == $team->id ? 'selected' : '' }}>
                                                                {{ optional($team->user)->name ?? $team->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </form>
                                            @else
                                                @if ($projectManagement->assignedBy && $projectManagement->assignedBy->user)
                                                    <span class="badge bg-primary">
                                                        <i class="ri-user-line me-1"></i>
                                                        {{ $projectManagement->assignedBy->user->name }}
                                                    </span>
                                                @else
                                                    <span class="badge bg-secondary">Not Assigned</span>
                                                @endif
                                            @endif
                                        </td>



                                        <!-- Responsible Team Column -->
                                        <td>
                                            @if ($projectManagement->responsibleTeam && $projectManagement->responsibleTeam->user)
                                                <span class="badge bg-success">
                                                    <i class="ri-team-line me-1"></i>
                                                    {{ $projectManagement->responsibleTeam->user->name }}
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">Not Assigned</span>
                                            @endif
                                        </td>



                                        <!-- Deadline Column -->
                                        <td>
                                            @if (!$isCompleted && $deadline)
                                                <span
                                                    class="{{ $isPast ? 'text-danger' : ($diff->d < 3 ? 'text-warning' : 'text-success') }}">
                                                    <i class="ri-time-line"></i>
                                                    {{ $isPast ? 'Overdue by ' . $timeString : $timeString }}
                                                </span>
                                            @elseif ($isCompleted)
                                                <span class="text-muted">Completed</span>
                                            @else
                                                <span class="text-muted">No deadline</span>
                                            @endif
                                        </td>

                                        <!-- Actions Column -->
                                        <td class="text-nowrap">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="ri-more-2-fill"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item"
                                                            href="{{ route('project-managements.show', $projectManagement->id) }}">
                                                            <i class="ri-eye-line me-2"></i>View Details
                                                        </a>
                                                    </li>

                                                    @if (!$isCompleted)
                                                        <li>
                                                            <a class="dropdown-item"
                                                                href="{{ route('project-managements.edit', $projectManagement->id) }}">
                                                                <i class="ri-edit-line me-2"></i>Edit
                                                            </a>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="11" class="text-center">No projects found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if($projectManagements->hasPages())
                        {{ $projectManagements->onEachSide(1)->links('pagination::bootstrap-5') }}
                    @endif
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection
