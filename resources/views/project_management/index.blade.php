@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h1>Projects</h1>

                    @if (session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif
                    @if (session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif

                    <a href="{{ route('project-managements.create') }}" class="btn btn-primary mb-3">Create New Project</a>

                    <form method="GET" action="{{ route('project-managements.index') }}" class="row mb-3 g-2">
                        <div class="col-md-2">
                            <label>Search</label>
                            <input type="text" name="q" class="form-control" placeholder="Client, URL or Amount"
                                value="{{ request('q') }}">
                        </div>
                        <div class="col-md-2">
                            <label>Status</label>
                            <select name="status" class="form-control">
                                <option value="">-- All Statuses --</option>
                                @foreach (['pending', 'in_progress', 'completed', 'on_hold', 'cancelled', 'in_revision'] as $status)
                                    <option value="{{ $status }}"
                                        {{ request('status') == $status ? 'selected' : '' }}>
                                        {{ ucfirst(str_replace('_', ' ', $status)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-md-2">
                            <label>Assigned By</label>
                            <select name="assigned_by" class="form-control">
                                <option value="">-- All Users --</option>
                                @foreach ($teams as $team)
                                    <option value="{{ $team->id }}"
                                        {{ request('assigned_by') == $team->id ? 'selected' : '' }}>
                                        {{ optional($team->user)->name ?? 'Unnamed Team' }}
                                    </option>
                                @endforeach

                            </select>
                        </div>

                        <div class="col-md-2">
                            <label>Incoming Date (From)</label>
                            <input type="date" name="incoming_date" class="form-control"
                                value="{{ request('incoming_date') }}">
                        </div>

                        <div class="col-md-2">
                            <label>Delivered Date (To)</label>
                            <input type="date" name="delivered_date" class="form-control"
                                value="{{ request('delivered_date') }}">
                        </div>



                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">Filter</button>
                            <a href="{{ route('project-managements.index') }}" class="btn btn-secondary">Reset</a>
                        </div>
                    </form>

                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Sl.</th>
                                <th>Client</th>
                                <th>Order url</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Progress</th>
                                <th>Remaining Days</th>
                                <th>Assigned By</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($projectManagements as $projectManagement)
                                @php
                                    $isCompleted = in_array($projectManagement->status, ['completed', 'in_revision']);
                                    $now = now();
                                    $endDate = $projectManagement->delivered_date
                                        ? Carbon\Carbon::parse($projectManagement->delivered_date)
                                        : null;

                                    if ($endDate) {
                                        $isPast = $now->gt($endDate);
                                        $diff = $now->diff($endDate);
                                        $timeString = sprintf('%d days, %02d:%02d', $diff->d, $diff->h, $diff->i);
                                    }
                                @endphp

                                <tr class="{{ $isCompleted ? 'table-success' : '' }}">
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ $projectManagement->client_name ?? 'N/A' }}</td>
                                    <td>
                                        <a href="{{ $projectManagement->order_page_url }}" target="_blank"
                                            class="btn btn-sm btn-outline-primary">
                                            View
                                        </a>
                                    </td>
                                    <td>${{ number_format($projectManagement->delivered_amount ?? 0, 2) }}</td>

                                    <!-- Status Column -->
                                    <td>
                                        <form action="{{ route('project-managements.update', $projectManagement->id) }}"
                                            method="POST" class="d-flex align-items-center">
                                            @csrf
                                            @method('PUT')

                                            @if (!$isCompleted)
                                                <select name="status" class="form-select form-select-sm me-2"
                                                    onchange="this.form.submit()">
                                                    @foreach (['pending', 'in_progress', 'completed', 'on_hold', 'cancelled', 'in_revision'] as $status)
                                                        <option value="{{ $status }}"
                                                            {{ $projectManagement->status === $status ? 'selected' : '' }}>
                                                            {{ ucfirst(str_replace('_', ' ', $status)) }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            @else
                                                <span
                                                    class="badge bg-{{ $projectManagement->status === 'completed' ? 'success' : 'info' }}">
                                                    {{ ucfirst(str_replace('_', ' ', $projectManagement->status)) }}
                                                </span>
                                            @endif
                                    </td>

                                    <!-- Progress Column -->
                                    <td>
                                        @if (!$isCompleted)
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar
                        @if ($projectManagement->progress >= 90) bg-success
                        @elseif($projectManagement->progress >= 50) bg-primary
                        @else bg-warning @endif"
                                                    role="progressbar"
                                                    style="width: {{ $projectManagement->progress ?? 0 }}%"
                                                    aria-valuenow="{{ $projectManagement->progress ?? 0 }}"
                                                    aria-valuemin="0" aria-valuemax="100">
                                                    {{ $projectManagement->progress ?? 0 }}%
                                                </div>
                                            </div>
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </td>

                                    <!-- Deadline Column -->
                                    <td>
                                        @if (!$isCompleted)
                                            <span
                                                class="{{ $isPast ? 'text-danger' : ($diff->d < 3 ? 'text-warning' : 'text-success') }}">
                                                <i class="ri-time-line"></i>
                                                {{ $isPast ? 'Overdue by ' . $timeString : $timeString }}
                                            </span>
                                        @else
                                            <span class="text-muted">Completed</span>
                                        @endif
                                    </td>


                                    <!-- Assigned By Column -->
                                    <td>
                                        @if (!$isCompleted)
                                            <select name="assigned_by" class="form-select form-select-sm"
                                                onchange="this.form.submit()">
                                                <option value="">Select Team</option>
                                                @foreach ($teams as $team)
                                                    <option value="{{ $team->id }}"
                                                        {{ $projectManagement->assigned_by == $team->id ? 'selected' : '' }}>
                                                        {{ optional($team->user)->name ?? 'Unassigned' }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        @else
                                            <span
                                                class="text-muted">{{ $projectManagement->assignedBy && $projectManagement->assignedBy->user ? $projectManagement->assignedBy->user->name : 'N/A' }}</span>
                                        @endif
                                    </td>

                                    <!-- Actions Column -->
                                    <td class="text-nowrap">
                                        @if (!$isCompleted)
                                            <button type="submit" class="btn btn-sm btn-primary me-1" title="Save Changes">
                                                <i class="ri-save-line"></i>
                                            </button>
                                        @endif

                                        <div class="dropdown d-inline-block">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="ri-more-2-fill"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item"
                                                        href="{{ route('project-managements.show', $projectManagement->id) }}">
                                                        <i class="ri-eye-line me-2"></i>View Details
                                                    </a>
                                                </li>

                                                @if (!$isCompleted)
                                                    <li>
                                                        <a class="dropdown-item"
                                                            href="{{ route('project-managements.edit', $projectManagement->id) }}">
                                                            <i class="ri-edit-line me-2"></i>Edit
                                                        </a>
                                                    </li>
                                                @endif

                                                @can('delete', $projectManagement)
                                                    <li>
                                                        <form
                                                            action="{{ route('project-managements.destroy', $projectManagement->id) }}"
                                                            method="POST" class="d-inline">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="dropdown-item text-danger"
                                                                onclick="return confirm('Are you sure you want to delete this project?')">
                                                                <i class="ri-delete-bin-line me-2"></i>Delete
                                                            </button>
                                                        </form>
                                                    </li>
                                                @endcan
                                            </ul>
                                        </div>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>

                    </table>

                    {{ $projectManagements->onEachSide(1)->links('pagination::bootstrap-5') }}
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection
