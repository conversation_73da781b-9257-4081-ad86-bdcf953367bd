@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">

                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3 class="mb-0">Project Details</h3>
                        <div>

                            <a href="{{ route('project-managements.edit', $projectManagement->id) }}"
                                class="btn btn-primary">Edit Project</a>
                            <a href="{{ route('project-managements.index') }}" class="btn btn-outline-secondary ms-2">Back to
                                Projects</a>
                        </div>
                    </div>

                    <div class="row gy-4">
                        <!-- Project Overview -->
                        <div class="col-md-6">
                            <div class="card shadow-sm p-4 h-100">
                                <h5 class="fw-bold mb-4">Project Overview</h5>
                                <div class="row gy-4">
                                    <div class="col-md-12"><strong>Project Name:</strong>
                                        {{ $projectManagement->project_name }}</div>
                                    <div class="col-md-12"><strong>Client:</strong> {{ $projectManagement->client_name }}
                                    </div>
                                    <div class="col-md-12"><strong>Status:</strong>
                                        <span class="badge bg-primary">
                                            {{ ucfirst(str_replace('_', ' ', $projectManagement->status)) }}
                                        </span>
                                    </div>
                                    <div class="col-md-12"><strong>Type:</strong> {{ $projectManagement->cms ?? 'N/A' }}
                                    </div>
                                    <div class="col-md-12"><strong>Priority:</strong>
                                        <span class="badge bg-warning text-dark">High</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Team Assignment Summary -->
                        <div class="col-md-6">
                            <div class="card shadow-sm p-4 h-100">
                                <h5 class="fw-bold mb-4">Team Assignment Summary</h5>
                                <div class="row gy-3">
                                    <div class="col-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="ri-team-line text-success me-2"></i>
                                            <strong>Responsible:</strong>
                                            <span class="ms-2">
                                                @if($projectManagement->responsibleTeam && $projectManagement->responsibleTeam->user)
                                                    <span class="badge bg-success">{{ $projectManagement->responsibleTeam->user->name }}</span>
                                                @else
                                                    <span class="badge bg-secondary">Not Assigned</span>
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="ri-user-line text-primary me-2"></i>
                                            <strong>Assigned By:</strong>
                                            <span class="ms-2">
                                                @if($projectManagement->assignedBy && $projectManagement->assignedBy->user)
                                                    <span class="badge bg-primary">{{ $projectManagement->assignedBy->user->name }}</span>
                                                @else
                                                    <span class="badge bg-secondary">Not Assigned</span>
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="ri-check-line text-info me-2"></i>
                                            <strong>Delivered By:</strong>
                                            <span class="ms-2">
                                                @if($projectManagement->deliveredBy && $projectManagement->deliveredBy->user)
                                                    <span class="badge bg-info">{{ $projectManagement->deliveredBy->user->name }}</span>
                                                @else
                                                    <span class="badge bg-secondary">Not Delivered</span>
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="d-flex align-items-center">
                                            <i class="ri-hand-coin-line text-warning me-2"></i>
                                            <strong>Claim Order:</strong>
                                            <span class="ms-2">
                                                @if($projectManagement->team && $projectManagement->team->user)
                                                    <span class="badge bg-warning text-dark">{{ $projectManagement->team->user->name }}</span>
                                                @else
                                                    <span class="badge bg-secondary">Not Claimed</span>
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Timeline & Budget -->

                        <div class="col-md-6">
                            <div class="card shadow-sm p-4 h-100">
                                <h5 class="fw-bold mb-4">Timeline </h5>
                                <div class="row gy-4">
                                    @php
        $isCompleted = in_array($projectManagement->status, ['completed', 'in_revision']);
        $now = now();
        $endDate = $projectManagement->delivered_date ? Carbon\Carbon::parse($projectManagement->delivered_date) : null;

        if ($endDate) {
            $isPast = $now->gt($endDate);
            $diff = $now->diff($endDate);
            $timeString = sprintf(
                "%d days, %02d:%02d",
                $diff->d,
                $diff->h,
                $diff->i
            );
        }
    @endphp
                                    @if(!$isCompleted)


                                    <div class="col-md-12"><strong>Delivery Date:</strong>
                                        {{ $projectManagement->delivered_date ? \Carbon\Carbon::parse($projectManagement->delivered_date)->format('Y-m-d\TH:i:s') : 'N/A' }}
                                    </div>

                                    <div class="col-md-12">
                                        <strong>Remaining:</strong>
                                        <span id="remaining-time">Calculating...</span>
                                    </div>

                                    @if ($projectManagement->delivered_date)
                                        <script>
                                            document.addEventListener("DOMContentLoaded", function() {
                                                const deliveryDate = new Date(
                                                    "{{ \Carbon\Carbon::parse($projectManagement->delivered_date)->toISOString() }}");

                                                function updateRemainingTime() {
                                                    const now = new Date();
                                                    const diff = deliveryDate - now;

                                                    if (diff <= 0) {
                                                        document.getElementById("remaining-time").textContent = "Delivered";
                                                        return;
                                                    }

                                                    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                                                    const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);
                                                    const minutes = Math.floor((diff / (1000 * 60)) % 60);
                                                    const seconds = Math.floor((diff / 1000) % 60);

                                                    document.getElementById("remaining-time").textContent =
                                                        `${days} days, ${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
                                                }

                                                updateRemainingTime();
                                                setInterval(updateRemainingTime, 1000);
                                            });
                                        </script>
                                    @endif

                                    <div class="col-md-12">
                                        <strong>Progress:</strong>
                                        <div class="progress mt-1" style="height: 20px;">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                style="width: {{ $projectManagement->progress }}%;">
                                                {{ $projectManagement->progress }}%
                                            </div>
                                        </div>
                                    </div>
                                    @else
                                    <div class="col-md-12"><strong>Completed Date:</strong>
                                        {{ $projectManagement->completed_date ? \Carbon\Carbon::parse($projectManagement->completed_date)->format('Y-m-d\TH:i:s') : 'N/A' }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <!-- Assignment Info -->
                            <div class="card shadow-sm p-4 mb-4">
                                <h5 class="fw-bold mb-4">Assignment Information</h5>
                                <div class="row gy-4">
                                    <div class="col-md-6">
                                        <strong>Responsible Team:</strong>
                                        <div class="mt-1">
                                            @if($projectManagement->responsibleTeam && $projectManagement->responsibleTeam->user)
                                                <span class="badge bg-success">
                                                    <i class="ri-team-line me-1"></i>
                                                    {{ $projectManagement->responsibleTeam->user->name }}
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">Not Assigned</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Assigned By:</strong>
                                        <div class="mt-1">
                                            @if($projectManagement->assignedBy && $projectManagement->assignedBy->user)
                                                <span class="badge bg-primary">
                                                    <i class="ri-user-line me-1"></i>
                                                    {{ $projectManagement->assignedBy->user->name }}
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">Not Assigned</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Delivered By:</strong>
                                        <div class="mt-1">
                                            @if($projectManagement->deliveredBy && $projectManagement->deliveredBy->user)
                                                <span class="badge bg-info">
                                                    <i class="ri-check-line me-1"></i>
                                                    {{ $projectManagement->deliveredBy->user->name }}
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">Not Delivered</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Claim Order Team:</strong>
                                        <div class="mt-1">
                                            @if($projectManagement->team && $projectManagement->team->user)
                                                <span class="badge bg-warning text-dark">
                                                    <i class="ri-hand-coin-line me-1"></i>
                                                    {{ $projectManagement->team->user->name }}
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">Not Claimed</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <strong>Login Credentials:</strong>
                                        <div class="mt-1">
                                            @if($projectManagement->login_credentials)
                                                <code class="bg-light p-2 rounded d-block">{{ $projectManagement->login_credentials }}</code>
                                            @else
                                                <span class="text-muted">No credentials provided</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="col-md-6">
                            <!-- Technical Details -->
                            <div class="card shadow-sm p-4 mb-4">
                                <h5 class="fw-bold mb-4">Technical Details</h5>
                                <div class="row gy-4">
                                    <div class="col-md-6"><strong>Theme Name:</strong>
                                        {{ $projectManagement->theme_name ?? 'N/A' }}</div>
                                    <div class="col-md-6"><strong>Page Builder:</strong>
                                        {{ $projectManagement->page_builder_name ?? 'N/A' }}</div>
                                    <div class="col-md-6"><strong>Reference Website:</strong>
                                        <a href="{{ $projectManagement->reference_website }}"
                                            target="_blank">{{ $projectManagement->reference_website ?? 'N/A' }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Info -->
                    <div class="card shadow-sm p-4 mb-4">
                        <h5 class="fw-bold mb-4">Additional Information</h5>
                        <div class="row gy-4">
                            <div class="col-12"><strong>Instruction from Client:</strong>
                                <div class="border p-3 rounded bg-light">{{ $projectManagement->instruction_from_client }}
                                </div>
                            </div>

                            @foreach ([
                        'order_page_screenshot' => 'Order Page Screenshot',
                        'conversation_page_screenshot' => 'Conversation Screenshot',
                        'files' => 'Project Files',
                    ] as $field => $label)
                                <div class="col-md-6 mb-3">
                                    <strong>{{ $label }}:</strong><br>
                                    @if ($projectManagement->$field)
                                        @php
                                            // Check if the field contains JSON (multiple files)
                                            $files = json_decode($projectManagement->$field);
                                            $isMultiple = is_array($files);

                                            if ($isMultiple) {
                                                $filePaths = $files;
                                            } else {
                                                $filePaths = [$projectManagement->$field];
                                            }
                                        @endphp

                                        @foreach ($filePaths as $filePath)
                                            @php
                                                $cleanPath = str_replace('storage/', '', $filePath);
                                                $fullPath = asset('storage/' . $cleanPath);
                                                $extension = strtolower(pathinfo($cleanPath, PATHINFO_EXTENSION));
                                            @endphp

                                            <div class="mb-2">
                                                @if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']))
                                                    <!-- Image Thumbnail with fixed size container -->
                                                    <div
                                                        style="width: 300px; height: 200px; background-size: cover; overflow: hidden; display: flex; justify-content: center; align-items: center; border: 1px solid #ddd; background: #f8f9fa;">
                                                        <a href="{{ $fullPath }}" target="_blank"
                                                            style="display: block; max-width: 100%; ">
                                                            <img src="{{ $fullPath }}" alt="{{ $label }}"
                                                                style="max-width: 100%; background-size: cover; max-height: 100%; object-fit: contain;">
                                                        </a>
                                                    </div>
                                                @elseif ($extension === 'pdf')
                                                    <!-- PDF Preview Icon with Link -->
                                                    <a href="{{ $fullPath }}" target="_blank"
                                                        class="btn btn-outline-primary">
                                                        <i class="bi bi-file-earmark-pdf-fill"></i> View PDF
                                                    </a>
                                                @else
                                                    <!-- Generic file link -->
                                                    <a href="{{ $fullPath }}" target="_blank"
                                                        class="btn btn-outline-info">
                                                        View {{ strtoupper($extension) }} File
                                                    </a>
                                                @endif
                                            </div>
                                        @endforeach
                                    @else
                                        <span class="text-muted">No file uploaded</span>
                                    @endif
                                </div>
                            @endforeach


                            <div class="col-12"><strong>Special Plugins:</strong>
                                <div class="border p-3 rounded bg-light">{{ $projectManagement->special_plugins }}</div>
                            </div>
                            <div class="col-12"><strong>Special Requirements:</strong>
                                <div class="border p-3 rounded bg-light">{{ $projectManagement->special_requirements }}
                                </div>
                            </div>
                            <div class="col-12"><strong>Notes:</strong>
                                <div class="border p-3 rounded bg-light">{{ $projectManagement->notes }}</div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
@endsection
