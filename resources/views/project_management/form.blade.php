<div class="row">
    <div class="col-md-4">
        <label for="client_name">Client Name*</label>
        <input type="text" id="client_name" class="form-control @error('client_name') is-invalid @enderror"
            name="client_name" value="{{ old('client_name', $projectManagement->client_name ?? '') }}" required>
        @error('client_name')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
    <div class="col-md-4">
        <label for="project_name">Project Name*</label>
        <input type="text" id="project_name" name="project_name"
            class="form-control @error('project_name') is-invalid @enderror"
            value="{{ old('project_name', $projectManagement->project_name ?? '') }}" required>
        @error('project_name')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="col-md-4">
        <label for="team_id">Claim Order</label>
        <select id="team_id" name="team_id" class="form-control @error('team_id') is-invalid @enderror" required>
    <option value="">-- Select Team --</option>
    @foreach ($teams as $team)
        <option value="{{ $team->id }}"
            {{ old('team_id', $projectManagement->team_id ?? '') == $team->id ? 'selected' : '' }}>
            {{ $team->name ?? optional($team->user)->name ?? 'Unnamed Team' }}
        </option>
    @endforeach
</select>

        @error('team_id')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-4">
        <label for="amount">Amount*</label>
        <input type="number" id="amount" name="amount" step="0.01" min="0"
            class="form-control @error('amount') is-invalid @enderror"
            value="{{ old('amount', $projectManagement->amount ?? '') }}" required>
        @error('amount')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror

    </div>
    <div class="col-md-4">
        <label for="assigned_by">Assigned By</label>
        <select id="assigned_by" name="assigned_by" class="form-control @error('assigned_by') is-invalid @enderror">
            <option value="">-- Select User --</option>
            @foreach ($teams as $team)
            <option value="{{ $team->id }}"
        {{ old('assigned_by', $projectManagement->assigned_by ?? '') == $team->id ? 'selected' : '' }}>
        {{ optional($team->user)->name ?? 'Unnamed Team' }}
    </option>
@endforeach

        </select>
        @error('assigned_by')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror

    </div>
    <div class="col-md-4">
        <label for="delivered_by">Delivered By</label>
        <select id="delivered_by" name="delivered_by" class="form-control @error('delivered_by') is-invalid @enderror">
            <option value="">-- Select User --</option>
            @foreach ($teams as $team)
    <option value="{{ $team->id }}"
        {{ old('delivered_by', $projectManagement->delivered_by ?? '') == $team->id ? 'selected' : '' }}>
        {{ optional($team->user)->name ?? 'Unnamed Team' }}
    </option>
@endforeach
        </select>
        @error('delivered_by')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
</div>

<div class="row mt-3">
  <div class="col-md-4">
    <label for="order_account_id">Order Account*</label>
    <select id="order_account_id" name="order_account_id"
        class="form-control @error('order_account_id') is-invalid @enderror" required>
        <option value="">-- Select Order Account --</option>
        @foreach ($orderAccounts as $orderAccount)
            <option value="{{ $orderAccount->id }}"
                {{ old('order_account_id', $projectManagement->order_account_id ?? '') == $orderAccount->id ? 'selected' : '' }}
                data-category-info="{{ $orderAccount->category ? $orderAccount->category->delivery_amount_charge_percentage : '' }}"
                data-loan-info="{{ $orderAccount->getTotalActiveLoanChargePercentage() }}">
                {{ $orderAccount->order_account_name }}
                @if($orderAccount->category)
                    ({{ $orderAccount->category->name }} - {{ number_format($orderAccount->category->delivery_amount_charge_percentage, 2) }}%)
                @else
                    (No Category)
                @endif
                @if($orderAccount->getTotalActiveLoanChargePercentage() > 0)
                    - Loan: {{ number_format($orderAccount->getTotalActiveLoanChargePercentage(), 2) }}%
                @endif
            </option>
        @endforeach
    </select>
    @error('order_account_id')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>

    <div class="col-md-4">
        <label for="order_page_url">Order Page URL*</label>
        <input type="url" id="order_page_url" name="order_page_url"
            class="form-control @error('order_page_url') is-invalid @enderror"
            value="{{ old('order_page_url', $projectManagement->order_page_url ?? '') }}" required>
        @error('order_page_url')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
    <div class="col-md-4">
        <label for="responsible_team">Responsible Team*</label>
        <select id="responsible_team" name="responsible_team"
            class="form-control @error('responsible_team') is-invalid @enderror" required>
            <option value="">-- Select Responsible Team --</option>
            @foreach ($teams as $team)
                <option value="{{ $team->id }}"
                    {{ old('responsible_team', $projectManagement->responsible_team ?? '') == $team->id ? 'selected' : '' }}>
                    {{ optional($team->user)->name ?? 'Unnamed Team' }}
                </option>
            @endforeach
        </select>
        @error('responsible_team')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-4">
        @php
            // Set the default status to 'pending' if nothing is selected
            $currentStatus = old('status', $projectManagement->status ?? 'in_progress');
        @endphp

        <label for="status">Status</label>
        <select name="status" class="form-control @error('status') is-invalid @enderror">
            <option value="">-- Select Status --</option>
            @foreach ($statuses as $status)
                <option value="{{ $status }}" {{ $currentStatus === $status ? 'selected' : '' }}>
                    {{ ucfirst(str_replace('_', ' ', $status)) }}
                </option>
            @endforeach
        </select>

        @error('status')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>





    <div class="col-md-4">
        <label for="incoming_date">Incoming Date*</label>
        <input type="date" id="incoming_date" name="incoming_date" class="form-control"
            value="{{ old('incoming_date', isset($projectManagement->incoming_date) ? $projectManagement->incoming_date->format('Y-m-d') : '') }}"
            required>

    </div>
    <div class="col-md-4">
        <label for="cms">CMS*</label>
        <input type="text" id="cms" name="cms" class="form-control"
            value="{{ old('cms', $projectManagement->cms ?? '') }}" required>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-4">
        <label for="delivered_date">Delivered Date</label>
        <!-- Delivered Date -->
        <input type="datetime-local" name="delivered_date" id="delivered_date" class="form-control"
            value="{{ old('delivered_date', $projectManagement->delivered_date ? $projectManagement->delivered_date->format('Y-m-d\TH:i') : '') }}">


    </div>
    <div class="col-md-4">
        <label for="delivered_amount">Delivered Amount</label>
        <input type="number" id="delivered_amount" name="delivered_amount" class="form-control" step="0.01"
            min="0" value="{{ old('delivered_amount', $projectManagement->delivered_amount ?? '') }}">
    </div>
</div>

<!-- Calculation Display -->
<div class="row mt-3">
    <div class="col-12">
        <div id="calculation-display"></div>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-4">
        <label for="progress">Progress (%)</label>
        <input type="number" id="progress" name="progress" class="form-control" min="0" max="100"
            value="{{ old('progress', $projectManagement->progress ?? '') }}">
    </div>
</div>

<div class="mt-3">
    <label for="instruction_from_client">Instructions from Client</label>
    <textarea id="instruction_from_client" name="instruction_from_client" class="form-control">{{ old('instruction_from_client', $projectManagement->instruction_from_client ?? '') }}</textarea>
</div>


<div class="row mt-3">
    {{-- Order Page Screenshot --}}
    <div class="col-md-4">
        <label for="order_page_screenshot">Order Page Screenshot</label>
        <input type="file" id="order_page_screenshot" name="order_page_screenshot[]" class="form-control"
            accept="image/*" multiple>

        <div id="order_page_screenshot_preview" class="mt-2">
            @php
                $orderScreenshots = old(
                    'existing_order_page_screenshot',
                    isset($projectManagement->order_page_screenshot) &&
                    is_array(json_decode($projectManagement->order_page_screenshot))
                        ? json_decode($projectManagement->order_page_screenshot)
                        : [],
                );
                $orderPagePreview = old('order_page_screenshot_preview', []);
            @endphp

            {{-- Existing screenshots from DB or old --}}
            @foreach ($orderScreenshots as $index => $screenshot)
                <div class="image-preview-container mb-2" data-index="{{ $index }}">
                    <img src="{{ asset($screenshot) }}" class="img-thumbnail" style="height: 100px;width: 150px;">
                    <button type="button" class="btn btn-sm btn-danger remove-image"
                        data-field="order_page_screenshot" data-index="{{ $index }}"
                        data-file="{{ $screenshot }}">Remove</button>
                    <input type="hidden" name="existing_order_page_screenshot[]" value="{{ $screenshot }}">
                </div>
            @endforeach

            {{-- Previews from failed validation --}}
            @foreach ($orderPagePreview as $index => $preview)
                <div class="image-preview-container mb-2" data-index="{{ $index }}">
                    <img src="{{ $preview['url'] }}" class="img-thumbnail" style="max-width: 100px;">
                    <button type="button" class="btn btn-sm btn-danger remove-image"
                        data-field="order_page_screenshot" data-index="{{ $index }}"
                        data-temp="true">Remove</button>
                    <input type="hidden" name="order_page_screenshot_preview[{{ $index }}][url]"
                        value="{{ $preview['url'] }}">
                    <input type="hidden" name="order_page_screenshot_preview[{{ $index }}][name]"
                        value="{{ $preview['name'] }}">
                </div>
            @endforeach
        </div>
    </div>

    {{-- Conversation Page Screenshot --}}
    <div class="col-md-4">
        <label for="conversation_page_screenshot">Conversation Page Screenshot</label>
        <input type="file" id="conversation_page_screenshot" name="conversation_page_screenshot[]"
            class="form-control" accept="image/*" multiple>

        <div id="conversation_page_screenshot_preview" class="mt-2">
            @php
                $conversationScreenshots = old(
                    'existing_conversation_page_screenshot',
                    isset($projectManagement->conversation_page_screenshot) &&
                    is_array(json_decode($projectManagement->conversation_page_screenshot))
                        ? json_decode($projectManagement->conversation_page_screenshot)
                        : [],
                );
                $conversationPreview = old('conversation_page_screenshot_preview', []);
            @endphp

            @foreach ($conversationScreenshots as $index => $screenshot)
                <div class="image-preview-container mb-2" data-index="{{ $index }}">
                    <img src="{{ asset($screenshot) }}" class="img-thumbnail" style="height: 100px;width: 150px;">
                    <button type="button" class="btn btn-sm btn-danger remove-image"
                        data-field="conversation_page_screenshot" data-index="{{ $index }}"
                        data-file="{{ $screenshot }}">Remove</button>
                    <input type="hidden" name="existing_conversation_page_screenshot[]"
                        value="{{ $screenshot }}">
                </div>
            @endforeach

            @foreach ($conversationPreview as $index => $preview)
                <div class="image-preview-container mb-2" data-index="{{ $index }}">
                    <img src="{{ $preview['url'] }}" class="img-thumbnail" style="max-width: 100px;">
                    <button type="button" class="btn btn-sm btn-danger remove-image"
                        data-field="conversation_page_screenshot" data-index="{{ $index }}"
                        data-temp="true">Remove</button>
                    <input type="hidden" name="conversation_page_screenshot_preview[{{ $index }}][url]"
                        value="{{ $preview['url'] }}">
                    <input type="hidden" name="conversation_page_screenshot_preview[{{ $index }}][name]"
                        value="{{ $preview['name'] }}">
                </div>
            @endforeach
        </div>
    </div>

    {{-- Additional Files --}}
    <div class="col-md-4">
        <label for="files">Additional Files</label>
        <input type="file" id="files" name="files[]" class="form-control" multiple>

        <div id="files_preview" class="mt-2">
            @php
                $additionalFiles = old(
                    'existing_files',
                    isset($projectManagement->files) && is_array(json_decode($projectManagement->files))
                        ? json_decode($projectManagement->files)
                        : [],
                );
                $filesPreview = old('files_preview', []);
            @endphp

            @foreach ($additionalFiles as $index => $file)
                <div class="file-preview-container mb-2" data-index="{{ $index }}">
                    @if (in_array(pathinfo($file, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png', 'gif']))
                        <img src="{{ asset($file) }}" class="img-thumbnail" style="height: 100px;width: 150px;">
                    @else
                        <a href="{{ asset($file) }}" target="_blank">View File</a>
                    @endif
                    <button type="button" class="btn btn-sm btn-danger remove-image" data-field="files"
                        data-index="{{ $index }}" data-file="{{ $file }}">Remove</button>
                    <input type="hidden" name="existing_files[]" value="{{ $file }}">
                </div>
            @endforeach

            @foreach ($filesPreview as $index => $preview)
                <div class="file-preview-container mb-2" data-index="{{ $index }}">
                    @if ($preview['is_image'])
                        <img src="{{ $preview['url'] }}" class="img-thumbnail" style="max-width: 100px;">
                    @else
                        <span>{{ $preview['name'] }}</span>
                    @endif
                    <button type="button" class="btn btn-sm btn-danger remove-image" data-field="files"
                        data-index="{{ $index }}" data-temp="true">Remove</button>
                    <input type="hidden" name="files_preview[{{ $index }}][url]"
                        value="{{ $preview['url'] }}">
                    <input type="hidden" name="files_preview[{{ $index }}][name]"
                        value="{{ $preview['name'] }}">
                    <input type="hidden" name="files_preview[{{ $index }}][is_image]"
                        value="{{ $preview['is_image'] }}">
                </div>
            @endforeach
        </div>
    </div>
</div>


<div class="row mt-3">
    <div class="col-md-4">
        <label for="login_credentials">Login Credentials</label>
        <input type="text" id="login_credentials" name="login_credentials" class="form-control"
            value="{{ old('login_credentials', $projectManagement->login_credentials ?? '') }}">
    </div>
    <div class="col-md-4">
        <label for="theme_name">Theme Name</label>
        <input type="text" id="theme_name" name="theme_name" class="form-control"
            value="{{ old('theme_name', $projectManagement->theme_name ?? '') }}">
    </div>
    <div class="col-md-4">
        <label for="page_builder_name">Page Builder Name</label>
        <input type="text" id="page_builder_name" name="page_builder_name" class="form-control"
            value="{{ old('page_builder_name', $projectManagement->page_builder_name ?? '') }}">
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-4">
        <label for="reference_website">Reference Website</label>
        <input type="text" id="reference_website" name="reference_website" class="form-control"
            value="{{ old('reference_website', $projectManagement->reference_website ?? '') }}">
    </div>
    <div class="col-md-4">
        <label for="special_plugins">Special Plugins</label>
        <input type="text" id="special_plugins" name="special_plugins" class="form-control"
            value="{{ old('special_plugins', $projectManagement->special_plugins ?? '') }}">
    </div>
    <div class="col-md-4">
        <label for="special_requirements">Special Requirements</label>
        <input type="text" id="special_requirements" name="special_requirements" class="form-control"
            value="{{ old('special_requirements', $projectManagement->special_requirements ?? '') }}">
    </div>
</div>

<div class="row mt-3">
    <div class="col">
        <label for="notes">Notes</label>
        <textarea id="notes" name="notes" class="form-control">{{ old('notes', $projectManagement->notes ?? '') }}</textarea>
    </div>


</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Amount calculation functionality
        const amountInput = document.getElementById('amount');
        const deliveredAmountInput = document.getElementById('delivered_amount');

        function updateDeliveredAmount() {
            const amount = parseFloat(amountInput.value.replace(',', '.')) || 0;
            const orderAccountSelect = document.getElementById('order_account_id');

            if (amount > 0 && orderAccountSelect && orderAccountSelect.value) {
                // Get the selected marketplace account's category delivery charge and loan charge
                const selectedOption = orderAccountSelect.options[orderAccountSelect.selectedIndex];
                const categoryInfo = selectedOption.getAttribute('data-category-info');
                const loanInfo = selectedOption.getAttribute('data-loan-info');

                if (categoryInfo) {
                    const deliveryChargePercentage = parseFloat(categoryInfo);
                    const loanChargePercentage = loanInfo ? parseFloat(loanInfo) : 0;

                    // Calculate delivery charge first
                    const deliveryChargeAmount = (amount * deliveryChargePercentage) / 100;
                    const afterDelivery = amount - deliveryChargeAmount;

                    // Calculate loan charge on the amount after delivery
                    const loanChargeAmount = (afterDelivery * loanChargePercentage) / 100;

                    // Final amount after both charges
                    const finalAmount = amount - deliveryChargeAmount - loanChargeAmount;

                    deliveredAmountInput.value = finalAmount.toFixed(2);

                    // Update the calculation display
                    updateCalculationDisplay(amount, deliveryChargePercentage, loanChargePercentage);
                } else {
                    deliveredAmountInput.value = '';
                    clearCalculationDisplay();
                }
            } else {
                deliveredAmountInput.value = '';
                clearCalculationDisplay();
            }
        }

        function updateCalculationDisplay(amount, deliveryChargePercentage, loanChargePercentage) {
            const calculationDiv = document.getElementById('calculation-display');
            if (!calculationDiv) return;

            const deliveryChargeAmount = (amount * deliveryChargePercentage) / 100;
            const afterDelivery = amount - deliveryChargeAmount;

            // Loan applies after delivery
            const loanChargeAmount = (afterDelivery * loanChargePercentage) / 100;

            const totalDeduction = deliveryChargeAmount + loanChargeAmount;
            const finalAmount = amount - totalDeduction;
            const totalDeductionPercentage = ((totalDeduction / amount) * 100).toFixed(2);

            calculationDiv.innerHTML = `
                <div class="alert alert-info">
                    <h6>Calculation Breakdown:</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Project Amount:</strong><br>
                            $${amount.toFixed(2)}
                        </div>
                        <div class="col-md-3">
                            <strong>Delivery Charge (${deliveryChargePercentage}%):</strong><br>
                            <span class="text-warning">-$${deliveryChargeAmount.toFixed(2)}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Loan Charge (${loanChargePercentage}% ):</strong><br>
                            <span class="text-danger">-$${loanChargeAmount.toFixed(2)}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Total Deduction:</strong><br>
                            <span class="text-danger fw-bold">-$${totalDeduction.toFixed(2)} (${totalDeductionPercentage}%)</span>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <strong>Final Amount :</strong><br>
                            <span class="text-success fw-bold fs-5">$${finalAmount.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        function clearCalculationDisplay() {
            const calculationDiv = document.getElementById('calculation-display');
            if (calculationDiv) {
                calculationDiv.innerHTML = '';
            }
        }

        if (amountInput && deliveredAmountInput) {
            amountInput.addEventListener('input', updateDeliveredAmount);

            // Also listen for changes in order account selection
            const orderAccountSelect = document.getElementById('order_account_id');
            if (orderAccountSelect) {
                orderAccountSelect.addEventListener('change', updateDeliveredAmount);
            }

            if (amountInput.value) updateDeliveredAmount();
        }

        // File preview functionality
        const setupFilePreview = (field) => {
            const input = document.getElementById(field);
            const previewContainer = document.getElementById(`${field}_preview`);

            if (!input || !previewContainer) return;

            input.addEventListener('change', function(e) {
                // Clear only temporary previews for new uploads
                previewContainer.querySelectorAll('[data-temp="true"]').forEach(el => el.remove());

                Array.from(e.target.files).forEach((file, index) => {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        const previewItem = document.createElement('div');
                        previewItem.className = field === 'files' ?
                            'file-preview-container mb-2' :
                            'image-preview-container mb-2';
                        previewItem.dataset.index = index;
                        previewItem.dataset.temp = "true";

                        if (field === 'files') {
                            // For generic files, show filename with a remove button
                            const fileName = document.createElement('span');
                            fileName.textContent = file.name;

                            const removeBtn = document.createElement('button');
                            removeBtn.type = 'button';
                            removeBtn.className =
                                'remove-image btn btn-sm btn-danger ms-2';
                            removeBtn.dataset.field = field;
                            removeBtn.dataset.index = index;
                            removeBtn.dataset.temp = "true";
                            removeBtn.textContent = 'Remove';

                            previewItem.appendChild(fileName);
                            previewItem.appendChild(removeBtn);
                        } else {
                            // For images, show thumbnail preview and remove button
                            const img = document.createElement('img');
                            img.src = e.target.result;
                            img.alt = file.name;
                            img.style.maxWidth = '150px';
                            img.style.maxHeight = '150px';
                            img.classList.add('img-thumbnail');

                            const removeBtn = document.createElement('button');
                            removeBtn.type = 'button';
                            removeBtn.className =
                                'remove-image btn btn-sm btn-danger mt-1';
                            removeBtn.dataset.field = field;
                            removeBtn.dataset.index = index;
                            removeBtn.dataset.temp = "true";
                            removeBtn.textContent = 'Remove';

                            previewItem.appendChild(img);
                            previewItem.appendChild(removeBtn);
                        }

                        previewContainer.appendChild(previewItem);
                    };

                    reader.readAsDataURL(file);
                });
            });
        };

        // Initialize previews for all file inputs you have
        ['order_page_screenshot', 'conversation_page_screenshot', 'files'].forEach(setupFilePreview);

        // Handle file removal for both temporary (new) and existing files
        document.addEventListener('click', function(e) {
            if (!e.target.classList.contains('remove-image')) return;

            e.preventDefault();

            const field = e.target.dataset.field;
            const index = parseInt(e.target.dataset.index);
            const filePath = e.target.dataset.file;
            const isTemp = e.target.dataset.temp === 'true';
            const container = e.target.closest('.image-preview-container, .file-preview-container');
            const input = document.getElementById(field);
            const form = document.querySelector('form');

            if (!container || !form) return;

            if (isTemp) {
                // Remove temporary preview and update input.files accordingly
                container.remove();

                if (input) {
                    const files = Array.from(input.files);
                    files.splice(index, 1);

                    const dataTransfer = new DataTransfer();
                    files.forEach(file => dataTransfer.items.add(file));
                    input.files = dataTransfer.files;
                }
            } else if (filePath) {
                // Mark existing file for deletion via hidden input
                const deleteInput = document.createElement('input');
                deleteInput.type = 'hidden';
                deleteInput.name = `delete_${field}[]`;
                deleteInput.value = filePath;
                form.appendChild(deleteInput);

                // Remove the hidden input tracking existing files
                const existingInputs = form.querySelectorAll(
                    `input[name="existing_${field}[]"][value="${filePath}"]`);
                existingInputs.forEach(input => input.remove());

                container.remove();
            }
        });
    });
</script>
