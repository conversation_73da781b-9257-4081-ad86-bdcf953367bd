@extends('layouts.master')

@section('maincontent')
<!-- <body data-layout="horizontal" data-topbar="dark"> -->
<!-- Begin page -->
<div id="layout-wrapper">
@include('layouts.sections.navbar.navbar')
<!-- ========== Left Sidebar Start ========== -->
@include('layouts.sections.menu.sidebar')
<!-- Left Sidebar End -->
<!-- ============================================================== -->
<!-- Start right Content here -->
<!-- ============================================================== -->
    <div class="main-content">
        <!-- start Page-content -->

        <div class="page-content">

<div class="container-fluid">

    {{-- <h1>{{ isset($project) ? 'Edit' : 'Create' }} Project</h1> --}}
    @if(session('success'))<div class="alert alert-success">{{ session('success') }}</div>@endif
    @if(session('error'))<div class="alert alert-danger">{{ session('error') }}</div>@endif
    <form action="{{ route('project-managements.update', $projectManagement->id) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
    @include('project_management.form')
    <button type="submit" class="btn btn-primary">Update Project</button>
    </form>
</div>

</div>

        <!-- End Page-content -->
@include('layouts.sections.footer.footer')

    </div>
    <!-- end main content-->

</div>
<!-- END layout-wrapper -->

@include('layouts.sections.menu.rightsidebar')

@endsection
