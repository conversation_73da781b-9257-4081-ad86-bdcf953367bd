@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h2>Edit Shift</h2>
                    <form action="{{ route('duty_shift.update', $shift->id) }}" method="POST" class="row g-3">
                        @csrf @method('PUT')
                        <div class="col-md-4">
                            <label class="form-label">Team Member</label>
                            <select name="team_id" class="form-select" required>
                                @foreach($teams as $team)
                                    <option value="{{ $team->id }}" {{ $shift->team_id == $team->id ? 'selected' : '' }}>{{ $team->user->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Start Time</label>
                            <input type="time" name="start_time" class="form-control" value="{{ \Carbon\Carbon::parse($shift->start_time)->format('H:i') }}" required>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">End Time</label>
                            <input type="time" name="end_time" class="form-control" value="{{ \Carbon\Carbon::parse($shift->end_time)->format('H:i') }}" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Grace (min)</label>
                            <input type="number" name="grace_minutes" class="form-control" min="0" max="120" value="{{ $shift->grace_minutes }}">
                        </div>

                        <div class="col-12">
                            <button class="btn btn-primary" type="submit">Save</button>
                        </div>
                    </form>

                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection


