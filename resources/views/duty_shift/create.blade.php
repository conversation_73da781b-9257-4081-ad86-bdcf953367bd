@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h2>Create Shift</h2>
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <form action="{{ route('duty_shift.store') }}" method="POST" class="row g-3">
                        @csrf
                        <div class="col-md-4">
                            <label class="form-label">Team Member</label>
                            <select name="team_id" class="form-select" required>
                                @foreach ($teams as $team)
                                    <option value="{{ $team->id }}">{{ $team->user->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Name</label>
                            <select name="name" class="form-select" required>
                                <option value="Morning">Morning</option>
                                <option value="Noon">Noon</option>
                                <option value="Night">Night</option>
                            </select>
                        </div>
                        <div class="col-md-8">
                            <label class="form-label">Select Dates</label>
                            <input type="text" id="dates" name="dates" class="form-control"
                                placeholder="Pick multiple dates" required>
                            <small class="text-muted">You can pick multiple dates from the calendar.</small>
                        </div>
                        @push('scripts')
                            <!-- jQuery + jQuery UI (multi-date picker) -->
                            <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
                            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                            <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>
                            <script src="https://rawgit.com/dubrox/Multiple-Dates-Picker-for-jQuery-UI/master/jquery-ui.multidatespicker.js">
                            </script>

                            <script>
                                $(function() {
                                    $('#dates').multiDatesPicker({
                                        dateFormat: "yy-mm-dd" // Format matches your DB column
                                    });
                                });
                            </script>
                        @endpush


                        <div class="col-md-2">
                            <label class="form-label">Grace (min)</label>
                            <input type="number" name="grace_minutes" class="form-control" min="0" max="120"
                                value="5">
                        </div>

                        <div class="col-12">
                            <button class="btn btn-primary" type="submit">Save</button>
                        </div>
                    </form>

                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection
