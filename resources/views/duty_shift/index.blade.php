@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h2>Duty Shifts</h2>
                    <a href="{{ route('duty_shift.create') }}" class="btn btn-primary mb-3">Create Shift</a>
                    <form method="GET" class="row g-2 mb-3">
                        <div class="col-auto">
                            <select name="team_id" class="form-select">
                                <option value="">All members</option>
                                @foreach(($teams ?? \App\Models\Team::with('user')->get()) as $team)
                                    <option value="{{ $team->id }}" {{ request('team_id') == $team->id ? 'selected' : '' }}>{{ $team->user->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-auto">
                            <input type="date" name="from" class="form-control" value="{{ request('from') }}" placeholder="From">
                        </div>
                        <div class="col-auto">
                            <input type="date" name="to" class="form-control" value="{{ request('to') }}" placeholder="To">
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-secondary" type="submit">Filter</button>
                        </div>
                    </form>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>SL</th>
                                <th>User</th>
                                <th>Date</th>
                                <th>Shift</th>
                                <th>Start</th>
                                <th>End</th>
                                <th>Grace (min)</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($shifts as $s)
                                <tr>
                                    <td>{{ $loop->iteration + ($shifts->currentPage() - 1) * $shifts->perPage() }}</td>
                                    <td>{{ $s->team->user->name ?? 'N/A' }}</td>
                                    <td>{{ $s->date ?? '-' }}</td>
                                    <td>{{ $s->name }}</td>
                                    <td>{{ \Carbon\Carbon::parse($s->start_time)->format('H:i') }}</td>
                                    <td>{{ \Carbon\Carbon::parse($s->end_time)->format('H:i') }}</td>
                                    <td>{{ $s->grace_minutes }}</td>

                                    <td>
                                        <a href="{{ route('duty_shift.edit', $s->id) }}" class="btn btn-sm btn-warning">Edit</a>
                                        <form action="{{ route('duty_shift.destroy', $s->id) }}" method="POST" style="display:inline-block" onsubmit="return confirm('Are you sure?');">
                                            @csrf @method('DELETE')
                                            <button class="btn btn-sm btn-danger" type="submit">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    {{ $shifts->onEachSide(1)->links('pagination::bootstrap-5') }}
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection


