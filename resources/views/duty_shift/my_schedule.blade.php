@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h2>My Duty Schedule (Next 30 days)</h2>

                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Shift</th>
                                <th>Start</th>
                                <th>End</th>
                                <th>Grace (min)</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                // Get off_days array from the authenticated user's team
                                $team = auth()->user()->team;
                                $offDays = $team?->off_days ?? [];
                            @endphp

                            @foreach ($days as $d)
                                @php
                                    $dayName = \Carbon\Carbon::parse($d['date'])->format('l'); // Full day name, e.g., "Friday"
                                    $isOffDay = in_array($dayName, $offDays);
                                @endphp
                                <tr @if ($isOffDay) class="table-danger" @endif>
                                    <td>{{ \Carbon\Carbon::parse($d['date'])->format('l, d F Y') }}</td>

                                    <td>{{ $d['name'] ?? '-' }}</td>
                                    <td>{{ $d['start_time'] ? \Carbon\Carbon::parse($d['start_time'])->format('h:i A') : '-' }}
                                    </td>
                                    <td>{{ $d['end_time'] ? \Carbon\Carbon::parse($d['end_time'])->format('h:i A') : '-' }}
                                    </td>
                                    <td>{{ $d['grace_minutes'] }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>

                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection
