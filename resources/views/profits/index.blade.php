@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">Profit Table - Monthly Breakdown</h4>
                                <div class="page-title-right">
                                    <span class="text-muted">Showing data for {{ $availableMonths[$selectedMonth] ?? 'Month ' . $selectedMonth }} {{ $selectedYear }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <form method="GET" action="{{ route('profits.index') }}" class="row g-3">
                                        <div class="col-md-4">
                                            <label class="form-label">Month</label>
                                            <select name="month" class="form-select">
                                                @foreach($availableMonths as $monthNum => $monthName)
                                                    <option value="{{ $monthNum }}" {{ $selectedMonth == $monthNum ? 'selected' : '' }}>
                                                        {{ $monthName }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Year</label>
                                            <select name="year" class="form-select">
                                                @foreach($availableYears as $year)
                                                    <option value="{{ $year }}" {{ $selectedYear == $year ? 'selected' : '' }}>
                                                        {{ $year }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">&nbsp;</label>
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-filter"></i> Filter
                                                </button>
                                                <a href="{{ route('profits.index') }}" class="btn btn-secondary">
                                                    <i class="fas fa-times"></i> Clear
                                                </a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-xl-2 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Total Orders</p>
                                            <h4 class="mb-0">{{ number_format($grandTotals['orders']) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-primary align-self-center">
                                                <span class="avatar-title">
                                                    <i class="fas fa-shopping-cart font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Pending Orders</p>
                                            <h4 class="mb-0">{{ number_format(collect($summaryData)->sum('pending_orders')) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-warning align-self-center">
                                                <span class="avatar-title">
                                                    <i class="fas fa-clock font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Total Delivery Amount</p>
                                            <h4 class="mb-0">${{ number_format($grandTotals['delivery_amount'], 2) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-info align-self-center">
                                                <span class="avatar-title">
                                                    <i class="fas fa-truck font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Total Profit</p>
                                            <h4 class="mb-0 {{ $grandTotals['profit'] >= 0 ? 'text-success' : 'text-danger' }}">
                                                {{ number_format($grandTotals['profit'], 2) }} BDT
                                            </h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle {{ $grandTotals['profit'] >= 0 ? 'bg-success' : 'bg-danger' }} align-self-center">
                                                <span class="avatar-title">
                                                    <i class="fas fa-chart-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <div class="col-xl-2 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Total Salary</p>
                                            <h4 class="mb-0 text-info">
                                                ${{ number_format($totalSalary, 2) }}
                                            </h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-info align-self-center">
                                                <span class="avatar-title">
                                                    <i class="fas fa-money-bill-wave font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Total Expense</p>
                                            <h4 class="mb-0 text-warning">
                                                ${{ number_format($totalExpenses, 2) }}
                                            </h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-warning align-self-center">
                                                <span class="avatar-title">
                                                    <i class="fas fa-receipt font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Account Summary Table -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Order Account Summary</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Order Account Name</th>
                                                    <th>Real Orders </th>
                                                    <th>Special Orders</th>
                                                    <th>Pending Orders </th>
                                                    <th>Completed Orders</th>
                                                    <th>Available Balance $</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($summaryData as $data)
                                                    <tr>
                                                        <td>
                                                            <strong>{{ $data['marketplace']->order_account_name ?? 'N/A' }}</strong>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-primary">{{ number_format($data['real_orders']) }}</span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-info">{{ number_format($data['special_projects_orders']) }}</span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-warning">{{ number_format($data['pending_orders']) }}</span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-success">{{ number_format($data['completed_orders']) }}</span>
                                                        </td>
                                                        <td>
                                                            <span class="badge {{ $data['available_balance'] >= 0 ? 'bg-success' : 'bg-danger' }}">
                                                                ${{ number_format($data['available_balance'], 2) }}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="6" class="text-center py-4">
                                                            <div class="text-muted">
                                                                <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                                                <p>No summary data found.</p>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                            <tfoot>
                                                <tr class="table-dark">
                                                    <td><strong>TOTALS</strong></td>
                                                    <td><strong>{{ number_format(collect($summaryData)->sum('real_orders')) }}</strong></td>
                                                    <td><strong>{{ number_format(collect($summaryData)->sum('special_projects_orders')) }}</strong></td>
                                                    <td><strong>{{ number_format(collect($summaryData)->sum('pending_orders')) }}</strong></td>
                                                    <td><strong>{{ number_format(collect($summaryData)->sum('completed_orders')) }}</strong></td>
                                                    <td><strong>${{ number_format(collect($summaryData)->sum('available_balance'), 2) }}</strong></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Profit Table -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Monthly Profit Breakdown by Marketplace</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th rowspan="2" class="align-middle">Marketplace</th>
                                                    <th colspan="2" class="text-center">Project Management</th>
                                                    <th colspan="2" class="text-center">Special Projects</th>
                                                    <th colspan="3" class="text-center">Totals</th>
                                                </tr>
                                                <tr>
                                                    <!-- Project Management Headers -->
                                                    <th>Orders</th>
                                                    <th>Delivery Amount ($)</th>

                                                    <!-- Special Projects Headers -->
                                                    <th>Orders</th>
                                                    <th>in_hand_amount (BDT)</th>

                                                    <!-- Totals Headers -->
                                                    <th>Orders</th>
                                                    <th>Delivery Amount ($)</th>
                                                    <th>in_hand_amount (BDT)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($profitData as $data)
                                                    <tr>
                                                        <td>
                                                            <strong>{{ $data['marketplace']->order_account_name ?? 'N/A' }}</strong>
                                                        </td>

                                                        <!-- Project Management Data -->
                                                        <td>{{ number_format($data['project_management']['orders']) }}</td>
                                                        <td>${{ number_format($data['project_management']['delivery_amount'], 2) }}</td>

                                                        <!-- Special Projects Data -->
                                                        <td>{{ number_format($data['special_projects']['orders']) }}</td>
                                                        <td class="{{ $data['special_projects']['profit'] >= 0 ? 'text-success' : 'text-danger' }}">
                                                            {{ number_format($data['special_projects']['profit'], 2) }}
                                                        </td>

                                                        <!-- Totals -->
                                                        <td><strong>{{ number_format($data['totals']['orders']) }}</strong></td>
                                                        <td><strong>${{ number_format($data['totals']['delivery_amount'], 2) }}</strong></td>
                                                        <td class="{{ $data['totals']['profit'] >= 0 ? 'text-success' : 'text-danger' }}">
                                                            <strong>{{ number_format($data['totals']['profit'], 2) }}</strong>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="7" class="text-center py-4">
                                                            <div class="text-muted">
                                                                <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                                                <p>No profit data found for the selected period.</p>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                            <tfoot>
                                                <tr class="table-dark">
                                                    <td><strong>GRAND TOTALS</strong></td>

                                                    <!-- Project Management Totals -->
                                                    <td><strong>{{ number_format(collect($profitData)->sum('project_management.orders')) }}</strong></td>
                                                    <td><strong>${{ number_format(collect($profitData)->sum('project_management.delivery_amount'), 2) }}</strong></td>

                                                    <!-- Special Projects Totals -->
                                                    <td><strong>{{ number_format(collect($profitData)->sum('special_projects.orders')) }}</strong></td>
                                                    <td class="{{ collect($profitData)->sum('special_projects.profit') >= 0 ? 'text-success' : 'text-danger' }}">
                                                        <strong>{{ number_format(collect($profitData)->sum('special_projects.profit'), 2) }}</strong>
                                                    </td>

                                                    <!-- Grand Totals -->
                                                    <td><strong>{{ number_format($grandTotals['orders']) }}</strong></td>
                                                    <td><strong>${{ number_format($grandTotals['delivery_amount'], 2) }}</strong></td>
                                                    <td class="{{ $grandTotals['profit'] >= 0 ? 'text-success' : 'text-danger' }}">
                                                        <strong>{{ number_format($grandTotals['profit'], 2) }}</strong>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection
