@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">Project Details</h4>
                                <div class="page-title-right">
                                    <a href="{{ route('special-projects.edit', $specialProject->id) }}" class="btn btn-warning">
                                        <i class="fas fa-edit"></i> Edit Project
                                    </a>
                                    <a href="{{ route('special-projects.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Projects
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Project Information -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Project Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Project Name</label>
                                                <p class="form-control-plaintext">{{ $specialProject->project_name }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Status</label>
                                                <div>
                                                    <span class="{{ $specialProject->status_badge_class }}">
                                                        {{ ucfirst(str_replace('_', ' ', $specialProject->status)) }}
                                                    </span>
                                                    @if($specialProject->is_overdue)
                                                        <span class="badge bg-danger ms-2">Overdue</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    @if($specialProject->description)
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Description</label>
                                            <p class="form-control-plaintext">{{ $specialProject->description }}</p>
                                        </div>
                                    @endif

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Marketplace</label>
                                                <p class="form-control-plaintext">{{ $specialProject->marketplace->order_account_name ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Assigned By</label>
                                                <p class="form-control-plaintext">{{ $specialProject->assignedBy->user->name ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Order Date</label>
                                                <p class="form-control-plaintext">{{ $specialProject->formatted_order_date }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Delivery Date</label>
                                                <p class="form-control-plaintext">{{ $specialProject->formatted_delivery_date }}</p>
                                            </div>
                                        </div>
                                    </div>

                                    @if($specialProject->notes)
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Notes</label>
                                            <p class="form-control-plaintext">{{ $specialProject->notes }}</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Financial Summary -->
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Financial Summary</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6 mb-3">
                                            <div class="border rounded p-3">
                                                <h6 class="text-muted mb-1">Order Amount</h6>
                                                <h4 class="text-primary mb-0">${{ number_format($specialProject->order_amount, 2) }}</h4>
                                                <small class="text-muted">Local: {{ number_format($specialProject->order_amount_local, 2) }}TK</small>
                                            </div>
                                        </div>
                                        <div class="col-6 mb-3">
                                            <div class="border rounded p-3">
                                                <h6 class="text-muted mb-1">Delivery Amount</h6>
                                                <h4 class="text-success mb-0">
                                                    @if($specialProject->delivery_amount)
                                                        ${{ number_format($specialProject->delivery_amount, 2) }}
                                                        <br><small class="text-muted">Local: {{ number_format($specialProject->delivery_amount_local, 2) }}TK</small>
                                                    @else
                                                        <span class="text-muted">-</span>
                                                    @endif
                                                </h4>
                                            </div>
                                        </div>
                                        <div class="col-6 mb-3">
                                            <div class="border rounded p-3">
                                                <h6 class="text-muted mb-1">Order Rate</h6>
                                                <h4 class="text-info mb-0">{{ number_format($specialProject->order_amount_rate, 2) }}TK</h4>
                                                <small class="text-muted">Exchange rate</small>
                                            </div>
                                        </div>
                                        <div class="col-6 mb-3">
                                            <div class="border rounded p-3">
                                                <h6 class="text-muted mb-1">Withdrawn Rate</h6>
                                                <h4 class="text-warning mb-0">{{ number_format($specialProject->withdrawn_amount_rate, 2) }}TK</h4>
                                                <small class="text-muted">Exchange rate</small>
                                            </div>
                                        </div>

                                    </div>

                                    <hr>
                                    <div class="text-center">
                                        <div class="border rounded p-3">
                                            <h6 class="text-muted mb-1">In Hand Amount (BDT)</h6>
                                            <h4 class="{{ (($specialProject->order_amount * $specialProject->order_amount_rate) - ($specialProject->delivery_amount * $specialProject->withdrawn_amount_rate)) >= 0 ? 'text-success' : 'text-danger' }} mb-0">
                                                {{ number_format(($specialProject->order_amount * $specialProject->order_amount_rate) - ($specialProject->delivery_amount * $specialProject->withdrawn_amount_rate), 2) }}
                                            </h4>
                                            <small class="text-muted">(Order Amount × Order Rate) - (Delivery Amount × Withdrawn Rate)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status Update Section (Admin Only) -->
                    @if(auth()->user()->role === 'super_admin' || auth()->user()->role === 'admin')
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Update Project Status</h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="{{ route('special-projects.update-status', $specialProject->id) }}" method="POST">
                                            @csrf
                                            @method('PATCH')

                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="status" class="form-label">Status</label>
                                                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                                            <option value="pending" {{ $specialProject->status == 'pending' ? 'selected' : '' }}>Pending</option>
                                                            <option value="in_progress" {{ $specialProject->status == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                                            <option value="completed" {{ $specialProject->status == 'completed' ? 'selected' : '' }}>Completed</option>
                                                            <option value="cancelled" {{ $specialProject->status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                                        </select>
                                                        @error('status')
                                                            <div class="invalid-feedback">
                                                                {{ $message }}
                                                            </div>
                                                        @enderror
                                                    </div>
                                                </div>

                                            </div>

                                            <div class="mb-3">
                                                <label for="notes" class="form-label">Update Notes</label>
                                                <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="2"
                                                          placeholder="Add any notes about this status update...">{{ $specialProject->notes }}</textarea>
                                                @error('notes')
                                                    <div class="invalid-feedback">
                                                        {{ $message }}
                                                    </div>
                                                @enderror
                                            </div>

                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save"></i> Update Status
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection

@push('scripts')
<style>
    #status {
        display: block !important;
        width: 100% !important;
        padding: 0.375rem 0.75rem !important;
        font-size: 1rem !important;
        font-weight: 400 !important;
        line-height: 1.5 !important;
        color: #212529 !important;
        background-color: #fff !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
        background-repeat: no-repeat !important;
        background-position: right 0.75rem center !important;
        background-size: 16px 12px !important;
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
        appearance: none !important;
        -webkit-appearance: none !important;
        -moz-appearance: none !important;
    }

    #status:focus {
        border-color: #86b7fe !important;
        outline: 0 !important;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
    }

    #status.is-invalid {
        border-color: #dc3545 !important;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const statusSelect = document.getElementById('status');
        if (statusSelect) {
            // Ensure visibility
            statusSelect.style.display = 'block';

            // Add form-select class if missing
            if (!statusSelect.classList.contains('form-select')) {
                statusSelect.classList.add('form-select');
            }

            // Ensure proper styling
            statusSelect.style.width = '100%';
            statusSelect.style.padding = '0.375rem 0.75rem';
            statusSelect.style.fontSize = '1rem';
            statusSelect.style.border = '1px solid #ced4da';
            statusSelect.style.borderRadius = '0.375rem';
            statusSelect.style.backgroundColor = '#fff';
        }
    });
</script>
@endpush
