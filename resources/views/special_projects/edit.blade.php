@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">Edit Special Project</h4>
                                <div class="page-title-right">
                                    <a href="{{ route('special-projects.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Projects
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <form action="{{ route('special-projects.update', $specialProject->id) }}" method="POST" enctype="multipart/form-data">
                                        @csrf
                                        @method('PUT')

                                        <div class="row">
                                            <!-- Project Information -->
                                            <div class="col-md-6">
                                                <h5 class="mb-3">Project Information</h5>

                                                <div class="mb-3">
                                                    <label for="project_name" class="form-label">Project Name <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control @error('project_name') is-invalid @enderror"
                                                           id="project_name" name="project_name"
                                                           value="{{ old('project_name', $specialProject->project_name) }}" required>
                                                    @error('project_name')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="mb-3">
                                                    <label for="description" class="form-label">Description</label>
                                                    <textarea class="form-control @error('description') is-invalid @enderror"
                                                              id="description" name="description" rows="3">{{ old('description', $specialProject->description) }}</textarea>
                                                    @error('description')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="mb-3">
                                                    <label for="marketplace_id" class="form-label">Marketplace <span class="text-danger">*</span></label>
                                                    <select class="form-select @error('marketplace_id') is-invalid @enderror"
                                                            id="marketplace_id" name="marketplace_id" required>
                                                        <option value="">Select Marketplace</option>
                                                        @foreach($marketplaces as $marketplace)
                                                            <option value="{{ $marketplace->id }}"
                                                                {{ old('marketplace_id', $specialProject->marketplace_id) == $marketplace->id ? 'selected' : '' }}
                                                                data-category-info="{{ $marketplace->category ? $marketplace->category->delivery_amount_charge_percentage : '' }}"
                                                                data-loan-info="{{ $marketplace->getTotalActiveLoanChargePercentage() }}">
                                                                {{ $marketplace->order_account_name }}
                                                                @if ($marketplace->category)
                                                                    - {{ $marketplace->category->name }}
                                                                    -
                                                                    {{ number_format($marketplace->category->delivery_amount_charge_percentage, 2) }}%
                                                                @else
                                                                    - No Category
                                                                @endif
                                                                @if($marketplace->getTotalActiveLoanChargePercentage() > 0)
                                                                    - Loan: {{ number_format($marketplace->getTotalActiveLoanChargePercentage(), 2) }}%
                                                                @endif
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('marketplace_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="mb-3">
                                                    <label for="assigned_by" class="form-label">Assigned By <span class="text-danger">*</span></label>
                                                    <select class="form-select @error('assigned_by') is-invalid @enderror"
                                                            id="assigned_by" name="assigned_by" required>
                                                        <option value="">Select Team Member</option>
                                                        @foreach($teams as $team)
                                                            <option value="{{ $team->id }}" {{ old('assigned_by', $specialProject->assigned_by) == $team->id ? 'selected' : '' }}>
                                                                {{ $team->user->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('assigned_by')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>

                                            <!-- Financial Information -->
                                            <div class="col-md-6">
                                                <h5 class="mb-3">Financial Information</h5>

                                                <div class="mb-3">
                                                    <label for="order_amount" class="form-label">Order Amount ($) <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control @error('order_amount') is-invalid @enderror"
                                                           id="order_amount" name="order_amount" step="0.01" min="0"
                                                           value="{{ old('order_amount', $specialProject->order_amount) }}" required>
                                                    @error('order_amount')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="mb-3">
                                                    <label for="delivery_amount" class="form-label">Delivery Amount ($) <span class="text-muted">(Auto-calculated: Order Amount - 20%)</span></label>
                                                    <input type="number" class="form-control @error('delivery_amount') is-invalid @enderror"
                                                           id="delivery_amount" name="delivery_amount" step="0.01" min="0"
                                                           value="{{ old('delivery_amount', $specialProject->delivery_amount) }}" readonly>
                                                    @error('delivery_amount')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <!-- Calculation Display -->
                                                <div class="mb-3">
                                                    <div id="calculation-display"></div>
                                                </div>

                                                <div class="mb-3">
                                                    <label for="order_amount_rate" class="form-label">Order Amount Rate (BDT) <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control @error('order_amount_rate') is-invalid @enderror"
                                                           id="order_amount_rate" name="order_amount_rate" step="0.01" min="0.01"
                                                           value="{{ old('order_amount_rate', $specialProject->order_amount_rate) }}" required>
                                                    <small class="text-muted">Rate to convert USD to BDT for order amount</small>
                                                    @error('order_amount_rate')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="mb-3">
                                                    <label for="withdrawn_amount_rate" class="form-label">Delivery Amount Rate (BDT) <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control @error('withdrawn_amount_rate') is-invalid @enderror"
                                                           id="withdrawn_amount_rate" name="withdrawn_amount_rate" step="0.01" min="0.01"
                                                           value="{{ old('withdrawn_amount_rate', $specialProject->withdrawn_amount_rate) }}" required>
                                                    <small class="text-muted">Rate to convert USD to BDT for delivery amount</small>
                                                    @error('withdrawn_amount_rate')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">In Hand Amount (BDT) <span class="text-muted">(Auto-calculated)</span></label>
                                                    <input type="text" class="form-control" id="calculated_in_hand_amount" readonly>
                                                    <small class="text-muted">Delivery Amount converted to BDT</small>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">Profit/Loss (BDT) <span class="text-muted">(Auto-calculated)</span></label>
                                                    <input type="text" class="form-control" id="calculated_profit_loss" readonly>
                                                    <small class="text-muted">Order Amount BDT - Delivery Amount BDT</small>
                                                </div>
                                            </div>

                                            <!-- Dates and Status -->
                                            <div class="col-12">
                                                <h5 class="mb-3">Timeline & Status</h5>

                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="mb-3">
                                                            <label for="order_date" class="form-label">Order Date <span class="text-danger">*</span></label>
                                                            <input type="date" class="form-control @error('order_date') is-invalid @enderror"
                                                                   id="order_date" name="order_date"
                                                                   value="{{ old('order_date', $specialProject->order_date->format('Y-m-d')) }}" required>
                                                            @error('order_date')
                                                                <div class="invalid-feedback">{{ $message }}</div>
                                                            @enderror
                                                        </div>
                                                    </div>

                                                    <div class="col-md-3">
                                                        <div class="mb-3">
                                                            <label for="delivery_date" class="form-label">Delivery Date</label>
                                                            <input type="date" class="form-control @error('delivery_date') is-invalid @enderror"
                                                                   id="delivery_date" name="delivery_date"
                                                                   value="{{ old('delivery_date', $specialProject->delivery_date ? $specialProject->delivery_date->format('Y-m-d') : '') }}">
                                                            @error('delivery_date')
                                                                <div class="invalid-feedback">{{ $message }}</div>
                                                            @enderror
                                                        </div>
                                                    </div>

                                                    <div class="col-md-3">
                                                        <div class="mb-3">
                                                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                                            <select class="form-select status-select" id="status" name="status" required style="display: block !important;">
                                                                <option value="pending" {{ old('status', $specialProject->status) == 'pending' ? 'selected' : '' }}>Pending</option>
                                                                <option value="in_progress" {{ old('status', $specialProject->status) == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                                                <option value="completed" {{ old('status', $specialProject->status) == 'completed' ? 'selected' : '' }}>Completed</option>
                                                                <option value="cancelled" {{ old('status', $specialProject->status) == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                                            </select>
                                                            @error('status')
                                                                <div class="invalid-feedback">{{ $message }}</div>
                                                            @enderror
                                                        </div>
                                                    </div>

                                                    <div class="col-md-3">
                                                        <div class="mb-3">
                                                            <label for="notes" class="form-label">Notes</label>
                                                            <textarea class="form-control @error('notes') is-invalid @enderror"
                                                                      id="notes" name="notes" rows="1">{{ old('notes', $specialProject->notes) }}</textarea>
                                                            @error('notes')
                                                                <div class="invalid-feedback">{{ $message }}</div>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <div class="d-flex gap-2">
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="fas fa-save"></i> Update Project
                                                    </button>
                                                    <a href="{{ route('special-projects.index') }}" class="btn btn-secondary">
                                                        <i class="fas fa-times"></i> Cancel
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection

@push('scripts')
<style>
.status-select {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const orderAmountInput = document.getElementById('order_amount');
    const deliveryAmountInput = document.getElementById('delivery_amount');
    const orderAmountRateInput = document.getElementById('order_amount_rate');
    const withdrawnAmountRateInput = document.getElementById('withdrawn_amount_rate');
    const calculatedInHandAmountInput = document.getElementById('calculated_in_hand_amount');
    const statusSelect = document.getElementById('status');

    // Ensure status select is visible
    if (statusSelect) {
        statusSelect.style.display = 'block';
        statusSelect.style.visibility = 'visible';
        statusSelect.style.opacity = '1';
    }

    function calculateDeliveryAmount() {
        const orderAmount = parseFloat(orderAmountInput.value) || 0;
        const marketplaceSelect = document.getElementById('marketplace_id');

        if (orderAmount > 0 && marketplaceSelect && marketplaceSelect.value) {
            // Get the selected marketplace account's category delivery charge and loan charge
            const selectedOption = marketplaceSelect.options[marketplaceSelect.selectedIndex];
            const categoryInfo = selectedOption.getAttribute('data-category-info');
            const loanInfo = selectedOption.getAttribute('data-loan-info');

            if (categoryInfo) {
                const deliveryChargePercentage = parseFloat(categoryInfo);
                const loanChargePercentage = loanInfo ? parseFloat(loanInfo) : 0;

                // Calculate delivery charge first
                const deliveryChargeAmount = (orderAmount * deliveryChargePercentage) / 100;
                const afterDelivery = orderAmount - deliveryChargeAmount;

                // Calculate loan charge on the amount after delivery
                const loanChargeAmount = (afterDelivery * loanChargePercentage) / 100;

                // Final amount after both charges
                const finalAmount = orderAmount - deliveryChargeAmount - loanChargeAmount;

                deliveryAmountInput.value = finalAmount.toFixed(2);

                // Update calculation display
                updateCalculationDisplay(orderAmount, deliveryChargePercentage, loanChargePercentage);
            } else {
                deliveryAmountInput.value = '';
                clearCalculationDisplay();
            }
        } else {
            deliveryAmountInput.value = '';
            clearCalculationDisplay();
        }
        calculateInHandAmount();
    }

    function updateCalculationDisplay(orderAmount, deliveryChargePercentage, loanChargePercentage) {
        const calculationDiv = document.getElementById('calculation-display');
        if (!calculationDiv) return;

        const deliveryChargeAmount = (orderAmount * deliveryChargePercentage) / 100;
        const afterDelivery = orderAmount - deliveryChargeAmount;

        // Loan charge is calculated on the amount after delivery
        const loanChargeAmount = (afterDelivery * loanChargePercentage) / 100;

        const totalDeduction = deliveryChargeAmount + loanChargeAmount;
        const finalAmount = orderAmount - totalDeduction;

        calculationDiv.innerHTML = `
            <div class="alert alert-info">
                <h6>Calculation Breakdown:</h6>
                <div class="row">
                    <div class="col-md-3">
                        <strong>Order Amount:</strong><br>
                        $${orderAmount.toFixed(2)}
                    </div>
                    <div class="col-md-3">
                        <strong>Delivery Charge (${deliveryChargePercentage}%):</strong><br>
                        <span class="text-warning">-$${deliveryChargeAmount.toFixed(2)}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>Loan Charge (${loanChargePercentage}%):</strong><br>
                        <span class="text-danger">-$${loanChargeAmount.toFixed(2)}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>Final Amount:</strong><br>
                        <span class="text-success">$${finalAmount.toFixed(2)}</span>
                    </div>
                </div>
            </div>
        `;
    }

    function clearCalculationDisplay() {
        const calculationDiv = document.getElementById('calculation-display');
        if (calculationDiv) {
            calculationDiv.innerHTML = '';
        }
    }

    function calculateInHandAmount() {
        const orderAmount = parseFloat(orderAmountInput.value) || 0;
        const deliveryAmount = parseFloat(deliveryAmountInput.value) || 0;
        const orderAmountRate = parseFloat(orderAmountRateInput.value) || 0;
        const withdrawnAmountRate = parseFloat(withdrawnAmountRateInput.value) || 0;

        // In Hand Amount is the delivery amount converted to BDT
        const calculatedInHand = deliveryAmount * withdrawnAmountRate;
        calculatedInHandAmountInput.value = calculatedInHand.toFixed(2);

        // Calculate Profit/Loss
        const orderAmountBDT = orderAmount * orderAmountRate;
        const deliveryAmountBDT = deliveryAmount * withdrawnAmountRate;
        const profitLoss = orderAmountBDT - deliveryAmountBDT;

        const profitLossInput = document.getElementById('calculated_profit_loss');
        if (profitLossInput) {
            profitLossInput.value = profitLoss.toFixed(2);

            // Visual feedback for profit/loss
            if (profitLoss >= 0) {
                profitLossInput.style.backgroundColor = '#d4edda';
                profitLossInput.style.borderColor = '#c3e6cb';
                profitLossInput.style.color = '#155724';
            } else {
                profitLossInput.style.backgroundColor = '#f8d7da';
                profitLossInput.style.borderColor = '#f5c6cb';
                profitLossInput.style.color = '#721c24';
            }
        }

        // Visual feedback for In Hand Amount
        if (calculatedInHand >= 0) {
            calculatedInHandAmountInput.style.backgroundColor = '#d4edda';
            calculatedInHandAmountInput.style.borderColor = '#c3e6cb';
        } else {
            calculatedInHandAmountInput.style.backgroundColor = '#f8d7da';
            calculatedInHandAmountInput.style.borderColor = '#f5c6cb';
        }
    }

    // Event listeners
    orderAmountInput.addEventListener('input', calculateDeliveryAmount);
    orderAmountRateInput.addEventListener('input', calculateInHandAmount);
    withdrawnAmountRateInput.addEventListener('input', calculateInHandAmount);

    // Also listen for changes in marketplace selection
    const marketplaceSelect = document.getElementById('marketplace_id');
    if (marketplaceSelect) {
        marketplaceSelect.addEventListener('change', calculateDeliveryAmount);
    }

    // Initial calculation
    calculateDeliveryAmount();
});
</script>
@endpush
