@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">Special Projects Management</h4>
                                <div class="page-title-right">
                                    <a href="{{ route('special-projects.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Add New Project
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <form method="GET" action="{{ route('special-projects.index') }}" class="row g-3">
                                        <div class="col-md-2">
                                            <label class="form-label">Search</label>
                                            <input type="text" name="search" class="form-control"
                                                   value="{{ request('search') }}" placeholder="Project name or description">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Status</label>
                                            <select name="status" class="form-select">
                                                <option value="">All Status</option>
                                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                                <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Marketplace</label>
                                            <select name="marketplace_id" class="form-select">
                                                <option value="">All Marketplaces</option>
                                                @foreach($marketplaces as $marketplace)
                                                    <option value="{{ $marketplace->id }}" {{ request('marketplace_id') == $marketplace->id ? 'selected' : '' }}>
                                                        {{ $marketplace->order_account_name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Assigned By</label>
                                            <select name="assigned_by" class="form-select">
                                                <option value="">All Team Members</option>
                                                @foreach($teams as $team)
                                                    <option value="{{ $team->id }}" {{ request('assigned_by') == $team->id ? 'selected' : '' }}>
                                                        {{ $team->user->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Date From</label>
                                            <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Date To</label>
                                            <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-search"></i> Search
                                                </button>
                                                <a href="{{ route('special-projects.index') }}" class="btn btn-secondary">
                                                    <i class="fas fa-times"></i> Clear
                                                </a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Projects Table -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>SL</th>
                                                    <th>Project Name</th>
                                                    <th>Marketplace</th>
                                                    <th>Order Amount ($)</th>
                                                    <th>Profit Amount (BDT)</th>
                                                    <th>Assigned By</th>
                                                    <th>Order Date</th>
                                                    <th>Delivery Date</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($specialProjects as $project)
                                                    <tr>
                                                        <td>{{ $loop->iteration }}</td>
                                                        <td>
                                                            <div>
                                                                <strong>{{ $project->project_name }}</strong>
                                                                @if($project->description)
                                                                    <br><small class="text-muted">{{ Str::limit($project->description, 50) }}</small>
                                                                @endif
                                                            </div>
                                                        </td>
                                                        <td>{{ $project->marketplace->order_account_name ?? 'N/A' }}</td>
                                                        <td>${{ number_format($project->order_amount, 2) }}</td>

                                                        <td>
                                                            <span class="badge {{ (($project->order_amount * $project->order_amount_rate) - ($project->delivery_amount * $project->withdrawn_amount_rate)) >= 0 ? 'bg-success' : 'bg-danger' }}">
                                                                {{ number_format(($project->order_amount * $project->order_amount_rate) - ($project->delivery_amount * $project->withdrawn_amount_rate), 2) }}
                                                            </span>
                                                        </td>
                                                        <td>{{ $project->assignedBy->user->name ?? 'N/A' }}</td>
                                                        <td>{{ $project->formatted_date }}</td>
                                                        <td>{{ $project->formatted_delivery_date }}</td>
                                                        <td>
                                                            <span class="badge {{ $project->status_badge_class }}">
                                                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a href="{{ route('special-projects.show', $project->id) }}"
                                                                   class="btn btn-sm btn-outline-primary" title="View">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                <a href="{{ route('special-projects.edit', $project->id) }}"
                                                                   class="btn btn-sm btn-outline-warning" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <form action="{{ route('special-projects.destroy', $project->id) }}"
                                                                      method="POST" class="d-inline"
                                                                      onsubmit="return confirm('Are you sure you want to delete this project?')">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </form>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="12" class="text-center py-4">
                                                            <div class="text-muted">
                                                                <i class="fas fa-project-diagram fa-3x mb-3"></i>
                                                                <p>No special projects found.</p>
                                                                <a href="{{ route('special-projects.create') }}" class="btn btn-primary">
                                                                    <i class="fas fa-plus"></i> Add Your First Project
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Pagination -->
                                    @if($specialProjects->hasPages())
                                        <div class="d-flex justify-content-center mt-3">
                                            {{ $specialProjects->onEachSide(1)->links('pagination::bootstrap-5') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection
