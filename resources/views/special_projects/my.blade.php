@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">My Special Projects</h4>
                                <div class="page-title-right">
                                    <a href="{{ route('special-projects.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Add New Project
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Total Projects</p>
                                            <h4 class="mb-0">{{ $specialProjects->total() }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-primary align-self-center">
                                                <span class="avatar-title">
                                                    <i class="fas fa-project-diagram font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Pending</p>
                                            <h4 class="mb-0">{{ $specialProjects->where('status', 'pending')->count() }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-warning align-self-center">
                                                <span class="avatar-title">
                                                    <i class="fas fa-clock font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">In Progress</p>
                                            <h4 class="mb-0">{{ $specialProjects->where('status', 'in_progress')->count() }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-info align-self-center">
                                                <span class="avatar-title">
                                                    <i class="fas fa-spinner font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Completed</p>
                                            <h4 class="mb-0">{{ $specialProjects->where('status', 'completed')->count() }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-success align-self-center">
                                                <span class="avatar-title">
                                                    <i class="fas fa-check-circle font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <form method="GET" action="{{ route('special-projects.my') }}" class="row g-3">
                                        <div class="col-md-3">
                                            <label class="form-label">Search</label>
                                            <input type="text" name="search" class="form-control"
                                                   value="{{ request('search') }}" placeholder="Project name or description">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Status</label>
                                            <select name="status" class="form-select">
                                                <option value="">All Status</option>
                                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                                <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Marketplace</label>
                                            <select name="marketplace_id" class="form-select">
                                                <option value="">All Marketplaces</option>
                                                @foreach($marketplaces as $marketplace)
                                                    <option value="{{ $marketplace->id }}" {{ request('marketplace_id') == $marketplace->id ? 'selected' : '' }}>
                                                        {{ $marketplace->order_account_name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Date From</label>
                                            <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Date To</label>
                                            <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                                        </div>
                                        <div class="col-md-1">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                                <a href="{{ route('special-projects.my') }}" class="btn btn-secondary">
                                                    <i class="fas fa-times"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Projects Table -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>SL</th>
                                                    <th>Project Name</th>
                                                    <th>Marketplace</th>
                                                    <th>Order Amount</th>
                                                    <th>Delivery Amount</th>
                                                    <th>Order Rate</th>
                                                    <th>Withdrawn Rate</th>
                                                    <th>Profit Amount (BDT)</th>
                                                    <th>Order Date</th>
                                                    <th>Delivery Date</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($specialProjects as $project)
                                                    <tr>
                                                        <td>{{ $loop->iteration }}</td>
                                                        <td>
                                                            <div>
                                                                <strong>{{ $project->project_name }}</strong>
                                                                @if($project->description)
                                                                    <br><small class="text-muted">{{ Str::limit($project->description, 50) }}</small>
                                                                @endif
                                                            </div>
                                                        </td>
                                                        <td>{{ $project->marketplace->order_account_name ?? 'N/A' }}</td>
                                                        <td>
                                                            ${{ number_format($project->order_amount, 2) }}
                                                            <br><small class="text-muted">Local: ${{ number_format($project->order_amount_local, 2) }}</small>
                                                        </td>
                                                        <td>
                                                            @if($project->delivery_amount)
                                                                ${{ number_format($project->delivery_amount, 2) }}
                                                                <br><small class="text-muted">Local: ${{ number_format($project->delivery_amount_local, 2) }}</small>
                                                            @else
                                                                <span class="text-muted">-</span>
                                                            @endif
                                                        </td>
                                                        <td>{{ number_format($project->order_amount_rate, 2) }}</td>
                                                        <td>{{ number_format($project->withdrawn_amount_rate, 2) }}</td>
                                                        <td>
                                                            <span class="badge {{ (($project->order_amount * $project->order_amount_rate) - ($project->delivery_amount * $project->withdrawn_amount_rate)) >= 0 ? 'bg-success' : 'bg-danger' }}">
                                                                {{ number_format(($project->order_amount * $project->order_amount_rate) - ($project->delivery_amount * $project->withdrawn_amount_rate), 2) }}
                                                            </span>
                                                        </td>
                                                        <td>{{ $project->formatted_order_date }}</td>
                                                        <td>{{ $project->formatted_delivery_date }}</td>
                                                        <td>
                                                            <span class="{{ $project->status_badge_class }}">
                                                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                                            </span>
                                                            @if($project->is_overdue)
                                                                <br><small class="text-danger">Overdue</small>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a href="{{ route('special-projects.show', $project->id) }}"
                                                                   class="btn btn-sm btn-outline-primary" title="View">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                <a href="{{ route('special-projects.edit', $project->id) }}"
                                                                   class="btn btn-sm btn-outline-warning" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="12" class="text-center py-4">
                                                            <div class="text-muted">
                                                                <i class="fas fa-project-diagram fa-3x mb-3"></i>
                                                                <p>No special projects found.</p>
                                                                <a href="{{ route('special-projects.create') }}" class="btn btn-primary">
                                                                    <i class="fas fa-plus"></i> Add Your First Project
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Pagination -->
                                    @if($specialProjects->hasPages())
                                        <div class="d-flex justify-content-center mt-3">
                                            {{ $specialProjects->onEachSide(1)->links('pagination::bootstrap-5') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection
