@extends('layouts.master')

@section('maincontent')
    <!-- Begin page -->
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        <!-- ========== Left Sidebar Start ========== -->
        @include('layouts.sections.menu.sidebar')
        <!-- Left Sidebar End -->
        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">
            <!-- start Page-content -->
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-4">
                                        <h4 class="card-title">Project Details</h4>
                                        <div>
                                            <a href="{{ route('projects.edit', $project->id) }}"
                                                class="btn btn-primary btn-sm me-2">
                                                <i class="fas fa-edit"></i> Edit Project
                                            </a>
                                            <a href="{{ route('projects.index') }}" class="btn btn-secondary btn-sm">
                                                <i class="fas fa-arrow-left"></i> Back to Projects
                                            </a>
                                        </div>
                                    </div>

                                    <!-- Project Overview -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card border shadow-none">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Project Overview</h5>
                                                </div>
                                                <div class="card-body">
                                                    <table class="table table-borderless">
                                                        <tr>
                                                            <th width="30%">Project Name:</th>
                                                            <td>{{ $project->project_name }}</td>
                                                        </tr>
                                                        <tr>
                                                            <th>Client:</th>
                                                            <td>{{ $project->client_name ?? 'N/A' }}</td>
                                                        </tr>
                                                        <tr>
                                                            <th>Status:</th>
                                                            <td>
                                                                <span
                                                                    class="badge bg-{{ $project->status == 'completed' ? 'success' : ($project->status == 'in_revision' ? 'warning' : ($project->status == 'on_hold' ? 'secondary' : ($project->status == 'cancelled' ? 'danger' : 'primary'))) }}">
                                                                    {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>Type:</th>
                                                            <td>{{ ucfirst($project->project_type) }}</td>
                                                        </tr>
                                                        <tr>
                                                            <th>Priority:</th>
                                                            <td>
                                                                <span
                                                                    class="badge bg-{{ $project->project_priority == 'high' ? 'danger' : ($project->project_priority == 'medium' ? 'warning' : 'info') }}">
                                                                    {{ ucfirst($project->project_priority) }}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="card border shadow-none">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Timeline & Budget</h5>
                                                </div>
                                                <div class="card-body">
                                                    <table class="table table-borderless">
                                                        <tr>
                                                            <th width="30%">Budget:</th>
                                                            <td>${{ number_format($project->budget, 2) }}</td>
                                                        </tr>
                                                        <tr>
                                                            <th>Duration:</th>
                                                            <td>{{ $project->duration ? $project->duration->format('M d, Y') : 'N/A' }}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>Remaining:</th>
                                                            <td>
                                                                @php
                                                                    $today = \Carbon\Carbon::today();
                                                                    $endDate = \Carbon\Carbon::parse(
                                                                        $project->duration,
                                                                    );
                                                                    $remaining = $today->diffInDays($endDate, false);
                                                                @endphp
                                                                <span
                                                                    class="{{ $remaining < 0 ? 'text-danger' : ($remaining < 3 ? 'text-warning' : 'text-success') }}">
                                                                    {{ $remaining >= 0 ? $remaining . ' days' : 'Overdue by ' . abs($remaining) . ' days' }}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>Progress:</th>
                                                            <td>
                                                                <div class="progress" style="height: 20px;">
                                                                    <div class="progress-bar bg-{{ $project->progress < 30 ? 'danger' : ($project->progress < 70 ? 'warning' : 'success') }}"
                                                                        role="progressbar"
                                                                        style="width: {{ $project->progress }}%"
                                                                        aria-valuenow="{{ $project->progress }}"
                                                                        aria-valuemin="0" aria-valuemax="100">
                                                                        {{ $project->progress }}%
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>Approved:</th>
                                                            <td>
                                                                <span
                                                                    class="badge bg-{{ $project->is_approved ? 'success' : 'secondary' }}">
                                                                    {{ $project->is_approved ? 'Yes' : 'No' }}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Assignment Information -->
                                    <div class="row mt-4">
                                        <div class="col-md-12">
                                            <div class="card border shadow-none">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Assignment Information</h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <p><strong>Assigned To:</strong>
                                                                {{ $project->assignedTo->name ?? 'Not assigned' }}
                                                            </p>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <p><strong>Assigned By:</strong>
                                                                {{ $project->assignedBy->name ?? 'N/A' }}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Project Details -->
                                    <div class="row mt-4">
                                        <div class="col-md-12">
                                            <div class="card border shadow-none">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Technical Details</h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <p><strong>Theme Name:</strong>
                                                                {{ $project->theme_name ?? 'N/A' }}</p>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <p><strong>Page Builder:</strong>
                                                                {{ $project->page_builder_name ?? 'N/A' }}</p>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <p><strong>Reference Website:</strong>
                                                                @if ($project->reference_website)
                                                                    <a href="{{ $project->reference_website }}"
                                                                        target="_blank">{{ $project->reference_website }}</a>
                                                                @else
                                                                    N/A
                                                                @endif
                                                            </p>
                                                        </div>
                                                    </div>

                                                    <div class="row mt-3">
                                                        <div class="col-md-6">
                                                            <p><strong>Special Plugins:</strong></p>
                                                            <p>{{ $project->special_plugins ?? 'None specified' }}</p>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <p><strong>Special Requirements:</strong></p>
                                                            <p>{{ $project->special_requirements ?? 'None specified' }}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Client Instructions -->
                                    <div class="row mt-4">
                                        <div class="col-md-12">
                                            <div class="card border shadow-none">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Client Instructions</h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-12">
                                                            <div class="instruction-content">
                                                                {!! nl2br(e($project->instruction_from_client ?? 'No instructions provided')) !!}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Login Credentials -->
                                    <div class="row mt-4">
                                        <div class="col-md-12">
                                            <div class="card border shadow-none">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Login Credentials</h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-12">
                                                            <div class="credentials-content">
                                                                {!! nl2br(e($project->login_credentials ?? 'No login credentials provided')) !!}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Notes -->
                                    <div class="row mt-4">
                                        <div class="col-md-12">
                                            <div class="card border shadow-none">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Notes</h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-12">
                                                            <div class="notes-content">
                                                                {!! nl2br(e($project->notes ?? 'No notes available')) !!}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Files Section -->
                                    <div class="row mt-4">
                                        <div class="col-md-12">
                                            <div class="card border shadow-none">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Project Files</h5>
                                                </div>
                                                <div class="card-body">
                                                    <!-- Order Page Screenshots -->
                                                    @if (isset($project->order_page_screenshot) &&
                                                            is_array($project->order_page_screenshot) &&
                                                            count($project->order_page_screenshot) > 0)
                                                        <div class="mb-4">
                                                            <h6 class="text-muted mb-3">Order Page Screenshots</h6>
                                                            <div class="file-previews">
                                                                @foreach ($project->order_page_screenshot as $file)
                                                                    <div class="file-preview-item">
                                                                        @php
                                                                            $extension = pathinfo(
                                                                                $file,
                                                                                PATHINFO_EXTENSION,
                                                                            );
                                                                        @endphp
                                                                        @if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif']))
                                                                            <img src="{{ asset('storage/' . $file) }}"
                                                                                alt="Screenshot" class="img-thumbnail"
                                                                                style="max-height: 100px;">
                                                                        @elseif($extension == 'pdf')
                                                                            <div class="file-icon"><i
                                                                                    class="fas fa-file-pdf fa-2x"></i>
                                                                            </div>
                                                                        @elseif(in_array($extension, ['doc', 'docx']))
                                                                            <div class="file-icon"><i
                                                                                    class="fas fa-file-word fa-2x"></i>
                                                                            </div>
                                                                        @else
                                                                            <div class="file-icon"><i
                                                                                    class="fas fa-file fa-2x"></i></div>
                                                                        @endif
                                                                        <div class="file-name">{{ basename($file) }}</div>
                                                                        <a href="{{ asset('storage/' . $file) }}"
                                                                            target="_blank"
                                                                            class="btn btn-sm btn-info">View</a>
                                                                    </div>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                    @endif

                                                    <!-- Conversation Page Screenshots -->
                                                    @if (isset($project->conversation_page_screenshot) &&
                                                            is_array($project->conversation_page_screenshot) &&
                                                            count($project->conversation_page_screenshot) > 0)
                                                        <div class="mb-4">
                                                            <h6 class="text-muted mb-3">Conversation Page Screenshots</h6>
                                                            <div class="file-previews">
                                                                @foreach ($project->conversation_page_screenshot as $file)
                                                                    <div class="file-preview-item">
                                                                        @php
                                                                            $extension = pathinfo(
                                                                                $file,
                                                                                PATHINFO_EXTENSION,
                                                                            );
                                                                        @endphp
                                                                        @if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif']))
                                                                            <img src="{{ asset('storage/' . $file) }}"
                                                                                alt="Screenshot" class="img-thumbnail"
                                                                                style="max-height: 100px;">
                                                                        @elseif($extension == 'pdf')
                                                                            <div class="file-icon"><i
                                                                                    class="fas fa-file-pdf fa-2x"></i>
                                                                            </div>
                                                                        @elseif(in_array($extension, ['doc', 'docx']))
                                                                            <div class="file-icon"><i
                                                                                    class="fas fa-file-word fa-2x"></i>
                                                                            </div>
                                                                        @else
                                                                            <div class="file-icon"><i
                                                                                    class="fas fa-file fa-2x"></i></div>
                                                                        @endif
                                                                        <div class="file-name">{{ basename($file) }}</div>
                                                                        <a href="{{ asset('storage/' . $file) }}"
                                                                            target="_blank"
                                                                            class="btn btn-sm btn-info">View</a>
                                                                    </div>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                    @endif

                                                    <!-- Additional Files -->
                                                    @if (isset($project->files) && is_array($project->files) && count($project->files) > 0)
                                                        <div>
                                                            <h6 class="text-muted mb-3">Additional Files</h6>
                                                            <div class="file-previews">
                                                                @foreach ($project->files as $file)
                                                                    <div class="file-preview-item">
                                                                        @php
                                                                            $extension = pathinfo(
                                                                                $file,
                                                                                PATHINFO_EXTENSION,
                                                                            );
                                                                        @endphp
                                                                        @if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif']))
                                                                            <img src="{{ asset('storage/' . $file) }}"
                                                                                alt="File" class="img-thumbnail"
                                                                                style="max-height: 100px;">
                                                                        @else
                                                                            <div class="file-icon">
                                                                                @php
                                                                                    $iconClass = 'fa-file';
                                                                                    if ($extension == 'pdf') {
                                                                                        $iconClass = 'fa-file-pdf';
                                                                                    } elseif (
                                                                                        in_array($extension, [
                                                                                            'doc',
                                                                                            'docx',
                                                                                        ])
                                                                                    ) {
                                                                                        $iconClass = 'fa-file-word';
                                                                                    } elseif (
                                                                                        in_array($extension, [
                                                                                            'zip',
                                                                                            'rar',
                                                                                        ])
                                                                                    ) {
                                                                                        $iconClass = 'fa-file-archive';
                                                                                    }
                                                                                @endphp
                                                                                <i
                                                                                    class="fas {{ $iconClass }} fa-2x"></i>
                                                                            </div>
                                                                        @endif
                                                                        <div class="file-name">{{ basename($file) }}</div>
                                                                        <a href="{{ asset('storage/' . $file) }}"
                                                                            target="_blank"
                                                                            class="btn btn-sm btn-info">View</a>
                                                                    </div>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                    @endif

                                                    @if (
                                                        (!isset($project->order_page_screenshot) ||
                                                            !is_array($project->order_page_screenshot) ||
                                                            count($project->order_page_screenshot) == 0) &&
                                                            (!isset($project->conversation_page_screenshot) ||
                                                                !is_array($project->conversation_page_screenshot) ||
                                                                count($project->conversation_page_screenshot) == 0) &&
                                                            (!isset($project->files) || !is_array($project->files) || count($project->files) == 0))
                                                        <p class="text-muted">No files attached to this project.</p>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Project Revisions -->
                                    <div class="row mt-4">
                                        <div class="col-md-12">
                                            <div class="card border shadow-none">
                                                <div
                                                    class="card-header bg-light d-flex justify-content-between align-items-center">
                                                    <h5 class="mb-0">Project Revisions</h5>
                                                    <a href="{{ route('project-revisions.create') }}"
                                                        class="btn btn-sm btn-primary">Add Revision</a>
                                                </div>
                                                <div class="card-body">
                                                    @php
                                                        $revisions = \App\Models\ProjectRevision::where(
                                                            'project_id',
                                                            $project->id,
                                                        )
                                                            ->latest()
                                                            ->get();
                                                    @endphp

                                                    @if ($revisions->count() > 0)
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Date</th>
                                                                        <th>Message</th>
                                                                        <th>File</th>
                                                                        <th>Actions</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    @foreach ($revisions as $revision)
                                                                        <tr>
                                                                            <td>{{ $revision->date ? \Carbon\Carbon::parse($revision->date)->format('M d, Y') : 'N/A' }}
                                                                            </td>
                                                                            <td>{{ $revision->message }}</td>
                                                                            <td>
                                                                                @if ($revision->file)
                                                                                    <a href="{{ asset('storage/' . $revision->file) }}"
                                                                                        target="_blank">Download</a>
                                                                                @else
                                                                                    N/A
                                                                                @endif
                                                                            </td>
                                                                            <td>
                                                                                <a href="{{ route('project-revisions.edit', $revision->id) }}"
                                                                                    class="btn btn-sm btn-warning">Edit</a>
                                                                                <form
                                                                                    action="{{ route('project-revisions.destroy', $revision->id) }}"
                                                                                    method="POST"
                                                                                    style="display:inline;">
                                                                                    @csrf
                                                                                    @method('DELETE')
                                                                                    <button class="btn btn-sm btn-danger"
                                                                                        onclick="return confirm('Delete this revision?')">Delete</button>
                                                                                </form>
                                                                            </td>
                                                                        </tr>
                                                                    @endforeach
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    @else
                                                        <p class="text-muted">No revisions for this project yet.</p>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Page-content -->
            @include('layouts.sections.footer.footer')
        </div>
        <!-- end main content-->
    </div>
    <!-- END layout-wrapper -->

    @include('layouts.sections.menu.rightsidebar')

    <style>
        .file-previews {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .file-preview-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            text-align: center;
            width: 150px;
            background-color: #f9f9f9;
        }

        .file-name {
            margin-top: 8px;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .file-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 80px;
            color: #6c757d;
        }

        .instruction-content,
        .credentials-content,
        .notes-content {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            min-height: 100px;
        }

        .badge {
            text-transform: capitalize;
        }
    </style>

@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Enable tooltips
            $('[data-toggle="tooltip"]').tooltip();

            // Add click handler for file preview images
            $('.file-preview-item img').on('click', function() {
                window.open($(this).parent().find('a').attr('href'), '_blank');
            });
        });
    </script>
@endsection
