<div class="row">
    <div class="col-md-6 mb-3">
        <label for="title">Title <span class="text-danger">*</span></label>
        <input type="text" id="title" name="title" class="form-control"
            value="{{ old('title', $service->title ?? '') }}" required>
    </div>

    <div class="col-md-6 mb-3">
        <label for="slug">Slug (auto-generated)</label>
        <input type="text" id="slug" name="slug" class="form-control"
            value="{{ old('slug', $service->slug ?? '') }}" readonly>
    </div>
</div>

<div class="mb-3">
    <label for="subtitle">Subtitle</label>
    <input type="text" id="subtitle" name="subtitle" class="form-control"
        value="{{ old('subtitle', $service->subtitle ?? '') }}">
</div>

<div class="row">
    <div class="col-md-6 mb-3">
        <label for="image" class="form-label">Picture</label>
        <input type="file" id="image" name="image" class="form-control" accept="image/*"
            @if (!isset($service)) required @endif>
        @if (isset($service) && $service->image)
            <div class="mt-2">
                <img src="{{ asset($service->image) }}" alt="{{ $service->title }}" width="100">
            </div>
        @endif
    </div>

    <div class="col-md-6 mb-3">
        <label for="icon">Icon</label>
        <input type="text" id="icon" name="icon" class="form-control"
            value="{{ old('icon', $service->icon ?? '') }}">
    </div>
</div>

<div class="mb-3">
    <label for="description">Description</label>
    <textarea id="description" name="description" class="form-control">{{ old('description', $service->description ?? '') }}</textarea>
</div>

<div class="mb-3">
    <label for="meta_title">Meta Title</label>
    <input type="text" id="meta_title" name="meta_title" class="form-control"
        value="{{ old('meta_title', $service->meta_title ?? '') }}">
</div>

<div class="mb-3">
    <label for="meta_description">Meta Description</label>
    <textarea id="meta_description" name="meta_description" class="form-control">{{ old('meta_description', $service->meta_description ?? '') }}</textarea>
</div>

<div class="mb-3">
    <label for="meta_keywords">Meta Keywords</label>
    <input type="text" id="meta_keywords" name="meta_keywords" class="form-control"
        value="{{ old('meta_keywords', $service->meta_keywords ?? '') }}">
</div>

{{-- SEO Score Section --}}
@push('scripts')
    <script>
        function slugify(text) {
            return text.toString().toLowerCase()
                .trim()
                .replace(/\s+/g, '-') // Replace spaces with -
                .replace(/[^\w\-]+/g, '') // Remove all non-word chars
                .replace(/\-\-+/g, '-') // Replace multiple - with single -
                .replace(/^-+/, '') // Trim - from start
                .replace(/-+$/, ''); // Trim - from end
        }

        document.addEventListener('DOMContentLoaded', function() {
            const titleInput = document.getElementById('title');
            const slugInput = document.getElementById('slug');
            const metaTitleInput = document.getElementById('meta_title');
            const metaDescInput = document.getElementById('meta_description');

            let userEditedMetaDesc = false;
            let userEditedMetaTitle = false;

            // Detect manual edit in meta description
            metaDescInput.addEventListener('input', function() {
                userEditedMetaDesc = true;
            });

            // Detect manual edit in meta title
            metaTitleInput.addEventListener('input', function() {
                userEditedMetaTitle = true;
            });

            tinymce.init({
                selector: '#description',
                height: 300,
                plugins: [
                    'advlist autolink lists link image charmap print preview anchor',
                    'searchreplace visualblocks code fullscreen',
                    'insertdatetime media table paste code help wordcount'
                ],
                toolbar: 'undo redo | formatselect | bold italic backcolor | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist outdent indent | removeformat | help',
                content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                setup: function(editor) {
                    editor.on('Change KeyUp', function() {
                        if (!userEditedMetaDesc) {
                            const plainText = editor.getContent({
                                format: 'text'
                            }).substring(0, 160);
                            metaDescInput.value = plainText;
                        }
                    });
                }
            });

            titleInput.addEventListener('input', function() {
                const titleVal = titleInput.value;
                slugInput.value = slugify(titleVal);

                // Update meta title only if user has NOT manually edited meta title
                if (!userEditedMetaTitle) {
                    metaTitleInput.value = titleVal;
                }
            });

            const form = document.getElementById('blogForm');
            form.addEventListener('submit', function(e) {
                tinymce.triggerSave();
                const description = document.getElementById('description').value.trim();

                if (description === '') {
                    e.preventDefault();
                    alert('The description field is required.');
                    tinymce.get('description').focus();
                }
            });
        });
    </script>
@endpush
