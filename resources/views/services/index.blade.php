@extends('layouts.master')

@section('maincontent')
    <!-- <body data-layout="horizontal" data-topbar="dark"> -->
    <!-- Begin page -->
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        <!-- ========== Left Sidebar Start ========== -->
        @include('layouts.sections.menu.sidebar')
        <!-- Left Sidebar End -->
        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">
            <!-- start Page-content -->
            <div class="page-content">

                <div class="container-fluid">
                    <h2>Services</h2>

                    {{-- Show "Create New" button only if user has "create" permission --}}
                    @if (auth()->user()->hasPermission('create'))
                        <a href="{{ route('services.create') }}" class="btn btn-primary mb-3">Create New</a>
                    @endif

                    <table class="table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Subtitle</th>
                                <th>Slug</th>
                                <th>SEO Score</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($services as $service)
                                <tr>
                                    <td>{{ $service->title }}</td>
                                    <td>{{ \Illuminate\Support\Str::words($service->subtitle ?? '-', 5, '...') }}</td>

                                    <td>{{ $service->slug }}</td>
                                    <td>
                                        @php
                                            $score = 0;
                                            $metaTitleLength = strlen(trim($service->meta_title ?? ''));
                                            $metaDescLength = strlen(trim($service->meta_description ?? ''));

                                            // Meta Title Scoring
                                            if ($metaTitleLength >= 50 && $metaTitleLength <= 60) {
                                                $score += 50;
                                            } elseif ($metaTitleLength >= 40 && $metaTitleLength <= 70) {
                                                $score += 30;
                                            }

                                            // Meta Description Scoring
                                            if ($metaDescLength >= 120 && $metaDescLength <= 160) {
                                                $score += 50;
                                            } elseif ($metaDescLength >= 100 && $metaDescLength <= 180) {
                                                $score += 30;
                                            }

                                            $score = min($score, 100); // clamp to 100 max

                                            // Determine badge style and label
                                            $badgeClass = 'bg-secondary';
                                            $label = 'Very Poor';

                                            if ($score >= 90) {
                                                $badgeClass = 'bg-success';
                                                $label = 'Excellent';
                                            } elseif ($score >= 75) {
                                                $badgeClass = 'bg-success text-dark';
                                                $label = 'Very Good';
                                            } elseif ($score >= 60) {
                                                $badgeClass = 'bg-info text-dark';
                                                $label = 'Good';
                                            } elseif ($score >= 45) {
                                                $badgeClass = 'bg-warning text-dark';
                                                $label = 'Fair';
                                            } elseif ($score >= 30) {
                                                $badgeClass = 'bg-danger';
                                                $label = 'Poor';
                                            } else {
                                                $badgeClass = 'bg-dark';
                                                $label = 'Very Poor';
                                            }
                                        @endphp
                                        <span class="badge {{ $badgeClass }}">{{ $score }}%
                                            ({{ $label }})</span>
                                    </td>
                                    <td>
                                        {{-- Edit button only if user has "edit" permission --}}
                                        @if (auth()->user()->hasPermission('edit'))
                                            <a href="{{ route('services.edit', $service) }}"
                                                class="btn btn-sm btn-warning">Edit</a>
                                        @endif

                                        {{-- Delete button only if user has "delete" permission --}}
                                        @if (auth()->user()->hasPermission('delete'))
                                            <form action="{{ route('services.destroy', $service) }}" method="POST"
                                                class="d-inline" onsubmit="return confirm('Delete this service?');">
                                                @csrf
                                                @method('DELETE')
                                                <button class="btn btn-sm btn-danger">Delete</button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>

                    </table>
                    {{ $services->onEachSide(1)->links('pagination::bootstrap-5') }}
                </div>

            </div>

            <!-- End Page-content -->
            @include('layouts.sections.footer.footer')

        </div>
        <!-- end main content-->

    </div>
    <!-- END layout-wrapper -->

    @include('layouts.sections.menu.rightsidebar')
@endsection
