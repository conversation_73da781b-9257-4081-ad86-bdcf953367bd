@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">

                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="card-title">Project Details</h4>
                        <div>
                            <a href="{{ route('projects.edit', $project->id) }}" class="btn btn-primary btn-sm me-2">
                                <i class="fas fa-edit"></i> Edit Project
                            </a>
                            <a href="{{ route('projects.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> Back to Projects
                            </a>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">

                            <h3 class="mb-3">{{ $project->title }}</h3>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p><strong>Subtitle:</strong> {{ $project->subtitle ?? '-' }}</p>
                                    <p><strong>User:</strong> {{ $project->user?->name ?? '-' }}</p>
                                    <p><strong>Budget:</strong>
                                        {{ $project->budget ? '$' . number_format($project->budget, 2) : '-' }}</p>
                                    <p><strong>Date:</strong> {{ $project->date ? $project->date->format('Y-m-d') : '-' }}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Description:</strong></p>
                                    <div class="instruction-content">
                                        {!! nl2br(e($project->description)) !!}
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="mb-4">
                                <h5><strong>Images</strong></h5>
                                @if (is_array($project->images) && count($project->images))
                                    <div class="file-previews">
                                        @foreach ($project->images as $img)
                                            <div class="file-preview-item">
                                                <img src="{{ asset($img) }}" alt="Image"
                                                    style="width: 100%; max-height: 120px; object-fit: cover;">
                                                <div class="file-name">{{ basename($img) }}</div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <p>No images available.</p>
                                @endif
                            </div>

                        </div>
                    </div>

                </div>
            </div>

            @include('layouts.sections.footer.footer')
        </div>
    </div>

    @include('layouts.sections.menu.rightsidebar')

    <style>
        .file-previews {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .file-preview-item {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            text-align: center;
            width: 180px;
            background-color: #f9f9f9;
        }

        .file-name {
            margin-top: 8px;
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .instruction-content {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 12px;
            border: 1px solid #e9ecef;
        }
    </style>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            $('[data-toggle="tooltip"]').tooltip();
        });
    </script>
@endsection
