@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')
    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="page-title-box d-flex align-items-center justify-content-between">
                            <h4 class="mb-0">Category Details</h4>
                            <div class="page-title-right">
                                <a href="{{ route('marketplace-account-categories.edit', $category->id) }}" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="{{ route('marketplace-account-categories.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Categories
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Category Information</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td>{{ $category->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Delivery Amount Charge:</strong></td>
                                        <td><span class="badge bg-info">{{ number_format($category->delivery_amount_charge_percentage, 2) }}%</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            @if($category->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-danger">Inactive</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Created:</strong></td>
                                        <td>{{ $category->created_at->format('M d, Y H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Updated:</strong></td>
                                        <td>{{ $category->updated_at->format('M d, Y H:i') }}</td>
                                    </tr>
                                </table>

                                @if($category->description)
                                    <div class="mt-3">
                                        <h6>Description:</h6>
                                        <p class="text-muted">{{ $category->description }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Associated Marketplace Accounts ({{ $category->marketplaceAccounts->count() }})</h5>
                            </div>
                            <div class="card-body">
                                @if($category->marketplaceAccounts->count() > 0)
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Account Name</th>
                                                    <th>Username</th>
                                                    <th>Created Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($category->marketplaceAccounts as $account)
                                                    <tr>
                                                        <td>{{ $account->order_account_name }}</td>
                                                        <td>{{ $account->username }}</td>
                                                        <td>{{ \Carbon\Carbon::parse($account->created_date)->format('M d, Y') }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-store fa-3x mb-3"></i>
                                        <p>No marketplace accounts associated with this category.</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>
@include('layouts.sections.menu.rightsidebar')
@endsection
