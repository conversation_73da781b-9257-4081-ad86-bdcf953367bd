@if ($errors->any())
    <div class="alert alert-danger">
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<div class="row">
    <div class="col-md-6">
        <div class="mb-3">
            <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
            <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror"
                   value="{{ old('name', $marketplaceAccountCategory->name ?? '') }}" required>
            @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <div class="col-md-6">
        <div class="mb-3">
            <label for="delivery_amount_charge_percentage" class="form-label">Delivery Amount Charge (%) <span class="text-danger">*</span></label>
            <div class="input-group">
                <input type="number" name="delivery_amount_charge_percentage" id="delivery_amount_charge_percentage"
                       class="form-control @error('delivery_amount_charge_percentage') is-invalid @enderror"
                       value="{{ old('delivery_amount_charge_percentage', $marketplaceAccountCategory->delivery_amount_charge_percentage ?? '') }}"
                       step="0.01" min="0" max="100" required>
                <span class="input-group-text">%</span>
            </div>
            <small class="text-muted">Enter percentage (0-100) for delivery amount charge</small>
            @error('delivery_amount_charge_percentage')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>

<div class="mb-3">
    <label for="description" class="form-label">Description</label>
    <textarea name="description" id="description" rows="3"
              class="form-control @error('description') is-invalid @enderror">{{ old('description', $marketplaceAccountCategory->description ?? '') }}</textarea>
    @error('description')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>

<div class="mb-3">
    <div class="form-check">
        <input type="checkbox" name="is_active" id="is_active" class="form-check-input" value="1"
               {{ old('is_active', $marketplaceAccountCategory->is_active ?? true) ? 'checked' : '' }}>
        <label for="is_active" class="form-check-label">Active</label>
    </div>
    <small class="text-muted">Inactive categories won't be available for new marketplace accounts</small>
</div>

<div class="d-flex gap-2">
    <button type="submit" class="btn btn-primary">
        <i class="fas fa-save"></i> {{ $buttonText }}
    </button>
    <a href="{{ route('marketplace-account-categories.index') }}" class="btn btn-secondary">
        <i class="fas fa-times"></i> Cancel
    </a>
</div>
