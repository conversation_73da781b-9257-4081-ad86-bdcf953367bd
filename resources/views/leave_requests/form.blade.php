<div class="row">

    {{-- Team Member Info
    <div class="mb-3 col-md-4">
        <label class="form-label"><strong>Team Member:</strong></label>
        <div class="form-control-plaintext">{{ auth()->user()->name }}</div>
        <input type="hidden" name="team_id" value="{{ auth()->user()->team->id ?? '' }}">
    </div> --}}

    {{-- Leave Type --}}
    <div class="mb-3 col-md-4">
        <label for="leave_type" class="form-label">Leave Type</label>
        <select name="leave_type" id="leave_type" class="form-select" required>
            @php $types = ['sick', 'casual', 'earned', 'maternity', 'paternity', 'unpaid']; @endphp
            @foreach($types as $type)
                <option value="{{ $type }}" {{ (old('leave_type', $leaveRequest->leave_type ?? '') == $type) ? 'selected' : '' }}>
                    {{ ucfirst($type) }}
                </option>
            @endforeach
        </select>
        @error('leave_type') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Start Date --}}
    <div class="mb-3 col-md-4">
        <label for="start_date" class="form-label">Start Date</label>
        <input type="date" name="start_date" id="start_date" class="form-control" required
               value="{{ old('start_date', $leaveRequest->start_date ?? '') }}">
        @error('start_date') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- End Date --}}
    <div class="mb-3 col-md-4">
        <label for="end_date" class="form-label">End Date</label>
        <input type="date" name="end_date" id="end_date" class="form-control" required
               value="{{ old('end_date', $leaveRequest->end_date ?? '') }}">
        @error('end_date') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Status (hidden default pending) --}}
    <input type="hidden" name="status" value="pending">

    {{-- Approved By --}}
    <div class="mb-3 col-md-4">
        <label for="approved_by" class="form-label">Approved By</label>
        <select name="approved_by" id="approved_by" class="form-select">
            <option value="">Select Approver (optional)</option>
            @foreach($teams as $team)
                <option value="{{ $team->id }}" {{ (old('approved_by', $leaveRequest->approved_by ?? '') == $team->id) ? 'selected' : '' }}>
                    {{ $team->user->name ?? 'N/A' }}
                </option>
            @endforeach
        </select>
        @error('approved_by') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Reason --}}
    <div class="mb-3 col-md-12">
        <label for="reason" class="form-label">Reason</label>
        <textarea name="reason" id="reason" rows="3" class="form-control"
                  placeholder="Optional">{{ old('reason', $leaveRequest->reason ?? '') }}</textarea>
        @error('reason') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Submit Button --}}


</div>
