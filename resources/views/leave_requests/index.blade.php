@extends('layouts.master')

@section('maincontent')
    <!-- <body data-layout="horizontal" data-topbar="dark"> -->
    <!-- Begin page -->
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        <!-- ========== Left Sidebar Start ========== -->
        @include('layouts.sections.menu.sidebar')
        <!-- Left Sidebar End -->
        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">
            <!-- start Page-content -->
            <div class="page-content">

                <div class="container-fluid">
                    <h1>Leave Requests</h1>
                    <a href="{{ route('leave_requests.create') }}" class="btn btn-primary mb-3">Create Leave Request</a>

                    @if (session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif

                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>SL</th>
                                <th>Team Member</th>
                                <th>Leave Type</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($leaveRequests as $leaveRequest)
                                <tr>
                                    <td>{{ $loop->iteration + ($leaveRequests->currentPage() - 1) * $leaveRequests->perPage() }}</td>
                                    <td>{{ $leaveRequest->team->user->name ?? 'N/A' }}</td>
                                    <td>{{ ucfirst($leaveRequest->leave_type) }}</td>
                                    <td>{{ $leaveRequest->start_date }}</td>
                                    <td>{{ $leaveRequest->end_date }}</td>

                                    <td>
                                        @if ($leaveRequest->status === 'pending')
                                            @if (auth()->user()->hasRole('admin') || auth()->user()->hasRole('super_admin'))
                                                <form action="{{ route('leave_requests.approve', $leaveRequest->id) }}"
                                                    method="POST" style="display:inline;">
                                                    @csrf
                                                    @method('PATCH')
                                                    <button class="btn btn-success btn-sm"
                                                        onclick="return confirm('Approve this leave request?')">Approve</button>
                                                </form>

                                                <form action="{{ route('leave_requests.reject', $leaveRequest->id) }}"
                                                    method="POST" style="display:inline;">
                                                    @csrf
                                                    @method('PATCH')
                                                    <button class="btn btn-danger btn-sm"
                                                        onclick="return confirm('Reject this leave request?')">Reject</button>
                                                </form>
                                            @else
                                                <span class="badge bg-warning">Pending (waiting for admin)</span>
                                            @endif
                                        @else
                                            {{ ucfirst($leaveRequest->status) }}
                                        @endif
                                    </td>

                                </tr>
                            @endforeach

                        </tbody>
                    </table>

                    {{ $leaveRequests->onEachSide(1)->links('pagination::bootstrap-5') }}
                </div>

            </div>

            <!-- End Page-content -->
            @include('layouts.sections.footer.footer')

        </div>
        <!-- end main content-->

    </div>
    <!-- END layout-wrapper -->

    @include('layouts.sections.menu.rightsidebar')
@endsection
