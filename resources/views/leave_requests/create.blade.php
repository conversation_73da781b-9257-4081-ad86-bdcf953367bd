@extends('layouts.master')

@section('maincontent')
    <!-- <body data-layout="horizontal" data-topbar="dark"> -->
    <!-- Begin page -->
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        <!-- ========== Left Sidebar Start ========== -->
        @include('layouts.sections.menu.sidebar')
        <!-- Left Sidebar End -->
        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">
            <!-- start Page-content -->

            <div class="page-content">

                <div class="container-fluid">
                    {{-- error --}}
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                   <h1>{{ isset($leaveRequest) ? 'Edit Leave Request' : 'Create Leave Request' }}</h1>

    <form method="POST" action="{{ isset($leaveRequest) ? route('leave_requests.update', $leaveRequest) : route('leave_requests.store') }}">
        @csrf
        @if(isset($leaveRequest))
            @method('PUT')
        @endif
        @include('leave_requests.form')
                <div class="col-md-12 d-grid">
        <button type="submit" class="btn btn-primary">
            {{ isset($leaveRequest) ? 'Update Leave Request' : 'Submit Leave Request' }}
        </button>
    </div>
    </form>
                </div>

            </div>

            <!-- End Page-content -->
            @include('layouts.sections.footer.footer')

        </div>
        <!-- end main content-->

    </div>
    <!-- END layout-wrapper -->

    @include('layouts.sections.menu.rightsidebar')
@endsection
