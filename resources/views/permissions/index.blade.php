@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Permission Management</h4>
                    <div class="d-flex gap-2">
                        <a href="{{ route('permissions.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add Permission
                        </a>
                        <a href="{{ route('roles.index') }}" class="btn btn-info">
                            <i class="ri-shield-user-line me-1"></i>Manage Roles
                        </a>
                    </div>
                </div>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <!-- Search and Filter Form -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="permissionSearch" class="form-label">Search Permissions</label>
                                <input type="text" id="permissionSearch" class="form-control" placeholder="Search permissions...">
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#assignPermissionModal">
                                    <i class="ri-links-line me-1"></i>Assign Permission
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permissions Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">All Permissions ({{ $permissions->count() }})</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="permissionsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>Permission</th>
                                        <th>Guard</th>
                                        <th>Roles</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($permissions as $permission)
                                        <tr>
                                            <td>{{ $permission->id }}</td>
                                            <td>
                                                <span class="badge bg-primary fs-6">{{ $permission->name }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ $permission->guard_name }}</span>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-wrap gap-1">
                                                    @forelse($permission->roles as $role)
                                                        <span class="badge bg-success">{{ $role->name }}</span>
                                                    @empty
                                                        <span class="text-muted small">No roles</span>
                                                    @endforelse
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('permissions.show', $permission) }}" class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="ri-eye-line"></i>
                                                    </a>
                                                    <a href="{{ route('permissions.edit', $permission) }}" class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="ri-edit-line"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-success assign-permission-btn"
                                                            data-permission-id="{{ $permission->id }}"
                                                            data-permission-name="{{ $permission->name }}"
                                                            title="Assign to Role/User">
                                                        <i class="ri-links-line"></i>
                                                    </button>
                                                    <form action="{{ route('permissions.destroy', $permission) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                onclick="return confirm('Are you sure you want to delete this permission?')"
                                                                title="Delete">
                                                            <i class="ri-delete-bin-line"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="ri-shield-line fs-1"></i>
                                                    <p class="mt-2">No permissions found</p>
                                                    <a href="{{ route('permissions.create') }}" class="btn btn-primary btn-sm">Create First Permission</a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')

<!-- Assign Permission Modal -->
<div class="modal fade" id="assignPermissionModal" tabindex="-1" aria-labelledby="assignPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignPermissionModalLabel">Assign Permission</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="assignPermissionForm">
                    @csrf
                    <input type="hidden" id="selectedPermissionId" name="permission_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="assignType" class="form-label">Assign To</label>
                                <select class="form-select" id="assignType" name="assign_type" required>
                                    <option value="">Select Type</option>
                                    <option value="role">Role</option>
                                    <option value="user">User</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="assignTarget" class="form-label">Select Target</label>
                                <select class="form-select" id="assignTarget" name="target_id" required disabled>
                                    <option value="">Select target...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="assignPermissionBtn">Assign Permission</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.permission-list, .role-list {
    max-height: 100px;
    overflow-y: auto;
}

.role-permissions-list, .user-roles-list {
    scrollbar-width: thin;
}

.role-permissions-list::-webkit-scrollbar, .user-roles-list::-webkit-scrollbar {
    width: 6px;
}

.role-permissions-list::-webkit-scrollbar-track, .user-roles-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.role-permissions-list::-webkit-scrollbar-thumb, .user-roles-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.role-permissions-list::-webkit-scrollbar-thumb:hover, .user-roles-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Permission search functionality
    $('#permissionSearch').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        $('#permissionsTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Assign permission button click
    $('.assign-permission-btn').on('click', function() {
        const permissionId = $(this).data('permission-id');
        const permissionName = $(this).data('permission-name');

        $('#selectedPermissionId').val(permissionId);
        $('#assignPermissionModalLabel').text('Assign Permission: ' + permissionName);
        $('#assignPermissionModal').modal('show');
    });

    // Assign type change
    $('#assignType').on('change', function() {
        const assignType = $(this).val();
        const targetSelect = $('#assignTarget');

        targetSelect.prop('disabled', !assignType);
        targetSelect.html('<option value="">Select target...</option>');

        if (assignType === 'role') {
            @foreach($roles as $role)
                targetSelect.append('<option value="{{ $role->id }}">{{ $role->name }}</option>');
            @endforeach
        } else if (assignType === 'user') {
            @foreach($users as $user)
                targetSelect.append('<option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>');
            @endforeach
        }
    });

    // Assign permission
    $('#assignPermissionBtn').on('click', function() {
        const formData = {
            _token: '{{ csrf_token() }}',
            permission_id: $('#selectedPermissionId').val(),
            assign_type: $('#assignType').val(),
            target_id: $('#assignTarget').val()
        };

        if (!formData.permission_id || !formData.assign_type || !formData.target_id) {
            toastr.error('Please fill in all fields');
            return;
        }

        let url = '';
        if (formData.assign_type === 'role') {
            url = '{{ route("permissions.assign-to-role") }}';
            formData.role_id = formData.target_id;
        } else if (formData.assign_type === 'user') {
            url = '{{ route("permissions.assign-to-user") }}';
            formData.user_id = formData.target_id;
        }

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    $('#assignPermissionModal').modal('hide');
                    location.reload();
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred while assigning permission');
            }
        });
    });
});
</script>
@endpush
