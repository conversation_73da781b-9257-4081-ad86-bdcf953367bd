@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Edit Permission: {{ $permission->name }}</h4>
                    <div class="d-flex gap-2">
                        <a href="{{ route('permissions.index') }}" class="btn btn-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Permissions
                        </a>
                        <a href="{{ route('permissions.show', $permission) }}" class="btn btn-info">
                            <i class="ri-eye-line me-1"></i>View Permission
                        </a>
                    </div>
                </div>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <div class="row">
                    <div class="col-xl-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Permission Details</h5>
                            </div>
                            <div class="card-body">
                                <form action="{{ route('permissions.update', $permission) }}" method="POST">
                                    @csrf
                                    @method('PUT')

                                    <div class="mb-3">
                                        <label for="name" class="form-label">Permission Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror"
                                               id="name" name="name" value="{{ old('name', $permission->name) }}"
                                               placeholder="e.g., manage user, create blog" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">Use descriptive names like "manage user", "create blog", etc.</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="guard_name" class="form-label">Guard Name <span class="text-danger">*</span></label>
                                        <select class="form-select @error('guard_name') is-invalid @enderror"
                                                id="guard_name" name="guard_name" required>
                                            <option value="">Select Guard</option>
                                            <option value="web" {{ old('guard_name', $permission->guard_name) == 'web' ? 'selected' : '' }}>Web</option>
                                            <option value="api" {{ old('guard_name', $permission->guard_name) == 'api' ? 'selected' : '' }}>API</option>
                                        </select>
                                        @error('guard_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ri-save-line me-1"></i>Update Permission
                                        </button>
                                        <a href="{{ route('permissions.index') }}" class="btn btn-secondary">
                                            <i class="ri-arrow-left-line me-1"></i>Back to Permissions
                                        </a>
                                        <a href="{{ route('permissions.show', $permission) }}" class="btn btn-info">
                                            <i class="ri-eye-line me-1"></i>View Permission
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Current Permission Info</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Permission Name:</label>
                                    <div class="fw-bold">{{ $permission->name }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted">Guard:</label>
                                    <div>
                                        <span class="badge bg-secondary">{{ $permission->guard_name }}</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted">Assigned to Roles ({{ $permission->roles->count() }}):</label>
                                    <div class="d-flex flex-wrap gap-1">
                                        @forelse($permission->roles as $role)
                                            <span class="badge bg-success">{{ $role->name }}</span>
                                        @empty
                                            <span class="text-muted small">No roles assigned</span>
                                        @endforelse
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted">Assigned to Users ({{ $permission->users->count() }}):</label>
                                    <div class="d-flex flex-wrap gap-1">
                                        @forelse($permission->users as $user)
                                            <span class="badge bg-info">{{ $user->name }}</span>
                                        @empty
                                            <span class="text-muted small">No users assigned</span>
                                        @endforelse
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created:</label>
                                    <div class="small text-muted">{{ $permission->created_at->format('M d, Y H:i') }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted">Last Updated:</label>
                                    <div class="small text-muted">{{ $permission->updated_at->format('M d, Y H:i') }}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Permission Guidelines</h5>
                            </div>
                            <div class="card-body">
                                <h6>Naming Convention:</h6>
                                <ul class="list-unstyled">
                                    <li><span class="badge bg-primary me-1">manage</span> manage [resource]</li>
                                    <li><span class="badge bg-primary me-1">create</span> create [resource]</li>
                                    <li><span class="badge bg-primary me-1">edit</span> edit [resource]</li>
                                    <li><span class="badge bg-primary me-1">delete</span> delete [resource]</li>
                                    <li><span class="badge bg-primary me-1">show</span> show [resource]</li>
                                </ul>
                                
                                <h6 class="mt-3">Examples:</h6>
                                <ul class="list-unstyled">
                                    <li>• manage user</li>
                                    <li>• create blog</li>
                                    <li>• edit project</li>
                                    <li>• delete expense</li>
                                    <li>• view salary</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
