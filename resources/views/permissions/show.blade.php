@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Permission Details: {{ $permission->name }}</h4>
                    <div class="d-flex gap-2">
                        <a href="{{ route('permissions.index') }}" class="btn btn-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Permissions
                        </a>
                        <a href="{{ route('permissions.edit', $permission) }}" class="btn btn-primary">
                            <i class="ri-edit-line me-1"></i>Edit Permission
                        </a>
                    </div>
                </div>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <div class="row">
                    <!-- Permission Info -->
                    <div class="col-xl-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Permission Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Permission Name:</label>
                                    <div class="fw-bold fs-5">
                                        <span class="badge bg-primary fs-6">{{ $permission->name }}</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted">Guard:</label>
                                    <div>
                                        <span class="badge bg-secondary">{{ $permission->guard_name }}</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted">Created:</label>
                                    <div class="small text-muted">{{ $permission->created_at->format('M d, Y H:i') }}</div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted">Last Updated:</label>
                                    <div class="small text-muted">{{ $permission->updated_at->format('M d, Y H:i') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Assign to Roles -->
                    <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Assign to Roles</h5>
                </div>
                <div class="card-body">
                    <form id="assignToRoleForm">
                        @csrf
                        <input type="hidden" name="permission_id" value="{{ $permission->id }}">

                        <div class="mb-3">
                            <label for="role_id" class="form-label">Select Role</label>
                            <select class="form-select" id="role_id" name="role_id" required>
                                <option value="">Choose a role...</option>
                                @foreach($roles as $role)
                                    <option value="{{ $role->id }}">{{ $role->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <button type="submit" class="btn btn-success w-100">
                            <i class="ri-add-line"></i> Assign to Role
                        </button>
                    </form>

                    <hr>

                    <h6>Currently Assigned Roles:</h6>
                    <div id="assignedRoles">
                        @forelse($permission->roles as $role)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-success">{{ $role->name }}</span>
                                <button class="btn btn-sm btn-outline-danger remove-role-btn"
                                        data-role-id="{{ $role->id }}" data-permission-id="{{ $permission->id }}">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                            </div>
                        @empty
                            <p class="text-muted">No roles assigned</p>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>

                    <!-- Assign to Users -->
                    <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Assign to Users</h5>
                </div>
                <div class="card-body">
                    <form id="assignToUserForm">
                        @csrf
                        <input type="hidden" name="permission_id" value="{{ $permission->id }}">

                        <div class="mb-3">
                            <label for="user_id" class="form-label">Select User</label>
                            <select class="form-select" id="user_id" name="user_id" required>
                                <option value="">Choose a user...</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                @endforeach
                            </select>
                        </div>

                        <button type="submit" class="btn btn-info w-100">
                            <i class="ri-user-add-line"></i> Assign to User
                        </button>
                    </form>

                    <hr>

                    <h6>Currently Assigned Users:</h6>
                    <div id="assignedUsers">
                        @forelse($permission->users as $user)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-info">{{ $user->name }}</span>
                                <button class="btn btn-sm btn-outline-danger remove-user-btn"
                                        data-user-id="{{ $user->id }}" data-permission-id="{{ $permission->id }}">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                            </div>
                        @empty
                            <p class="text-muted">No users assigned</p>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
                </div>
            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Assign permission to role
    $('#assignToRoleForm').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: '{{ route("permissions.assign-to-role") }}',
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    location.reload();
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred');
            }
        });
    });

    // Remove permission from role
    $('.remove-role-btn').on('click', function() {
        const roleId = $(this).data('role-id');
        const permissionId = $(this).data('permission-id');

        if (confirm('Are you sure you want to remove this permission from the role?')) {
            $.ajax({
                url: '{{ route("permissions.remove-from-role") }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    role_id: roleId,
                    permission_id: permissionId
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        location.reload();
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('An error occurred');
                }
            });
        }
    });

    // Assign permission to user
    $('#assignToUserForm').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: '{{ route("permissions.assign-to-user") }}',
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    location.reload();
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred');
            }
        });
    });

    // Remove permission from user
    $('.remove-user-btn').on('click', function() {
        const userId = $(this).data('user-id');
        const permissionId = $(this).data('permission-id');

        if (confirm('Are you sure you want to remove this permission from the user?')) {
            $.ajax({
                url: '{{ route("permissions.remove-from-user") }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    user_id: userId,
                    permission_id: permissionId
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        location.reload();
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('An error occurred');
                }
            });
        }
    });
});
</script>
@endpush
