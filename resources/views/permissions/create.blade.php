@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Create Permission</h4>
                    <a href="{{ route('permissions.index') }}" class="btn btn-secondary">
                        <i class="ri-arrow-left-line me-1"></i>Back to Permissions
                    </a>
                </div>

                <div class="row">
                    <div class="col-xl-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Permission Details</h5>
                            </div>
                            <div class="card-body">
                                <form action="{{ route('permissions.store') }}" method="POST">
                                    @csrf

                                    <div class="mb-3">
                                        <label for="name" class="form-label">Permission Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror"
                                               id="name" name="name" value="{{ old('name') }}"
                                               placeholder="e.g., manage user, create blog" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">Use descriptive names like "manage user", "create blog", etc.</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="guard_name" class="form-label">Guard Name <span class="text-danger">*</span></label>
                                        <select class="form-select @error('guard_name') is-invalid @enderror"
                                                id="guard_name" name="guard_name" required>
                                            <option value="">Select Guard</option>
                                            <option value="web" {{ old('guard_name') == 'web' ? 'selected' : '' }}>Web</option>
                                            <option value="api" {{ old('guard_name') == 'api' ? 'selected' : '' }}>API</option>
                                        </select>
                                        @error('guard_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ri-save-line me-1"></i>Create Permission
                                        </button>
                                        <a href="{{ route('permissions.index') }}" class="btn btn-secondary">
                                            <i class="ri-arrow-left-line me-1"></i>Back to Permissions
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Permission Guidelines</h5>
                            </div>
                            <div class="card-body">
                                <h6>Naming Convention:</h6>
                                <ul class="list-unstyled">
                                    <li><span class="badge bg-primary me-1">manage</span> manage [resource]</li>
                                    <li><span class="badge bg-primary me-1">create</span> create [resource]</li>
                                    <li><span class="badge bg-primary me-1">edit</span> edit [resource]</li>
                                    <li><span class="badge bg-primary me-1">delete</span> delete [resource]</li>
                                    <li><span class="badge bg-primary me-1">show</span> show [resource]</li>
                                </ul>

                                <h6 class="mt-3">Examples:</h6>
                                <ul class="list-unstyled">
                                    <li>• manage user</li>
                                    <li>• create blog</li>
                                    <li>• edit project</li>
                                    <li>• delete expense</li>
                                    <li>• view salary</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
