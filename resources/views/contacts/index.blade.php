@extends('layouts.master')

@section('maincontent')
    <!-- <body data-layout="horizontal" data-topbar="dark"> -->
    <!-- Begin page -->
    <div id="layout-wrapper">

        @include('layouts.sections.navbar.navbar')

        <!-- ========== Left Sidebar Start ========== -->
        @include('layouts.sections.menu.sidebar')
        <!-- Left Sidebar End -->

        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">
            <div class="page-content">

                <h1>Contacts</h1>

                @if (session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Name</th>
                            <th>Service</th>
                            <th>Message</th>
                            <th>Date & Time</th>
                            <th>Actions</th>
                        </tr>
                    </thead>

                    <tbody>
                        @foreach ($contacts as $index => $contact)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $contact->name }}</td>
                                <td>{{ $contact->service->title ?? 'N/A' }}</td>
                                <td>{{ \Illuminate\Support\Str::words($contact->message, 10, '...') }}</td>

                                <td>{{ $contact->created_at->format('M d,  h:i A') }}</td>

                                <td>
                                    @if ($contact->email)
                                        <a href="{{ route('contacts.reply', $contact) }}"
                                            class="btn btn-sm btn-info">Reply</a>
                                    @else
                                        <span class="text-muted">No Email</span>
                                    @endif
                                    <form action="{{ route('contacts.destroy', $contact) }}" method="POST"
                                        style="display:inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger"
                                            onclick="return confirm('Are you sure?')">Delete</button>
                                    </form>
                                </td>

                            </tr>
                        @endforeach
                    </tbody>
                </table>
                {{ $contacts->onEachSide(1)->links('pagination::bootstrap-5') }}

            </div>

            <!-- End Page-content -->
            @include('layouts.sections.footer.footer')

        </div>
        <!-- end main content-->

    </div>
    <!-- END layout-wrapper -->

    @include('layouts.sections.menu.rightsidebar')
@endsection
