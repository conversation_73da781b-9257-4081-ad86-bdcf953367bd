@extends('layouts.master')

@section('maincontent')
    <!-- <body data-layout="horizontal" data-topbar="dark"> -->
    <!-- Begin page -->
    <div id="layout-wrapper">

        @include('layouts.sections.navbar.navbar')

        <!-- ========== Left Sidebar Start ========== -->
        @include('layouts.sections.menu.sidebar')
        <!-- Left Sidebar End -->

        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">
            <div class="page-content">

                <h2>Reply to: {{ $contact->name }} ({{ $contact->email }})</h2>

                <!-- Show existing contact info -->
                <div class="mb-4 p-3 border rounded bg-light">
                    <p><strong>Name:</strong> {{ $contact->name }}</p>
                    <p><strong>Email:</strong> {{ $contact->email }}</p>
                    <p><strong>Phone:</strong> {{ $contact->number ?? 'N/A' }}</p>
                    <p><strong>Service:</strong> {{ $contact->service->title ?? 'N/A' }}</p>
                    <p><strong>Received At:</strong> {{ $contact->created_at->format('M d, Y h:i A') }}</p>
                    @php
                        function formatMessage($text)
                        {
                            // Escape HTML
                            $text = e($text);

                            // Convert "* item" into list items
                            $text = preg_replace('/\* (.+)/', '<li>$1</li>', $text);

                            // Convert line breaks to <br> for spacing
                            $text = nl2br($text);

                            // Wrap list items in <ul> if any exist
                            if (str_contains($text, '<li>')) {
                                $text = preg_replace('/(<li>.*<\/li>)/sU', '<ul>$1</ul>', $text);
                            }

                            return $text;
                        }
                    @endphp

                    <p><strong>Message:</strong></p>
                    <div>{!! formatMessage($contact->message) !!}</div>

                </div>

                <!-- Reply form -->
                <form action="{{ route('contacts.sendReply', $contact) }}" method="POST">
                    @csrf
                    <div class="form-group">
                        <label>Subject</label>
                        <input type="text" name="subject" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label>Message</label>
                        <textarea name="body" rows="6" class="form-control" required></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary mt-2">Send Reply</button>
                </form>

            </div>

            <!-- End Page-content -->
            @include('layouts.sections.footer.footer')

        </div>
        <!-- end main content-->

    </div>
    <!-- END layout-wrapper -->

    @include('layouts.sections.menu.rightsidebar')
@endsection
