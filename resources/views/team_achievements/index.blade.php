@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Team Achievements</h4>
                    <a href="{{ route('team-achievements.create') }}" class="btn btn-primary">Add Achievement</a>
                </div>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="{{ route('team-achievements.index') }}">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="team_id" class="form-label">Team Member</label>
                                    <select name="team_id" id="team_id" class="form-select">
                                        <option value="">All Members</option>
                                        @foreach($teams as $team)
                                            <option value="{{ $team->id }}" {{ request('team_id') == $team->id ? 'selected' : '' }}>
                                                {{ $team->user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="label_id" class="form-label">Label</label>
                                    <select name="label_id" id="label_id" class="form-select">
                                        <option value="">All Labels</option>
                                        @foreach($labels as $label)
                                            <option value="{{ $label->id }}" {{ request('label_id') == $label->id ? 'selected' : '' }}>
                                                {{ $label->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="start_date" class="form-label">Start Date</label>
                                    <input type="date" name="start_date" id="start_date" class="form-control" value="{{ request('start_date') }}">
                                </div>
                                <div class="col-md-2">
                                    <label for="end_date" class="form-label">End Date</label>
                                    <input type="date" name="end_date" id="end_date" class="form-control" value="{{ request('end_date') }}">
                                </div>
                                <div class="col-md-2">
                                    <label for="is_paid" class="form-label">Payment Status</label>
                                    <select name="is_paid" id="is_paid" class="form-select">
                                        <option value="">All</option>
                                        <option value="1" {{ request('is_paid') === '1' ? 'selected' : '' }}>Paid</option>
                                        <option value="0" {{ request('is_paid') === '0' ? 'selected' : '' }}>Unpaid</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">Filter</button>
                                    <a href="{{ route('team-achievements.index') }}" class="btn btn-secondary">Clear</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Team Member</th>
                                        <th>Label</th>
                                        <th>Achieved Amount</th>
                                        <th>Bonus Earned</th>
                                        <th>Achievement Date</th>
                                        <th>Payment Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($achievements as $achievement)
                                        <tr>
                                            <td>{{ $achievement->id }}</td>
                                            <td>{{ $achievement->team->user->name }}</td>
                                            <td>{{ $achievement->label->name }}</td>
                                            <td>${{ number_format($achievement->achieved_amount, 2) }}</td>
                                            <td>${{ number_format($achievement->bonus_earned, 2) }}</td>
                                            <td>{{ $achievement->achievement_date->format('M d, Y') }}</td>
                                            <td>
                                                <span class="badge {{ $achievement->is_paid ? 'bg-success' : 'bg-warning' }}">
                                                    {{ $achievement->is_paid ? 'Paid' : 'Unpaid' }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ route('team-achievements.show', $achievement) }}" class="btn btn-info btn-sm">View</a>
                                                <a href="{{ route('team-achievements.edit', $achievement) }}" class="btn btn-warning btn-sm">Edit</a>
                                                @if(!$achievement->is_paid)
                                                    <form action="{{ route('team-achievements.mark-paid', $achievement) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-success btn-sm">Mark Paid</button>
                                                    </form>
                                                @else
                                                    <form action="{{ route('team-achievements.mark-unpaid', $achievement) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-secondary btn-sm">Mark Unpaid</button>
                                                    </form>
                                                @endif
                                                <form action="{{ route('team-achievements.destroy', $achievement) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button onclick="return confirm('Are you sure?')" class="btn btn-danger btn-sm">Delete</button>
                                                </form>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="8">No achievements found.</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        {{ $achievements->appends(request()->query())->links() }}
                    </div>
                </div>

            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
