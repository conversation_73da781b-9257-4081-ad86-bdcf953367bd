@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Achievement Details</h4>
                    <div>
                        <a href="{{ route('team-achievements.edit', $teamAchievement) }}" class="btn btn-warning">Edit</a>
                        <a href="{{ route('team-achievements.index') }}" class="btn btn-secondary">Back to List</a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Achievement Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Team Member:</strong> {{ $teamAchievement->team->user->name }}</p>
                                        <p><strong>Label:</strong> {{ $teamAchievement->label->name }}</p>
                                        <p><strong>Achieved Amount:</strong> ${{ number_format($teamAchievement->achieved_amount, 2) }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Bonus Earned:</strong> ${{ number_format($teamAchievement->bonus_earned, 2) }}</p>
                                        <p><strong>Achievement Date:</strong> {{ $teamAchievement->achievement_date->format('M d, Y') }}</p>
                                        <p><strong>Payment Status:</strong>
                                            <span class="badge {{ $teamAchievement->is_paid ? 'bg-success' : 'bg-warning' }}">
                                                {{ $teamAchievement->is_paid ? 'Paid' : 'Unpaid' }}
                                            </span>
                                        </p>
                                    </div>
                                </div>

                                @if($teamAchievement->notes)
                                    <div class="mt-3">
                                        <strong>Notes:</strong>
                                        <p class="mt-2">{{ $teamAchievement->notes }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Team Member Info</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Target Amount:</strong> ${{ number_format($teamAchievement->team->target_amount, 2) }}</p>
                                <p><strong>Base Salary:</strong> ${{ number_format($teamAchievement->team->base_salary, 2) }}</p>
                                <p><strong>Total Achieved:</strong> ${{ number_format($teamAchievement->team->achieved_amount, 2) }}</p>
                                <p><strong>Achievement %:</strong> {{ number_format($teamAchievement->team->getAchievementPercentage(), 1) }}%</p>

                                @if($teamAchievement->team->hasAchievedTarget())
                                    <div class="alert alert-success mt-3">
                                        <i class="fas fa-check-circle"></i> Target Achieved!
                                    </div>
                                @else
                                    <div class="alert alert-warning mt-3">
                                        <i class="fas fa-clock"></i> Target Not Yet Achieved
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Label Info</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Bonus Type:</strong>
                                    @if($teamAchievement->label->bonus_amount > 0)
                                        Fixed Amount
                                    @else
                                        Percentage Based
                                    @endif
                                </p>
                                @if($teamAchievement->label->bonus_amount > 0)
                                    <p><strong>Fixed Bonus:</strong> ${{ number_format($teamAchievement->label->bonus_amount, 2) }}</p>
                                @else
                                    <p><strong>Bonus Percentage:</strong> {{ $teamAchievement->label->bonus_percent }}%</p>
                                @endif
                                @if($teamAchievement->label->description)
                                    <p><strong>Description:</strong> {{ $teamAchievement->label->description }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
