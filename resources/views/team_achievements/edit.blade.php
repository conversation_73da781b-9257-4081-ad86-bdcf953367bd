@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Edit Team Achievement</h4>
                    <a href="{{ route('team-achievements.index') }}" class="btn btn-secondary">Back to List</a>
                </div>

                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form action="{{ route('team-achievements.update', $teamAchievement) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="team_id" class="form-label">Team Member</label>
                                <select name="team_id" id="team_id" class="form-select" required>
                                    <option value="">Select Team Member</option>
                                    @foreach($teams as $team)
                                        <option value="{{ $team->id }}" {{ old('team_id', $teamAchievement->team_id) == $team->id ? 'selected' : '' }}>
                                            {{ $team->user->name }} (Target: ${{ number_format($team->target_amount, 2) }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="label_id" class="form-label">Label</label>
                                <select name="label_id" id="label_id" class="form-select" required>
                                    <option value="">Select Label</option>
                                    @foreach($labels as $label)
                                        <option value="{{ $label->id }}" {{ old('label_id', $teamAchievement->label_id) == $label->id ? 'selected' : '' }}>
                                            {{ $label->name }}
                                            (Target: ${{ number_format($label->bonus_target, 2) }} → Bonus: ${{ number_format($label->bonus_amount, 2) }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="achieved_amount" class="form-label">Achieved Amount ($)</label>
                                <input type="number" name="achieved_amount" id="achieved_amount" step="0.01" min="0" class="form-control" value="{{ old('achieved_amount', $teamAchievement->achieved_amount) }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="achievement_date" class="form-label">Achievement Date</label>
                                <input type="date" name="achievement_date" id="achievement_date" class="form-control" value="{{ old('achievement_date', $teamAchievement->achievement_date->format('Y-m-d')) }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes (Optional)</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3">{{ old('notes', $teamAchievement->notes) }}</textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" name="is_paid" id="is_paid" class="form-check-input" value="1" {{ old('is_paid', $teamAchievement->is_paid) ? 'checked' : '' }}>
                            <label for="is_paid" class="form-check-label">Mark as Paid</label>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">Update Achievement</button>
                </form>

            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
