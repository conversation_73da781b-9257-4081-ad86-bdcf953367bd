@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1>Team Member Details</h1>
                        <div>
                            <a href="{{ route('teams.edit', $team) }}" class="btn btn-primary me-2">
                                <i class="ri-edit-line me-1"></i>Edit
                            </a>
                            <a href="{{ route('teams.index') }}" class="btn btn-secondary">
                                <i class="ri-arrow-left-line me-1"></i>Back to List
                            </a>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Basic Information Card -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-user-line me-2"></i>Basic Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        @if ($team->user && $team->user->picture)
                                            <img src="{{ asset($team->user->picture) }}" alt="User Picture" width="80"
                                                height="80" style="border-radius: 50%; object-fit: cover; margin-right: 16px;">
                                        @else
                                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-3"
                                                 style="width: 80px; height: 80px;">
                                                <i class="ri-user-line text-white fs-1"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <h4 class="mb-1">{{ $team->user->name ?? 'N/A' }}</h4>
                                            <p class="text-muted mb-0">{{ $team->user->email ?? 'N/A' }}</p>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-6">
                                            <strong>Designation:</strong><br>
                                            @if($team->designation)
                                                <span class="badge bg-primary">
                                                    <i class="ri-briefcase-line me-1"></i>
                                                    {{ $team->designation->name }}
                                                </span>
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </div>
                                        <div class="col-6">
                                            <strong>Label:</strong><br>
                                            @if($team->label)
                                                <span class="badge bg-info">
                                                    <i class="ri-price-tag-line me-1"></i>
                                                    {{ $team->label->name }}
                                                </span>
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status and Target Card -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-settings-line me-2"></i>Status & Target
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <strong>Status:</strong><br>
                                            @if($team->status === 'active')
                                                <span class="badge bg-success">
                                                    <i class="ri-check-line me-1"></i>
                                                    Active
                                                </span>
                                            @else
                                                <span class="badge bg-danger">
                                                    <i class="ri-close-line me-1"></i>
                                                    Inactive
                                                </span>
                                            @endif
                                        </div>
                                        <div class="col-6">
                                            <strong>Leave Status:</strong><br>
                                            @if($team->leave_status === 'on_duty')
                                                <span class="badge bg-success">
                                                    <i class="ri-user-line me-1"></i>
                                                    On Duty
                                                </span>
                                            @else
                                                <span class="badge bg-warning">
                                                    <i class="ri-time-line me-1"></i>
                                                    On Leave
                                                </span>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <strong>Monthly Target:</strong><br>
                                        @if($team->target)
                                            <span class="fw-bold text-primary fs-4">
                                                <i class="ri-target-line me-1"></i>
                                                ${{ number_format($team->target, 2) }}
                                            </span>
                                        @else
                                            <span class="text-muted">Not set</span>
                                        @endif
                                    </div>

                                    <div>
                                        <strong>Joining Date:</strong><br>
                                        @if($team->joining_date)
                                            <span class="text-success">
                                                <i class="ri-calendar-line me-1"></i>
                                                {{ \Carbon\Carbon::parse($team->joining_date)->format('F d, Y') }}
                                            </span>
                                        @else
                                            <span class="text-muted">Not set</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Off Days Card -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-calendar-line me-2"></i>Off Days Schedule
                                    </h5>
                                </div>
                                <div class="card-body">
                                    @if($team->off_days && count($team->off_days) > 0)
                                        <div class="row">
                                            @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                <div class="col-md-3 col-sm-6 mb-2">
                                                    <div class="d-flex align-items-center">
                                                        @if(in_array($day, $team->off_days))
                                                            <span class="badge bg-warning me-2">
                                                                <i class="ri-calendar-line"></i>
                                                            </span>
                                                            <span class="fw-bold">{{ $day }}</span>
                                                        @else
                                                            <span class="badge bg-success me-2">
                                                                <i class="ri-check-line"></i>
                                                            </span>
                                                            <span class="text-muted">{{ $day }}</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="text-center text-muted">
                                            <i class="ri-calendar-line fs-1"></i>
                                            <p class="mt-2">No off days scheduled</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information Card -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-information-line me-2"></i>Additional Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>Created:</strong><br>
                                            <span class="text-muted">
                                                {{ $team->created_at->format('F d, Y \a\t g:i A') }}
                                            </span>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Last Updated:</strong><br>
                                            <span class="text-muted">
                                                {{ $team->updated_at->format('F d, Y \a\t g:i A') }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>

    @include('layouts.sections.menu.rightsidebar')
@endsection
