     {{-- User Dropdown --}}
    <div class="mb-3">
        <label>User <span class="text-danger">*</span></label>
        <select name="user_id" class="form-control" required>
            <option value="">Select User</option>
            @foreach($users as $user)
                <option value="{{ $user->id }}"
                    {{ old('user_id', $team->user_id ?? '') == $user->id ? 'selected' : '' }}>
                    {{ $user->name }}
                </option>
            @endforeach
        </select>
        <small class="text-muted">Select the user to add to the team</small>
        @error('user_id') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Designation Dropdown --}}
    <div class="mb-3">
        <label>Designation <span class="text-danger">*</span></label>
        <select name="designation_id" class="form-control" required>
            <option value="">Select Designation</option>
            @foreach($designations as $designation)
                <option value="{{ $designation->id }}"
                    {{ old('designation_id', $team->designation_id ?? '') == $designation->id ? 'selected' : '' }}>
                    {{ $designation->name }}
                </option>
            @endforeach
        </select>
        <small class="text-muted">Select the designation for this team member</small>
        @error('designation_id') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Label Dropdown --}}
    <div class="mb-3">
        <label>Label <span class="text-danger">*</span></label>
        <select name="label_id" class="form-control" required>
            <option value="">Select Label</option>
            @foreach($labels as $label)
                <option value="{{ $label->id }}"
                    {{ old('label_id', $team->label_id ?? '') == $label->id ? 'selected' : '' }}>
                    {{ $label->name }}
                </option>
            @endforeach
        </select>
        <small class="text-muted">Select the label for this team member</small>
        @error('label_id') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Joining Date --}}
    <div class="mb-3">
        <label>Joining Date</label>
        <input type="date" name="joining_date"
               value="{{ old('joining_date', $team->joining_date ?? '') }}" class="form-control">
        <small class="text-muted">Date when the team member joined</small>
        @error('joining_date') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Target --}}
    <div class="mb-3">
        <label>Monthly Target ($)</label>
        <div class="input-group">
            <span class="input-group-text">$</span>
            <input type="number" name="target" step="0.01" min="0"
                   value="{{ old('target', $team->target ?? '') }}" class="form-control"
                   placeholder="Enter monthly target amount">
        </div>
        <small class="text-muted">This is the monthly target amount for salary calculation</small>
        @error('target') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Target Amount --}}
    <div class="mb-3">
        <label>Target Amount ($) <span class="text-danger">*</span></label>
        <div class="input-group">
            <span class="input-group-text">$</span>
            <input type="number" name="target_amount" step="0.01" min="0"
                   value="{{ old('target_amount', $team->target_amount ?? '') }}" class="form-control"
                   placeholder="Enter target amount for bonus calculation" required>
        </div>
        <small class="text-muted">This is the target amount that needs to be achieved to get salary and bonus</small>
        @error('target_amount') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Base Salary --}}
    <div class="mb-3">
        <label>Base Salary ($) <span class="text-danger">*</span></label>
        <div class="input-group">
            <span class="input-group-text">$</span>
            <input type="number" name="base_salary" step="0.01" min="0"
                   value="{{ old('base_salary', $team->base_salary ?? '') }}" class="form-control"
                   placeholder="Enter base salary amount" required>
        </div>
        <small class="text-muted">This is the base salary amount that will be paid regardless of achievements</small>
        @error('base_salary') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Off Days --}}
    <div class="mb-3">
        <label>Off Days (Max 2 per week)</label>
        <div class="card">
            <div class="card-body">
                <div class="row">
                    @php
                        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                        $selectedOffDays = old('off_days', $team->off_days ?? []);
                    @endphp
                    @foreach($days as $day)
                        <div class="col-md-3 mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="off_days[]"
                                       value="{{ $day }}" id="off_day_{{ $loop->index }}"
                                       {{ in_array($day, $selectedOffDays) ? 'checked' : '' }}>
                                <label class="form-check-label" for="off_day_{{ $loop->index }}">
                                    <i class="ri-calendar-line me-1"></i>{{ $day }}
                                </label>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        <small class="text-muted">Select up to 2 days when this team member will be off duty</small>
        @error('off_days') <small class="text-danger">{{ $message }}</small> @enderror
        @error('off_days.*') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('input[name="off_days[]"]');
            const maxOffDays = 2;

            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const checkedBoxes = document.querySelectorAll('input[name="off_days[]"]:checked');

                    if (checkedBoxes.length > maxOffDays) {
                        this.checked = false;
                        alert('You can only select maximum 2 off days per week.');
                    }
                });
            });
        });
    </script>

    {{-- Status --}}
    <div class="mb-3">
        <label>Status <span class="text-danger">*</span></label>
        <select name="status" class="form-control" required>
            <option value="">Select Status</option>
            <option value="active" {{ old('status', $team->status ?? '') == 'active' ? 'selected' : '' }}>
                Active
            </option>
            <option value="inactive" {{ old('status', $team->status ?? '') == 'inactive' ? 'selected' : '' }}>
                Inactive
            </option>
        </select>
        <small class="text-muted">Active team members can participate in projects and receive salary</small>
        @error('status') <small class="text-danger">{{ $message }}</small> @enderror
    </div>

    {{-- Leave Status --}}
    <div class="mb-3">
        <label>Leave Status <span class="text-danger">*</span></label>
        <select name="leave_status" class="form-control" required>
            <option value="">Select Leave Status</option>
            <option value="on_duty" {{ old('leave_status', $team->leave_status ?? '') == 'on_duty' ? 'selected' : '' }}>
                On Duty
            </option>
            <option value="on_leave" {{ old('leave_status', $team->leave_status ?? '') == 'on_leave' ? 'selected' : '' }}>
                On Leave
            </option>
        </select>
        <small class="text-muted">Current leave status of the team member</small>
        @error('leave_status') <small class="text-danger">{{ $message }}</small> @enderror
    </div>
