@extends('layouts.master')

@section('title', 'User Details')

@section('content')
<div class="container-fluid">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0 font-size-18">User Details</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('users.index') }}">Users</a></li>
                        <li class="breadcrumb-item active">User Details</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">User Information</h4>
                    <div class="card-tools">
                        <a href="{{ route('users.index') }}" class="btn btn-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Users
                        </a>
                        @if (!$user->hasRole('super_admin'))
                            <a href="{{ route('users.edit', $user) }}" class="btn btn-primary">
                                <i class="ri-edit-line me-1"></i>Edit User
                            </a>
                        @endif
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-4">
                                @if($user->picture && file_exists(public_path($user->picture)))
                                    <img src="{{ asset($user->picture) }}" alt="User Image"
                                         class="rounded-circle border border-4 border-light"
                                         style="width: 150px; height: 150px;">
                                @else
                                    <div class="bg-secondary rounded-circle border border-4 border-light d-flex align-items-center justify-content-center mx-auto" style="width: 150px; height: 150px;">
                                        <i class="ri-user-line text-white" style="font-size: 60px;"></i>
                                    </div>
                                @endif
                                <h5 class="mt-3 mb-1">{{ $user->name }}</h5>
                                <p class="text-muted">{{ $user->email }}</p>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="table-responsive">
                                <table class="table table-borderless">
                                    <tbody>
                                        <tr>
                                            <td class="fw-bold" style="width: 30%;">Name:</td>
                                            <td>{{ $user->name }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Email:</td>
                                            <td>{{ $user->email }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Role:</td>
                                            <td>
                                                <span class="badge bg-{{ $user->hasRole('super_admin') ? 'danger' : ($user->hasRole('admin') ? 'warning' : 'primary') }}">
                                                    {{ ucfirst($user->getRoleNames()->first() ?? 'user') }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Status:</td>
                                            <td>
                                                <span class="badge bg-{{ $user->email_verified_at ? 'success' : 'warning' }}">
                                                    {{ $user->email_verified_at ? 'Verified' : 'Unverified' }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Created:</td>
                                            <td>{{ $user->created_at->format('M d, Y H:i:s') }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Last Updated:</td>
                                            <td>{{ $user->updated_at->format('M d, Y H:i:s') }}</td>
                                        </tr>
                                        @if($user->last_login_at)
                                        <tr>
                                            <td class="fw-bold">Last Login:</td>
                                            <td>{{ $user->last_login_at->format('M d, Y H:i:s') }}</td>
                                        </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    @if($user->getRoleNames()->isNotEmpty())
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Permissions</h5>
                            <div class="row">
                                @foreach($user->getAllPermissions() as $permission)
                                <div class="col-md-3 mb-2">
                                    <span class="badge bg-info">{{ $permission->name }}</span>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
