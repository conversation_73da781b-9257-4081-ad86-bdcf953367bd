<div class="row">
    <div class="mb-3 col-md-4">
        <label>Name</label>
        <input type="text" name="name" value="{{ old('name', $user->name ?? '') }}" class="form-control" required>
    </div>

    <div class="mb-3 col-md-4">
        <label>Email</label>
        <input type="email" name="email" value="{{ old('email', $user->email ?? '') }}" class="form-control" required>
    </div>

    <div class="mb-3 col-md-4">
        <label>Number</label>
        <input type="text" name="number" value="{{ old('number', $user->number ?? '') }}" class="form-control">
    </div>

    <div class="mb-3 col-md-4">
        <label>Picture</label>
        <input type="file" name="picture" class="form-control">
        @if (!empty($user->picture))
            <img src="{{ asset($user->picture) }}" width="100" class="mt-2 d-block">
        @endif
    </div>

    <div class="mb-3 col-md-4">
        <label>Role</label>
        <select name="role" class="form-control" required>
            @foreach (['user', 'affiliate', 'admin', 'super_admin', 'editor', 'developer', 'marketer'] as $role)
                <option value="{{ $role }}" {{ old('role', $user->role ?? '') == $role ? 'selected' : '' }}>
                    {{ ucfirst($role) }}</option>
            @endforeach
        </select>
    </div>

    <div class="mb-3 col-md-4">
        <label>Permission</label>
        <select name="permission" class="form-control" required>
            @foreach (['read', 'write', 'read_write', 'none', 'delete', 'full'] as $permission)
                <option value="{{ $permission }}"
                    {{ old('permission', $user->permission ?? '') == $permission ? 'selected' : '' }}>
                    {{ ucfirst($permission) }}</option>
            @endforeach
        </select>
    </div>
    <div class="mb-3 col-md-4">
        <label>Address</label>
        <textarea name="address" class="form-control">{{ old('address', $user->address ?? '') }}</textarea>
    </div>
    <div class="mb-3 col-md-4">
        <label>Password</label>
        <input type="password" name="password" class="form-control" {{ isset($user) ? '' : 'required' }}>
    </div>

    <div class="mb-3 col-md-4">
        <label>Confirm Password</label>
        <input type="password" name="password_confirmation" class="form-control" {{ isset($user) ? '' : 'required' }}>
    </div>
</div>
