@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <!-- Header -->
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div>
                                <h4 class="mb-1">Audit Logs</h4>
                                <p class="text-muted mb-0">Monitor all user activities and system changes</p>
                            </div>
                            <div>
                                <a href="{{ route('audit-logs.statistics') }}" class="btn btn-outline-info me-2">
                                    <i class="ri-bar-chart-line me-1"></i>Statistics
                                </a>
                                <a href="{{ route('audit-logs.export', request()->query()) }}" class="btn btn-outline-success me-2">
                                    <i class="ri-download-line me-1"></i>Export
                                </a>
                                @if(auth()->user()->hasRole('super_admin'))
                                    <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#cleanModal">
                                        <i class="ri-delete-bin-line me-1"></i>Clean Old Logs
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="{{ route('audit-logs.index') }}">
                            <div class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label">Action</label>
                                    <select name="action" class="form-select">
                                        <option value="">All Actions</option>
                                        @foreach($actions as $action)
                                            <option value="{{ $action }}" {{ request('action') == $action ? 'selected' : '' }}>
                                                {{ ucfirst($action) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label class="form-label">User</label>
                                    <select name="user_id" class="form-select">
                                        <option value="">All Users</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label class="form-label">Model Type</label>
                                    <select name="model_type" class="form-select">
                                        <option value="">All Models</option>
                                        @foreach($modelTypes as $modelType)
                                            <option value="{{ $modelType }}" {{ request('model_type') == $modelType ? 'selected' : '' }}>
                                                {{ class_basename($modelType) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label class="form-label">Start Date</label>
                                    <input type="date" name="start_date" class="form-control" value="{{ request('start_date') }}">
                                </div>

                                <div class="col-md-2">
                                    <label class="form-label">End Date</label>
                                    <input type="date" name="end_date" class="form-control" value="{{ request('end_date') }}">
                                </div>

                                <div class="col-md-2">
                                    <label class="form-label">Search</label>
                                    <input type="text" name="search" class="form-control" placeholder="Search description..." value="{{ request('search') }}">
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="ri-search-line me-1"></i>Filter
                                    </button>
                                    <a href="{{ route('audit-logs.index') }}" class="btn btn-outline-secondary">
                                        <i class="ri-refresh-line me-1"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Audit Logs Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Action</th>
                                        <th>Model</th>
                                        <th>Description</th>
                                        <th>IP Address</th>
                                        <th>Date & Time</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($logs as $log)
                                        <tr>
                                            <td>{{ $log->id }}</td>
                                            <td>
                                                @if($log->user)
                                                    <div class="d-flex align-items-center">
                                                        @if($log->user->picture && file_exists(public_path($log->user->picture)))
                                                            <img src="{{ asset($log->user->picture) }}" alt="User Image"
                                                                width="32" height="32" style="border-radius: 50%;" class="me-2">
                                                        @else
                                                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                                                <i class="ri-user-line text-white" style="font-size: 14px;"></i>
                                                            </div>
                                                        @endif
                                                        <div>
                                                            <div class="fw-bold">{{ $log->user->name }}</div>
                                                            <small class="text-muted">{{ $log->user->email }}</small>
                                                        </div>
                                                    </div>
                                                @else
                                                    <span class="text-muted">System</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $log->action_badge_color }}">
                                                    <i class="{{ $log->action_icon }} me-1"></i>
                                                    {{ ucfirst($log->action) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($log->model_type)
                                                    <div>
                                                        <strong>{{ class_basename($log->model_type) }}</strong>
                                                        @if($log->model_name)
                                                            <br><small class="text-muted">{{ $log->model_name }}</small>
                                                        @endif
                                                    </div>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 300px;" title="{{ $log->formatted_description }}">
                                                    {{ $log->formatted_description }}
                                                </div>
                                            </td>
                                            <td>
                                                <small class="text-muted">{{ $log->ip_address ?? '-' }}</small>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ $log->created_at->format('M d, Y') }}</strong>
                                                    <br><small class="text-muted">{{ $log->created_at->format('H:i:s') }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="{{ route('audit-logs.show', $log) }}" class="btn btn-sm btn-outline-primary" title="View Details">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <span class="btn btn-sm btn-outline-secondary ms-1" title="Audit logs cannot be deleted">
                                                    <i class="ri-lock-line"></i>
                                                </span>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="ri-file-list-line fs-1"></i>
                                                    <p class="mt-2">No audit logs found</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        {{ $logs->withQueryString()->links('pagination::bootstrap-5') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Clean Old Logs Modal -->
<div class="modal fade" id="cleanModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Clean Old Audit Logs</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('audit-logs.clean') }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Delete logs older than (days)</label>
                        <select name="days" class="form-select">
                            <option value="30">30 days</option>
                            <option value="60">60 days</option>
                            <option value="90" selected>90 days</option>
                            <option value="180">180 days</option>
                            <option value="365">1 year</option>
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <i class="ri-alert-line me-1"></i>
                        This action cannot be undone. Old audit logs will be permanently deleted.
                        <br><strong>Note:</strong> Only super admin can perform this action. Minimum retention period is 30 days.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Clean Logs</button>
                </div>
            </form>
        </div>
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
