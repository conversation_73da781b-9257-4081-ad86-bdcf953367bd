@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <!-- Header -->
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div>
                                <h4 class="mb-1">Audit Log Details</h4>
                                <p class="text-muted mb-0">Detailed information about this activity</p>
                            </div>
                            <div>
                                <a href="{{ route('audit-logs.index') }}" class="btn btn-outline-secondary">
                                    <i class="ri-arrow-left-line me-1"></i>Back to Logs
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Main Information -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="{{ $auditLog->action_icon }} me-2"></i>
                                    {{ ucfirst($auditLog->action) }} Activity
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Basic Information</h6>
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Action:</strong></td>
                                                <td>
                                                    <span class="badge bg-{{ $auditLog->action_badge_color }}">
                                                        {{ ucfirst($auditLog->action) }}
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Description:</strong></td>
                                                <td>{{ $auditLog->formatted_description }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Date & Time:</strong></td>
                                                <td>
                                                    {{ $auditLog->created_at->format('M d, Y H:i:s') }}
                                                    <br><small class="text-muted">{{ $auditLog->created_at->diffForHumans() }}</small>
                                                </td>
                                            </tr>
                                            @if($auditLog->model_type)
                                                <tr>
                                                    <td><strong>Model Type:</strong></td>
                                                    <td>{{ class_basename($auditLog->model_type) }}</td>
                                                </tr>
                                            @endif
                                            @if($auditLog->model_id)
                                                <tr>
                                                    <td><strong>Model ID:</strong></td>
                                                    <td>{{ $auditLog->model_id }}</td>
                                                </tr>
                                            @endif
                                            @if($auditLog->model_name)
                                                <tr>
                                                    <td><strong>Model Name:</strong></td>
                                                    <td>{{ $auditLog->model_name }}</td>
                                                </tr>
                                            @endif
                                        </table>
                                    </div>

                                    <div class="col-md-6">
                                        <h6>Technical Details</h6>
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>IP Address:</strong></td>
                                                <td>{{ $auditLog->ip_address ?? '-' }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Method:</strong></td>
                                                <td>
                                                    @if($auditLog->method)
                                                        <span class="badge bg-info">{{ $auditLog->method }}</span>
                                                    @else
                                                        -
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>URL:</strong></td>
                                                <td>
                                                    @if($auditLog->url)
                                                        <small class="text-break">{{ $auditLog->url }}</small>
                                                    @else
                                                        -
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>User Agent:</strong></td>
                                                <td>
                                                    @if($auditLog->user_agent)
                                                        <small class="text-break">{{ Str::limit($auditLog->user_agent, 100) }}</small>
                                                    @else
                                                        -
                                                    @endif
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Changes Details -->
                        @if($auditLog->old_values || $auditLog->new_values)
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="ri-git-commit-line me-2"></i>
                                        Changes Made
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        @if($auditLog->old_values)
                                            <div class="col-md-6">
                                                <h6 class="text-danger">Previous Values</h6>
                                                <div class="bg-light p-3 rounded">
                                                    <pre class="mb-0 small">{{ json_encode($auditLog->old_values, JSON_PRETTY_PRINT) }}</pre>
                                                </div>
                                            </div>
                                        @endif

                                        @if($auditLog->new_values)
                                            <div class="col-md-6">
                                                <h6 class="text-success">New Values</h6>
                                                <div class="bg-light p-3 rounded">
                                                    <pre class="mb-0 small">{{ json_encode($auditLog->new_values, JSON_PRETTY_PRINT) }}</pre>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Metadata -->
                        @if($auditLog->metadata)
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="ri-information-line me-2"></i>
                                        Additional Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="bg-light p-3 rounded">
                                        <pre class="mb-0 small">{{ json_encode($auditLog->metadata, JSON_PRETTY_PRINT) }}</pre>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- User Information -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="ri-user-line me-2"></i>
                                    User Information
                                </h5>
                            </div>
                            <div class="card-body">
                                @if($auditLog->user)
                                    <div class="text-center mb-3">
                                        @if($auditLog->user->picture && file_exists(public_path($auditLog->user->picture)))
                                            <img src="{{ asset($auditLog->user->picture) }}" alt="User Image"
                                                width="80" height="80" style="border-radius: 50%;" class="mb-2">
                                        @else
                                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 80px; height: 80px;">
                                                <i class="ri-user-line text-white" style="font-size: 32px;"></i>
                                            </div>
                                        @endif
                                        <h6 class="mb-1">{{ $auditLog->user->name }}</h6>
                                        <p class="text-muted mb-0">{{ $auditLog->user->email }}</p>
                                        <span class="badge bg-primary">{{ ucfirst($auditLog->user->role) }}</span>
                                    </div>

                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>User ID:</strong></td>
                                            <td>{{ $auditLog->user->id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Role:</strong></td>
                                            <td>{{ ucfirst($auditLog->user->role) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Permission:</strong></td>
                                            <td>{{ ucfirst($auditLog->user->permission) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Created:</strong></td>
                                            <td>{{ $auditLog->user->created_at->format('M d, Y') }}</td>
                                        </tr>
                                    </table>
                                @else
                                    <div class="text-center">
                                        <i class="ri-computer-line fs-1 text-muted"></i>
                                        <p class="text-muted mt-2">System Activity</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Related Model -->
                        @if($auditLog->model_type && $auditLog->model_id)
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="ri-file-list-line me-2"></i>
                                        Related Record
                                    </h5>
                                </div>
                                <div class="card-body">
                                    @php
                                        $model = $auditLog->model;
                                    @endphp

                                    @if($model)
                                        <div class="alert alert-info">
                                            <i class="ri-information-line me-1"></i>
                                            This record still exists in the system.
                                        </div>

                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Type:</strong></td>
                                                <td>{{ class_basename($auditLog->model_type) }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>ID:</strong></td>
                                                <td>{{ $auditLog->model_id }}</td>
                                            </tr>
                                            @if($auditLog->model_name)
                                                <tr>
                                                    <td><strong>Name:</strong></td>
                                                    <td>{{ $auditLog->model_name }}</td>
                                                </tr>
                                            @endif
                                        </table>
                                    @else
                                        <div class="alert alert-warning">
                                            <i class="ri-alert-line me-1"></i>
                                            This record may have been deleted or no longer exists.
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
