@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <!-- Header -->
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div>
                                <h4 class="mb-1">Audit Log Statistics</h4>
                                <p class="text-muted mb-0">Activity overview and trends</p>
                            </div>
                            <div>
                                <a href="{{ route('audit-logs.index') }}" class="btn btn-outline-secondary">
                                    <i class="ri-arrow-left-line me-1"></i>Back to Logs
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Time Period Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="{{ route('audit-logs.statistics') }}">
                            <div class="row align-items-end">
                                <div class="col-md-3">
                                    <label class="form-label">Time Period</label>
                                    <select name="days" class="form-select">
                                        <option value="7" {{ $days == 7 ? 'selected' : '' }}>Last 7 days</option>
                                        <option value="30" {{ $days == 30 ? 'selected' : '' }}>Last 30 days</option>
                                        <option value="90" {{ $days == 90 ? 'selected' : '' }}>Last 90 days</option>
                                        <option value="180" {{ $days == 180 ? 'selected' : '' }}>Last 6 months</option>
                                        <option value="365" {{ $days == 365 ? 'selected' : '' }}>Last year</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-refresh-line me-1"></i>Update
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex">
                                    <div class="flex-1 overflow-hidden">
                                        <p class="text-truncate font-size-14 mb-2">Total Activities</p>
                                        <h4 class="mb-0">{{ number_format($stats['total_logs']) }}</h4>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <div class="avatar-sm rounded-circle bg-primary bg-soft">
                                            <i class="ri-file-list-line font-size-20 text-primary"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex">
                                    <div class="flex-1 overflow-hidden">
                                        <p class="text-truncate font-size-14 mb-2">Active Users</p>
                                        <h4 class="mb-0">{{ $stats['users']->count() }}</h4>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <div class="avatar-sm rounded-circle bg-success bg-soft">
                                            <i class="ri-user-line font-size-20 text-success"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex">
                                    <div class="flex-1 overflow-hidden">
                                        <p class="text-truncate font-size-14 mb-2">Model Types</p>
                                        <h4 class="mb-0">{{ $stats['models']->count() }}</h4>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <div class="avatar-sm rounded-circle bg-info bg-soft">
                                            <i class="ri-database-2-line font-size-20 text-info"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex">
                                    <div class="flex-1 overflow-hidden">
                                        <p class="text-truncate font-size-14 mb-2">Daily Average</p>
                                        <h4 class="mb-0">{{ number_format($stats['total_logs'] / $days, 1) }}</h4>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <div class="avatar-sm rounded-circle bg-warning bg-soft">
                                            <i class="ri-bar-chart-line font-size-20 text-warning"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Daily Activity Chart -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="ri-line-chart-line me-2"></i>
                                    Daily Activity Trend
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="dailyActivityChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Action Distribution -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="ri-pie-chart-line me-2"></i>
                                    Action Distribution
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="actionChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- Top Users -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="ri-user-star-line me-2"></i>
                                    Most Active Users
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Activities</th>
                                                <th>Percentage</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($stats['users']->take(10) as $userStat)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            @if($userStat->user)
                                                                @if($userStat->user->picture && file_exists(public_path($userStat->user->picture)))
                                                                    <img src="{{ asset($userStat->user->picture) }}" alt="User Image"
                                                                        width="24" height="24" style="border-radius: 50%;" class="me-2">
                                                                @else
                                                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px;">
                                                                        <i class="ri-user-line text-white" style="font-size: 12px;"></i>
                                                                    </div>
                                                                @endif
                                                                <div>
                                                                    <div class="fw-bold">{{ $userStat->user->name }}</div>
                                                                    <small class="text-muted">{{ $userStat->user->email }}</small>
                                                                </div>
                                                            @else
                                                                <span class="text-muted">System</span>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary">{{ $userStat->count }}</span>
                                                    </td>
                                                    <td>
                                                        <div class="progress" style="height: 6px;">
                                                            <div class="progress-bar" style="width: {{ ($userStat->count / $stats['total_logs']) * 100 }}%"></div>
                                                        </div>
                                                        <small class="text-muted">{{ number_format(($userStat->count / $stats['total_logs']) * 100, 1) }}%</small>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Model Activity -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="ri-database-2-line me-2"></i>
                                    Model Activity
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Model</th>
                                                <th>Activities</th>
                                                <th>Percentage</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($stats['models']->take(10) as $modelStat)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="ri-file-list-line me-2 text-primary"></i>
                                                            <span class="fw-bold">{{ class_basename($modelStat->model_type) }}</span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">{{ $modelStat->count }}</span>
                                                    </td>
                                                    <td>
                                                        <div class="progress" style="height: 6px;">
                                                            <div class="progress-bar bg-info" style="width: {{ ($modelStat->count / $stats['total_logs']) * 100 }}%"></div>
                                                        </div>
                                                        <small class="text-muted">{{ number_format(($modelStat->count / $stats['total_logs']) * 100, 1) }}%</small>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Daily Activity Chart
    const dailyCtx = document.getElementById('dailyActivityChart').getContext('2d');
    const dailyData = @json($stats['daily_activity']);

    new Chart(dailyCtx, {
        type: 'line',
        data: {
            labels: dailyData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            }),
            datasets: [{
                label: 'Daily Activities',
                data: dailyData.map(item => item.count),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Action Distribution Chart
    const actionCtx = document.getElementById('actionChart').getContext('2d');
    const actionData = @json($stats['actions']);

    const colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
    ];

    new Chart(actionCtx, {
        type: 'doughnut',
        data: {
            labels: actionData.map(item => item.action.charAt(0).toUpperCase() + item.action.slice(1)),
            datasets: [{
                data: actionData.map(item => item.count),
                backgroundColor: colors.slice(0, actionData.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
});
</script>
@endpush
@endsection
