@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">

                    <!-- Page Title -->
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                                <h4 class="mb-sm-0">My Dashboard</h4>
                                <div class="page-title-right">
                                    <ol class="breadcrumb m-0">
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">User</a></li>
                                        <li class="breadcrumb-item active">My Dashboard</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="ri-check-line me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="ri-error-warning-line me-2"></i>{{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <!-- Welcome Message -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h4 class="mb-1">Welcome back, {{ auth()->user()->name }}!</h4>
                                            <p class="text-muted mb-0">Here's what's happening with your projects and activities today.</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <div class="avatar-lg">
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    <i class="ri-user-line font-size-24"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Personal Statistics Cards -->
                    <div class="row">
                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">My Projects</p>
                                            <h4 class="mb-0">{{ number_format($myCompletedProjects + $myPendingProjects) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-primary align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-briefcase-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-success-subtle text-success">
                                            <i class="mdi mdi-arrow-up me-1"></i>{{ $myCompletedProjects }} Completed
                                        </span>
                                        <p class="text-muted mb-0">{{ $myPendingProjects }} Pending</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">My Salary</p>
                                            <h4 class="mb-0">${{ number_format($myTotalSalary, 2) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-success align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-money-dollar-circle-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-success-subtle text-success">
                                            <i class="mdi mdi-arrow-up me-1"></i>This Month
                                        </span>
                                        <p class="text-muted mb-0">Total salary earned</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">My Expenses</p>
                                            <h4 class="mb-0">${{ number_format($myExpenses, 2) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-warning align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-bank-card-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-warning-subtle text-warning">
                                            <i class="mdi mdi-arrow-up me-1"></i>This Month
                                        </span>
                                        <p class="text-muted mb-0">Total expenses</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Leave Requests</p>
                                            <h4 class="mb-0">{{ number_format($myPendingLeaveRequests) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-info align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-calendar-event-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-info-subtle text-info">
                                            <i class="mdi mdi-arrow-up me-1"></i>Pending
                                        </span>
                                        <p class="text-muted mb-0">Leave requests</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Personal Target vs Achievement Section -->
                    <div class="row">
                        <!-- Personal Target vs Achievement Card -->
                        <div class="col-xl-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h5 class="card-title mb-0">My Target vs Achievement</h5>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <div class="avatar-sm rounded-circle bg-primary align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-target-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-primary mb-1">${{ number_format($myTargetAmount, 2) }}</h4>
                                                    <p class="text-muted mb-0">My Target</p>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-success mb-1">${{ number_format($myAchievedAmount, 2) }}</h4>
                                                    <p class="text-muted mb-0">My Achievement</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="text-muted">Achievement Rate</span>
                                                <span class="text-primary fw-bold">{{ number_format($myAchievementPercentage, 1) }}%</span>
                                            </div>
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-primary" role="progressbar"
                                                     style="width: {{ min($myAchievementPercentage, 100) }}%"
                                                     aria-valuenow="{{ $myAchievementPercentage }}"
                                                     aria-valuemin="0" aria-valuemax="100">
                                                </div>
                                            </div>
                                        </div>

                                        @if($myAchievementPercentage >= 100)
                                            <div class="mt-3">
                                                <span class="badge bg-success-subtle text-success">
                                                    <i class="ri-check-line me-1"></i>Target Achieved!
                                                </span>
                                            </div>
                                        @elseif($myAchievementPercentage >= 75)
                                            <div class="mt-3">
                                                <span class="badge bg-warning-subtle text-warning">
                                                    <i class="ri-time-line me-1"></i>On Track
                                                </span>
                                            </div>
                                        @else
                                            <div class="mt-3">
                                                <span class="badge bg-danger-subtle text-danger">
                                                    <i class="ri-alert-line me-1"></i>Behind Target
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Personal Monthly Target vs Achievement Chart -->
                        <div class="col-xl-8">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h5 class="card-title mb-0">My 6-Month Target vs Achievement Trend</h5>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <div class="avatar-sm rounded-circle bg-info align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-bar-chart-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <canvas id="myTargetAchievementChart" height="300"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance Status -->
                    <div class="row">
                        <div class="col-xl-6">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-4">
                                        <h4 class="card-title mb-0">Today's Attendance</h4>
                                        <div class="text-muted">
                                            <i class="ri-time-line me-1"></i>
                                            <span id="current-time">{{ now()->format('H:i:s') }}</span>
                                        </div>
                                    </div>
                                    @if($myTodayAttendance && $myTodayAttendance->check_in)
                                        <div class="text-center">
                                            <div class="avatar-lg mx-auto mb-3">
                                                <div class="avatar-title bg-success rounded-circle">
                                                    <i class="ri-check-line font-size-24 text-white"></i>
                                                </div>
                                            </div>
                                            <h5 class="text-success">Checked In</h5>
                                            <p class="text-muted">You checked in at {{ $myTodayAttendance->check_in }}</p>
                                            @if($myTodayAttendance->check_out)
                                                <p class="text-muted">You checked out at {{ $myTodayAttendance->check_out }}</p>
                                                <div class="mt-3">
                                                    <span class="badge bg-success">Completed for Today</span>
                                                </div>
                                            @else
                                                <div class="mt-3">
                                                    <form action="{{ route('attendance.check-out') }}" method="POST" style="display: inline;">
                                                        @csrf
                                                        <button type="submit" class="btn btn-warning">
                                                            <i class="ri-logout-box-line me-1"></i>Check Out
                                                        </button>
                                                    </form>
                                                </div>
                                            @endif
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <div class="avatar-lg mx-auto mb-3">
                                                <div class="avatar-title bg-warning rounded-circle">
                                                    <i class="ri-time-line font-size-24 text-white"></i>
                                                </div>
                                            </div>
                                            <h5 class="text-warning">Not Checked In</h5>
                                            <p class="text-muted">You haven't checked in today yet</p>
                                            <div class="mt-3">
                                                <form action="{{ route('attendance.check-in') }}" method="POST" style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="btn btn-success">
                                                        <i class="ri-login-box-line me-1"></i>Check In
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-6">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title mb-4">Monthly Attendance</h4>
                                    <div class="text-center">
                                        <h3 class="text-primary">{{ $myThisMonthAttendance }}</h3>
                                        <p class="text-muted">Days attended this month</p>
                                        <div class="progress mt-3" style="height: 6px;">
                                            @php
                                                $daysInMonth = Carbon\Carbon::now()->daysInMonth;
                                                $attendancePercentage = ($myThisMonthAttendance / $daysInMonth) * 100;
                                            @endphp
                                            <div class="progress-bar bg-primary" role="progressbar"
                                                 style="width: {{ $attendancePercentage }}%"
                                                 aria-valuenow="{{ $attendancePercentage }}"
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <small class="text-muted mt-2 d-block">{{ number_format($attendancePercentage, 1) }}% attendance rate</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- My Projects Row -->
                    <div class="row">
                        <!-- My Project Managements -->
                        <div class="col-xl-6">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-4">
                                        <h4 class="card-title mb-0">My Project Managements</h4>
                                        <div class="flex-shrink-0 ms-auto">
                                            <a href="{{ route('project-managements.my') }}" class="btn btn-sm btn-primary">View All</a>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-centered table-nowrap mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Project</th>
                                                    <th>Status</th>
                                                    <th>Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($myProjectManagements as $project)
                                                    <tr>
                                                        <td>
                                                            <div>
                                                                <h6 class="mb-0">{{ Str::limit($project->project_name, 25) }}</h6>
                                                                <small class="text-muted">{{ $project->client_name }}</small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-{{ $project->status === 'completed' ? 'success' : ($project->status === 'pending' ? 'warning' : 'info') }}-subtle text-{{ $project->status === 'completed' ? 'success' : ($project->status === 'pending' ? 'warning' : 'info') }}">
                                                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                                            </span>
                                                        </td>
                                                        <td>${{ number_format($project->amount, 2) }}</td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="3" class="text-center">No projects assigned yet</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- My Special Projects -->
                        <div class="col-xl-6">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-4">
                                        <h4 class="card-title mb-0">My Special Projects</h4>
                                        <div class="flex-shrink-0 ms-auto">
                                            <a href="{{ route('special-projects.my') }}" class="btn btn-sm btn-primary">View All</a>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-centered table-nowrap mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Project</th>
                                                    <th>Status</th>
                                                    <th>Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($mySpecialProjects as $project)
                                                    <tr>
                                                        <td>
                                                            <div>
                                                                <h6 class="mb-0">{{ Str::limit($project->project_name, 25) }}</h6>
                                                                <small class="text-muted">{{ $project->marketplace->order_account_name ?? 'N/A' }}</small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-{{ $project->status === 'completed' ? 'success' : ($project->status === 'pending' ? 'warning' : 'info') }}-subtle text-{{ $project->status === 'completed' ? 'success' : ($project->status === 'pending' ? 'warning' : 'info') }}">
                                                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                                            </span>
                                                        </td>
                                                        <td>${{ number_format($project->order_amount, 2) }}</td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="3" class="text-center">No special projects assigned yet</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- My Leave Requests -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-4">
                                        <h4 class="card-title mb-0">My Leave Requests</h4>
                                        <div class="flex-shrink-0 ms-auto">
                                            <a href="{{ route('leave_requests.index') }}" class="btn btn-sm btn-primary">View All</a>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-centered table-nowrap mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Leave Type</th>
                                                    <th>Start Date</th>
                                                    <th>End Date</th>
                                                    <th>Status</th>
                                                    <th>Reason</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($myLeaveRequests as $leave)
                                                    <tr>
                                                        <td>
                                                            <h6 class="mb-0">{{ ucfirst($leave->leave_type) }}</h6>
                                                        </td>
                                                        <td>{{ $leave->start_date->format('M d, Y') }}</td>
                                                        <td>{{ $leave->end_date->format('M d, Y') }}</td>
                                                        <td>
                                                            <span class="badge bg-{{ $leave->status === 'approved' ? 'success' : ($leave->status === 'pending' ? 'warning' : 'danger') }}-subtle text-{{ $leave->status === 'approved' ? 'success' : ($leave->status === 'pending' ? 'warning' : 'danger') }}">
                                                                {{ ucfirst($leave->status) }}
                                                            </span>
                                                        </td>
                                                        <td>{{ Str::limit($leave->reason, 30) }}</td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="5" class="text-center">No leave requests found</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title mb-4">Quick Actions</h4>
                                    <div class="row">
                                        <div class="col-md-3">
                                            @if($myTodayAttendance && $myTodayAttendance->check_in && !$myTodayAttendance->check_out)
                                                <form action="{{ route('attendance.check-out') }}" method="POST" style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="btn btn-warning w-100 mb-3">
                                                        <i class="ri-logout-box-line me-2"></i>Check Out
                                                    </button>
                                                </form>
                                            @elseif(!$myTodayAttendance || !$myTodayAttendance->check_in)
                                                <form action="{{ route('attendance.check-in') }}" method="POST" style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="btn btn-success w-100 mb-3">
                                                        <i class="ri-login-box-line me-2"></i>Check In
                                                    </button>
                                                </form>
                                            @else
                                                <button class="btn btn-secondary w-100 mb-3" disabled>
                                                    <i class="ri-check-line me-2"></i>Completed Today
                                                </button>
                                            @endif
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('attendance.my') }}" class="btn btn-outline-primary w-100 mb-3">
                                                <i class="ri-calendar-check-line me-2"></i>View Attendance
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('leave_requests.create') }}" class="btn btn-outline-success w-100 mb-3">
                                                <i class="ri-calendar-event-line me-2"></i>Request Leave
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('expenses.my') }}" class="btn btn-outline-warning w-100 mb-3">
                                                <i class="ri-bank-card-line me-2"></i>Submit Expense
                                            </a>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-3">
                                            <a href="{{ route('salaries.my') }}" class="btn btn-outline-info w-100 mb-3">
                                                <i class="ri-money-dollar-circle-line me-2"></i>View Payslips
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('project-managements.my') }}" class="btn btn-outline-dark w-100 mb-3">
                                                <i class="ri-briefcase-line me-2"></i>My Projects
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('special-projects.my') }}" class="btn btn-outline-secondary w-100 mb-3">
                                                <i class="ri-folder-line me-2"></i>Special Projects
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('duty_shift.my.schedule') }}" class="btn btn-outline-primary w-100 mb-3">
                                                <i class="ri-time-line me-2"></i>Duty Schedule
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection

@push('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live clock
    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    // Update clock every second
    setInterval(updateClock, 1000);
    updateClock(); // Initial update

    // Add loading state to check-in/check-out buttons
    const checkInForm = document.querySelector('form[action*="check-in"]');
    const checkOutForm = document.querySelector('form[action*="check-out"]');

    if (checkInForm) {
        checkInForm.addEventListener('submit', function() {
            const button = this.querySelector('button');
            button.disabled = true;
            button.innerHTML = '<i class="ri-loader-4-line me-2"></i>Checking In...';
        });
    }

    if (checkOutForm) {
        checkOutForm.addEventListener('submit', function() {
            const button = this.querySelector('button');
            button.disabled = true;
            button.innerHTML = '<i class="ri-loader-4-line me-2"></i>Checking Out...';
        });
    }

    // Auto-refresh attendance status every 30 seconds
    setInterval(function() {
        fetch('{{ route("attendance.status") }}')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.log('Attendance status error:', data.error);
                }
            })
            .catch(error => {
                console.log('Error fetching attendance status:', error);
            });
    }, 30000);

    // Personal Target vs Achievement Chart
    const myCtx = document.getElementById('myTargetAchievementChart');
    if (myCtx) {
        const myTargetAchievementChart = new Chart(myCtx.getContext('2d'), {
            type: 'line',
            data: {
                labels: @json($myMonthlyLabels),
                datasets: [{
                    label: 'My Target',
                    data: @json($myMonthlyTargetData),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: 'My Achievement',
                    data: @json($myMonthlyAchievementData),
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.4,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'My Monthly Target vs Achievement Comparison'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }
});
</script>
@endpush
