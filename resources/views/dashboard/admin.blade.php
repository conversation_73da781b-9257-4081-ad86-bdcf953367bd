@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">

                    <!-- Page Title -->
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                                <h4 class="mb-sm-0">Admin Dashboard</h4>
                                <div class="page-title-right">
                                    <ol class="breadcrumb m-0">
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">Admin</a></li>
                                        <li class="breadcrumb-item active">Dashboard</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif

                    <!-- Statistics Cards Row 1 -->
                    <div class="row">
                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Total Users</p>
                                            <h4 class="mb-0">{{ number_format($totalUsers) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-primary align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-user-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-success-subtle text-success">
                                            <i class="mdi mdi-arrow-up me-1"></i>{{ $activeUsers }} Verified
                                        </span>
                                        <p class="text-muted mb-0">From previous period</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Total Projects</p>
                                            <h4 class="mb-0">{{ number_format($totalProjectManagements + $totalSpecialProjects) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-success align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-briefcase-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-success-subtle text-success">
                                            <i class="mdi mdi-arrow-up me-1"></i>{{ $completedProjectManagements + $completedSpecialProjects }} Completed
                                        </span>
                                        <p class="text-muted mb-0">From previous period</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Total Salary</p>
                                            <h4 class="mb-0">${{ number_format($totalSalary, 2) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-warning align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-money-dollar-circle-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-warning-subtle text-warning">
                                            <i class="mdi mdi-arrow-up me-1"></i>This Month
                                        </span>
                                        <p class="text-muted mb-0">Total salary paid</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Total Expenses</p>
                                            <h4 class="mb-0">${{ number_format($totalExpenses, 2) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-danger align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-bank-card-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-danger-subtle text-danger">
                                            <i class="mdi mdi-arrow-up me-1"></i>This Month
                                        </span>
                                        <p class="text-muted mb-0">Total expenses</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards Row 2 -->
                    <div class="row">
                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Pending Projects</p>
                                            <h4 class="mb-0">{{ number_format($pendingProjectManagements + $pendingSpecialProjects) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-info align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-time-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-info-subtle text-info">
                                            <i class="mdi mdi-arrow-up me-1"></i>In Progress: {{ $inProgressProjectManagements }}
                                        </span>
                                        <p class="text-muted mb-0">Projects pending</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Total Loans</p>
                                            <h4 class="mb-0">${{ number_format($totalLoans, 2) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-secondary align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-bank-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-secondary-subtle text-secondary">
                                            <i class="mdi mdi-arrow-up me-1"></i>{{ $pendingLoans }} Pending
                                        </span>
                                        <p class="text-muted mb-0">Approved loans</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Today's Attendance</p>
                                            <h4 class="mb-0">{{ number_format($todayAttendance) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-primary align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-calendar-check-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-primary-subtle text-primary">
                                            <i class="mdi mdi-arrow-up me-1"></i>{{ $thisMonthAttendance }} This Month
                                        </span>
                                        <p class="text-muted mb-0">Attendance records</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6">
                            <div class="card mini-stats-wid">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <p class="text-muted fw-medium">Leave Requests</p>
                                            <h4 class="mb-0">{{ number_format($pendingLeaveRequests) }}</h4>
                                        </div>
                                        <div class="flex-shrink-0 align-self-center">
                                            <div class="mini-stat-icon avatar-sm rounded-circle bg-warning align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-calendar-event-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-warning-subtle text-warning">
                                            <i class="mdi mdi-arrow-up me-1"></i>{{ $approvedLeaveRequests }} Approved
                                        </span>
                                        <p class="text-muted mb-0">Pending requests</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Target vs Achievement Section -->
                    <div class="row">
                        <!-- Target vs Achievement Card -->
                        <div class="col-xl-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h5 class="card-title mb-0">Monthly Target vs Achievement</h5>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <div class="avatar-sm rounded-circle bg-primary align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-target-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-primary mb-1">${{ number_format($totalTargetAmount, 2) }}</h4>
                                                    <p class="text-muted mb-0">Total Target</p>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-success mb-1">${{ number_format($totalAchievedAmount, 2) }}</h4>
                                                    <p class="text-muted mb-0">Total Achieved</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="text-muted">Achievement Rate</span>
                                                <span class="text-primary fw-bold">{{ number_format($achievementPercentage, 1) }}%</span>
                                            </div>
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-primary" role="progressbar"
                                                     style="width: {{ min($achievementPercentage, 100) }}%"
                                                     aria-valuenow="{{ $achievementPercentage }}"
                                                     aria-valuemin="0" aria-valuemax="100">
                                                </div>
                                            </div>
                                        </div>

                                        @if($achievementPercentage >= 100)
                                            <div class="mt-3">
                                                <span class="badge bg-success-subtle text-success">
                                                    <i class="ri-check-line me-1"></i>Target Achieved!
                                                </span>
                                            </div>
                                        @elseif($achievementPercentage >= 75)
                                            <div class="mt-3">
                                                <span class="badge bg-warning-subtle text-warning">
                                                    <i class="ri-time-line me-1"></i>On Track
                                                </span>
                                            </div>
                                        @else
                                            <div class="mt-3">
                                                <span class="badge bg-danger-subtle text-danger">
                                                    <i class="ri-alert-line me-1"></i>Behind Target
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Monthly Target vs Achievement Chart -->
                        <div class="col-xl-8">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h5 class="card-title mb-0">6-Month Target vs Achievement Trend</h5>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <div class="avatar-sm rounded-circle bg-info align-self-center">
                                                <span class="avatar-title">
                                                    <i class="ri-bar-chart-line font-size-24"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <canvas id="targetAchievementChart" height="300"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities Row -->
                    <div class="row">
                        <!-- Recent Project Managements -->
                        <div class="col-xl-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-4">
                                        <h4 class="card-title mb-0">Recent Project Managements</h4>
                                        <div class="flex-shrink-0 ms-auto">
                                            <a href="{{ route('project-managements.index') }}" class="btn btn-sm btn-primary">View All</a>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-centered table-nowrap mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Project</th>
                                                    <th>Status</th>
                                                    <th>Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($recentProjectManagements as $project)
                                                    <tr>
                                                        <td>
                                                            <div>
                                                                <h6 class="mb-0">{{ Str::limit($project->project_name, 20) }}</h6>
                                                                <small class="text-muted">{{ $project->assignedBy->user->name ?? 'N/A' }}</small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-{{ $project->status === 'completed' ? 'success' : ($project->status === 'pending' ? 'warning' : 'info') }}-subtle text-{{ $project->status === 'completed' ? 'success' : ($project->status === 'pending' ? 'warning' : 'info') }}">
                                                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                                            </span>
                                                        </td>
                                                        <td>${{ number_format($project->amount, 2) }}</td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="3" class="text-center">No recent projects</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Special Projects -->
                        <div class="col-xl-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-4">
                                        <h4 class="card-title mb-0">Recent Special Projects</h4>
                                        <div class="flex-shrink-0 ms-auto">
                                            <a href="{{ route('special-projects.index') }}" class="btn btn-sm btn-primary">View All</a>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-centered table-nowrap mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Project</th>
                                                    <th>Status</th>
                                                    <th>Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($recentSpecialProjects as $project)
                                                    <tr>
                                                        <td>
                                                            <div>
                                                                <h6 class="mb-0">{{ Str::limit($project->project_name, 20) }}</h6>
                                                                <small class="text-muted">{{ $project->marketplace->order_account_name ?? 'N/A' }}</small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-{{ $project->status === 'completed' ? 'success' : ($project->status === 'pending' ? 'warning' : 'info') }}-subtle text-{{ $project->status === 'completed' ? 'success' : ($project->status === 'pending' ? 'warning' : 'info') }}">
                                                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                                            </span>
                                                        </td>
                                                        <td>${{ number_format($project->order_amount, 2) }}</td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="3" class="text-center">No recent special projects</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Loans -->
                        <div class="col-xl-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-4">
                                        <h4 class="card-title mb-0">Recent Loans</h4>
                                        <div class="flex-shrink-0 ms-auto">
                                            <a href="{{ route('loans.index') }}" class="btn btn-sm btn-primary">View All</a>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-centered table-nowrap mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Marketplace</th>
                                                    <th>Status</th>
                                                    <th>Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($recentLoans as $loan)
                                                    <tr>
                                                        <td>
                                                            <div>
                                                                <h6 class="mb-0">{{ $loan->marketplace->order_account_name ?? 'N/A' }}</h6>
                                                                <small class="text-muted">{{ $loan->description ? Str::limit($loan->description, 20) : 'No description' }}</small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-{{ $loan->status === 'approved' ? 'success' : ($loan->status === 'pending' ? 'warning' : 'danger') }}-subtle text-{{ $loan->status === 'approved' ? 'success' : ($loan->status === 'pending' ? 'warning' : 'danger') }}">
                                                                {{ ucfirst($loan->status) }}
                                                            </span>
                                                        </td>
                                                        <td>${{ number_format($loan->amount, 2) }}</td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="3" class="text-center">No recent loans</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Statistics Row -->
                    <div class="row">
                        <div class="col-xl-6">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title mb-4">Content Statistics</h4>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="text-center">
                                                <h3 class="text-primary">{{ $totalBlogs }}</h3>
                                                <p class="text-muted">Total Blogs</p>
                                                <small class="text-success">All Published</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="text-center">
                                                <h3 class="text-success">{{ $totalServices }}</h3>
                                                <p class="text-muted">Total Services</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <div class="text-center">
                                                <h3 class="text-info">{{ $totalContacts }}</h3>
                                                <p class="text-muted">Total Contacts</p>
                                                <small class="text-info">Contact Messages</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="text-center">
                                                <h3 class="text-warning">{{ $totalMarketplaceAccounts }}</h3>
                                                <p class="text-muted">Marketplace Accounts</p>
                                                <small class="text-success">{{ $activeMarketplaceAccounts }} Active</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-6">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title mb-4">Team Statistics</h4>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="text-center">
                                                <h3 class="text-primary">{{ $totalTeams }}</h3>
                                                <p class="text-muted">Total Teams</p>
                                                <small class="text-success">{{ $teamsWithMembers }} With Members</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="text-center">
                                                <h3 class="text-success">{{ $totalProjects }}</h3>
                                                <p class="text-muted">Portfolio Projects</p>
                                                <small class="text-info">Portfolio showcase</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <div class="text-center">
                                                <h3 class="text-info">{{ $newUsersThisMonth }}</h3>
                                                <p class="text-muted">New Users This Month</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="text-center">
                                                <h3 class="text-warning">{{ $totalUsers - $activeUsers }}</h3>
                                                <p class="text-muted">Unverified Users</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // Target vs Achievement Chart
        const ctx = document.getElementById('targetAchievementChart').getContext('2d');
        const targetAchievementChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: @json($monthlyLabels),
                datasets: [{
                    label: 'Target',
                    data: @json($monthlyTargetData),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: 'Achievement',
                    data: @json($monthlyAchievementData),
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.4,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Monthly Target vs Achievement Comparison'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    </script>
@endsection
