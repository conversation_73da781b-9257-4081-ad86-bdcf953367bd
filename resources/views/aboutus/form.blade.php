<div class="form-group">
    <label for="project_id">Project</label>
    <select name="project_id" class="form-control" required>
        <option value="">Select Project</option>
        @foreach($projects as $id => $name)
            <option value="{{ $id }}" {{ old('project_id', $projectRevision->project_id ?? '') == $id ? 'selected' : '' }}>
                {{ $name }}
            </option>
        @endforeach
    </select>
</div>

<div class="form-group">
    <label for="date">Revision Date</label>
    <input type="date" class="form-control" name="date" value="{{ old('date', $projectRevision->date ?? '') }}">
</div>

<div class="form-group">
    <label for="message">Message</label>
    <textarea class="form-control" name="message">{{ old('message', $projectRevision->message ?? '') }}</textarea>
</div>

<div class="form-group">
    <label for="file">Upload File</label>
    <input type="file" class="form-control-file" name="file">
    @if(!empty($projectRevision->file))
        <p>Current File: <a href="{{ asset('storage/' . $projectRevision->file) }}" target="_blank">Download</a></p>
    @endif
</div>
