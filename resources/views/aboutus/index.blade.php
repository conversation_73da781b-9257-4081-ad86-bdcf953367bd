@extends('layouts.master')

@section('maincontent')
<!-- <body data-layout="horizontal" data-topbar="dark"> -->
<!-- Begin page -->
<div id="layout-wrapper">
@include('layouts.sections.navbar.navbar')
<!-- ========== Left Sidebar Start ========== -->
@include('layouts.sections.menu.sidebar')
<!-- Left Sidebar End -->
<!-- ============================================================== -->
<!-- Start right Content here -->
<!-- ============================================================== -->
    <div class="main-content">
        <!-- start Page-content -->
        <div class="page-content">



            <div class="container-fluid">
                <h3>Project Revisions</h3>
                <a href="{{ route('project-revisions.create') }}" class="btn btn-primary mb-3">Add Revision</a>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Project</th>
                            <th>Date</th>
                            <th>Message</th>
                            <th>File</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($revisions as $revision)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>{{ $revision->project->project_name ?? 'N/A' }}</td>
                                <td>{{ $revision->date }}</td>
                                <td>{{ $revision->message }}</td>
                                <td>
                                    @if($revision->file)
                                        <a href="{{ asset('storage/' . $revision->file) }}" target="_blank">Download</a>
                                    @else
                                        N/A
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('project-revisions.edit', $revision->id) }}" class="btn btn-sm btn-warning">Edit</a>
                                    <form action="{{ route('project-revisions.destroy', $revision->id) }}" method="POST" style="display:inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button class="btn btn-sm btn-danger" onclick="return confirm('Delete this revision?')">Delete</button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>






        </div>

        <!-- End Page-content -->
@include('layouts.sections.footer.footer')

    </div>
    <!-- end main content-->

</div>
<!-- END layout-wrapper -->

@include('layouts.sections.menu.rightsidebar')

@endsection




