@extends('frontend.layouts.master')
@section('maincontent')

<!--Start Page Header-->
<section class="page-header">
    <div class="page-header__bg" style="background-image: url('{{ asset('frontend/assets/img/bg/page-header-bg.jpg') }}')">
    </div>
    <div class="container">
        <div class="page-header__inner">
            <h2>Services</h2>
            <ul class="thm-breadcrumb">
                <li><a href="{{ url('/') }}">Home</a></li>
                <li><span>/</span></li>
                <li>Services</li>
            </ul>
        </div>
    </div>
</section>
<!--End Page Header-->

<!-- Start Services One -->
<div class="services-one pdt0">
    <div class="container">
        <div class="row">
            @foreach ($services as $service)
                <div class="col-xl-4 col-lg-4 wow animated fadeInUp" data-wow-delay="0.{{ $loop->iteration }}s">
                    <div class="services-one__single">
                        <div class="services-one__single-title">
                            <div class="icon">
                                <span class="{{ $service->icon ?? 'icon-default' }}"></span>
                            </div>
                            <h3><a href="{{ route('serviceDetails', $service->slug) }}">{{ $service->title }}</a></h3>
                        </div>
                        <div class="services-one__single-content">
                            <p>{{ Str::limit(strip_tags($service->subtitle), 100) }}</p>
                            <div class="btn-box">
                                <a class="button-style-2" href="{{ route('serviceDetails', $service->slug) }}">
                                    Read More
                                    <i class="icon-plus-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-4">
            {{ $services->links('pagination::bootstrap-5') }}
        </div>
    </div>
</div>
<!-- End Services One -->

@endsection

