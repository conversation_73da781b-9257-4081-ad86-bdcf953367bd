
    <!--Start Contact One-->
    <section class="contact-one">
        <div class="contact-one__bg" style="background-image: url({{ asset('frontend/assets/img/bg/contact-v1-bg.jpg') }});">
        </div>
        <div class="container">
            <div class="contact-one__inner clearfix">
                <div class="contact-one__form wow fadeInRight" data-wow-delay="200ms" data-wow-duration="1500ms">
                    <div class="section-title tg-heading-subheading animation-style2">
                        <h5 class="tg-element-title">Talt to us</h5>
                        <h2 class="tg-element-title">Unleash the Power of <br> Technology</h2>
                    </div>
                    <form id="contact-form" class="default-form2 contact-form-validated"
                        action="{{ route('contact.store') }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-xl-6 col-lg-6 col-md-6">
                                <div class="input-box">
                                    <input type="text" name="name" placeholder="Your name" required>
                                    <div class="icon"><span class="icon-user"></span></div>
                                </div>
                            </div>
                            <div class="col-xl-6 col-lg-6 col-md-6">
                                <div class="input-box">
                                    <input type="email" name="email" placeholder="Your Email" required>
                                    <div class="icon"><span class="icon-paper-plane"></span></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xl-6 col-lg-6 col-md-6">
                                <div class="input-box">
                                    <input type="text" placeholder="Your Mobile" name="number">
                                    <div class="icon"><span class="icon-call"></span></div>
                                </div>
                            </div>
                            <div class="col-xl-6 col-lg-6 col-md-6">
                                <div class="input-box">
                                    <select name="service_id" class="form-control " required>
                                        <option class="form-control" value="">Select Service</option>
                                        @foreach ($services as $service)
                                            <option value="{{ $service->id }}">{{ $service->title }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>



                        <div class="row">
                            <div class="col-xl-12">
                                <div class="input-box">
                                    <textarea name="message" placeholder="Write Message.."></textarea>
                                    <div class="icon"><span class="icon-envelope"></span></div>
                                </div>
                            </div>

                            <div class="col-xl-12 col-lg-12 col-md-12">
                                <div class="contact-one__form-btn">
                                    <button class="button-style-1" type="submit" data-loading-text="Please wait...">
                                        Send Now
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    <p class="ajax-response mb-0"></p>
                </div>
            </div>
        </div>
    </section>
    <!--End Contact One-->

    {{-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> <!-- ensure jQuery is loaded -->

    <meta name="csrf-token" content="{{ csrf_token() }}"> <!-- ensure this is in your layout or page -->

<script>
$(function() {
    $.ajaxSetup({
        headers: {'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')}
    });

    // Track submission status to prevent duplicates
    let isSubmitting = false;

    $('#contact-form').on('submit', function(e) {
        e.preventDefault();

        // Prevent duplicate submissions
        if (isSubmitting) {
            return false;
        }

        var form = $(this);
        var submitBtn = form.find('button[type="submit"]');
        var url = form.attr('action');
        var method = form.attr('method');
        var formData = form.serialize();

        // Simple required field validation
        var valid = true;
        form.find('[required]').each(function() {
            if (!$(this).val().trim()) {
                valid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        if (!valid) {
            $('.ajax-response').removeClass('alert-success').addClass('alert alert-danger').text('Please fill all required fields.');
            return;
        }

        // Set submission flag and disable button
        isSubmitting = true;
        submitBtn.prop('disabled', true).text('Sending...');

        $.ajax({
            url: url,
            method: method,
            data: formData,
            timeout: 15000,
            success: function(response) {
                $('.ajax-response')
                    .removeClass('alert-danger')
                    .addClass('alert alert-success')
                    .text(response.message || 'Message sent successfully!');
                form[0].reset();
            },
            error: function(xhr) {
                var errorMsg = 'Something went wrong. Please try again.';
                if (xhr.responseJSON) {
                    if (xhr.responseJSON.errors) {
                        errorMsg = '';
                        $.each(xhr.responseJSON.errors, function(_, msgs) {
                            errorMsg += msgs.join(' ') + ' ';
                        });
                    } else if (xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                }
                $('.ajax-response')
                    .removeClass('alert-success')
                    .addClass('alert alert-danger')
                    .text(errorMsg);
            },
            complete: function() {
                // Reset submission flag after a delay to prevent rapid resubmission
                setTimeout(function() {
                    isSubmitting = false;
                    submitBtn.prop('disabled', false).text('Send a Message');
                }, 2000);
            }
        });
    });
});
</script> --}}
    <style>
        /* Default light mode */
        .form-control {
            display: block;
            width: 100%;
            padding: 17px 0.75rem;
            font-size: 16px;
            font-weight: 400;
            line-height: 1.5;
            color: var(--thm-body-font-color);
            background-color: #f5f4fa;
            background-clip: padding-box;
            border: 1px solid #ddd;
            border-radius: 5px;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        /* Dark mode */
        html[data-theme="dark-mode"] .form-control {
            background-color: #1a1a1a;
            color: #ffffff;
            border-color: #333;
            /* Optional: dark border */
        }

        /* Optional: Dark mode placeholder styling */
        html[data-theme="dark-mode"] .form-control::placeholder {
            color: #bbb;
        }
    </style>

