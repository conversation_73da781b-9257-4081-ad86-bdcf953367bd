@section('title', $blog->meta_title ?? $blog->title)
@section('meta_description', $blog->meta_description)
@section('meta_keywords', $blog->meta_keywords)

@extends('frontend.layouts.master')

@section('content')
   <!-- Page Banner -->
<div class="page__banner">
  <div class="page__banner-shape">
    <img src="{{ asset('assets/img/shape/page-banner-shape.png') }}" alt="Banner Shape">
  </div>
  <div class="container">
    <div class="row justify-content-between align-items-center">
      <div class="col-xl-6 col-lg-7">
        <div class="page__banner-content">
          <h2>{{ $post->title }}</h2>
          <span><a href="{{ url('/') }}">Home</a> <span>|</span> {{ $post->title }}</span>
        </div>
      </div>
      <div class="col-xl-4 col-lg-5">
        <div class="page__banner-img">
          <img src="{{ asset('assets/img/banner/page-banner-img.png') }}" alt="Banner Image">
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Blog Details -->
<section class="blog__details section-padding">
  <div class="container">
    <div class="row gy-4 flex-wrap-reverse">
      <!-- Blog Content -->
      <div class="col-xl-8">
        <div class="blog__details-thumb">
          <span class="date">{{ $post->created_at->format('d F Y') }}</span>
          <img src="{{ asset('storage/' . $post->image) }}" alt="{{ $post->title }}">
        </div>
        <div class="blog__details-content">
          <div class="blog__details-content-top mb-3">
            <span><i class="far fa-user"></i> by {{ $post->author->name }}</span>
            <span><i class="far fa-folder-open"></i> {{ $post->category->name }}</span>
            <span><i class="far fa-comments"></i> Comments ({{ $post->comments->count() }})</span>
          </div>

          <h2>{{ $post->title }}</h2>

          <div class="blog__details-text">
            {!! $post->content !!}
          </div>

          @if($post->quote)
          <div class="blog__details-quote my-5 p-4 bg-light rounded">
            <div class="d-flex align-items-center mb-3">
              <div class="blog__details-quote-avatar me-3">
                <img src="{{ asset('storage/' . $post->quote_avatar) }}" alt="{{ $post->quote_author }}" class="rounded-circle" style="width:60px; height:60px; object-fit:cover;">
              </div>
              <div>
                <h4 class="mb-0">{{ $post->quote_author }}</h4>
              </div>
              <div class="ms-auto">
                <img src="{{ asset('assets/img/icon/blog-details-quote.png') }}" alt="Quote Icon" style="width:40px;">
              </div>
            </div>
            <p class="fst-italic">"{{ $post->quote_text }}"</p>
          </div>
          @endif

          {{-- Pagination --}}
          <div class="blog__details-pagination d-flex justify-content-between mt-5">
            @if($previous = $post->previous())
            <div class="blog__details-pagination-btn blog__details-pagination-prev d-flex align-items-center">
              <a href="{{ route('blog.show', $previous->slug) }}" class="pagination-btn me-3">
                <i class="fas fa-arrow-left"></i>
              </a>
              <div class="blog__details-pagination-text">
                <span>Previous post</span>
                <strong>{{ $previous->title }}</strong>
              </div>
            </div>
            @endif

            @if($next = $post->next())
            <div class="blog__details-pagination-btn blog__details-pagination-next d-flex align-items-center">
              <div class="blog__details-pagination-text me-3 text-end">
                <span>Next post</span>
                <strong>{{ $next->title }}</strong>
              </div>
              <a href="{{ route('blog.show', $next->slug) }}" class="pagination-btn">
                <i class="fas fa-arrow-right"></i>
              </a>
            </div>
            @endif
          </div>

          {{-- Comments --}}
          <div class="blog__details-comments mt-5">
            <h3>{{ $post->comments->count() }} Comment{{ $post->comments->count() > 1 ? 's' : '' }}</h3>

            @foreach($post->comments as $comment)
            <div class="blog__details-single-comment d-flex mb-4">
              <div class="blog__details-single-comment-user-pic me-3">
                <img src="{{ asset('storage/' . ($comment->user->avatar ?? 'default-avatar.png')) }}" alt="{{ $comment->user->name }}" class="rounded-circle" style="width:50px; height:50px; object-fit:cover;">
              </div>
              <div class="blog__details-single-comment-body flex-grow-1">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h5 class="mb-0">{{ $comment->user->name }}</h5>
                  <span class="text-muted small">{{ $comment->created_at->format('F d, Y') }}</span>
                </div>
                <p>{{ $comment->body }}</p>
                <ul class="user-socials list-inline mb-2">
                  @if($comment->user->facebook)
                  <li class="list-inline-item"><a href="{{ $comment->user->facebook }}"><i class="fab fa-facebook-f"></i></a></li>
                  @endif
                  @if($comment->user->twitter)
                  <li class="list-inline-item"><a href="{{ $comment->user->twitter }}"><i class="fab fa-twitter"></i></a></li>
                  @endif
                  @if($comment->user->instagram)
                  <li class="list-inline-item"><a href="{{ $comment->user->instagram }}"><i class="fab fa-instagram"></i></a></li>
                  @endif
                </ul>
                <a href="#" class="comment-reply-btn text-decoration-none">Reply</a>
              </div>
            </div>
            @endforeach
          </div>

          {{-- Comment Form --}}
          <form action="{{ route('comments.store', $post->id) }}" method="POST" class="blog__details-comment-form mt-5">
            @csrf
            <h3>Leave a comment</h3>
            <p>By using form you agree with the message storage, you can contact us directly now.</p>
            <div class="mb-3">
              <input type="text" name="name" placeholder="Name" class="form-control" required>
            </div>
            <div class="mb-3">
              <textarea name="message" placeholder="Message here..." class="form-control" rows="5" required></textarea>
            </div>
            <button type="submit" class="btn btn-primary">Send Message</button>
          </form>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="col-xl-4">
        <aside class="blog__sidebar-main-sidebar">
          {{-- Search --}}
          <div class="sidebar-item mb-4 sidebar-search">
            <h3>Search</h3>
            <form action="{{ route('blog.search') }}" method="GET" class="d-flex">
              <input type="text" name="query" placeholder="Type here..." class="form-control me-2">
              <button type="submit" class="btn btn-secondary"><i class="fas fa-search"></i></button>
            </form>
          </div>

          {{-- Categories --}}
          <div class="sidebar-item mb-4 sidebar-category">
            <h3>Categories</h3>
            <div class="categories">
              @foreach($categories as $category)
              <a href="{{ route('blog.category', $category->slug) }}" class="single-category d-flex justify-content-between align-items-center mb-2 text-decoration-none">
                <div class="single-category-name d-flex align-items-center">
                  <i class="fas fa-angle-double-right me-2"></i>
                  <h4 class="mb-0">{{ $category->name }}</h4>
                </div>
                <span class="category-count">({{ $category->posts_count }})</span>
              </a>
              @endforeach
            </div>
          </div>

          {{-- Recent Posts --}}
          <div class="sidebar-item mb-4 recent-blog-post">
            <h3>Recent Posts</h3>
            <div class="blog-post">
              @foreach($recentPosts as $recent)
              <div class="blog-post-single d-flex mb-3">
                <div class="blog-post-single-img me-3" style="width: 70px; flex-shrink: 0;">
                  <img src="{{ asset('storage/' . $recent->image) }}" alt="{{ $recent->title }}" class="img-fluid rounded">
                </div>
                <div class="blog-post-single-content flex-grow-1">
                  <div class="blog-post-single-content-top mb-1">
                    <span><i class="far fa-folder-open"></i> {{ $recent->category->name }}</span>
                  </div>
                  <a href="{{ route('blog.show', $recent->slug) }}" class="text-decoration-none fw-semibold">{{ $recent->title }}</a>
                </div>
              </div>
              @endforeach
            </div>
          </div>

          {{-- Tags --}}
          <div class="sidebar-item sidebar-tags">
            <h3>Tags</h3>
            <div>
              @foreach($tags as $tag)
              <a href="{{ route('blog.tag', $tag->slug) }}" class="badge bg-secondary text-decoration-none me-1 mb-1">{{ $tag->name }}</a>
              @endforeach
            </div>
          </div>
        </aside>
      </div>
    </div>
  </div>
</section>
@endsection
