@extends('frontend.layouts.master')
@section('maincontent')

<!--Start Page Header-->
<section class="page-header">
    <div class="page-header__bg" style="background-image: url('{{ asset('frontend/assets/img/bg/page-header-bg.jpg') }}')"></div>
    <div class="container">
        <div class="page-header__inner">
            <h2>Contact Us</h2>
            <ul class="thm-breadcrumb">
                <li><a href="{{ url('/') }}">Home</a></li>
                <li><span>/</span></li>
                <li>Contact Us</li>
            </ul>
        </div>
    </div>
</section>
<!--End Page Header-->

<!-- Start Contact Page -->
<section class="contact-page">
    <div class="container">
        <div class="row">

            <!--Start Contact Page Form-->
            <div class="col-xl-8 col-lg-8">
                <div class="contact-page__form">
                    <div class="title-box">
                        <h2>Feel free to message</h2>
                    </div>



                    {{-- Contact Form --}}
                    <form id="contact-form" action="{{ route('contact.store') }}" method="POST">
                        @csrf

                        <div class="row">
                            <div class="col-xl-6 col-lg-6 col-md-6">
                                <div class="input-box">
                                    <input type="text" name="name" placeholder="Your name" required>
                                </div>
                            </div>

                            <div class="col-xl-6 col-lg-6 col-md-6">
                                <div class="input-box">
                                    <input type="email" name="email" placeholder="Your Email" required>

                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-xl-6 col-lg-6 col-md-6">
                                <div class="input-box">
                                    <input type="number" name="number" placeholder="Mobile">
                                </div>
                            </div>

                            <div class="col-xl-6 col-lg-6 col-md-6">
                                <div class="input-box">
                                    <select name="service_id" class="form-control " required>
                                        <option class="form-control" value="">Select Service</option>
                                        @foreach($services as $service)
                                            <option value="{{ $service->id }}">{{ $service->title }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-xl-12">
                                <div class="input-box">
                                    <textarea name="message" placeholder="Message"></textarea>
                                </div>
                            </div>

                            <div class="col-xl-6 col-lg-6 col-md-6">
                                <div class="contact-page__form-btn">
                                    <button class="button-style-1" type="submit" id="successMessage" data-loading-text="Please wait...">
                                        Send a Message
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <div class="success-message" id="successMessage" style="display: none;">
                        <h3>Thank You!</h3>
                        <p>Your message has been sent successfully! We will contact you soon.</p>
                    </div>

        <p class="ajax-response mb-0"></p>
                </div>
            </div>
            <!--End Contact One Form-->

            <!--Start Contact Page Contact Info-->
            <div class="col-xl-4 col-lg-4">
                <div class="contact-page__contact-info">
                    <div class="title-box">
                        <h2>Get in touch</h2>
                        <p>Feel free to reach out to us anytime. We’d love to hear from you.</p>
                    </div>

                    <ul>
                        <li>
                            <div class="icon-box">
                                <span class="icon-pin"></span>
                            </div>
                            <div class="text-box">
                                <h3>Address</h3>
                                <p>Dhaka 102, UTL 1216, Road 45 House<br>Shantighar Rahuta, 1213</p>
                            </div>
                        </li>

                        <li>
                            <div class="icon-box">
                                <span class="icon-envelope"></span>
                            </div>
                            <div class="text-box">
                                <h3>Email Address</h3>
                                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            </div>
                        </li>

                        <li>
                            <div class="icon-box">
                                <span class="icon-call"></span>
                            </div>
                            <div class="text-box">
                                <h3>Phone number</h3>
                                <p><a href="tel:+13323224126">+****************</a></p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <!--End Contact Page Contact Info-->
        </div>
    </div>
</section>
<!-- End Contact Page -->

<!--Start Google Map One-->
<section class="google-map-one">
    <iframe
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4562.753041141002!2d-118.80123790098536!3d34.152323469614075!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80e82469c2162619%3A0xba03efb7998eef6d!2sCostco+Wholesale!5e0!3m2!1sbn!2sbd!4v1562518641290!5m2!1sbn!2sbd"
        class="google-map-one__map" allowfullscreen></iframe>
</section>
<!--End Google Map One-->
<!-- your form and markup here -->


<style>
/* Default light mode */
.form-control {
    display: block;
    width: 100%;
    padding: 1.47rem 0.75rem;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
    color: var(--thm-body-font-color);
    background-color: #f5f4fa;
    background-clip: padding-box;
    border: var(--bs-border-width) solid var(--bs-border-color-translucent);
    border-radius: var(--bs-border-radius);
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Dark mode */
html[data-theme="dark-mode"] .form-control {
    background-color: #1a1a1a;
    color: #ffffff;
    border-color: #333; /* Optional: dark border */
}

/* Optional: Dark mode placeholder styling */
html[data-theme="dark-mode"] .form-control::placeholder {
    color: #bbb;
}

/* Success Message */
.success-message {
    background-color: #4CAF50;
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 20px;
    text-align: center;
    animation: fadeIn 0.5s ease-in-out;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.success-message h3 {
    margin-top: 0;
    font-size: 24px;
    font-weight: bold;
}

.success-message p {
    font-size: 16px;
    margin-bottom: 0;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Dark mode support */
html[data-theme="dark-mode"] .success-message {
    background-color: #2e7d32;
}
</style>

<script>
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const form = e.target;
    const formData = new FormData(form);
    const responseEl = document.querySelector('.ajax-response');

    fetch(form.action, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': form.querySelector('[name=_token]').value,
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            responseEl.innerText = data.message;
            responseEl.style.color = 'green';
            form.reset();
        } else {
            responseEl.innerText = data.message;
            responseEl.style.color = 'red';
        }
    })
    .catch(error => {
        responseEl.innerText = 'An error occurred.';
        responseEl.style.color = 'red';
        console.error('Error:', error);
    });
});
</script>


@endsection


