@extends('frontend.layouts.master')
@section('maincontent')
 <!-- Banner Area Start -->

	<div class="page__banner">
		<div class="page__banner-shape">
			<img src="{{ asset('frontend/assets/img/shape/page-banner-shape.png') }}" alt="image">
		</div>
		<div class="container">
			<div class="row justify-content-between align-items-center">
				<div class="col-xl-6 col-lg-7">
					<div class="page__banner-content">
						<h2>Blog</h2>
						<span><a href="index.html">Home</a>
							<span>|</span>
							Blog
						</span>
					</div>
				</div>
				<div class="col-xl-4 col-lg-5">
					<div class="page__banner-img">
						<img src="{{ asset('frontend/assets/img/banner/page-banner-img.png') }}" alt="image">
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Banner Area End -->

   <!-- Blog Page Section -->
<section class="bg-gray-50 py-12">
  <div class="container mx-auto px-4">
    <div class="flex flex-wrap -mx-6">

      <!-- Blog Posts Column -->
      <main class="w-full lg:w-8/12 px-6">

        @foreach ($blogs as $blog)
        <article class="bg-white rounded-lg shadow-md mb-10 overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <a href="{{ route('blogDetails', $blog->slug) }}" class="block overflow-hidden">
            <img
              src="{{ asset($blog->image) }}"
              alt="{{ $blog->title }}"
              class="w-full h-60 object-cover transform hover:scale-105 transition-transform duration-300"
            />
          </a>
          <div class="p-6">
            <ul class="flex space-x-4 text-sm text-gray-500 mb-3">
              <li class="flex items-center space-x-1">
                <i class="icon-user-2"></i>
                <span>By {{ $blog->author_name ?? 'Admin' }}</span>
              </li>
              <li class="flex items-center space-x-1">
                <i class="icon-open-file"></i>
                <span>{{ $blog->category->name ?? 'Uncategorized' }}</span>
              </li>
              <li class="flex items-center space-x-1">
                <i class="icon-speech-bubble"></i>
                <span>Comments (0)</span>
              </li>
            </ul>

            <h2 class="text-2xl font-semibold mb-3">
              <a href="{{ route('blogDetails', $blog->slug) }}" class="text-gray-900 hover:text-indigo-600 transition-colors duration-200">
                {{ $blog->title }}
              </a>
            </h2>

            <p class="text-gray-700 mb-4">{{ Str::limit(strip_tags($blog->description), 150) }}</p>

            <a href="{{ route('blogDetails', $blog->slug) }}" class="inline-block text-indigo-600 font-semibold hover:underline">
              Read More &rarr;
            </a>
          </div>
        </article>
        @endforeach

        <div class="mt-8">
          {{ $blogs->onEachSide(1)->links('frontend.components.pagination') }}
        </div>

      </main>

      <!-- Sidebar Column -->
      <aside class="w-full lg:w-4/12 px-6 space-y-10">

        <!-- Search -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-xl font-semibold mb-4">Search</h3>
          <form action="#" method="GET" class="flex">
            <input
              type="search"
              name="q"
              placeholder="Enter search"
              class="flex-grow border border-gray-300 rounded-l px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
            <button
              type="submit"
              class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 rounded-r"
              aria-label="Search"
            >
              <i class="icon-loupe"></i>
            </button>
          </form>
        </div>

        <!-- Categories -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-xl font-semibold mb-4">Categories</h3>
          <ul class="space-y-2 text-gray-700">
            @foreach (\App\Models\BlogCategory::all() as $cat)
              <li>
                <a
                  href="#"
                  class="flex items-center space-x-2 hover:text-indigo-600 transition-colors duration-200"
                >
                  <span class="w-3 h-3 bg-indigo-600 rounded-full inline-block"></span>
                  <span>{{ $cat->name }}</span>
                </a>
              </li>
            @endforeach
          </ul>
        </div>

        <!-- Tags -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-xl font-semibold mb-4">Tags</h3>
          <div class="flex flex-wrap gap-2">
            @foreach ($allTags as $tag => $count)
              <a
                href="{{ url('blog/tag/' . urlencode($tag)) }}"
                class="bg-indigo-100 text-indigo-700 px-3 py-1 rounded-full text-sm hover:bg-indigo-200 transition-colors duration-200"
              >
                {{ $tag }}
              </a>
            @endforeach
          </div>
        </div>

      </aside>
    </div>
  </div>
</section>

@endsection
