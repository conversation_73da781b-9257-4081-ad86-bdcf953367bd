@extends('frontend.layouts.master')
@section('maincontent')
<!-- Start Project One -->
<section class="project-one project-one--project">
    <div class="container-fluid">
        <div class="section-title text-center tg-heading-subheading animation-style2">
            <h5 class="tg-element-title">latest portfolio</h5>
            <h2 class="tg-element-title">Where Innovation Meets <br> IT Excellence</h2>
        </div>
        <div class="row">
            <!--Start Project One Single -->
            @foreach($projects as $project)
            <div class="col-xl-3 col-lg-6 col-md-6 wow fadeInUp" data-wow-delay=".3s">
    <div class="project-one__single">
        <div class="project-one__img-box">
            <div class="project-one__img">
                @if (!empty($project->images) && is_array($project->images) && count($project->images))
    <img src="{{ asset($project->images[0]) }}" alt="{{ $project->title }}">
@endif

            </div>
            <div class="project-one__content">
                <h4 class="project-one__title">
                    <a href="{{ route('projectDetails', $project->slug) }}">{{ $project->title }}</a>
                </h4>
                <p class="project-one__sub-title">{{ $project->subtitle ?? 'No subtitle' }}</p>
            </div>
        </div>
    </div>
</div>
@endforeach
            <!--End Project One Single -->
<div class="pagination-wrapper mt-4">
                            {{ $projects->onEachSide(1)->links('frontend.components.pagination') }}

                        </div>
        </div>
    </div>
</section>
<!-- End Project One -->
@endsection
