@if ($paginator->hasPages())
    <ul class="styled-pagination clearfix">
        {{-- Previous Page Link --}}
        @if ($paginator->onFirstPage())
            <li class="arrow prev disabled"><span><span class="icon-left-arrow-2"></span></span></li>
        @else
            <li class="arrow prev">
                <a href="{{ $paginator->previousPageUrl() }}"><span class="icon-left-arrow-2"></span></a>
            </li>
        @endif

        {{-- Pagination Elements --}}
        @foreach ($elements as $element)
            {{-- "Three Dots" Separator --}}
            @if (is_string($element))
                <li class="disabled"><span>{{ $element }}</span></li>
            @endif

            {{-- Page Links --}}
            @if (is_array($element))
                @foreach ($element as $page => $url)
                    @if ($page == $paginator->currentPage())
                        <li class="active"><a href="#">{{ str_pad($page, 2, '0', STR_PAD_LEFT) }}</a></li>
                    @else
                        <li><a href="{{ $url }}">{{ str_pad($page, 2, '0', STR_PAD_LEFT) }}</a></li>
                    @endif
                @endforeach
            @endif
        @endforeach

        {{-- Next Page Link --}}
        @if ($paginator->hasMorePages())
            <li class="arrow next">
                <a href="{{ $paginator->nextPageUrl() }}"><span class="icon-right-arrow-2"></span></a>
            </li>
        @else
            <li class="arrow next disabled"><span><span class="icon-right-arrow-2"></span></span></li>
        @endif
    </ul>
@endif
