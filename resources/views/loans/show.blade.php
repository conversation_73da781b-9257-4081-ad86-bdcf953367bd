@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">Loan #{{ $loan->id }} Details</h4>
                                <div class="page-title-right">
                                    <a href="{{ route('loans.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Loans
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="card-title mb-0">Loan #{{ $loan->id }} Details</h4>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('loans.edit', $loan) }}" class="btn btn-warning">
                                    <i class="ri-edit-line align-middle me-1"></i> Edit
                                </a>
                                <a href="{{ route('loans.index') }}" class="btn btn-secondary">
                                    <i class="ri-arrow-left-line align-middle me-1"></i> Back to List
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Loan ID:</strong></td>
                                            <td>#{{ $loan->id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Marketplace Account:</strong></td>
                                            <td>{{ $loan->marketplace->order_account_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Loan Amount:</strong></td>
                                            <td class="text-primary">${{ number_format($loan->amount, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Charge Percentage:</strong></td>
                                            <td>{{ $loan->loan_amount_charge_percentage }}%</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Charge Amount:</strong></td>
                                            <td class="text-warning">${{ number_format(($loan->amount * $loan->loan_amount_charge_percentage) / 100, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Amount:</strong></td>
                                            <td class="text-success fw-bold">${{ number_format($loan->total_amount, 2) }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Status & Dates -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Status & Dates</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="{{ $loan->status_badge_class }}">
                                                    {{ ucfirst($loan->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Due Amount:</strong></td>
                                            <td class="text-danger fw-bold">${{ number_format($loan->due_amounts, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Deducted:</strong></td>
                                            <td class="text-warning">${{ number_format($loan->total_deducted, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Available Balance:</strong></td>
                                            <td class="text-info">${{ number_format($loan->remaining_amount, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Created:</strong></td>
                                            <td>{{ $loan->created_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Last Updated:</strong></td>
                                            <td>{{ $loan->updated_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                        @if($loan->paid_amount)
                                        <tr>
                                            <td><strong>Paid Amount:</strong></td>
                                            <td class="text-success">${{ number_format($loan->paid_amount, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Paid Date:</strong></td>
                                            <td>{{ $loan->formatted_paid_date }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Remaining Amount:</strong></td>
                                            <td class="text-danger fw-bold">${{ number_format($loan->remaining_amount, 2) }}</td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description & Notes -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Description</h5>
                                </div>
                                <div class="card-body">
                                    @if($loan->description)
                                        <p class="mb-0">{{ $loan->description }}</p>
                                    @else
                                        <p class="text-muted mb-0">No description provided.</p>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Notes</h5>
                                </div>
                                <div class="card-body">
                                    @if($loan->notes)
                                        <p class="mb-0">{{ $loan->notes }}</p>
                                    @else
                                        <p class="text-muted mb-0">No notes available.</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    @if($loan->status !== 'paid')
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex gap-2">
                                        @if($loan->status === 'pending')
                                            <form action="{{ route('loans.update-status', $loan) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('PATCH')
                                                <input type="hidden" name="status" value="approved">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="ri-check-line align-middle me-1"></i> Approve Loan
                                                </button>
                                            </form>
                                            <form action="{{ route('loans.update-status', $loan) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('PATCH')
                                                <input type="hidden" name="status" value="rejected">
                                                <button type="submit" class="btn btn-danger">
                                                    <i class="ri-close-line align-middle me-1"></i> Reject Loan
                                                </button>
                                            </form>
                                        @endif

                                        @if(in_array($loan->status, ['approved', 'overdue']))
                                            <a href="{{ route('loans.deduction-form', $loan) }}" class="btn btn-primary">
                                                <i class="ri-subtract-line align-middle me-1"></i> Add Deduction
                                            </a>
                                            <button type="button" class="btn btn-success"
                                                    data-bs-toggle="modal" data-bs-target="#markPaidModal">
                                                <i class="ri-money-dollar-circle-line align-middle me-1"></i> Mark as Paid
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Mark as Paid Modal -->
    @if($loan->status !== 'paid')
    <div class="modal fade" id="markPaidModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('loans.mark-paid', $loan) }}" method="POST">
                    @csrf
                    @method('PATCH')
                    <div class="modal-header">
                        <h5 class="modal-title">Mark Loan as Paid</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="paid_amount" class="form-label">Paid Amount</label>
                            <input type="number" step="0.01" class="form-control" id="paid_amount"
                                   name="paid_amount" value="{{ $loan->due_amounts }}"
                                   max="{{ $loan->due_amounts }}" required>
                            <small class="text-muted">Total amount due: ${{ number_format($loan->due_amounts, 2) }}</small>
                        </div>
                        <div class="mb-3">
                            <label for="paid_date" class="form-label">Paid Date</label>
                            <input type="date" class="form-control" id="paid_date"
                                   name="paid_date" value="{{ date('Y-m-d') }}">
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Payment Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                      placeholder="Enter payment notes..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Mark as Paid</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endif
                </div>
            </div>
        </div>
        <!-- End Page-content -->
        @include('layouts.sections.footer.footer')
    </div>
    <!-- end main content-->
</div>
<!-- END layout-wrapper -->

@include('layouts.sections.menu.rightsidebar')

@endsection
