@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <h2>Loan Management</h2>

    <div class="row">
        <!-- Summary Cards -->
        <div class="col-xl-3 col-md-6">
            <div class="card mini-stats-wid">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium">Total Loans</p>
                            <h4 class="mb-0">{{ $totalLoans }}</h4>
                        </div>
                        <div class="flex-shrink-0 align-self-center">
                            <div class="mini-stat-icon avatar-sm rounded-circle bg-primary align-self-center">
                                <span class="avatar-title">
                                    <i class="ri-money-dollar-circle-line font-size-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card mini-stats-wid">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium">Pending Loans</p>
                            <h4 class="mb-0">{{ $pendingLoans }}</h4>
                        </div>
                        <div class="flex-shrink-0 align-self-center">
                            <div class="mini-stat-icon avatar-sm rounded-circle bg-warning align-self-center">
                                <span class="avatar-title">
                                    <i class="ri-time-line font-size-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card mini-stats-wid">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium">Overdue Loans</p>
                            <h4 class="mb-0">{{ $overdueLoans }}</h4>
                        </div>
                        <div class="flex-shrink-0 align-self-center">
                            <div class="mini-stat-icon avatar-sm rounded-circle bg-danger align-self-center">
                                <span class="avatar-title">
                                    <i class="ri-error-warning-line font-size-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card mini-stats-wid">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium">Total Amount</p>
                            <h4 class="mb-0">${{ number_format($totalAmount, 2) }}</h4>
                        </div>
                        <div class="flex-shrink-0 align-self-center">
                            <div class="mini-stat-icon avatar-sm rounded-circle bg-success align-self-center">
                                <span class="avatar-title">
                                    <i class="ri-money-dollar-box-line font-size-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="card-title mb-0">All Loans</h4>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end">
                                <a href="{{ route('loans.create') }}" class="btn btn-primary">
                                    <i class="ri-add-line align-middle me-1"></i> Add New Loan
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="{{ route('loans.index') }}" class="row g-3 mb-4">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ request('search') }}" placeholder="Search loans...">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                                <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>Paid</option>
                                <option value="overdue" {{ request('status') == 'overdue' ? 'selected' : '' }}>Overdue</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="marketplace_id" class="form-label">Marketplace</label>
                            <select class="form-select" id="marketplace_id" name="marketplace_id">
                                <option value="">All Marketplaces</option>
                                @foreach($marketplaces as $marketplace)
                                    <option value="{{ $marketplace->id }}" {{ request('marketplace_id') == $marketplace->id ? 'selected' : '' }}>
                                        {{ $marketplace->order_account_name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="amount_from" class="form-label">From Amount</label>
                            <input type="number" step="0.01" class="form-control" id="amount_from" name="amount_from"
                                   value="{{ request('amount_from') }}" placeholder="Min amount">
                        </div>
                        <div class="col-md-2">
                            <label for="amount_to" class="form-label">To Amount</label>
                            <input type="number" step="0.01" class="form-control" id="amount_to" name="amount_to"
                                   value="{{ request('amount_to') }}" placeholder="Max amount">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="ri-search-line"></i>
                            </button>
                        </div>
                    </form>

                    <!-- Loans Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered dt-responsive nowrap w-100">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Marketplace</th>
                                    <th>Original Amount</th>
                                    <th>Charge %</th>
                                    <th>Total Deducted</th>
                                    <th>Due Amount</th>
                                    <th>Available Balance</th>
                                    <th>Status</th>
                                    <th>Paid Amount</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($loans as $loan)
                                    <tr>
                                        <td>{{ $loan->id }}</td>
                                        <td>{{ $loan->marketplace->order_account_name }}</td>
                                        <td>${{ number_format($loan->original_amount, 2) }}</td>
                                        <td>{{ $loan->loan_amount_charge_percentage }}%</td>
                                        <td class="text-warning">${{ number_format($loan->total_deducted, 2) }}</td>
                                        <td class="text-danger fw-bold">${{ number_format($loan->due_amounts, 2) }}</td>
                                        <td class="text-info">${{ number_format($loan->remaining_amount, 2) }}</td>
                                        <td>
                                            <span class="{{ $loan->status_badge_class }}">
                                                {{ ucfirst($loan->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($loan->paid_amount)
                                                ${{ number_format($loan->paid_amount, 2) }}
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2">
                                                <a href="{{ route('loans.show', $loan) }}" class="btn btn-sm btn-info">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <a href="{{ route('loans.edit', $loan) }}" class="btn btn-sm btn-warning">
                                                    <i class="ri-edit-line"></i>
                                                </a>
                                                @if($loan->status !== 'paid')
                                                    <button type="button" class="btn btn-sm btn-success"
                                                            data-bs-toggle="modal" data-bs-target="#markPaidModal{{ $loan->id }}">
                                                        <i class="ri-check-line"></i>
                                                    </button>
                                                @endif
                                                <form action="{{ route('loans.destroy', $loan) }}" method="POST"
                                                      onsubmit="return confirm('Are you sure you want to delete this loan?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="ri-delete-bin-line"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Mark as Paid Modal -->
                                    @if($loan->status !== 'paid')
                                        <div class="modal fade" id="markPaidModal{{ $loan->id }}" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <form action="{{ route('loans.mark-paid', $loan) }}" method="POST">
                                                        @csrf
                                                        @method('PATCH')
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Mark Loan as Paid</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="mb-3">
                                                                <label for="paid_amount" class="form-label">Paid Amount</label>
                                                                <input type="number" step="0.01" class="form-control" id="paid_amount"
                                                                       name="paid_amount" value="{{ $loan->total_amount }}"
                                                                       max="{{ $loan->total_amount }}" required>
                                                                <small class="text-muted">Total amount due: ${{ number_format($loan->total_amount, 2) }}</small>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label for="paid_date" class="form-label">Paid Date</label>
                                                                <input type="date" class="form-control" id="paid_date"
                                                                       name="paid_date" value="{{ date('Y-m-d') }}">
                                                            </div>
                                                            <div class="mb-3">
                                                                <label for="notes" class="form-label">Notes</label>
                                                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <button type="submit" class="btn btn-success">Mark as Paid</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">No loans found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $loans->links() }}
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page-content -->
        @include('layouts.sections.footer.footer')
    </div>
    <!-- end main content-->
</div>
<!-- END layout-wrapper -->

@include('layouts.sections.menu.rightsidebar')

@endsection
