@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">Edit Loan #{{ $loan->id }}</h4>
                                <div class="page-title-right">
                                    <a href="{{ route('loans.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Loans
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Edit Loan #{{ $loan->id }}</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('loans.update', $loan) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="marketplace_id" class="form-label">Marketplace Account <span class="text-danger">*</span></label>
                                    <select class="form-select @error('marketplace_id') is-invalid @enderror" id="marketplace_id" name="marketplace_id" required>
                                        <option value="">Select Marketplace Account</option>
                                        @foreach($marketplaces as $marketplace)
                                            <option value="{{ $marketplace->id }}" {{ old('marketplace_id', $loan->marketplace_id) == $marketplace->id ? 'selected' : '' }}>
                                                {{ $marketplace->order_account_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('marketplace_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="amount" class="form-label">Loan Amount <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" step="0.01" class="form-control @error('amount') is-invalid @enderror"
                                               id="amount" name="amount" value="{{ old('amount', $loan->amount) }}" required>
                                    </div>
                                    @error('amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="loan_amount_charge_percentage" class="form-label">Loan Charge Percentage</label>
                                    <div class="input-group">
                                        <input type="number" step="0.01" min="0" max="100" class="form-control @error('loan_amount_charge_percentage') is-invalid @enderror"
                                               id="loan_amount_charge_percentage" name="loan_amount_charge_percentage"
                                               value="{{ old('loan_amount_charge_percentage', $loan->loan_amount_charge_percentage) }}">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <small class="text-muted">Enter the percentage charge for this loan (0-100%)</small>
                                    @error('loan_amount_charge_percentage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="due_amounts" class="form-label">Due Amount <span class="text-danger">*</span></label>
                                    <input type="number" step="0.01" min="0" class="form-control @error('due_amounts') is-invalid @enderror"
                                           id="due_amounts" name="due_amounts" value="{{ old('due_amounts', $loan->due_amounts) }}" placeholder="Enter due amount" required>
                                    @error('due_amounts')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                        <option value="pending" {{ old('status', $loan->status) == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="approved" {{ old('status', $loan->status) == 'approved' ? 'selected' : '' }}>Approved</option>
                                        <option value="rejected" {{ old('status', $loan->status) == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                        <option value="paid" {{ old('status', $loan->status) == 'paid' ? 'selected' : '' }}>Paid</option>
                                        <option value="overdue" {{ old('status', $loan->status) == 'overdue' ? 'selected' : '' }}>Overdue</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="paid_amount" class="form-label">Paid Amount</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" step="0.01" class="form-control @error('paid_amount') is-invalid @enderror"
                                               id="paid_amount" name="paid_amount" value="{{ old('paid_amount', $loan->paid_amount) }}">
                                    </div>
                                    @error('paid_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="paid_date" class="form-label">Paid Date</label>
                                    <input type="date" class="form-control @error('paid_date') is-invalid @enderror"
                                           id="paid_date" name="paid_date"
                                           value="{{ old('paid_date', $loan->paid_date ? $loan->paid_date->format('Y-m-d') : '') }}">
                                    @error('paid_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror"
                                              id="description" name="description" rows="3"
                                              placeholder="Enter loan description...">{{ old('description', $loan->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror"
                                              id="notes" name="notes" rows="3"
                                              placeholder="Enter any additional notes...">{{ old('notes', $loan->notes) }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Summary Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h5 class="card-title">Loan Summary</h5>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <p class="mb-1"><strong>Loan Amount:</strong></p>
                                                <p class="text-primary" id="summary-amount">${{ number_format($loan->amount, 2) }}</p>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="mb-1"><strong>Charge Amount:</strong></p>
                                                <p class="text-warning" id="summary-charge">${{ number_format(($loan->amount * $loan->loan_amount_charge_percentage) / 100, 2) }}</p>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="mb-1"><strong>Total Amount:</strong></p>
                                                <p class="text-success" id="summary-total">${{ number_format($loan->total_amount, 2) }}</p>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="mb-1"><strong>Due Amount:</strong></p>
                                                <p class="text-info" id="summary-due">{{ $loan->formatted_due_amount }}</p>
                                            </div>
                                        </div>
                                        @if($loan->paid_amount)
                                        <div class="row mt-2">
                                            <div class="col-md-3">
                                                <p class="mb-1"><strong>Paid Amount:</strong></p>
                                                <p class="text-success">${{ number_format($loan->paid_amount, 2) }}</p>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="mb-1"><strong>Remaining:</strong></p>
                                                <p class="text-danger">${{ number_format($loan->remaining_amount, 2) }}</p>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="mb-1"><strong>Paid Date:</strong></p>
                                                <p class="text-info">{{ $loan->formatted_paid_date }}</p>
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('loans.index') }}" class="btn btn-secondary">
                                        <i class="ri-arrow-left-line align-middle me-1"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-save-line align-middle me-1"></i> Update Loan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
                </div>
            </div>
        </div>
        <!-- End Page-content -->
        @include('layouts.sections.footer.footer')
    </div>
    <!-- end main content-->
</div>
<!-- END layout-wrapper -->

@include('layouts.sections.menu.rightsidebar')

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('amount');
    const chargeInput = document.getElementById('loan_amount_charge_percentage');
    const dueInput = document.getElementById('due_amounts');

    const summaryAmount = document.getElementById('summary-amount');
    const summaryCharge = document.getElementById('summary-charge');
    const summaryTotal = document.getElementById('summary-total');
    const summaryDue = document.getElementById('summary-due');

    function updateSummary() {
        const amount = parseFloat(amountInput.value) || 0;
        const chargePercentage = parseFloat(chargeInput.value) || 0;
        const chargeAmount = (amount * chargePercentage) / 100;
        const total = amount + chargeAmount;

        summaryAmount.textContent = '$' + amount.toFixed(2);
        summaryCharge.textContent = '$' + chargeAmount.toFixed(2);
        summaryTotal.textContent = '$' + total.toFixed(2);

        if (dueInput.value) {
            summaryDue.textContent = '$' + parseFloat(dueInput.value).toFixed(2);
        } else {
            summaryDue.textContent = '$0.00';
        }
    }

    amountInput.addEventListener('input', updateSummary);
    chargeInput.addEventListener('input', updateSummary);
    dueInput.addEventListener('input', updateSummary);

    // Initial update
    updateSummary();
});
</script>
@endpush
