@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Add Loan Deduction</h4>
                    <a href="{{ route('loans.show', $loan) }}" class="btn btn-secondary">Back to Loan</a>
                </div>

                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <!-- Loan Information Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Loan Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Marketplace:</strong> {{ $loan->marketplace->order_account_name }}</p>
                                <p><strong>Original Amount:</strong> ${{ number_format($loan->original_amount, 2) }}</p>
                                <p><strong>Current Due Amount:</strong> ${{ number_format($loan->due_amounts, 2) }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Total Deducted:</strong> ${{ number_format($loan->total_deducted, 2) }}</p>
                                <p><strong>Paid Amount:</strong> ${{ number_format($loan->paid_amount ?? 0, 2) }}</p>
                                <p><strong>Available Balance:</strong> ${{ number_format($loan->remaining_amount, 2) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Deduction Form -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Add Deduction</h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('loans.add-deduction', $loan) }}" method="POST">
                            @csrf

                            <div class="mb-3">
                                <label for="deduction_amount" class="form-label">Deduction Amount ($)</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="deduction_amount" id="deduction_amount"
                                           step="0.01" min="0.01" max="{{ $loan->remaining_amount }}"
                                           class="form-control" value="{{ old('deduction_amount') }}" required>
                                </div>
                                <small class="form-text text-muted">
                                    Maximum available: ${{ number_format($loan->remaining_amount, 2) }}
                                </small>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <input type="text" name="description" id="description"
                                       class="form-control" value="{{ old('description') }}"
                                       placeholder="e.g., Project payment, Equipment purchase, etc." required>
                                <small class="form-text text-muted">Brief description of what this deduction is for</small>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Additional Notes (Optional)</label>
                                <textarea name="notes" id="notes" class="form-control" rows="3"
                                          placeholder="Any additional details about this deduction">{{ old('notes') }}</textarea>
                            </div>

                            <div class="alert alert-info">
                                <i class="ri-information-line"></i>
                                <strong>Note:</strong> This deduction will be added to the due amount, increasing the total amount that needs to be repaid.
                            </div>

                            <button type="submit" class="btn btn-primary">Add Deduction</button>
                            <a href="{{ route('loans.show', $loan) }}" class="btn btn-secondary">Cancel</a>
                        </form>
                    </div>
                </div>

            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
