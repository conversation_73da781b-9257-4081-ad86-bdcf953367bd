@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')

        <div class="main-content">
            <div class="page-content">

                <div class="container-fluid">
                    <h2>Edit Blog Category</h2>

                    <form action="{{ route('blog-categories.update', $blog_category->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label>Name</label>
                            <input type="text" name="name" class="form-control"
                                value="{{ old('name', $blog_category->name) }}" required>
                            @error('name')
                                <small class="text-danger">{{ $message }}</small>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label>Parent Category (Optional)</label>
                            <select name="parent_id" class="form-control">
                                <option value="">-- Select Parent Category --</option>
                                @foreach ($categories as $parentCategory)
                                    <option value="{{ $parentCategory->id }}"
                                        {{ old('parent_id', $blog_category->parent_id) == $parentCategory->id ? 'selected' : '' }}>
                                        {{ $parentCategory->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('parent_id')
                                <small class="text-danger">{{ $message }}</small>
                            @enderror
                        </div>

                        <button type="submit" class="btn btn-primary">Update</button>
                    </form>
                </div>

            </div>

            @include('layouts.sections.footer.footer')
        </div>
    </div>

    @include('layouts.sections.menu.rightsidebar')
@endsection
