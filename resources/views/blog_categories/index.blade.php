@extends('layouts.master')

@section('maincontent')
    <!-- <body data-layout="horizontal" data-topbar="dark"> -->
    <!-- Begin page -->
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        <!-- ========== Left Sidebar Start ========== -->
        @include('layouts.sections.menu.sidebar')
        <!-- Left Sidebar End -->
        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">
            <!-- start Page-content -->
            <div class="page-content">

                <div class="container-fluid">
                    <h2>Blog Categories</h2>

                    {{-- Show "Add Category" button only if user has "create" permission --}}
                    @if (auth()->user()->hasPermission('create'))
                        <a href="{{ route('blog-categories.create') }}" class="btn btn-primary mb-3">Add Category</a>
                    @endif

                    @if (session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif

                    <table class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Name</th>
                                <th>Parent Category</th>
                                <th>Slug</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($categories as $index => $category)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>{{ $category->name }}</td>
                                    <td>{{ $category->parent ? $category->parent->name : '-' }}</td>
                                    <td>{{ $category->slug }}</td>
                                    <td>
                                        {{-- Edit button only if user has "edit" permission --}}
                                        @if (auth()->user()->hasPermission('edit'))
                                            <a href="{{ route('blog-categories.edit', $category->id) }}"
                                                class="btn btn-sm btn-warning">Edit</a>
                                        @endif

                                        {{-- Delete button only if user has "delete" permission --}}
                                        @if (auth()->user()->hasPermission('delete'))
                                            <form action="{{ route('blog-categories.destroy', $category->id) }}"
                                                method="POST" class="d-inline"
                                                onsubmit="return confirm('Delete this category?');">
                                                @csrf
                                                @method('DELETE')
                                                <button class="btn btn-sm btn-danger">Delete</button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    {{ $categories->onEachSide(1)->links('pagination::bootstrap-5') }}
                </div>

            </div>

            <!-- End Page-content -->
            @include('layouts.sections.footer.footer')

        </div>
        <!-- end main content-->

    </div>
    <!-- END layout-wrapper -->

    @include('layouts.sections.menu.rightsidebar')
@endsection
