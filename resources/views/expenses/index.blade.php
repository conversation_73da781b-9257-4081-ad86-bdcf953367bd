@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">Expenses Management</h4>
                                <div class="page-title-right">
                                    <a href="{{ route('expenses.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Add New Expense
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <form method="GET" action="{{ route('expenses.index') }}" class="row g-3">
                                        <div class="col-md-3">
                                            <label class="form-label">Search</label>
                                            <input type="text" name="search" class="form-control" 
                                                   value="{{ request('search') }}" placeholder="Search by name or purpose">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Status</label>
                                            <select name="status" class="form-select">
                                                <option value="">All Status</option>
                                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                                <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                                                <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Date From</label>
                                            <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Date To</label>
                                            <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-search"></i> Search
                                                </button>
                                                <a href="{{ route('expenses.index') }}" class="btn btn-secondary">
                                                    <i class="fas fa-times"></i> Clear
                                                </a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Expenses Table -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>SL</th>
                                                    <th>Name</th>
                                                    <th>Date</th>
                                                    <th>Purpose</th>
                                                    <th>Given Amount</th>
                                                    <th>Accepted Amount</th>
                                                    <th>Status</th>
                                                    <th>Submitted By</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($expenses as $expense)
                                                    <tr>
                                                        <td>{{ $loop->iteration }}</td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                @if($expense->picture)
                                                                    <img src="{{ $expense->picture_url }}" alt="Expense" 
                                                                         class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                                @else
                                                                    <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                                                         style="width: 40px; height: 40px;">
                                                                        <i class="fas fa-image text-muted"></i>
                                                                    </div>
                                                                @endif
                                                                <div>
                                                                    <strong>{{ $expense->name }}</strong>
                                                                    @if($expense->notes)
                                                                        <br><small class="text-muted">{{ Str::limit($expense->notes, 50) }}</small>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>{{ $expense->formatted_date }}</td>
                                                        <td>{{ Str::limit($expense->purpose, 50) }}</td>
                                                        <td>${{ number_format($expense->given_amount, 2) }}</td>
                                                        <td>
                                                            @if($expense->accepted_amount)
                                                                ${{ number_format($expense->accepted_amount, 2) }}
                                                            @else
                                                                <span class="text-muted">-</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            <span class="{{ $expense->status_badge_class }}">
                                                                {{ ucfirst($expense->status) }}
                                                            </span>
                                                        </td>
                                                        <td>{{ $expense->user->name }}</td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a href="{{ route('expenses.show', $expense->id) }}" 
                                                                   class="btn btn-sm btn-outline-primary" title="View">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                @if(auth()->user()->role === 'super_admin' || auth()->user()->role === 'admin' || $expense->user_id === auth()->id())
                                                                    <a href="{{ route('expenses.edit', $expense->id) }}" 
                                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                                        <i class="fas fa-edit"></i>
                                                                    </a>
                                                                @endif
                                                                @if(auth()->user()->role === 'super_admin' || auth()->user()->role === 'admin' || $expense->user_id === auth()->id())
                                                                    <form action="{{ route('expenses.destroy', $expense->id) }}" 
                                                                          method="POST" class="d-inline" 
                                                                          onsubmit="return confirm('Are you sure you want to delete this expense?')">
                                                                        @csrf
                                                                        @method('DELETE')
                                                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                                            <i class="fas fa-trash"></i>
                                                                        </button>
                                                                    </form>
                                                                @endif
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="9" class="text-center py-4">
                                                            <div class="text-muted">
                                                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                                                <p>No expenses found.</p>
                                                                <a href="{{ route('expenses.create') }}" class="btn btn-primary">
                                                                    <i class="fas fa-plus"></i> Add Your First Expense
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Pagination -->
                                    @if($expenses->hasPages())
                                        <div class="d-flex justify-content-center mt-3">
                                            {{ $expenses->onEachSide(1)->links('pagination::bootstrap-5') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection
