@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">Edit Expense</h4>
                                <div class="page-title-right">
                                    <a href="{{ route('expenses.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Expenses
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <form action="{{ route('expenses.update', $expense->id) }}" method="POST" enctype="multipart/form-data">
                                        @csrf
                                        @method('PUT')

                                        <div class="row">
                                            <div class="col-md-6">
                                                <!-- Expense Name -->
                                                <div class="mb-3">
                                                    <label for="name" class="form-label">Expense Name <span class="text-danger">*</span></label>
                                                    <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror"
                                                           value="{{ old('name', $expense->name) }}" placeholder="Enter expense name" required>
                                                    @error('name')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <!-- Date -->
                                                <div class="mb-3">
                                                    <label for="date" class="form-label">Date <span class="text-danger">*</span></label>
                                                    <input type="date" name="date" id="date" class="form-control @error('date') is-invalid @enderror"
                                                           value="{{ old('date', $expense->date->format('Y-m-d')) }}" required>
                                                    @error('date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <!-- User Selection (Admin Only) -->
                                                @if(auth()->user()->role === 'super_admin' || auth()->user()->role === 'admin')
                                                    <div class="mb-3">
                                                        <label for="user_id" class="form-label">User <span class="text-danger">*</span></label>
                                                        <select name="user_id" id="user_id" class="form-select @error('user_id') is-invalid @enderror" required>
                                                            <option value="">-- Select User --</option>
                                                            @foreach($users as $user)
                                                                <option value="{{ $user->id }}"
                                                                        {{ old('user_id', $expense->user_id) == $user->id ? 'selected' : '' }}>
                                                                    {{ $user->name }}
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                        @error('user_id')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                @else
                                                    <!-- Auto-assign current user -->
                                                    <div class="mb-3">
                                                        <label class="form-label">User</label>
                                                        <input type="text" class="form-control" value="{{ $expense->user->name }}" readonly>
                                                        <small class="text-muted">This expense belongs to you</small>
                                                    </div>
                                                    <input type="hidden" name="user_id" value="{{ auth()->id() }}">
                                                @endif

                                                <!-- Given Amount -->
                                                <div class="mb-3">
                                                    <label for="given_amount" class="form-label">Given Amount <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">$</span>
                                                        <input type="number" name="given_amount" id="given_amount"
                                                               class="form-control @error('given_amount') is-invalid @enderror"
                                                               value="{{ old('given_amount', $expense->given_amount) }}" step="0.01" min="0"
                                                               placeholder="0.00" required>
                                                    </div>
                                                    @error('given_amount')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <!-- Picture Upload -->
                                                <div class="mb-3">
                                                    <label for="picture" class="form-label">Picture/Receipt</label>
                                                    <input type="file" name="picture" id="picture"
                                                           class="form-control @error('picture') is-invalid @enderror"
                                                           accept="image/*">
                                                    <small class="text-muted">Upload new receipt or related image (JPEG, PNG, JPG, GIF, max 2MB)</small>
                                                    @error('picture')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>

                                                <!-- Current Picture -->
                                                @if($expense->picture)
                                                    <div class="mb-3">
                                                        <label class="form-label">Current Picture</label>
                                                        <div class="border rounded p-2">
                                                            <img src="{{ $expense->picture_url }}" alt="Current Picture"
                                                                 class="img-fluid" style="max-height: 200px;">
                                                            <small class="text-muted d-block mt-1">Current: {{ $expense->picture }}</small>
                                                        </div>
                                                    </div>
                                                @endif

                                                <!-- Picture Preview -->
                                                <div class="mb-3" id="picture-preview" style="display: none;">
                                                    <label class="form-label">New Picture Preview</label>
                                                    <div class="border rounded p-2">
                                                        <img id="preview-img" src="" alt="Preview" class="img-fluid" style="max-height: 200px;">
                                                    </div>
                                                </div>

                                                <!-- Accepted Amount (Admin Only) -->
                                                @if(auth()->user()->role === 'super_admin' || auth()->user()->role === 'admin')
                                                    <div class="mb-3">
                                                        <label for="accepted_amount" class="form-label">Accepted Amount</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text">$</span>
                                                            <input type="number" name="accepted_amount" id="accepted_amount"
                                                                   class="form-control @error('accepted_amount') is-invalid @enderror"
                                                                   value="{{ old('accepted_amount', $expense->accepted_amount) }}" step="0.01" min="0"
                                                                   placeholder="0.00">
                                                        </div>
                                                        <small class="text-muted">Set the amount you're willing to accept (Admin only)</small>
                                                        @error('accepted_amount')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                @else
                                                    <!-- Hidden field for regular users -->
                                                    <input type="hidden" name="accepted_amount" value="{{ $expense->accepted_amount }}">
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Purpose -->
                                        <div class="mb-3">
                                            <label for="purpose" class="form-label">Purpose <span class="text-danger">*</span></label>
                                            <textarea name="purpose" id="purpose" rows="3"
                                                      class="form-control @error('purpose') is-invalid @enderror"
                                                      placeholder="Describe the purpose of this expense" required>{{ old('purpose', $expense->purpose) }}</textarea>
                                            @error('purpose')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Notes -->
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">Additional Notes</label>
                                            <textarea name="notes" id="notes" rows="2"
                                                      class="form-control @error('notes') is-invalid @enderror"
                                                      placeholder="Any additional notes or comments">{{ old('notes', $expense->notes) }}</textarea>
                                            @error('notes')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Submit Buttons -->
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save"></i> Update Expense
                                            </button>
                                            <a href="{{ route('expenses.index') }}" class="btn btn-secondary">
                                                <i class="fas fa-times"></i> Cancel
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection

@push('scripts')
<script>
// Picture preview functionality
document.getElementById('picture').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('picture-preview');
    const previewImg = document.getElementById('preview-img');

    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
});
</script>
@endpush
