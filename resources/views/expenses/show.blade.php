@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">Expense Details</h4>
                                <div class="page-title-right">
                                    <a href="{{ route('expenses.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Expenses
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <!-- Expense Details Card -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ $expense->name }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>Date:</strong></td>
                                                    <td>{{ $expense->formatted_date }}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Submitted By:</strong></td>
                                                    <td>{{ $expense->user->name }}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Status:</strong></td>
                                                    <td>
                                                        <span class="{{ $expense->status_badge_class }}">
                                                            {{ ucfirst($expense->status) }}
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Given Amount:</strong></td>
                                                    <td>${{ number_format($expense->given_amount, 2) }}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Accepted Amount:</strong></td>
                                                    <td>
                                                        @if($expense->accepted_amount)
                                                            ${{ number_format($expense->accepted_amount, 2) }}
                                                        @else
                                                            <span class="text-muted">Not set</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <strong>Purpose:</strong>
                                                <p class="mt-2">{{ $expense->purpose }}</p>
                                            </div>
                                            @if($expense->notes)
                                                <div class="mb-3">
                                                    <strong>Notes:</strong>
                                                    <p class="mt-2">{{ $expense->notes }}</p>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Picture/Receipt -->
                            @if($expense->picture)
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Receipt/Picture</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <img src="{{ $expense->picture_url }}" alt="Expense Receipt" 
                                             class="img-fluid" style="max-height: 400px;">
                                        <p class="text-muted mt-2">{{ $expense->picture }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="col-md-4">
                            <!-- Approval Section (Admin Only) -->
                            @if(auth()->user()->role === 'super_admin' || auth()->user()->role === 'admin')
                                @if($expense->status === 'pending')
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title mb-0">Approve/Reject Expense</h5>
                                        </div>
                                        <div class="card-body">
                                            <form action="{{ route('expenses.update-status', $expense->id) }}" method="POST">
                                                @csrf
                                                @method('PATCH')
                                                
                                                <div class="mb-3">
                                                    <label for="accepted_amount" class="form-label">Accepted Amount</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">$</span>
                                                        <input type="number" name="accepted_amount" id="accepted_amount" 
                                                               class="form-control" step="0.01" min="0" 
                                                               value="{{ $expense->accepted_amount ?? $expense->given_amount }}"
                                                               placeholder="0.00">
                                                    </div>
                                                    <small class="text-muted">Set the amount you're willing to accept</small>
                                                </div>

                                                <div class="mb-3">
                                                    <label for="notes" class="form-label">Admin Notes</label>
                                                    <textarea name="notes" id="notes" rows="3" 
                                                              class="form-control" 
                                                              placeholder="Add any notes about approval/rejection">{{ $expense->notes }}</textarea>
                                                </div>

                                                <div class="d-grid gap-2">
                                                    <button type="submit" name="status" value="approved" 
                                                            class="btn btn-success" 
                                                            onclick="return confirm('Are you sure you want to approve this expense?')">
                                                        <i class="fas fa-check"></i> Approve Expense
                                                    </button>
                                                    <button type="submit" name="status" value="rejected" 
                                                            class="btn btn-danger" 
                                                            onclick="return confirm('Are you sure you want to reject this expense?')">
                                                        <i class="fas fa-times"></i> Reject Expense
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                @else
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title mb-0">Expense Status</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="text-center">
                                                <span class="{{ $expense->status_badge_class }} fs-5">
                                                    {{ ucfirst($expense->status) }}
                                                </span>
                                                @if($expense->accepted_amount)
                                                    <div class="mt-3">
                                                        <strong>Accepted Amount:</strong><br>
                                                        ${{ number_format($expense->accepted_amount, 2) }}
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @endif

                            <!-- Action Buttons -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        @if(auth()->user()->role === 'super_admin' || auth()->user()->role === 'admin' || $expense->user_id === auth()->id())
                                            <a href="{{ route('expenses.edit', $expense->id) }}" class="btn btn-warning">
                                                <i class="fas fa-edit"></i> Edit Expense
                                            </a>
                                        @endif
                                        
                                        @if(auth()->user()->role === 'super_admin' || auth()->user()->role === 'admin' || $expense->user_id === auth()->id())
                                            <form action="{{ route('expenses.destroy', $expense->id) }}" 
                                                  method="POST" class="d-inline" 
                                                  onsubmit="return confirm('Are you sure you want to delete this expense?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger w-100">
                                                    <i class="fas fa-trash"></i> Delete Expense
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Expense Summary -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Summary</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="border-end">
                                                <h6 class="text-muted">Given Amount</h6>
                                                <h4 class="text-primary">${{ number_format($expense->given_amount, 2) }}</h4>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h6 class="text-muted">Accepted Amount</h6>
                                            <h4 class="text-success">
                                                @if($expense->accepted_amount)
                                                    ${{ number_format($expense->accepted_amount, 2) }}
                                                @else
                                                    -
                                                @endif
                                            </h4>
                                        </div>
                                    </div>
                                    @if($expense->accepted_amount && $expense->accepted_amount != $expense->given_amount)
                                        <div class="text-center mt-3">
                                            <small class="text-muted">
                                                Difference: ${{ number_format($expense->given_amount - $expense->accepted_amount, 2) }}
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')
@endsection
