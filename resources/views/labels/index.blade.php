@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Labels</h4>
                    <a href="{{ route('labels.create') }}" class="btn btn-primary">Add Label</a>
                </div>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Target Amount</th>
                            <th>Bonus Amount</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($labels as $label)
                            <tr>
                                <td>{{ $label->id }}</td>
                                <td>{{ $label->name }}</td>
                                <td>${{ number_format($label->bonus_target, 2) }}</td>
                                <td>${{ number_format($label->bonus_amount, 2) }}</td>
                                <td>{{ Str::limit($label->description, 50) }}</td>
                                <td>
                                    <span class="badge {{ $label->is_active ? 'bg-success' : 'bg-danger' }}">
                                        {{ $label->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ route('labels.edit', $label) }}" class="btn btn-warning btn-sm">Edit</a>
                                    <form action="{{ route('labels.destroy', $label) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button onclick="return confirm('Are you sure?')" class="btn btn-danger btn-sm">Delete</button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr><td colspan="7">No labels found.</td></tr>
                        @endforelse
                    </tbody>
                </table>

                {{ $labels->links() }}

            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
