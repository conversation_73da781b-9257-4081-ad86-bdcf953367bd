@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Add Label</h4>
                    <a href="{{ route('labels.index') }}" class="btn btn-secondary">Back to List</a>
                </div>

                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form action="{{ route('labels.store') }}" method="POST">
                    @csrf

                    <div class="mb-3">
                        <label for="name" class="form-label">Label Name</label>
                        <input type="text" name="name" id="name" class="form-control" value="{{ old('name') }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="bonus_target" class="form-label">Target Amount ($)</label>
                        <input type="number" name="bonus_target" id="bonus_target" step="0.01" min="0" class="form-control" value="{{ old('bonus_target') }}" required>
                        <small class="form-text text-muted">The amount that needs to be achieved to earn this label's bonus (e.g., $100 for Label 1)</small>
                    </div>

                    <div class="mb-3">
                        <label for="bonus_amount" class="form-label">Bonus Amount ($)</label>
                        <input type="number" name="bonus_amount" id="bonus_amount" step="0.01" min="0" class="form-control" value="{{ old('bonus_amount') }}" required>
                        <small class="form-text text-muted">The bonus amount that will be given when the target is achieved</small>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea name="description" id="description" class="form-control" rows="3">{{ old('description') }}</textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" name="is_active" id="is_active" class="form-check-input" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                            <label for="is_active" class="form-check-label">Active</label>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-success">Create Label</button>
                </form>

            </div>
        </div>
        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
