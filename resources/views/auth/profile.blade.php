@extends('layouts.master')

@section('maincontent')
    <div id="layout-wrapper">
        @include('layouts.sections.navbar.navbar')
        @include('layouts.sections.menu.sidebar')
        <div class="main-content">
            <div class="page-content">
                <div class="mx-auto p-4">
                    <form action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            @if (session('success'))
                                <div class="alert alert-success">{{ session('success') }}</div>
                            @endif

                            <!-- Left Profile Section -->
                            <div class="col-md-4 mb-4">
                                <div class="p-4 rounded shadow-sm">
                                    <div class="text-center">
                                        <div class="position-relative d-inline-block">
                                            @php
                                                $profilePicPath = public_path($user->picture);
                                                $profilePicture =
                                                    !empty($user->picture) && file_exists($profilePicPath)
                                                        ? asset($user->picture)
                                                        : asset('assets/images/profile.png');
                                            @endphp


                                            <img src="{{ $profilePicture }}" alt="Profile Picture"
                                                class="rounded-circle border border-4 border-light"
                                                style="width: 96px; height: 96px;">

                                            <label
                                                class="position-absolute bottom-0 end-0 bg-primary text-white rounded-circle p-1 cursor-pointer"
                                                style="width: 24px; height: 24px;">
                                                📷
                                                <input id="profilePictureInput" name="profilepicture" type="file"
                                                    class="d-none" accept=".jpg, .jpeg, .png, .svg">
                                            </label>
                                        </div>

                                        <h2 class="h5 font-weight-bold mt-2">{{ $user->name }}</h2>
                                        <a href="#" class="btn btn-light w-100 mt-3">Role:
                                            {{ ucfirst($user->role) }}</a>
                                        <a href="{{ url('/') }}" class="btn btn-light w-100 mt-3">Visit Main Site</a>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Account Setup Section -->
                            <div class="col-md-8">
                                <div class="p-4 rounded shadow-sm">
                                    <h2 class="h4 font-weight-bold mb-4">Account setup</h2>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Full Name</label>
                                            <input type="text" name="name" value="{{ old('name', $user->name) }}"
                                                class="form-control">
                                            @error('name')
                                                <small class="text-danger">{{ $message }}</small>
                                            @enderror
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Phone Number</label>
                                            <input type="text" name="number" value="{{ old('number', $user->number) }}"
                                                class="form-control">
                                            @error('number')
                                                <small class="text-danger">{{ $message }}</small>
                                            @enderror
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">E-Mail Address</label>
                                            <input type="email" name="email" value="{{ old('email', $user->email) }}"
                                                class="form-control">
                                            @error('email')
                                                <small class="text-danger">{{ $message }}</small>
                                            @enderror
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Address</label>
                                            <input type="text" name="address"
                                                value="{{ old('address', $user->address) }}" class="form-control">
                                            @error('address')
                                                <small class="text-danger">{{ $message }}</small>
                                            @enderror
                                        </div>
                                    </div>


                                    <div class="d-flex justify-content-between align-items-center mt-4">
                                        <button type="submit" class="btn btn-primary">Update Profile</button>
                                        <a href="{{ route('password.request') }}">Reset Password</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            @include('layouts.sections.footer.footer')
        </div>
    </div>
    @include('layouts.sections.menu.rightsidebar')

    <script>
        document.getElementById('profilePictureInput').addEventListener('change', function(event) {
            const file = event.target.files[0];

            if (file) {
                const allowedTypes = ['image/jpeg', 'image/png', 'image/svg+xml'];
                const maxSize = 5 * 1024 * 1024; // 5MB

                if (!allowedTypes.includes(file.type)) {
                    alert('Only JPG, JPEG, PNG, and SVG files are allowed.');
                    event.target.value = ''; // Reset file input
                    return;
                }

                if (file.size > maxSize) {
                    alert('File size must not exceed 5MB.');
                    event.target.value = ''; // Reset file input
                }
            }
        });
    </script>
@endsection
