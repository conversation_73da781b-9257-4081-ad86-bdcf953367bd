<div class="vertical-menu">
    <div data-simplebar class="h-100">
        <div id="sidebar-menu">
            <ul class="metismenu list-unstyled" id="side-menu">
                <li class="menu-title">Menu</li>

                @auth
                    <!-- Dashboard -->
                    <li class="{{ request()->routeIs('dashboard') ? 'mm-active' : '' }}">
                        <a href="{{ route('dashboard') }}" class="waves-effect">
                            <i class="ri-home-3-line"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>

                    <!-- Content Management -->
                    @if(auth()->user()->can('access-content-management'))
                        <!-- Blogs -->
                        <li class="has-submenu {{ request()->routeIs('blogs.*') || request()->routeIs('blog-categories.*') ? 'mm-active' : '' }}">
                            <a href="#blogsDropdown" data-bs-toggle="collapse"
                               aria-expanded="{{ request()->routeIs('blogs.*') || request()->routeIs('blog-categories.*') ? 'true' : 'false' }}">
                                <i class="ri-newspaper-line"></i><span>Blogs</span>
                                <i class="ri-arrow-down-s-line float-end"></i>
                            </a>
                            <ul class="submenu collapse {{ request()->routeIs('blogs.*') || request()->routeIs('blog-categories.*') ? 'show' : '' }}" id="blogsDropdown">
                                <li class="{{ request()->routeIs('blogs.index') ? 'mm-active' : '' }}">
                                    <a href="{{ route('blogs.index') }}">All Blogs</a>
                                </li>
                                @can('manage-blog-categories')
                                <li class="{{ request()->routeIs('blog-categories.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('blog-categories.index') }}">Blog Categories</a>
                                </li>
                                @endcan
                            </ul>
                        </li>

                        <!-- Services -->
                        @can('view-services')
                        <li class="{{ request()->routeIs('services.index') ? 'mm-active' : '' }}">
                            <a href="{{ route('services.index') }}">
                                <i class="ri-briefcase-line"></i> All Services
                            </a>
                        </li>
                        @endcan

                        <!-- Contacts -->
                        @can('view-contacts')
                        <li class="{{ request()->routeIs('contacts.index') ? 'mm-active' : '' }}">
                            <a href="{{ route('contacts.index') }}">
                                <i class="ri-contacts-line"></i> Contacts
                            </a>
                        </li>
                        @endcan
                    @endif

                    <!-- Project Management -->
                    @if(auth()->user()->can('access-project-management'))
                        <!-- Projects -->
                        <li class="{{ request()->routeIs('projects.index') ? 'mm-active' : '' }}">
                            <a href="{{ route('projects.index') }}">
                                <i class="ri-projector-line"></i> Projects
                            </a>
                        </li>

                        <!-- Project Management -->
                        @can('manage-projects')
                        <li class="{{ request()->routeIs('project-managements.index') ? 'mm-active' : '' }}">
                            <a href="{{ route('project-managements.index') }}">
                                <i class="ri-task-line"></i> Project Management
                            </a>
                        </li>
                        @endcan

                        <!-- Marketplace Accounts -->
                        @can('manage-marketplace')
                        <li class="{{ request()->routeIs('marketplace_accounts.index') ? 'mm-active' : '' }}">
                            <a href="{{ route('marketplace_accounts.index') }}">
                                <i class="ri-store-line"></i> Marketplace Accounts
                            </a>
                        </li>
                        @endcan
                    @endif

                    <!-- HR Management -->
                    @if(auth()->user()->can('access-hr-management'))
                        <!-- Leave Requests -->
                        <li class="{{ request()->routeIs('leave_requests.index') ? 'mm-active' : '' }}">
                            <a href="{{ route('leave_requests.index') }}">
                                <i class="ri-calendar-event-line"></i> Leave Requests
                            </a>
                        </li>

                        <!-- Teams -->
                        @can('view-teams')
                        <li class="{{ request()->routeIs('teams.index') ? 'mm-active' : '' }}">
                            <a href="{{ route('teams.index') }}">
                                <i class="ri-group-line"></i> Teams
                            </a>
                        </li>
                        @endcan

                        <!-- Salaries -->
                        @can('view-salaries')
                        <li class="{{ request()->routeIs('salaries.index') ? 'mm-active' : '' }}">
                            <a href="{{ route('salaries.index') }}">
                                <i class="ri-money-dollar-box-line"></i> Salaries
                            </a>
                        </li>
                        @endcan
                    @endif

                    <!-- Admin Sections -->
                    @admin
                        <!-- User Management -->
                        @can('manage-users')
                        <li class="{{ request()->routeIs('users.*') ? 'mm-active' : '' }}">
                            <a href="{{ route('users.index') }}">
                                <i class="ri-user-line"></i> User Management
                            </a>
                        </li>
                        @endcan

                        <!-- Designations -->
                        @can('manage-designations')
                        <li class="{{ request()->routeIs('designations.index') ? 'mm-active' : '' }}">
                            <a href="{{ route('designations.index') }}">
                                <i class="ri-briefcase-line"></i> Designations
                            </a>
                        </li>
                        @endcan

                        <!-- Labels -->
                        @can('manage-labels')
                        <li class="{{ request()->routeIs('labels.index') ? 'mm-active' : '' }}">
                            <a href="{{ route('labels.index') }}">
                                <i class="ri-price-tag-3-line"></i> Labels
                            </a>
                        </li>
                        @endcan
                    @endadmin
                @endauth
            </ul>
        </div>
    </div>
</div>
