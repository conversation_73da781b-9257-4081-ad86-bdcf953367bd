<div class="vertical-menu">
    <div data-simplebar class="h-100">

        <!--- Sidemenu -->
        <div id="sidebar-menu">
            <!-- Left Menu Start -->
            <ul class="metismenu list-unstyled" id="side-menu">

                {{-- <li class="menu-title">Frontend Menu</li> --}}

                {{-- ================== FRONTEND SECTIONS ================== --}}
                @if (auth()->check() &&
                        in_array(auth()->user()->permission, ['read', 'read_write', 'full']) &&
                        auth()->user()->role !== 'user')
                    <!-- Blogs -->
                    {{-- <li class="has-submenu {{ request()->routeIs('blogs.*') || request()->routeIs('blog-categories.*') ? 'mm-active' : '' }}">
                        <a href="#blogsDropdown" data-bs-toggle="collapse"
                           aria-expanded="{{ request()->routeIs('blogs.*') || request()->routeIs('blog-categories.*') ? 'true' : 'false' }}">
                            <i class="ri-newspaper-line"></i><span>Blogs</span>
                            <i class="ri-arrow-down-s-line float-end"></i>
                        </a>
                        <ul class="submenu collapse {{ request()->routeIs('blogs.*') || request()->routeIs('blog-categories.*') ? 'show' : '' }}" id="blogsDropdown">
                            <li class="{{ request()->routeIs('blogs.index') ? 'mm-active' : '' }}">
                                <a href="{{ route('blogs.index') }}">All Blogs</a>
                            </li>
                            <li class="{{ request()->routeIs('blog-categories.index') ? 'mm-active' : '' }}">
                                <a href="{{ route('blog-categories.index') }}">Blog Categories</a>
                            </li>
                        </ul>
                    </li>

                    <!-- Services -->
                    <li class="{{ request()->routeIs('services.index') ? 'mm-active' : '' }}">
                        <a href="{{ route('services.index') }}">
                            <i class="ri-briefcase-line"></i>
                            All Services
                        </a>
                    </li>

                    <!-- Contacts -->
                    <li class="{{ request()->routeIs('contacts.index') ? 'mm-active' : '' }}">
                        <a href="{{ route('contacts.index') }}">
                            <i class="ri-contacts-line"></i>
                            Contacts
                        </a>
                    </li>

                    <!-- Projects -->
                    <li class="{{ request()->routeIs('projects.index') ? 'mm-active' : '' }}">
                        <a href="{{ route('projects.index') }}">
                            <i class="ri-projector-line"></i>
                            Projects
                        </a>
                    </li> --}}
                @endif

                {{-- ================== DASHBOARDS ================== --}}
                @if (auth()->check() && in_array(auth()->user()->role, ['admin', 'super_admin']))
                    <!-- Admin Dashboard -->
                    <li class="{{ request()->routeIs('dashboard') ? 'mm-active' : '' }}"> <a
                            href="{{ route('dashboard') }}" class="waves-effect"> <i class="ri-home-3-line"></i>
                            <span>Dashboard</span> </a> </li>
                @endif

                @if (auth()->check() && auth()->user()->role === 'user')
                    <!-- User Dashboard -->
                    <li class="{{ request()->routeIs('dashboard.user') ? 'mm-active' : '' }}">
                        <a href="{{ route('dashboard.user') }}" class="waves-effect">
                            <i class="ri-dashboard-line"></i>
                            <span>My Dashboard</span>
                        </a>
                    </li>
                @endif

                {{-- ================== COMMON SECTIONS ================== --}}
                <li class="menu-title">Users Menu</li>

                @if (auth()->check())
                    <!-- ================== Project Management ================== -->
                    <li>
                        <a href="javascript:void(0);" class="has-arrow waves-effect">
                            <i class="ri-projector-line"></i>
                            <span>Project Management</span>
                        </a>
                        <ul class="sub-menu" aria-expanded="false">
                            <li class="{{ request()->routeIs('project-managements.my') ? 'mm-active' : '' }}">
                                <a href="{{ route('project-managements.my') }}">
                                    <i class="ri-projector-2-line"></i> My Projects
                                </a>
                            </li>
                            <li class="{{ request()->routeIs('special-projects.my') ? 'mm-active' : '' }}">
                                <a href="{{ route('special-projects.my') }}">
                                    <i class="ri-lightbulb-line"></i> My Special Projects
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- ================== HR Management ================== -->
                    <li>
                        <a href="javascript:void(0);" class="has-arrow waves-effect">
                            <i class="ri-team-line"></i>
                            <span>HR Management</span>
                        </a>
                        <ul class="sub-menu" aria-expanded="false">
                            <li class="{{ request()->routeIs('salaries.my*') ? 'mm-active' : '' }}">
                                <a href="{{ route('salaries.my') }}">
                                    <i class="ri-file-list-3-line"></i> My Payslips
                                </a>
                            </li>
                            <li class="{{ request()->routeIs('salaries.target-achievement') ? 'mm-active' : '' }}">
                                <a href="{{ route('salaries.target-achievement') }}">
                                    <i class="ri-bar-chart-2-line"></i> My Target vs Achieve
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- ================== Financial Management ================== -->
                    <li>
                        <a href="javascript:void(0);" class="has-arrow waves-effect">
                            <i class="ri-file-paper-line"></i>
                            <span>Financials</span>
                        </a>
                        <ul class="sub-menu" aria-expanded="false">
                            <li class="{{ request()->routeIs('expenses.my') ? 'mm-active' : '' }}">
                                <a href="{{ route('expenses.my') }}">
                                    <i class="ri-file-paper-line"></i> My Expenses
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- ================== Attendance & Duty ================== -->
                    <li>
                        <a href="javascript:void(0);" class="has-arrow waves-effect">
                            <i class="ri-calendar-check-line"></i>
                            <span>Attendance & Duty</span>
                        </a>
                        <ul class="sub-menu" aria-expanded="false">
                            <li class="{{ request()->routeIs('attendance.my*') ? 'mm-active' : '' }}">
                                <a href="{{ route('attendance.my') }}">
                                    <i class="ri-calendar-check-line"></i> My Attendance
                                </a>
                            </li>
                            <li class="{{ request()->routeIs('duty_shift.my.*') ? 'mm-active' : '' }}">
                                <a href="{{ route('duty_shift.my.schedule') }}">
                                    <i class="ri-time-line"></i> My Duty Schedule
                                </a>
                            </li>
                        </ul>
                    </li>
                @endif


                <li class="menu-title">Admin Menu</li>
                @if (auth()->check() && in_array(auth()->user()->role, ['admin', 'super_admin']))
                    <ul class="metismenu list-unstyled" id="side-menu">

                        <!-- ================== Project Management ================== -->
                        <li>
                            <a href="javascript:void(0);" class="has-arrow waves-effect">
                                <i class="ri-projector-line"></i>
                                <span>Project Management</span>
                            </a>
                            <ul class="sub-menu" aria-expanded="false">
                                <li class="{{ request()->routeIs('project-managements.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('project-managements.index') }}">
                                        <i class="ri-projector-2-line"></i> Project Management
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('special-projects.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('special-projects.index') }}">
                                        <i class="ri-lightbulb-line"></i> Special Projects
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('profits.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('profits.index') }}">
                                        <i class="ri-bar-chart-grouped-line"></i> Profit Table
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('expenses.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('expenses.index') }}">
                                        <i class="ri-file-paper-line"></i> Expenses
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('loans.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('loans.index') }}">
                                        <i class="ri-money-dollar-circle-line"></i> Loans
                                    </a>
                                </li>
                            </ul>
                        </li>

                        <!-- ================== Accounts Management ================== -->
                        <li>
                            <a href="javascript:void(0);" class="has-arrow waves-effect">
                                <i class="ri-store-2-line"></i>
                                <span>Accounts Management</span>
                            </a>
                            <ul class="sub-menu" aria-expanded="false">
                                <li class="{{ request()->routeIs('marketplace_accounts.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('marketplace_accounts.index') }}">
                                        <i class="ri-shopping-cart-2-line"></i> Marketplace Accounts
                                    </a>
                                </li>
                                <li
                                    class="{{ request()->routeIs('marketplace-account-categories.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('marketplace-account-categories.index') }}">
                                        <i class="ri-folder-2-line"></i> Account Categories
                                    </a>
                                </li>
                            </ul>
                        </li>

                        <!-- ================== HR Management ================== -->
                        <li>
                            <a href="javascript:void(0);" class="has-arrow waves-effect">
                                <i class="ri-team-line"></i>
                                <span>HR Management</span>
                            </a>
                            <ul class="sub-menu" aria-expanded="false">
                                <li class="{{ request()->routeIs('salaries.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('salaries.index') }}">
                                        <i class="ri-money-dollar-box-line"></i> Salaries
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('salaries.bonus-dashboard') ? 'mm-active' : '' }}">
                                    <a href="{{ route('salaries.bonus-dashboard') }}">
                                        <i class="ri-dashboard-3-line"></i> Bonus Dashboard
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('team-achievements.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('team-achievements.index') }}">
                                        <i class="ri-trophy-line"></i> Team Achievements
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('attendance.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('attendance.index') }}">
                                        <i class="ri-calendar-todo-line"></i> Attendance
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('duty_shift.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('duty_shift.index') }}">
                                        <i class="ri-timer-line"></i> Duty Shifts
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('leave_requests.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('leave_requests.index') }}">
                                        <i class="ri-calendar-event-line"></i> Leave Requests
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('designations.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('designations.index') }}">
                                        <i class="ri-briefcase-line"></i> Designations
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('labels.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('labels.index') }}">
                                        <i class="ri-price-tag-3-line"></i> Labels
                                    </a>
                                </li>
                            </ul>
                        </li>

                        <!-- ================== User Management ================== -->
                        <li>
                            <a href="javascript:void(0);" class="has-arrow waves-effect">
                                <i class="ri-user-settings-line"></i>
                                <span>User Management</span>
                            </a>
                            <ul class="sub-menu" aria-expanded="false">
                                <li class="{{ request()->routeIs('users.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('users.index') }}">
                                        <i class="ri-user-3-line"></i> Users
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('teams.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('teams.index') }}">
                                        <i class="ri-group-line"></i> Team Members
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('permissions.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('permissions.index') }}">
                                        <i class="ri-shield-line"></i> Permissions
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('roles.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('roles.index') }}">
                                        <i class="ri-shield-user-line"></i> Roles
                                    </a>
                                </li>

                                <!-- Audit Logs -->
                                <li class="{{ request()->routeIs('audit-logs.*') ? 'mm-active' : '' }}">
                                    <a href="{{ route('audit-logs.index') }}">
                                        <i class="ri-file-list-line"></i> Audit Logs
                                    </a>
                                </li>
                            </ul>
                        </li>

                    </ul>
                @endif



            </ul>
        </div>
        <!-- Sidebar -->
    </div>
</div>
