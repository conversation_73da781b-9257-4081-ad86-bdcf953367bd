@extends('layouts.master')

@section('maincontent')
<div id="layout-wrapper">
    @include('layouts.sections.navbar.navbar')
    @include('layouts.sections.menu.sidebar')

    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Designations</h4>
                    <a href="{{ route('designations.create') }}" class="btn btn-primary">Add Designation</a>
                </div>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Base Amount</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($designations as $designation)
                            <tr>
                                <td>{{ $designation->id }}</td>
                                <td>{{ $designation->name }}</td>
                                <td>${{ number_format($designation->base_amount, 2) }}</td>
                                <td>
                                    <a href="{{ route('designations.edit', $designation) }}" class="btn btn-warning btn-sm">Edit</a>
                                    <form action="{{ route('designations.destroy', $designation) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button onclick="return confirm('Are you sure?')" class="btn btn-danger btn-sm">Delete</button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4">No designations found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>

                {{ $designations->links() }}

            </div>
        </div>

        @include('layouts.sections.footer.footer')
    </div>
</div>

@include('layouts.sections.menu.rightsidebar')
@endsection
