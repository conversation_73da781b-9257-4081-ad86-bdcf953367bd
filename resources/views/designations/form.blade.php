
        <div class="mb-3">
            <label>User</label>
            <select name="user_id" class="form-control" required>
                <option value="">Select User</option>
                @foreach ($users as $user)
                    <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                        {{ $user->name }}
                    </option>
                @endforeach
            </select>
        </div>

        <div class="mb-3">
            <label>Designation</label>
            <input type="text" name="designation" class="form-control" value="{{ old('designation') }}" required>
        </div>

        <div class="mb-3">
            <label>Salary</label>
            <select name="salary_id" class="form-control" required>
                <option value="">Select Salary</option>
                @foreach ($salaries as $salary)
                    <option value="{{ $salary->id }}" {{ old('salary_id') == $salary->id ? 'selected' : '' }}>
                        {{ $salary->amount }} {{ $salary->currency }} ({{ $salary->description }})
                    </option>
                @endforeach
            </select>
        </div>

        <div class="mb-3">
            <label>Joining Date</label>
            <input type="date" name="joining_date" class="form-control" value="{{ old('joining_date') }}">
        </div>

        <div class="mb-3">
            <label>Target</label>
            <input type="text" name="target" class="form-control" value="{{ old('target') }}">
        </div>

        <div class="mb-3">
            <label>Status</label>
            <select name="status" class="form-control" required>
                <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
            </select>
        </div>

        <div class="mb-3">
            <label>Leave Status</label>
            <select name="leave_status" class="form-control" required>
                <option value="on_duty" {{ old('leave_status') == 'on_duty' ? 'selected' : '' }}>On Duty</option>
                <option value="on_leave" {{ old('leave_status') == 'on_leave' ? 'selected' : '' }}>On Leave</option>
            </select>
        </div>

        <div class="mb-3">
            <label>Leave Reason</label>
            <input type="text" name="leave_reason" class="form-control" value="{{ old('leave_reason') }}">
        </div>

        <div class="mb-3">
            <label>Leave Duration</label>
            <input type="text" name="leave_duration" class="form-control" value="{{ old('leave_duration') }}">
        </div>

        <div class="mb-3">
            <label>Leave Approved By</label>
            <input type="text" name="leave_approved_by" class="form-control" value="{{ old('leave_approved_by') }}">
        </div>

