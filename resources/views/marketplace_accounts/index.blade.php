@extends('layouts.master')

@section('maincontent')
<!-- <body data-layout="horizontal" data-topbar="dark"> -->
<!-- Begin page -->
<div id="layout-wrapper">
@include('layouts.sections.navbar.navbar')
<!-- ========== Left Sidebar Start ========== -->
@include('layouts.sections.menu.sidebar')
<!-- Left Sidebar End -->
<!-- ============================================================== -->
<!-- Start right Content here -->
<!-- ============================================================== -->
    <div class="main-content">
        <!-- start Page-content -->
        <div class="page-content">



            <div class="container-fluid">
                <h2>Marketplace Accounts</h2>
                <a href="{{ route('marketplace_accounts.create') }}" class="btn btn-primary mb-3">Add New Account</a>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif

                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Order Account Name</th>
                            <th>Created Date</th>
                            <th>Username</th>
                            <th>Password</th>
                            <th>WiFi IP</th>
                            <th>Category</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($accounts as $account)
                            <tr>
                                <td>{{ $account->order_account_name }}</td>
                                <td>{{ $account->created_date }}</td>
                                <td>{{ $account->username }}</td>
                                <td>{{ $account->password }}</td>
                                <td>{{ $account->wifi_ip }}</td>
                                <td>
                                    @if($account->category)
                                        <span class="badge bg-info">{{ $account->category->name }}</span>
                                        <br><small class="text-muted">{{ number_format($account->category->delivery_amount_charge_percentage, 2) }}%</small>
                                    @else
                                        <span class="text-muted">No Category</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('marketplace_accounts.edit', $account->id) }}" class="btn btn-sm btn-warning">Edit</a>
                                    <form action="{{ route('marketplace_accounts.destroy', $account->id) }}" method="POST" style="display:inline-block;">
                                        @csrf
                                        @method('DELETE')
                                        <button class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">Delete</button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>






        </div>

        <!-- End Page-content -->
@include('layouts.sections.footer.footer')

    </div>
    <!-- end main content-->

</div>
<!-- END layout-wrapper -->

@include('layouts.sections.menu.rightsidebar')

@endsection




