<div class="mb-3">
    <label>Order Account Name</label>
    <input type="text" name="order_account_name" class="form-control"
           value="{{ old('order_account_name', $account->order_account_name ?? '') }}" required>
</div>

<div class="mb-3">
    <label>Created Date</label>
    <input type="datetime-local" name="created_date" class="form-control"
           value="{{ old('created_date', isset($account->created_date) ? \Carbon\Carbon::parse($account->created_date)->format('Y-m-d\TH:i') : '') }}" required>
</div>

<div class="mb-3">
    <label>Username</label>
    <input type="text" name="username" class="form-control"
           value="{{ old('username', $account->username ?? '') }}" required>
</div>

<div class="mb-3">
    <label>Password</label>
    <input type="text" name="password" class="form-control"
           value="{{ old('password', $account->password ?? '') }}" required>
</div>

<div class="mb-3">
    <label>WiFi IP</label>
    <input type="text" name="wifi_ip" class="form-control"
           value="{{ old('wifi_ip', $account->wifi_ip ?? '') }}">
</div>

<div class="mb-3">
    <label>Category <span class="text-danger">*</span></label>
    <select name="category_id" class="form-control @error('category_id') is-invalid @enderror" required>
        <option value="">Select Category</option>
        @foreach(\App\Models\MarketplaceAccountCategory::active()->get() as $category)
            <option value="{{ $category->id }}" {{ old('category_id', $account->category_id ?? '') == $category->id ? 'selected' : '' }}>
                {{ $category->name }} - {{ number_format($category->delivery_amount_charge_percentage, 2) }}%
            </option>
        @endforeach
    </select>
    @error('category_id')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <small class="text-muted">Category is required for delivery amount calculations</small>
</div>

<button type="submit" class="btn btn-success">{{ $buttonText }}</button>
<a href="{{ route('marketplace_accounts.index') }}" class="btn btn-secondary">Cancel</a>
