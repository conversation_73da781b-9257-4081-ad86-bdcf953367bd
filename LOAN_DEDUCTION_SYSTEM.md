# Loan Deduction Tracking System

## Overview
The loan system has been updated to track loan deductions and maintain accurate due amounts. When money is taken from a loan account, the deducted amount is added to the `due_amounts` field, providing clear visibility into loan usage and remaining balances.

## How It Works

### Loan Structure
- **Original Amount**: The initial loan amount
- **Due Amount**: Total amount that needs to be repaid (original + deductions)
- **Total Deducted**: Amount that has been taken from the loan
- **Available Balance**: Remaining amount that can be deducted
- **Paid Amount**: Amount that has been repaid

### Example Scenario
1. **Initial Loan**: $1000
2. **First Deduction**: $200 (for project payment)
   - Due Amount becomes: $1200
   - Available Balance: $1000
3. **Second Deduction**: $300 (for equipment)
   - Due Amount becomes: $1500
   - Available Balance: $700
4. **Payment**: $500
   - Due Amount remains: $1500
   - Available Balance: $1200

## Key Features

### 1. Loan Deduction Tracking
- **Add Deductions**: Record when money is taken from the loan
- **Automatic Updates**: Due amounts are automatically updated
- **Balance Validation**: Prevents overdrafts
- **Audit Trail**: All deductions are logged with timestamps

### 2. Enhanced Loan Management
- **Real-time Balance**: Always shows current available balance
- **Deduction History**: Track all deductions with descriptions
- **Payment Tracking**: Monitor repayments against total due amount
- **Status Management**: Clear loan status indicators

### 3. User Interface
- **Deduction Form**: Easy-to-use form for adding deductions
- **Loan Dashboard**: Comprehensive view of loan status
- **Quick Actions**: Fast access to common operations
- **Visual Indicators**: Color-coded amounts for easy understanding

## Database Structure

### Loans Table
```sql
- id (primary key)
- amount (original loan amount)
- loan_amount_charge_percentage (interest rate)
- marketplace_id (foreign key to marketplace_accounts)
- due_amounts (total amount due - original + deductions)
- description (loan description)
- status (pending, approved, rejected, paid, overdue)
- paid_date (when loan was paid)
- paid_amount (amount paid back)
- notes (deduction history and notes)
- timestamps
```

## New Methods Added

### Loan Model Methods
```php
// Add a deduction to the loan
$loan->addDeduction($amount, $description);

// Get original loan amount
$loan->original_amount;

// Get total amount deducted
$loan->total_deducted;

// Check available balance
$loan->hasAvailableBalance($amount);

// Get remaining amount to be paid
$loan->remaining_amount;
```

### Controller Methods
```php
// Show deduction form
LoanController@showDeductionForm

// Add deduction
LoanController@addDeduction
```

## Usage Instructions

### 1. Creating a Loan
1. Go to Loans section
2. Click "Add New Loan"
3. Fill in loan details:
   - Original amount
   - Marketplace account
   - Due amount (initially same as original)
   - Description

### 2. Adding Deductions
1. Go to loan details page
2. Click "Add Deduction" button
3. Fill in deduction form:
   - Deduction amount
   - Description (what it's for)
   - Additional notes
4. System automatically updates due amount

### 3. Tracking Loan Status
- **Original Amount**: Shows initial loan amount
- **Total Deducted**: Shows how much has been taken
- **Due Amount**: Shows total amount to be repaid
- **Available Balance**: Shows remaining amount that can be deducted
- **Paid Amount**: Shows how much has been repaid

## Example Workflow

### Scenario: Project Loan
1. **Create Loan**: $5000 for project funding
2. **First Deduction**: $1000 for materials
   - Due Amount: $6000
   - Available: $5000
3. **Second Deduction**: $2000 for labor
   - Due Amount: $8000
   - Available: $3000
4. **Payment**: $3000
   - Due Amount: $8000
   - Available: $6000

### Benefits
- **Clear Tracking**: Always know how much has been used
- **Accurate Billing**: Due amounts reflect actual usage
- **Prevent Overdrafts**: System prevents taking more than available
- **Audit Trail**: Complete history of all transactions
- **Easy Management**: Simple interface for loan operations

## Routes Added
```php
// Show deduction form
GET /loans/{loan}/deduction

// Add deduction
POST /loans/{loan}/deduction
```

## Views Updated
- **Loan Index**: Shows deduction tracking columns
- **Loan Show**: Displays detailed loan information with deduction history
- **Deduction Form**: New form for adding deductions

## Security Features
- **Balance Validation**: Prevents overdrafts
- **Amount Limits**: Maximum deduction based on available balance
- **Audit Logging**: All deductions are logged with timestamps
- **Status Checks**: Only approved loans can have deductions

This system provides complete transparency and control over loan usage, ensuring accurate tracking of borrowed funds and their utilization.
