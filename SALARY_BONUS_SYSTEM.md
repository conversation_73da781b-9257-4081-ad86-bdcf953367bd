# Salary and Bonus System Implementation

## Overview
This system implements a comprehensive salary and bonus management system based on team member targets and label achievements. The system allows for flexible bonus calculations using either fixed amounts or percentage-based bonuses.

## Key Features

### 1. Label-Based Bonus System
- **Labels**: Each team member is assigned a label that determines their bonus structure
- **Bonus Types**:
  - **Fixed Amount**: A predetermined bonus amount (e.g., $100)
  - **Percentage-Based**: A percentage of the achieved amount (e.g., 10% of $1150 = $115)
- **Flexible Configuration**: Labels can be activated/deactivated and have descriptions

### 2. Team Member Target System
- **Target Amount**: Each team member has a monthly target amount (e.g., $1000)
- **Base Salary**: Guaranteed base salary regardless of achievements
- **Achievement Tracking**: Real-time tracking of achieved amounts
- **Performance Metrics**: Achievement percentage and status tracking

### 3. Achievement Recording
- **Team Achievements**: Record individual achievements with specific amounts
- **Automatic Bonus Calculation**: Bonuses are calculated automatically based on label settings
- **Payment Tracking**: Track which bonuses have been paid
- **Date-based Reporting**: Filter achievements by date ranges

## Database Structure

### Updated Tables

#### Labels Table
```sql
- id (primary key)
- name (label name)
- bonus_percent (percentage for bonus calculation)
- bonus_amount (fixed bonus amount, overrides percentage)
- description (optional description)
- is_active (active/inactive status)
- timestamps
```

#### Teams Table (Updated)
```sql
- id (primary key)
- user_id (foreign key to users)
- designation_id (foreign key to designations)
- label_id (foreign key to labels)
- target_amount (monthly target amount in dollars)
- achieved_amount (current achieved amount)
- base_salary (base salary amount)
- [existing fields...]
```

#### Team Achievements Table (New)
```sql
- id (primary key)
- team_id (foreign key to teams)
- label_id (foreign key to labels)
- achieved_amount (amount achieved)
- bonus_earned (calculated bonus amount)
- achievement_date (date of achievement)
- notes (optional notes)
- is_paid (payment status)
- timestamps
```

## How the System Works

### Example Scenario
1. **Team Member Setup**:
   - John is assigned to "Label 1"
   - His target amount is $1000
   - His base salary is $500
   - Label 1 has a fixed bonus of $100

2. **Achievement Recording**:
   - John achieves $1150 in a month
   - System records this as a team achievement
   - Since he exceeded his target ($1000), he gets his base salary ($500) + bonus ($100) = $600 total

3. **Multiple Labels Example**:
   - If John had "Label 2" with 10% bonus
   - He would get: Base salary ($500) + 10% of $1150 ($115) = $615 total

### Bonus Calculation Logic
```php
// In Label model
public function calculateBonus($achievedAmount): float
{
    // If fixed bonus amount is set, use it
    if ($this->bonus_amount > 0) {
        return $this->bonus_amount;
    }
    
    // Otherwise calculate based on percentage
    return ($achievedAmount * $this->bonus_percent) / 100;
}
```

## Controllers and Views

### Controllers
1. **LabelController**: Manages label CRUD operations
2. **TeamController**: Updated to handle new salary fields
3. **TeamAchievementController**: Manages achievement recording and tracking
4. **SalaryController**: Updated with bonus dashboard

### Views
1. **Labels**: Create, edit, and list labels with bonus configuration
2. **Team Achievements**: Record, view, and manage achievements
3. **Bonus Dashboard**: Overview of team performance and bonuses
4. **Team Forms**: Updated to include target amounts and base salary

## Routes
```php
// Label management
Route::resource('labels', LabelController::class);

// Team achievement management
Route::resource('team-achievements', TeamAchievementController::class);
Route::patch('/team-achievements/{teamAchievement}/mark-paid', [TeamAchievementController::class, 'markAsPaid']);
Route::patch('/team-achievements/{teamAchievement}/mark-unpaid', [TeamAchievementController::class, 'markAsUnpaid']);

// Bonus dashboard
Route::get('salaries/bonus-dashboard', [SalaryController::class, 'bonusDashboard']);
```

## Usage Instructions

### 1. Setting Up Labels
1. Go to Labels section
2. Create labels with either:
   - Fixed bonus amounts (e.g., $100)
   - Percentage-based bonuses (e.g., 10%)
3. Set descriptions and activate/deactivate as needed

### 2. Setting Up Team Members
1. Go to Teams section
2. When creating/editing team members:
   - Assign a label
   - Set target amount (e.g., $1000)
   - Set base salary (e.g., $500)

### 3. Recording Achievements
1. Go to Team Achievements section
2. Click "Add Achievement"
3. Select team member and label
4. Enter achieved amount
5. System automatically calculates bonus
6. Mark as paid when bonus is distributed

### 4. Monitoring Performance
1. Go to Bonus Dashboard
2. View team performance overview
3. See achievement percentages
4. Track total bonuses earned
5. Monitor payment status

## Key Benefits

1. **Flexible Bonus Structure**: Support for both fixed and percentage-based bonuses
2. **Real-time Tracking**: Live updates of achievements and bonuses
3. **Performance Monitoring**: Clear visibility into team performance
4. **Payment Management**: Track which bonuses have been paid
5. **Scalable System**: Easy to add new labels and team members
6. **Comprehensive Reporting**: Detailed dashboards and reports

## Example Use Cases

### Case 1: Sales Team
- **Label 1**: "Bronze" - 5% bonus
- **Label 2**: "Silver" - 10% bonus  
- **Label 3**: "Gold" - 15% bonus
- Team members get different bonus rates based on their performance level

### Case 2: Project Team
- **Label 1**: "Standard" - $100 fixed bonus
- **Label 2**: "Premium" - $200 fixed bonus
- Consistent bonus amounts regardless of achievement amount

### Case 3: Mixed System
- **Label 1**: "Entry Level" - 5% bonus
- **Label 2**: "Senior" - $150 fixed bonus
- Different bonus structures for different experience levels

This system provides a comprehensive solution for managing team member salaries and bonuses based on their achievements and assigned labels, with full flexibility in bonus calculation methods.
