/*==========================================================================
* Banner One CSS
==========================================================================*/
.banner__one {
    padding: 95px 0;
    background-image: linear-gradient(75deg, #fefefe 52%, var(--color-1) 48%);
    position: relative;
    overflow: hidden;
    .banner-shape {
        .shape {
            position: absolute;
        }
        &-1 {
            width: 86px;
            height: 115px;
            background-color: var(--primary-color-1);
            top: 0;
            left: 46%;
            transform: skew(18deg);
        }
        &-2 {
            width: 28px;
            height: 185px;
            background-color: var(--primary-color-1);
            bottom: 0;
            left: 52.5%;
            transform: skew(15deg);
        }
        &-3 {
            width: 300px;
            height: 300px;
            background-color: transparent;
            border: 4px solid #05163429;
            border-radius: 50%;
            bottom: -21%;
            left: 49%;
        }
        &-4 {
            width: 420px;
            height: 370px;
            background-color: var(--primary-color-1);
            filter: blur(195px);
            border-radius: 50%;
            left: 25px;
            bottom: -180px;
        }
        &-5 {
            top: 0;
            right: 0;
        }
        &-6 {
            width: 300px;
            height: 340px;
            background-color: transparent;
            border: 4px solid var(--bg-white);
            border-radius: 50%;
            top: 8%;
            right: -12%;
        }
        &-7 {
            width: 60px;
            height: 280px;
            background-color: #4b83f4;
            top: 8%;
            right: -1%;
            transform: skew(19deg);
        }
    }
    &-content {
        z-index: 3;
        position: relative;
        h2 {
            font-size: 72px;
            line-height: 83px;
            margin-bottom: 20px;
            text-transform: none;
            span {
                color: var(--primary-color-1);
            }
        }
        p {
            font-size: 18px;
            text-transform: none;
            margin-bottom: 40px;
            width: 93%;
        }
    }
    &-image {
        &-wrapper {
            position: relative;
            border-radius: 50%;
            &-shapes {
                width: 100%;
                height: 100%;
                position: absolute;
                border-radius: 50%;
                z-index: 4;
                .shape {
                    position: absolute;
                }
                .shape-1 {
                    width: 35px;
                    height: 35px;
                    background-color: var(--bg-white);
                    border-radius: 50%;
                    outline: 8px solid var(--primary-color-1);
                    top: 14%;
                    left: 7%;
                }
                .shape-2 {
                    width: 45px;
                    height: 45px;
                    background-color: var(--bg-white);
                    border-radius: 50%;
                    outline: 10px solid var(--primary-color-1);
                    bottom: 15%;
                    right: 20px;
                }
            }
            img {
                z-index: 3;
                position: relative;
                border-radius: 50%;
                outline: 20px solid var(--bg-white);
            }
        }
    }
  
}



@media (max-width: 1399px) {
    .banner__one {
        &-content {
            p {
                font-size: 16px;
            }
        }
    }
}
@media (max-width: 1099px) {
}
@media (max-width: 991px) {
    .banner__one {
        &-content {
            margin-bottom: 30px;
            h2 {
                max-width: 585px;
            }
        }
    }
}
@media (max-width: 767px) {
    .banner__one {
        &-content {
            h2 {
                font-size: 60px;
            }
        }
    }
}
@media (max-width: 535px) {
    .banner__one {
        &-content {
            h2 {
                font-size: 51px;
                line-height: 66px;
            }
        }
    }
}
@media (max-width: 575px) {
    .banner__one-image-wrapper-shapes {
        display: none;
    }
}
@media (max-width: 480px) {
    .banner__one {
        .banner-shape {
            &-2 {
                display: none;
            }
            &-5 {
                display: none;
            }
            &-6 {
                display: none;
            }
            &-7 {
                display: none;
            }
        }
        &-content {
            margin-bottom: 0;
            p {
                font-size: 15px;
            }
        }
        &-image {
            display: none;
        }
    }
}
@media (max-width: 359px) {
}


/*==========================================================================
* Banner Two CSS
==========================================================================*/

.banner__two {
    &-single-slider {
        padding: 155px 0;
        background-size: cover;
        background-repeat: no-repeat;
        position: relative;
        overflow: hidden;
        background-position: center center;
    }
    .banner-two-shape {
        .shape {
            position: absolute;
        }
        &-1 {
            bottom: 12%;
            right: 23%;
            z-index: 2;
            position: absolute;
            svg {
                position: absolute;
                left: 0;
                fill: var(--primary-color-1);
                width: 50px;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
        }
        &-2 {
            top: 215px;
            left: 45%;
            width: 150px;
        }
        &-3 {
            top: 0px;
            left: 46%;
        }
        &-4 {
            right: 22%;
            bottom: 0;
        }
    }
    &-content {
        z-index: 3;
        position: relative;
        h2 {   
            font-size: 74px;
            color: var(--text-white);
            font-weight: 700;
            line-height: 1.13;
            margin-bottom: 25px;
            .highlighted::before {
                content: "";
                width: 111%;
                height: 4px;
                bottom: 14%;
                left: 0;
                border-radius: 3px;
            }
        }
        p {
            color: var(--text-white);
            text-transform: none;
            margin-bottom: 42px;
            opacity: .7;
        }
        .btn-two {
            &:hover {
                color: var(--text-white);
                i {
                    color: var(--text-white);
                }
            }
        }
    }
}

@media (max-width: 1399px) {
    .banner__two-content h2 {
        font-size: 68px;
    }
}

@media (max-width: 1199px) {
    .banner-two-shape {
        &-2 {
            display: none;
        }
        &-3 {
            display: none;
        }
        &-4 {
            display: none;
        }
    }
}

@media (max-width: 767px) {
    .banner-two-shape {
        &-1 {
            display: none;
        }
    }
    .banner__two-single-slider {
        padding: 100px 0;
        background-color: var(--color-7);
        background-image: none !important;
    }
    .banner__two-content h2 {
        font-size: 58px;
    }
}

@media (max-width: 575px) {
    .banner__two {
        background-image: none;
        background-color: var(--color-7);
    }
}

@media (max-width: 480px) {
    .banner__two-content h2 {
        font-size: 45px;
    }
}

/*==========================================================================
* Banner Three CSS
==========================================================================*/

.banner__three {
    &-single-slide {
        padding: 180px 0;
        background-repeat: no-repeat;
        background-position: center center;
        position: relative;
        overflow: hidden;
        background-size: cover;
        &::before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background-color: var(--text-heading-color);
            z-index: 1;
            opacity: .8;
        }
    }
    &-bg-shape-overlay {
        width: 100%;
        height: 100%;
        z-index: 3;
        position: absolute;
        top: 0;
        left: 0;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    &-content {
        z-index: 3;
        position: relative;
        text-align: center;
        h2 {   
            font-size: 74px;
            color: var(--text-white);
            font-weight: 700;
            line-height: 1.15;
            margin-bottom: 25px;
            .text-bordered {
                color: transparent;
                -webkit-text-stroke-width: .5px;
                -webkit-text-stroke-color: var(--text-white);
            }
        }
        p {
            color: var(--text-white);
            text-transform: none;
            margin: auto;
            margin-bottom: 42px;
            width: 61%;
        }
        .btn-two {
            &:hover {
                color: var(--text-white);
                i {
                    color: var(--text-white);
                }
            }
        }
    }
}

@media (max-width: 1399px) {
    .banner__three-content h2 {
        font-size: 71px;
    }
}

@media (max-width: 991px) {
    .banner__three {
        padding: 131px 0;
    }
    .banner__three-content h2 {
        font-size: 68px;
    }
    .banner__three-content p {
        width: auto;
    }
}

@media (max-width: 767px) {
    .banner__three-content h2 {
        font-size: 50px;
    }
}

@media (max-width: 359px) {
    .banner__three-content h2 {
        font-size: 43px;
    }
}

/*==========================================================================
* Banner Four CSS
==========================================================================*/
.banner__four {
    padding: 0;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    overflow: hidden;
    &-content {
        z-index: 3;
        position: relative;
        h2 {
            font-size: 72px;
            line-height: 1.13;
            margin-bottom: 20px;
            text-transform: none;
            color: var(--text-white);
        }
        p {
            font-size: 18px;
            text-transform: none;
            margin-bottom: 40px;
            color: var(--text-white);
        }
        a:hover {
            color: var(--text-white);
            i {    
                color: var(--text-white);
            }
        }
        &-bottom {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            row-gap: 20px;
            column-gap: 30px;
        }
        &-call {
            display: flex;
            align-items: center;
            i {
                width: 50px;
                height: 50px;
                background-color: var(--bg-white);
                font-size: 30px;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
                color: var(--primary-color-1);
                margin-right: 15px;
            }
            &-right {
                span {
                    display: block;
                    color: var(--text-white);
                }
                a {
                    color: var(--text-white);
                    font-size: 18px;
                    transition: .4s;
                    font-weight: 500;
                    &:hover {
                        color: var(--primary-color-1);
                    }
                }
            }
        }
    }
    &-image {
        padding-left: 73px;
        &-wrapper {
            img {
                z-index: 3;
            }
        }
    }
  
}

@media (max-width: 991px) {
    .banner__four {
        padding: 80px 0;
        padding-bottom: 0;
    }
}

@media (max-width: 575px) {
    .banner__four-content h2 {
        font-size: 60px;
    }
}

@media (max-width: 480px) {
    .banner__four-content h2 {
        font-size: 50px;
    }
}

@media (max-width: 359px) {
    .banner__four-image-wrapper img {
        display: none;
    }
    .banner__four {
        padding: 50px 0;
        padding-bottom: 20px;
    }
    .banner__four-content h2 {
        font-size: 45px;
    }
}

/*==========================================================================
* Banner Five CSS
==========================================================================*/
.banner__five {
    padding: 165px 0px 135px 0px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: bottom center;
    overflow: hidden;
    &-content {
        z-index: 3;
        position: relative;
        h2 {
            font-size: 72px;
            line-height: 1.15;
            margin-bottom: 20px;
            text-transform: none;
        }
        p {
            font-size: 18px;
            text-transform: none;
            margin-bottom: 40px;
            width: 83%;
        }
        &-bottom {
            display: flex;
            align-items: center;
        }
        &-video-btn {
            margin-left: 30px;
            display: flex;
            align-items: center;
            i {
                width: 58px;
                height: 58px;
                background-color: var(--primary-color-1);
                font-size: 18px;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
                color: var(--text-white);
                margin-right: 15px;
            }
        }
    }
    &-image {
        padding-left: 60px;
        &-wrapper {
            position: relative;
            img {
                z-index: 3;
            }
            .img-2 {
                position: absolute;
                left: -50px;
                top: 65px;
            }
        }
    }
  
}

@media (max-width: 1399px) {
    .banner__five-content h2 {
        font-size: 68px;
    }
}

@media (max-width: 767px) {
    .banner__five-content h2 {
        font-size: 50px;
    }
}

@media (max-width: 359px) {
    .banner__three-content h2 {
        font-size: 40px;
    }
}

/*==========================================================================
* Page Banner CSS
==========================================================================*/

.page__banner {
    background-color: var(--bg-heading-color);
    position: relative;
    overflow: hidden;
    &-shape {
        position: absolute;
        right: 0;
        z-index: 1;
    }

    &-content {
        z-index: 3;
        position: relative;
        h2 {
            color: var(--text-white);
            margin-bottom: 25px;
            font-size: 64px;
            line-height: 1.13;
        }
        span {
            font-size: 18px;
            color: var(--primary-color-1);
            a {
                color: var(--text-white);
                transition: .4s;
                &:hover {
                    color: var(--primary-color-1);
                }
            }
            span {
                margin: 0 8px;
            }
        }
    }

    &-img {
        z-index: 2;
        position: relative;
    }
}

@media (max-width: 991px) {
    .page__banner-content {
        padding: 85px 0;
    }
    .page__banner-img {
        display: none;
    }
}

@media (max-width: 767px) {
    .page__banner-content h2 {
        margin-bottom: 20px;
        font-size: 64px;
        line-height: 80px;
    }
}

@media (max-width: 535px) {
    .page__banner-content h2 {
        margin-bottom: 18px;
        font-size: 40px;
        line-height: 60px;
    }
    .page__banner-content {
        padding: 60px 0;
    }
    .page__banner-shape {
        display: none;
    }
}