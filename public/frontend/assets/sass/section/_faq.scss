/*==========================================================================
* FAQ Style One
==========================================================================*/

.faq__one {
    padding-top: 120px;
    padding-bottom: 87px;
    &-title {
        margin-bottom: 60px;
        h2 {
            text-transform: none;
            max-width: 91%
        };
    }
    &-image {
        position: relative;
        &-wrapper {
            border: 35px solid transparent;
            outline: 2px dashed var(--primary-color-1);
            border-radius: 50%;
            margin-left: 53px;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

        }
        &-shape {
            .shape {
                position: absolute;
            }
            .shape-1 {
                width: 40px;
                height: 40px;
                background-color: var(--primary-color-1);
                border-radius: 50%;
                right: 27px;
                bottom: 100px;
            }
            .shape-2 {
                right: 26px;
                top: 0;
            }
            .shape-3 {
                bottom: 65px;
                left: 74px;
                z-index: -1;
            }
            .counter-shape {
                position: absolute;
                background-color: var(--primary-color-1);
                color: var(--text-white);
                width: 130px;
                height: 130px;
                border-radius: 50%;
                text-align: center;
                top: 5%;
                left: 13%;
                span {
                    display: inline-block;
                    margin-top: 35px;
                    font-size: 30px;
                    font-weight: 600;
                    text-transform: lowercase;
                }
                p {
                    color: var(--color-4);
                }
            }
        }
    }
    .faq-collapse-item {
        margin-bottom: 30px;
        &-card {
            border: 1px solid var(--color-4);
            border-radius: 5px;
            transition: .5s;
            border-left: 3px solid var(--color-4);
            &.active {
                border-color: transparent;
                border-left: 3px solid var(--primary-color-1);
                box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
                &:hover {
                    border-left: 3px solid var(--primary-color-1);
                }
            }
            &-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 22px 27px;
                cursor: pointer;
                h6 {
                    font-size: 22px;
                }
                .fa-minus-circle {
                    color: var(--primary-color-1);
                }
                &-content {
                    text-transform: none;
                    padding: 0 26px;
                    color: var(--color-2);
                    p {
                        margin-bottom: 27px;
                    }
                }
            }
        }
    }
}

@media (max-width: 1199px) {
    .faq__one-image-wrapper {
        margin-left: 0;
    }
}

@media (max-width: 991px) {
	.faq__one {
		padding-top: 80px;
	}
}

@media (max-width: 767px) {
    .faq__one-image-wrapper {
        border: 10px solid transparent;
    }
}

@media (max-width: 575px) {
    .faq__one {
        padding-top: 60px;
    }
}

/*==========================================================================
* FAQ Style Two
==========================================================================*/

.faq__two {
    background-color: var(--color-1);
    &-title {
        margin-bottom: 60px;
        h2 {
            text-transform: none;
            margin-bottom: 35px;
        }
        p {
            color: var(--color-2);
            text-transform: none;
            font-size: 17px;
        }
    }
    .award {
        background-color: var(--text-white);
        display: table;
        position: relative;
        margin-left: 40px;
        &-wrapper {
            display: flex;
            align-items: center;
            padding: 25px 47px;
            border-left: 2px solid var(--primary-color-1);
            border-radius: 10px;
            span {
                font-size: 18px;
            }
        }
        &-count {
            display: flex;
            align-items: center;
            margin-right: 15px;
            h3 {
                font-size: 60px;
            }
        }

        &-icon {
            position: absolute;
            left: -10%;
            background-color: var(--primary-color-1);
            width: 64px;
            height: 64px;
            text-align: center;
            line-height: 64px;
            border-radius: 50%;
            color: var(--text-white);
            font-size: 30px;
        }
    }
    .faq-collapse-item {
        margin-bottom: 30px;
        &-card {
            border-radius: 5px;
            transition: .5s;
            background-color: var(--bg-white);
            &-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 25px 27px;
                cursor: pointer;
                h6 {
                    font-size: 22px;
                }
                i {
                    color: var(--color-4);
                    font-weight: 300;
                    font-size: 18px;
                }
                .fa-minus-circle {
                    color: var(--primary-color-1);
                }
                &-content {
                    text-transform: none;
                    padding: 0 26px;
                    color: var(--color-2);
                    p {
                        padding-bottom: 27px;
                    }
                }
            }
        }
    }
}

@media (max-width: 359px) {
    .faq__two .award {
        margin-left: 0;
    }
    .award-icon {
        display: none;
    }
}


/*==========================================================================
* FAQ Style Three
==========================================================================*/

.faq__three {
    padding-top: 120px;
    padding-bottom: 87px;
    &-title {
        margin-bottom: 60px;
        h2 {
            text-transform: none;
            margin-bottom: 35px;
        }
        p {
            color: var(--color-2);
            text-transform: none;
            font-size: 17px;
        }
    }
    .faq-collapse-item {
        margin-bottom: 25px;
        box-shadow: rgb(100 100 111 / 10%) 0px 7px 29px 0px;
        &-card {
            border-radius: 5px;
            transition: .5s;
            background-color: var(--bg-white);
            &-header {
                display: flex;
                align-items: center;
                padding: 20px 18px;
                cursor: pointer;
                border: 2px solid transparent;
                border-radius: 5px;
                margin-bottom: 20px;
                transition: .4s;
                h6 {
                    font-size: 18px;
                }
                &-content {
                    text-transform: none;
                    padding: 0 26px;
                    color: var(--color-2);
                    p {
                        padding-bottom: 27px;
                    }
                }
                i {
                    background-color: var(--primary-color-1);
                    color: var(--text-white);
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    font-size: 14px;
                    text-align: center;
                    line-height: 30px;
                    margin-right: 20px;
                }
            }
            &.active {
                .faq-collapse-item-card-header {
                    border: 2px solid var(--color-4);
                    padding: 25px 18px;
                }
            }
        }
    }
}




