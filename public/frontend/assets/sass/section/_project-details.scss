/*==========================================================================
* Project Details CSS
==========================================================================*/

.project__details {
    .project-feature {
        display: flex;
        justify-content: space-between;
        background: var(--color-1);
        padding: 38px 40px 68px 40px;
        border-radius: 10px;
        position: relative;
        h4 {
            width: 54%;
        }
        &::before {
            content: "";
            width: 81%;
            height: 1px;
            position: absolute;
            background: var(--color-2);
            bottom: 38px;
            left: 50%;
            transform: translateX(-50%);
        }
    }
    &-thumb {
        position: relative;
        img{
            width: 100%;
        }
        .project-info {
            position: absolute;
            bottom: -44%;
            right: 0;
            width: 410px;
            border-radius: 10px 0 0 10px;
            overflow: hidden;
            .project-info-top {
                background: var(--primary-color-1);
                text-align: center;
                padding: 7px 0;
                h4 {
                    color: var(--text-white);
                    font-size: 18px;
                }
            }
            ul {
                background: var(--bg-white);
                border: 1px solid var(--color-4);
                border-top: 0;
                padding: 23px 30px;
                border-radius: 0 0 0 10px;
                list-style-type: none;
                margin: 0;
                li {
                    margin-bottom: 15px;
                    font-size: 18px;
                    font-weight: 600;
                    span {
                        color: var(--color-2);
                        margin-left: 10px;
                        font-weight: 400;
                    }
                    .value {
                        color: var(--primary-color-1);
                        font-size: 22px;
                        font-weight: 600;
                    }
                }
                .project-rating i {
                    color: #FBA758;
                    margin-right: 5px;
                }
            }
        }
    }
    .project__details {
        &-content {
            &-mid {
                margin-top: 30px;
                margin-bottom: 48px;
                p {
                    margin-bottom: 30px;
                }
            }
            h3 {
                margin-bottom: 15px;
            }
            p {
                font-size: 17px;
                text-transform: none;
                color: var(--color-2);
            }
        }
    } 

    &-images {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: 40px;
        gap: 30px;
    }
}

@media (max-width: 1399px) {
    .project__details-thumb .project-info {
        position: unset;
        border-radius: 10px;
        overflow: hidden;
        margin-top: 30px;
        width: 100%;
    }
}
