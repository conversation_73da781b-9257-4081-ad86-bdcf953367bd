/*==========================================================================
* Subscribe One, Two Three CSS
==========================================================================*/

.subscribe__one {
    margin-bottom: -128px;
    &-content {
        background-color: var(--primary-color-1);
        background-repeat: no-repeat;
        background-position: top left;
        background-size: 20%;
        padding: 50px 0;
        border-radius: 30px 30px 0 0;
        z-index: 4;
        position: relative;
        width: 100%;
        margin: auto;
    }
    &-title {
        margin-bottom: 35px;
        h3 {
            color: var(--text-white);
            font-size: 32px;
            font-weight: 500;
            text-transform: none;
        }
    }
    &-form {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        position: relative;
        position: relative;
        input {
            background-color: var(--bg-white);
            border-radius: 5px;
            width: 600px;
            height: 75px;
            border-color: var(--color-4);
            padding: 0 221px 0 30px;
            color: var(--text-heading-color);
            &::placeholder {
                color: var(--color-2);
            }
        }
        .btn-two {
            position: absolute;
            border-radius: 5px;
            border: 1px solid var(--primary-color-1);
            right: 72px;
            top: 6px;
            color: var(--text-white);
            text-transform: capitalize;
            padding: 18px 25px;
            &:hover {
                color: var(--text-heading-color);
            }
        } 
    }
    &.two {
        .subscribe__one-content {
            border-radius: 30px;
            &::before {
                content: "";
                background-image: url(../img/subscribe/subscribe-two-shape-1.png);
                width: 181px;
                height: 100%;
                background-size: contain;
                background-repeat: no-repeat;
                position: absolute;
                top: 0;
                right: 0;
            }
        }
        .subscribe__one-title h3 {
            font-size: 48px;
            line-height: 1.12;
            font-weight: 600;    
            z-index: 3;
            position: relative;
        }
        .subscribe-bottom {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            column-gap: 20px;
            row-gap: 18px;
            .btn-two {
                background-color: var(--color-5);
                color: var(--text-white);
                text-transform: capitalize;
                margin-right: 20px;
                &:hover {
                    color: var(--text-white);
                    background-color: var(--color-3);
                    i {
                        color: var(--text-white);
                    }
                }
            }
            .call-box {
                &-item {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    text-align-last: left;    
                    z-index: 3;
                    position: relative;
                    &-icon i {
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        width: 50px;
                        height: 50px;
                        font-size: 28px;
                        border-radius: 50%;
                        background-color: var(--bg-white);
                        color: var(--primary-color-1);                
                    }
                    &-info {
                        span {
                            font-size: 16px;
                            color: var(--text-white);
                            font-weight: 400;
                            display: block;
                            text-transform: none;
                        } 
                        a {
                            color: var(--text-white);
                            font-weight: 700;
                            font-size: 18px;
                            line-height: 26px;
                            display: block;
                        }
                    }
                }
            }
        }
    }
    &.three {
        margin-bottom: -8%;
        position: relative;
        .subscribe__three-shape {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            z-index: 5;
        }
        .subscribe__one-content {
            background-color: inherit;
            background-position: center center;
            background-size: cover;
            background-repeat: no-repeat;
            border-radius: 20px;
            padding: 50px 0;
        }
        .subscribe__one-title {
            h3 {
                font-size: 48px;
                line-height: 1.2;
                margin-bottom: 25px;
            }
            p {
                color: var(--text-white);
                text-transform: none;
                width: 80%;
                margin: auto;
            }
        } 
    }
}

@media (max-width: 1399px) {
    .subscribe__one-form .btn-two {
        right: 27px;
    }
}

@media (max-width: 1199px) {
    .subscribe__one-form input {
        width: 100%;
    }

    .subscribe__one-form .btn-two {
        right: 5px;
    }
}

@media (max-width: 767px) {
    .subscribe__one-form .email-input {
        margin-bottom: 20px;
        input {
            max-width: 100%;
        }
    }
    .subscribe__one-content {
        padding: 45px 0 45px 0;
    }
}

@media (max-width: 359px) {
    .subscribe__one-form input {
        height: 70px;
        padding: 0 170px 0 15px;
    }

    .subscribe__one-form .btn-two {
        top: 7px;
        padding: 14px 15px;
    }
}
