/*==========================================================================
* Blog One Style
==========================================================================*/

.blog__one {
    &-title {
        margin-bottom: 50px;
        h2 {
            text-transform: none;
        }
    }
    &-single-blog {
        position: relative;

        &-image {
            border-radius: 15px 5px 15px 15px;
            overflow: hidden;
            height: 260px;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
        &-date {
            position: absolute;
            top: 0;
            right: 0;
            background-color: var(--color-1);
            text-align: center;
            padding: 11px 14px;
            border-radius: 0 5px 0 5px;
            span {
                display: block;
            }
            .date {
                font-size: 22px;
                font-weight: 700;
                margin-bottom: 3px;
            }
            .month {
                font-size: 18px;
                color: var(--color-2);
            }
        }
        &-content {
            position: relative;
            width: 90%;
            background-color: var(--bg-white);
            padding: 30px;
            border-radius: 15px;
            margin: auto;
            margin-top: -58px;
            transition: 0.5s;
            box-shadow: 0px 5px 60px 0px rgba(0,0,0,.0509803922);
            border-bottom: 3px solid transparent;
            &-top {
                margin-bottom: 20px;
                display: flex;
                flex-wrap: wrap;
                span {
                    font-size: 14px;
                    color: var(--color-2);
                    margin-right: 27px;
                    i {
                        color: var(--primary-color-1);
                        margin-right: 8px;
                    }
                }
            }
            .blog-heading {
                font-size: 22px;
                font-weight: 700;
                margin-bottom: 30px;
                line-height: 32px;
                transition: .4s;
                &:hover {
                    color: var(--primary-color-1);
                }
            }
            a {
                display: block;
                text-align: left;
            }
            .btn-three {
                i {
                    margin-left: 5px;
                    font-size: 17px;
                    font-weight: 500;
                }
            }
        }
        &:hover {
            .blog__one-single-blog-content {
                border-bottom: 3px solid var(--primary-color-1);
            }
        }
    }
}

@media (max-width: 1399px) {
    .blog__one-single-blog-content-top span {
        margin-right: 15px;
    }
    .blog__one-single-blog-content .blog-heading {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .blog__one-single-blog-content {
        width: 100%;
        margin-top: -58px;
        margin-left: 0;
    }
}

/*==========================================================================
* Blog Two Style
==========================================================================*/

.blog__two {
    &-title {
        margin-bottom: 62px;
        h2 {
            text-transform: none;
            margin: auto;
        }
    }
    &-single-blog {
        &-content {
            box-shadow: 0px 5px 60px 0px rgba(0,0,0,.0509803922);
            padding: 20px 40px 40px;
            border-radius: 0 0 20px 20px;
            &-top {
                background-color: var(--color-1);
                padding: 10px 20px 9px 14px;
                display: table;
                margin-bottom: 10px;
                border-radius: 5px;
                span {
                    font-size: 14px;
                    margin-right: 18px;
                    color: var(--color-2);
                    i {
                        font-size: 13px;
                        color: var(--primary-color-1);
                        margin-right: 10px;
                    }
                }
            }
            &-title {
                font-size: 22px;
                font-weight: 700;
                margin-bottom: 20px;
                line-height: 36px;
                text-transform: none;
                display: block;
                transition: 0.4s;
                &:hover {
                    color: var(--primary-color-1);
                }
            }
            .btn-three {
                a {
                    i {
                        font-size: 14px;
                        color: var(--primary-color-1);
                    }
                }
            }
        }
        &-img {
            position: relative;
            img {
                width: 100%;
                border-radius: 20px 20px 0 0;
            }
        }
        &-date {
            position: absolute;
            bottom: 20px;
            right: 30px;
            background-color: var(--primary-color-1);
            text-align: center;
            padding: 10px 18px;
            border-radius: 10px;
            span {
                display: block;
            }
            .date {
                font-size: 22px;
                color: var(--text-white);
                font-weight: 700;
                margin-bottom: 3px;
            }
            .month {
                font-size: 18px;
                color: var(--text-white);
            }
        }
    }
}

@media (max-width: 535px) {
    .blog__two-left-sidebar-item-content a {
        font-size: 18px;
    }
    .blog__two-left-sidebar-item-content-top {
        margin-bottom: 10px;
    }

    .blog__two-left-sidebar {
        padding: 18px 16px;
    }
    .blog__two-left-sidebar-item-content-top span {
        margin-right: 10px;
    }
}

@media (max-width: 480px) {
    .blog__two-left-sidebar-item {
        flex-direction: column;
        &-img {
            width: 100%;
            margin-bottom: 19px;
            img {
                width: 100%;
            }
        }
        &-content {
            margin-left: 0; 
            width: 100%; 
        }
    }
}

@media (max-width: 359px) {
    .blog__two-right-content-top span {
        margin-right: 10px;
    }
    .blog__two-right-content {
        padding: 20px 20px;
    }
}



/*==========================================================================
* Blog Three Style
==========================================================================*/

.blog__three {
    background-color: var(--color-1);
    &-title {
        h2 {
            text-transform: none;
            margin: auto;
        }
        &-btn {
            a {
                i {
                    color: var(--primary-color-1);
                }
            }
        }
    }
    &-single-blog {
        display: flex;
        align-items: center;
        &-content {
            box-shadow: 0px 0px 0px 0px rgba(0,0,0,0.5);
            padding: 25px 33px;
            border-radius: 20px;
            margin-left: -10%;
            z-index: 153;
            background-color: var(--bg-white);
            transition: .4s;
            &-top {
                margin-bottom: 18px;
                display: flex;
                justify-content: start;
                align-items: center;
                flex-wrap: wrap;
                span {
                    font-size: 14px;
                    margin-right: 18px;
                    color: var(--color-2);
                    i {
                        font-size: 14px;
                        color: var(--primary-color-1);
                        margin-right: 5px;
                    }
                }
            }
            &-title {
                font-size: 22px;
                font-weight: 700;
                margin-bottom: 28px;
                line-height: 36px;
                text-transform: none;
                display: block;
                transition: 0.4s;
                &:hover {
                    color: var(--primary-color-1);
                }
            }
            p {
                text-transform: none;
                font-size: 17px;
                color: var(--color-2);
                margin-bottom: 30px;
            }
            .btn-three {
                a {
                    i {
                        font-size: 14px;
                        color: var(--primary-color-1);
                    }
                }
            }
        }
        &-img {
            width: 75%;
            height: 310px;
            overflow: hidden;
            border-radius: 21px;
            position: relative;
            transition: .4s;
            &-date {
                position: absolute;
                top: 0;
                left: 0;
                background-color: var(--bg-white);
                padding: 8px 15px 8px 15px;
                border-radius: 20px 0 10px 0;
                font-size: 16px;
                line-height: 1.62;
                text-align: center;
                span {
                    color: var(--color-2);
                    font-weight: 300;
                }
            }
            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }
        &:hover {
            .blog__three-single-blog-img img {
                transform: scale(1.1);
            }
            .blog__three-single-blog-content {
                transform: translateX(-8px);
            }
        }
    }
}

@media (max-width: 767px) {
    .blog__three-title {
        margin-bottom: 0;
    }

    .blog__three-right-img {
        width: 90%;
    }
}

@media (max-width: 575px) {
    .blog__three-left-sidebar-item {
        flex-direction: column;
        &-img {
            width: 100%;
        }
        &-content {
            width: 100%;
            padding: 20px 23px;
        }
    }
}

@media (max-width: 535px) {
    .blog__three-right {
        flex-direction: column;
        &-content {
            margin-left: 0;
            margin-top: -20%;
        }
        &-img {
            width: 100%;
        }
    }
}

@media (max-width: 480px) {
    .blog__three-single-blog {
        flex-direction: column;
    }
    .blog__three-single-blog-img {
        width: 95%;
    }
    .blog__three-single-blog-content {
        margin-left: 0;
        margin-top: -10%;
    }
}


/*==========================================================================
* Blog Four Style
==========================================================================*/

.blog__four {
    &-title {
        margin-bottom: 62px;
        h2 {
            text-transform: none;
            margin: auto;
        }
    }
    &-single-blog {
        margin-bottom: 35px;
        &-content {
            border: 2px solid var(--color-4);
            border-top: 0;
            padding: 20px 41px;
            padding-bottom: 15px;
            border-radius: 0 0 20px 20px;
            text-align: center;
            box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.5);
            &-top {
                background-color: var(--color-1);
                padding: 10px 25px;
                transform: translateY(-40px);
                margin: auto;
                margin-bottom: -20px;
                border-radius: 50px;
                display: flex;
                align-items: center;
                width: 290px;
                flex-wrap: wrap;
                column-gap: 24px;
                span {
                    font-size: 14px;
                    color: var(--color-2);
                    i {
                        font-size: 13px;
                        color: var(--primary-color-1);
                        margin-right: 10px;
                    }
                }
            }
            &-title {
                font-size: 22px;
                font-weight: 700;
                line-height: 36px;
                text-transform: none;
                display: block;
                transition: 0.4s;
                &:hover {
                    color: var(--primary-color-1);
                }
            }
            .btn-one {
                border-color: var(--color-4);
                transform: translateY(45px);
                background-color: var(--bg-white);
                padding: 15px 32px;
                &:hover {
                    border-color: var(--primary-color-1);
                    background-color: var(--primary-color-1);
                }
            }
        }
        &-img {
            border-radius: 15px 15px 0 0;
            overflow: hidden;
            position: relative;
            img {
                width: 100%;
            }
        }
        &-date {
            position: absolute;
            top: 0;
            left: 0;
            background: var(--primary-color-1);
            color: var(--color-1);
            padding: 10px 20px;
            font-size: 20px;
            font-weight: 600;
            border-radius: 0 0 10px 0;
        }
    }
}

@media (max-width: 575px) {
    .skill-area__one-left-video {
        width: 284px;
        height: 200px;
    }
}

@media (max-width: 359px) {
    .blog__four-right-content-top {
        width: 100%;
        justify-content: center;
        row-gap: 3px;
    }
}

/*==========================================================================
* Blog With Sidebar Style
==========================================================================*/

.blog__sidebar {
    &-single-blog {
        position: relative;
        margin-bottom: 60px;
        &:last-child {
            margin-bottom: 0;
        }
        &-image {
            border-radius: 10px 10px 0 10px;
            overflow: hidden;
            img {
                width: 100%;
                height: 552px;
                object-fit: cover;
            }
        }
        &-date {
            position: absolute;
            top: 0;
            right: 0;
            background-color: var(--color-1);
            text-align: center;
            padding: 8px 15px;
            border-radius: 0 5px 0 5px;
            span {
                display: block;
                font-size: 12px;
                color: var(--color-2);
            }
        }
        &-content {
            position: relative;
            width: 90%;
            background-color: var(--bg-white);
            padding: 30px 41px;
            border-radius: 0 10px 10px 10px;
            margin-top: -80px;
            transition: 0.5s;
            box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 50px;
            &-top {
                margin-bottom: 20px;
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                span {
                    font-size: 14px;
                    color: var(--color-2);
                    margin-right: 27px;
                    i {
                        color: var(--text-heading-color);
                        margin-right: 3px;
                    }
                }
            }
            p {
                text-transform: none;
                color: var(--color-2);
                font-size: 17px;
                margin-bottom: 40px;
            }
            .blog-heading {
                font-size: 32px;
                font-weight: 700;
                margin-bottom: 20px;
                line-height: 1.25;
                text-transform: none;
                transition: .4s;
                display: block;
                &:hover {
                    color: var(--primary-color-1);
                }
            }
            .btn-one {
                display: table;
                text-align: left;
                border-radius: 5px;
            }
        }
    }
    &.no-sidebar {
        .blog__sidebar-single-blog-content {
            border-radius: 10px;
            margin-left: 50%;
            transform: translateX(-50%);
        }
    }
}

@media (max-width: 991px) {
    .blog__sidebar-single-blog-content {
        width: 100%;
    }
}

@media (max-width: 575px) {
    .blog__sidebar-single-blog-content {
        padding: 18px 15px;
    }
    .blog__sidebar-single-blog-content .blog-heading {
        font-size: 24px;
    }
}

/*==========================================================================
* Blog Sidebar Style
==========================================================================*/

.sidebar-item {
    margin-left: 8px;
    &-single {
        background-color: var(--color-1);
        padding: 35px 40px;
        border-radius: 5px;
        margin-bottom: 35px;
        &:last-child {
            margin-bottom: 0;
        }
        h3 {
            margin-bottom: 15px;
        }
        &.sidebar-search {
            form {
                display: flex;
                margin-top: 28px;
                input {
                    border: 0;
                    height: 58px;
                    padding: 10px 20px;
                    border-radius: 10px 0 0 10px;
                    &::placeholder {
                        font-size: 18px;
                    }
                }
                button {
                    padding: 10px 21px;
                    border-radius: 0 10px 10px 0;
                }
            }
        }
        &.sidebar-category {
            .single-category {
                display: flex;
                justify-content: space-between;
                align-items: center;
                background-color: var(--bg-white);
                padding: 9px 23px;
                margin-bottom: 20px;
                border-radius: 10px;
                &:last-child {
                    margin-bottom: 0;
                }
                &-name {
                    display: flex;
                    align-items: center;
                    i {
                        color: var(--primary-color-1);
                        margin-right: 15px;
                    }
                }
                .category-count {
                    font-size: 12px;
                    color: var(--color-2);
                }
            }
        }

        &.sidebar-item-single {
            .blog-post {
                &-single {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    &:last-child {
                        margin: 0;
                    }
                    &-img {
                        width: 60%;
                    }
                    &-content {
                        background-color: var(--bg-white);
                        padding: 10px 13px;
                        border-radius: 0 5px 5px 0;
                        &-top {
                            margin-bottom: 8px;
                            span {
                                font-size: 12px;
                                color: var(--color-2);
                                margin-right: 20px;
                                i {
                                    color: var(--primary-color-1);
                                    margin-right: 5px;
                                }
                            }
                        }      
                        a {
                            font-size: 18px;
                            font-weight: 700;
                            transition: 0.4s;
                            text-transform: none;
                            &:hover {
                                color: var(--primary-color-1);
                            }
                        }
                    }
                }
            } 
        }

        &.tags {
            a {
                font-size: 18px;
                background-color: var(--bg-white);
                padding: 8px 12px;
                display: inline-block;
                margin: 8px 6px;
                color: var(--color-2);
                border-radius: 5px;
            }
        }
    }
}

@media (max-width: 575px) {
    .sidebar-item-single {
        padding: 20px 22px;
    }
}


/*==========================================================================
* Blog Five Style
==========================================================================*/

.blog__five {
    &-title {
        margin-bottom: 62px;
        h2 {
            text-transform: none;
            margin: auto;
        }
    }
    &-single-blog {
        &-content {
            box-shadow: 0px 5px 60px 0px rgba(0,0,0,.0509803922);
            padding: 20px 40px 40px;
            border-radius: 0 0 20px 20px;
            &-top {
                margin-bottom: 10px;
                span {
                    font-size: 14px;
                    margin-right: 18px;
                    color: var(--color-2);
                    i {
                        font-size: 13px;
                        color: var(--primary-color-1);
                        margin-right: 10px;
                    }
                }
            }
            &-title {
                font-size: 22px;
                font-weight: 700;
                margin-bottom: 20px;
                line-height: 36px;
                text-transform: none;
                display: block;
                transition: 0.4s;
                &:hover {
                    color: var(--primary-color-1);
                }
            }
            .btn-three {
                a {
                    i {
                        font-size: 14px;
                        color: var(--primary-color-1);
                    }
                }
            }
        }
        &-img {
            position: relative;
            img {
                width: 100%;
                border-radius: 20px 20px 0 0;
            }
        }
        &-date {
            position: absolute;
            bottom: 20px;
            left: 30px;
            background-color: var(--primary-color-1);
            text-align: center;
            padding: 8px 20px;
            border-radius: 5px;
            span {
                display: block;
            }
            .date {
                font-size: 22px;
                color: var(--text-white);
                font-weight: 700;
                margin-bottom: 3px;
            }
            .month {
                font-size: 18px;
                color: var(--text-white);
            }
        }
    }
}

@media (max-width: 535px) {
    .blog__five-left-sidebar-item-content a {
        font-size: 18px;
    }
    .blog__five-left-sidebar-item-content-top {
        margin-bottom: 10px;
    }

    .blog__five-left-sidebar {
        padding: 18px 16px;
    }
    .blog__five-left-sidebar-item-content-top span {
        margin-right: 10px;
    }
}

@media (max-width: 480px) {
    .blog__five-left-sidebar-item {
        flex-direction: column;
        &-img {
            width: 100%;
            margin-bottom: 19px;
            img {
                width: 100%;
            }
        }
        &-content {
            margin-left: 0; 
            width: 100%; 
        }
    }
}

@media (max-width: 359px) {
    .blog__five-right-content-top span {
        margin-right: 10px;
    }
    .blog__five-right-content {
        padding: 20px 20px;
    }
}


/*==========================================================================
* Blog Details Style
==========================================================================*/

.blog__details {
    &-thumb {
        position: relative;
        .date {
            position: absolute;
            color: var(--text-white);
            background-color: var(--primary-color-1);
            font-size: 18px;
            right: 0;
            padding: 10px 20px;
            border-radius: 0 5px 0 5px;
        }
        img {
            width: 100%;
        }
    }

    &-content {
        &-top {
            margin-top: 32px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            span {
                font-size: 14px;
                color: var(--color-2);
                margin-right: 27px;
                i {
                    color: var(--text-heading-color);
                    margin-right: 3px;
                }
            }
        }
        h2 {
            font-size: 32px;
            margin-bottom: 25px;
            text-transform: none;
        }
        p {
            text-transform: none;
            color: var(--color-2);
            font-size: 17px;
            margin-bottom: 28px;
        }
    } 
    
    &-quote {
        background-color: var(--color-1);
        padding: 38px 33px;
        margin-bottom: 38px;
        &-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        &-avatar {
            display: flex;
            align-items: center;
            &-wrapper {
                width: 45px;
                height: 45px;
                border-radius: 5px;
                overflow: hidden;
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
            h4 {
                font-size: 18px;
                color: var(--primary-color-1);
                margin-left: 20px;
                text-transform: none;
            }
        }
        p {
            margin-bottom: 0;
        }
    }

    &-portfolio {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 35px;
        flex-wrap: wrap;
        row-gap: 25px;
        &-middle {
            margin: 0;
            padding: 0;
            li {
                list-style: none;
                margin-bottom: 10px;
                font-size: 18px;
                color: var(--color-2);
                margin-bottom: 18px;
                &:last-child {
                    margin-bottom: 0;
                }
                i {
                    color: var(--primary-color-1);
                    margin-right: 3px;
                    font-size: 15px;
                }
            }
        }
    }

    &-pagination {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 50px;
        flex-wrap: wrap;
        row-gap: 25px;
        &-prev {
            text-align: left;
            .pagination-btn {
                margin-right: 30px;
            }
        }
        &-next {
            text-align: right;
            .pagination-btn {
                margin-left: 30px;
            }
        }
        &-text {
            span {
                font-size: 18px;
                text-transform: none;
                display: block;
                color: var(--color-2);
                &:last-child {
                    color: var(--text-heading-color);
                    font-weight: 700;
                }
            }   
        }
        &-btn {
            display: flex;
            align-items: center;
            .pagination-btn {
                background-color: transparent;
                width: 60px;
                height: 60px;
                border: 1px solid var(--color-4);
                text-align: center;
                line-height: 60px;
                border-radius: 5px;
                transition: .4s;
                font-size: 19px;
                &:hover {
                    border-color: var(--primary-color-1);
                    color: var(--primary-color-1);
                }
            }
        }
    } 

    &-comments {
        margin-top: 55px;
        h3 {
            margin-bottom: 18px;
        }
    }

    &-single-comment {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        gap: 20px;
        &:last-child {
            margin-bottom: 0;
        }
        &-user-pic {
            img {
                height: 85px;
                width: 85px;
                min-width: 85px;
                border-radius: 50%;
                object-fit: cover;
            }
        }
        &-body {
            border: 1px solid var(--color-4);
            border-radius: 5px;
            padding: 34px 33px;
            &-top {
                display: flex;
                align-items: center;
                margin-bottom: 12px;
                flex-wrap: wrap;
                row-gap: 8px;
                > * {
                    margin-right: 20px;
                }
                span {
                    font-size: 18px;
                    color: var(--color-2);
                }
                .user-socials {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    list-style-type: none;
                    margin: 0;
                    padding: 0;
                    margin-left: 10px;
                    li {
                        margin-right: 15px;
                        font-size: 13px;
                        transition: .4s;
                        &:last-child {
                            margin: 0;
                        }
                        &:hover {
                            color: var(--primary-color-1);
                        }
                    }
                }
            }
            p {
                text-transform: none;
                color: var(--color-2);
                margin-bottom: 20px;
                font-size: 17px;
            }
            .comment-reply-btn {
                font-size: 18px;
                font-weight: 700;
                transition: .4s;
                &:hover {
                    color: var(--primary-color-1);
                }
            }
        } 
    }

    &-comment-form {
        margin-top: 55px;
        padding: 55px 58px;
        background-color: var(--color-1);
        border-radius: 5px;
        h3 {
            text-transform: none;
            margin-bottom: 30px;
        }
        p {
            color: var(--color-2);
            text-transform: none;
            margin-bottom: 25px;
        }
        input {
            border: 1px solid transparent;
            width: 100%;
            transition: .5s;
            &:focus {
                border-color: var(--primary-color-1);
            }
        }
        input[type="text"] {
            margin-bottom: 30px;
        }
        textarea {
            border: 1px solid transparent;
            margin-bottom: 25px;
            height: 130px;
            &:focus {
                border-color: var(--primary-color-1);
            }
        }
    }
}


@media (max-width: 991px) {
    .blog__details-single-comment {
        flex-direction: column;
        row-gap: 10px;
        margin-bottom: 45px;
    }
}

@media (max-width: 767px) {
    .blog__details-comment-form {
        padding: 20px 30px;
    }
}

@media (max-width: 575px) {
    .blog__details-single-comment-body {
        padding: 20px 20px;
    }
}

@media (max-width: 359px) {
    .blog__details-content h2 {
        font-size: 25px;
    }
}
