
.request-quote__area {
    form {
        label {
            font-size: 18px;
        }
        input::placeholder {
            font-size: 18px;
            margin-bottom: 10px;
        }
        button:hover, input[type=button]:hover, input[type=reset]:hover, input[type=submit]:hover {
            border-color: var(--primary-color-1);
        }
    }
    &-inputs {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        row-gap: 30px;
    }

    &-input-field {  
        width: 49%;
        label {
            margin-bottom: 10px;
        }
    }
    &-service-input {
        display: flex;
        flex-wrap: wrap;
        column-gap: 20px;
        row-gap: 20px;
        margin: 30px 0;
        span {
            width: 100%;
            font-size: 18px;
            margin-bottom: 10px;
        }
    }
}

@media (max-width: 575px) {
    .request-quote__area-input-field {
        width: 100%;
    }    
}