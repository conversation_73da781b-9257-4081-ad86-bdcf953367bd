/*==========================================================================
* Portfolio One Style
==========================================================================*/

.portfolio__one {
    background: var(--color-1);
    .custom__container {
        max-width: 1745px ;
    }
    &-content-left {
        h2 {
            margin: auto;
            text-transform: none;
        }
    }

    &-single-portfolio {
        position: relative;
        border-radius: 30px;
        overflow: hidden;
        transition:all .3s linear;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        &-content {
            position: absolute;
            z-index: 3;
            bottom: 4%;
            left: 50%;
            transform: translateX(-50%);
            width: 94%;
            background: white;
            border-radius: 19px;
            border-bottom: 4px solid var(--primary-color-1);
            padding: 17px 30px;
            opacity: 0;
            visibility: hidden;
            transition: .4s all ease-in-out;
            h4 {
                font-size: 28px;
                font-weight: 600;
            }
            span {
                font-size: 18px;
                color: var(--primary-color-1);
            }
        }
    }
    .swiper-slide { 
        height: 365px;
        transform: scale(0.8);
        &.swiper-slide-active{
            transform: scale(1.2);
            .portfolio__one-single-portfolio-content {
                visibility: visible;
                opacity: 1;
            }
        }
    }
}


@media (max-width: 1399px) {
    .portfolio__one {
        &-content-right {
            p {
                margin-left: 0;
            }
            a {
                margin-left: 0;
            }
        }
        &-single-portfolio {
            &-content {
                h4 {
                    font-size: 28px;
                }
            }
        }
    }
}

@media (max-width: 1199px) {
    .portfolio__one {
        &-content-left {
            margin-bottom: 0;
            h2 {
                margin: 0;
            }
        }
    }
}

@media (max-width: 992px) {
    .portfolio__one .swiper-slide {
        transform: scale(1); 
        &.swiper-slide-active {
            transform: scale(1);
        }
    }
}

@media (max-width: 767px) {
    .portfolio__one {
        &-single-portfolio {
            width: auto;
            margin-bottom: 30px;
            &.active {
                width: auto;
                height: 365px;
            }
            &:last-child {
                margin: 0;
            }
            &-content {
                h4 {
                    font-size: 25px;
                }
            }
        }
    }
}

@media (max-width: 480px) {
    .portfolio__one {
        &-content-right {
            p {
                font-size: 15px;
            }
        }
    }
}

/*==========================================================================
* Portfolio Two CSS
==========================================================================*/

.portfolio__two {
    &-title {
        margin-bottom: 60px;
        h2 {
            text-transform: none;
        }
    }
    &-items {
        display: flex;
        justify-content: space-between;
        align-items: center;
        &:last-child .portfolio__two-single-item:last-child {
            margin-bottom: 0;
        }
    }
    &-single-item {
        height: 345px;
        overflow: hidden;
        margin-bottom: 30px;
        border-radius: 30px;
        transition: .5s;
        margin-right: 30px;
        position: relative;
        &:last-child {
            margin-right: 0;
        }
        &-img-wrapper {
            width: 100%;
            height: 100%;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
        &-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--bg-white);
            padding: 30px;
            position: absolute;
            bottom: 0;
            left: 48%;
            width: 88%;
            transform: translateX(-50%);
            border-radius: 10px;
            opacity: 0;
            transition: 0.3s;
            visibility: hidden;
            border-left: 4px solid var(--primary-color-1);
            &-left {
                h4 {
                    font-size: 28px;
                    margin-bottom: 3px;
                }
                p {
                    font-size: 17px;
                    text-transform: none;
                    color: var(--primary-color-1);
                }
            }
            &-right {
                margin-right: -13%;
                border: 3px solid var(--bg-white);
                border-radius: 50%;
                a {
                    font-size: 20px;
                    transform: rotate(-45deg);
                    width: 60px;
                    height: 60px;
                    display: block;
                    text-align: center;
                    line-height: 59px;
                    border-radius: 50%;
                    border: 1px solid var(--primary-color-1);
                    transition: 0.4s;
                    background-color: var(--primary-color-1);
                    color: var(--text-white);
                    &:hover {
                        color: var(--text-heading-color);
                        background-color: var(--bg-white);
                        transform: rotate(0);
                    }
                }
            }
        }
        &:hover {
            .portfolio__two-single-item-content {
                opacity: 1;
                bottom: 20px;
                visibility: visible;
            }
        }
    }
    &.three-columns {
        .portfolio__two-single-item {
            height: 500px;
            border-radius: 20px;
            h3 {
                font-size: 24px;
            }
        }
    }
}

@media (max-width: 991px) {
    .portfolio__two-items {
        flex-direction: column;
    }
    .portfolio__two-single-item {
        width: 100%;
        margin-right: 0;
    }
}

@media (max-width: 767px) {
    .portfolio__two-single-item-content-left {
        width: 80%;
    }
}

@media (max-width: 480px) {
    .portfolio__two-single-item-content-left {
        width: 100%;
        text-align: center;
        margin-bottom: 15px;
    }
    .portfolio__two-single-item-content {
        flex-direction: column;
    }
}