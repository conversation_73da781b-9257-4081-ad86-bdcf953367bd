/*==========================================================================
* Skill Style 1 Area Style
==========================================================================*/

.skill-area__one {
    &-left {
        position: relative;
        &-video {
            position: absolute;
            right: 70px;
            bottom: 0;
            z-index: 2;
            width: 350px;
            height: 280px;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            &::before {
                content: "";
                position: absolute;
                top: 1px;
                left: 0;
                width: 100%;
                height: 100%;
                background: var(--primary-color-1);
                z-index: -1;
                border-radius: 20px;
                transform: rotate(-10deg);
	            animation: halfRotationAnimation 5s infinite ease-in-out;
            }
            &-play-btn {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 65px;
                height: 65px;
                text-align: center;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                background: var(--primary-color-1);
                border-radius: 50%;
                font-size: 21px;
                color: var(--bg-white);
                transition: 0.4s;
                &:hover {
                    color: var(--primary-color-1);
                    background: var(--bg-white);
                }
            }
        }
    }
    &-right {
        h2 {
            margin-bottom: 18px;
        }
        p {
            color: var(--color-2);
            text-transform: none;
            margin-bottom: 35px;
        }
        &-skill-item {
            margin-bottom: 30px;
            &-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 22px;
                &-title {
                    font-size: 22px;
                    font-weight: 700;
                }
                &-count {
                    font-size: 18px;
                    .count {
                        width: 50px;
                    }
                }
            }
            &-inner {
                height: 3px;
                background: var(--color-4);
                border-radius: 5px;
            }
            &-bar {
                height: 3px;
                width: 0;
                border-radius: 5px;
                background: var(--primary-color-1);
                transition: all 3.5s ease-out 0s;
            }
        }
    }
}

@media (max-width: 767px) {
    .skill-area__one-left-video {
        right: 0;
    }
}

/*==========================================================================
* Skill Style 1 Area Style
==========================================================================*/

.skill__two {
    .tab-content>.active {
        display: flex;
    }
    &-tab {
        background: var(--bg-white);
        border: 1px solid var(--color-4);
        padding: 45px 60px 40px 60px;
        border-radius: 10px;
        text-align: left;
        transition: .5s;
        position: relative;
        &::before {
            content: "";
            width: 0;
            height: 0;
            position: absolute;
            bottom: -15%;
            left: 50%;
            transform: translateX(-50%);
            border: 17px solid transparent;
            border-top: 22px solid var(--primary-color-1);
            opacity: 0;
            visibility: hidden;
            transition: .5s;
            z-index: -1;
        }
        &-icon {
            color: var(--primary-color-1) !important;
            background: #0E59F21A;
            font-size: 40px !important;
            width: 65px;
            height: 65px;
            border-radius: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            line-height: 0 !important;
        }
        span {
            font-size: 22px;
            line-height: 1.63;
            font-weight: 600;
            color: var(--text-heading-color);
        }
        &:hover {
            border: 1px solid var(--color-1);
            background-color: var(--color-1);
        }
        &.active {
            border: 1px solid var(--primary-color-1);
            background: var(--primary-color-1);
            &::before {
                opacity: 1;
                visibility: visible;
            }
            .skill__two-tab-icon {
                color: var(--text-white) !important;
                background: #FFFFFF1C !important;
            }  
            span {
                color: var(--text-white);
            }
        }
    }
    &-tab-details {
        &-image {
            position: relative;
            .experience-bar {
                display: flex;
                align-items: center;
                background: var(--primary-color-1);
                position: absolute;
                padding: 25px 50px 25px 25px;
                border-radius: 10px;
                right: 110px;
                top: 80px;
                i {
                    color: var(--text-white);
                    font-size: 70px;
                    margin-right: 30px;
                }
                span {
                    font-size: 18px;
                    color: var(--text-white);
                    font-weight: 500;
                }
            }
            .image-1 {
                border-radius: 20px;
            }
        }
        &-content {
            padding-right: 55px;
            h2 {
                margin-bottom: 30px;
            }
            p {
                color: var(--color-2);
                text-transform: none;
                margin-bottom: 30px;
            }
            &-service {
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                margin-bottom: 30px;
                .service {
                    margin-bottom: 20px;
                    span {
                        font-size: 18px;
                        color: var(--color-2);
                    }
                    i {
                        color: var(--color-3);
                        margin-right: 10px;
                    }
                }
            }
        }
    }
}  

@media (max-width: 1399px) {
    .skill__two-tab {
        padding: 35px 0 30px 40px;
    }
}

@media (max-width: 575px) {
    .skill__two-tab {
        padding: 35px 40px;
        margin: auto;
        width: 55%;
    }
}

@media (max-width: 480px) {
    .skill__two-tab {
        width: 100%;
    }
}
