/*==========================================================================
* About Area One CSS
==========================================================================*/

.about__one {
    &-image {
        position: relative;
        .experience-bar {
            display: flex;
            align-items: center;
            background-color: var(--primary-color-1);
            position: absolute;
            padding: 25px 50px 25px 25px;
            border-radius: 10px;
            right: 110px;
            top: 80px;
            i {
                color: var(--text-white);
                font-size: 70px;
                margin-right: 30px;
            }
            span {
                font-size: 18px;
                color: var(--text-white);
                font-weight: 500;
            }
            &-counter {
                display: flex;
                align-items: center;
                color: var(--text-white);
                h4 {
                    font-size: 45px;
                    color: var(--text-white);
                    line-height: 1.2;
                }
                span {
                    font-size: 45px;
                    font-weight: 700;
                }
            }
        }
        img {
            border-radius: 10px;
        }
        .image-1 {
            width: 370px;
            height: 485px;
        }
        .image-2 {
            margin-left: 45%;
            margin-top: -40%;
            border: 10px solid var(--bg-white);
        }
    }
    &-content {
        h2 {
            text-transform: none;
            margin-bottom: 30px;
        }
        p {
            color: var(--color-2);
            margin-bottom: 30px;
        }
        &-service {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
            .service {
                margin-bottom: 20px;
                span {
                    font-size: 18px;
                    color: var(--color-2);
                }
                i {
                    color: var(--color-3);
                    margin-right: 10px;
                   font-weight: 300;
                }
            }
        }
    }
}


@media (max-width: 991px) {
    .about__one-content-service {
        display: block;
    }  
}

@media (max-width: 575px) {
    .about__one-image .experience-bar {
        right: 35px;
        top: 145px;
    }
    .about__one-image .image-2 {
        margin-left: 25%;
        margin-top: -51%;
    }
}

@media (max-width: 480px) {
    .about__one {
        &-image {
            &-shapes {
                display: none;
            }
        }
        &-content {
            p {
                font-size: 15px;
            }
        }
    }
    .about__one-image .image-2 {
        display: none;
    }
    .about__one-image .image-1 {
        width: 100%;
    }
    .about__one-image .experience-bar {
        right: 0;
        top: 32px;
    }
}


/*==========================================================================
* About Area Two CSS
==========================================================================*/

.about__two {
    &-content {
        padding-left: 54px;
        h2 {
            text-transform: none;
            margin-bottom: 17px;
        }
        p {
            color: var(--color-2);
            margin-bottom: 30px;
            text-transform: none;
        }
        &-service {
            display: block;
            margin-bottom: 40px;
            .service {
                margin-bottom: 13px;
                span {
                    font-size: 18px;
                    color: var(--color-2);
                }
                i {
                    color: var(--primary-color-1);
                    margin-right: 10px;
                }
            }
        }
    }
    &-left {
        &-right-image {
            border-radius: 0 10px 10px 0;
            overflow: hidden;
            margin-bottom: 30px;
            height: 214px;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
        &-progressbar {
            background-color: var(--color-1);
            width: 100%;
            height: 230px;
            border-radius: 0 10px 10px 0;
            padding: 18px;
            display: flex;
            flex-direction: column;
            align-items: center;
            row-gap: 10px;
            &-wrapper {
                width: 100%;
                height: 100%;
                border: 2px dashed var(--primary-color-1);
                border-radius: 0 10px 10px 0;
                padding: 15px;
                display: flex;    
                flex-direction: column;
                align-items: center;
                row-gap: 10px;
            }
            &-value {
                position: relative;
                width: 100px;
                height: 100px;
                border-radius: 50%;
                background: conic-gradient(var(--primary-color-1) 270deg, var(--color-4) 0deg);
                display: flex;
                justify-content: center;
                align-items: center;
                span {
                    position: relative;
                    font-size: 20px;
                    color: var(--color-2);
                }
                &::before {
                    content: "";
                    width: 88px;
                    height: 88px;
                    background-color: var(--color-1);
                    border-radius: 50%;
                    position: absolute;
                }
            }
            &-title {
                font-size: 18px;
                color: var(--color-2);
                font-weight: 400;
            }
        }
        &-image-left-side {
            border-radius: 40px 0 0 40px;
            overflow: hidden;
        }
    }
}


@media (max-width: 1199px) {
    .about__two-left-progressbar-value {
        width: 80px;
        height: 80px;
        &::before {
            width: 70px;
            height: 70px;
        }
    }

}

@media (max-width: 992px) {
    .about__two-content {
        padding-left: 0; 
    }
}

@media (max-width: 575px) {
    .about__two-right-progressbar {
        width: 60%;
        margin-top: -115px;
        margin-left: 170px;
        margin-bottom: 30px;
        position: relative;
        z-index: 3;
    }
    .about__two-left-image-left-side {
        margin-bottom: 20px;
    }
    .about__two-right-image-right-side {
        display: none;
    }
}

@media (max-width: 480px) {
    .about__two-right-left-image {
        margin: auto;
        margin-bottom: 30px;
        text-align: center;
        width: 100%;
        img {
            width: 100%;
        }
    }
    .about__two-right-progressbar {
        margin: 0;
        margin: auto;
        width: 100%;
    }
}


/*==========================================================================
* About Area Three CSS
==========================================================================*/

.about__three {
    &-content {
        h2 {
            text-transform: none;
            margin-bottom: 17px;
        }
        p {
            color: var(--color-2);
            margin-bottom: 30px;
            text-transform: none;
        }
        &-service {
            &-single {
                display: flex;
                justify-content: space-between;
                box-shadow: 0px 0px 50px 0px rgba(0, 0, 0, 0.06);
                align-items: center;
                margin-bottom: 20px;
                padding: 22px 30px;
                border-radius: 10px;
                i {
                    margin-right: 18px;
                    background-color: var(--primary-color-1);
                    border-radius: 50%;
                    font-size: 42px;
                    padding: 17px 17px;
                    color: var(--text-white);
                }
                h4 {
                    font-size: 22px;
                    margin-bottom: 10px;
                }
                p {
                    margin: 0;
                }
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
    &-right {
        position: relative;
        &-counter {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 258px;
            background-color: var(--primary-color-1);
            padding: 24px 25px;
            border-radius: 10px;
            h4 {
                color: var(--text-white);
                font-size: 60px;
                margin-right: 6px;
                width: 58%;
            }
            span {
                color: var(--text-white);
                font-size: 18px;
                opacity: .7;
            }
        }
        &-image-left-side {
            border-radius: 30px 0 0 30px;
            overflow: hidden;
            height: 600px;
            img {
                width: 100%;
                height: 100%;
            }
        }
        &-image {
            margin-bottom: 30px;
            border-radius: 0px 30px 0px 0px;
            overflow: hidden;
            height: 285px;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            &:last-child {
                margin-bottom: 0;
                border-radius: 0px 0px 30px 0px;
            }
        }
    }
}

@media (max-width: 480px) {
    .about__three-content-service-single {
        flex-direction: column;
        text-align: center;
        i {
            margin-bottom: 10px;
        }
    }
}

@media (max-width: 575px) {
    .about__three-right-image-left-side {
        display: none;
    }
    .about__three-right-image {
        width: 100%;
        img {
            width: 100%;
        }
    }
}

/*==========================================================================
* About Area Four CSS
==========================================================================*/

.about__four {
    background-color: var(--color-1);
    &-image {
        position: relative;
        .experience-bar {
            background-color: var(--primary-color-1);
            position: absolute;
            padding: 30px 40px 30px 40px;
            top: 200px;
            right: 160px;
            border-radius: 15px;
            span {
                font-size: 20px;
                color: var(--text-white);
                font-weight: 500;
            }
            &-counter {
                display: flex;
                justify-content: center;
                align-items: center;
                color: var(--text-white);
                h4 {
                    font-size: 52px;
                    color: var(--text-white);
                    line-height: 1.3;
                }
                span {
                    font-size: 52px;
                    font-weight: 700;
                }
            }
        }
        img {
            border-radius: 10px;
        }
        .image-1 {
            width: 440px;
            height: 470px;
        }
        .image-2 {
            margin-left: 51%;
            margin-top: -30%;
            border: 10px solid var(--bg-white);
            border-radius: 20px;
            width: 285px;
        }
    }
    &-content {
        h2 {
            text-transform: none;
            margin-bottom: 30px;
        }
        p {
            color: var(--color-2);
            margin-bottom: 30px;
        }
        &-service {
            margin-bottom: 30px;
            .service {
                margin-bottom: 12px;
                span {
                    font-size: 18px;
                }
                i {
                    color: var(--color-3);
                    margin-right: 5px;
                }
            }
        }
        a {
            border-color: var(--color-4);
        }
    }
}

@media (max-width: 575px) {
    .about__four-image .image-2 {
        margin-left: 0;
    }
    .about__four-image .experience-bar {
        padding: 15px 25px 18px 25px;
        left: 28px;
        width: fit-content;
    }
}


/*==========================================================================
* About Area Five CSS
==========================================================================*/

.about__five {
    background-repeat: no-repeat;
    background-position: center top;
    background-size: contain;
    &-image {
        position: relative;
        &-wrapper {
            padding: 0 35px;
        }
        .image-2 {
            position: absolute;
            width: 135px;
            right: 158px;
            top: 90px;
        }
    }
    &-content {
        h2 {
            text-transform: none;
            margin-bottom: 30px;
        }
        p {
            color: var(--color-2);
            margin-bottom: 30px;
        }
        &-service {
            margin-bottom: 30px;
            .single-service {
                margin-bottom: 12px;
                display: flex;
                align-items: baseline;
                span {
                    font-size: 18px;
                }
                i {
                    color: var(--color-3);
                    margin-right: 15px;
                    font-size: 22px;
                }
            }
        }
    }
}

@media (max-width: 767px) {
    .about__five-image .image-2 {
        width: 90px;
        right: 100px;
        top: 57px;
    }
}

@media (max-width: 359px) {
    .about__five-image .image-2 {
        display: none;
    }
}

/*==========================================================================
* Brand Area CSS
==========================================================================*/

@media (max-width: 991px) {
    .brand__area {
        padding-top: 80px;
        .swiper-wrapper {
            transition-timing-function: linear;
        }
    }
}