/*------------------------------------------------------------------------

Template Name:  Bantec - IT Service & Technology HTML5 Template
Author:  Theme<PERSON>ri
Author URI: http://themeori.com
Version: 1.0.2
Description: This Template is created for web template

--------------------------------------------------------------------------
TABLE OF CONTENTS
--------------------------------------------------------------------------

* Google Fonts
* Common CSS
* Form CSS
* Hedging CSS
* Bantec Button And Title Styles CSS
Highlighted Text Styles
* Scroll Top CSS
* Preloader CSS
* Animation CSS
* Bantec Color CSS
* Space And Container CSS
* Top Bar CSS
* Menu Bar Sticky CSS
* Menu Bar CSS
* Menu Sidebar CSS
* Banner One CSS
* Banner Two CSS
* Banner Three CSS
* Banner Four CSS
* Banner Five CSS
* Page Banner CSS
* About Area One CSS
* About Area Two CSS
* About Area Three CSS
* About Area Four CSS
* About Area Five CSS
* Brand Area CSS
* Skill Style 1 Area Style
* Skill Style 1 Area Style
* Services One Style
* Services Two Style
* Services Three Style
* Service One Style
* Service Five Style
* Service Details CSS
* Portfolio One Style
* Portfolio Two CSS
* Project Details CSS
* Team One Style
* Team Two Style
* Horizontal Scroll Animation
* Contact One CSS
* Contact Two CSS
Contact Location Map 
* Why Choose Us Style 
* Why Choose Us two CSS
* Why Choose Us three Style
* Why Choose Us four Style
* Pricing Plan One CSS
* Pricing Plan Two CSS
* Work Process One Style
* Work Process Two style 
* Work Process Three style 
* Work Process Four style 
* Counter One Style
* FAQ Style One
* FAQ Style Two
* FAQ Style Three
* Blog One Style
* Blog Two Style
* Blog Three Style
* Blog Four Style
* Blog With Sidebar Style
* Blog Sidebar Style
* Blog Five Style
* Blog Details Style
* Testimonial One Style
* Testimonial Two Style
* Testimonial Three Style
* Testimonial Four Style
* Testimonial Five Style
* Testimonial Six Style
* Video One Start Here
* About Area Five CSS
* Subscribe One, Two Three CSS
* Footer One CSS
* Footer Two CSS
* Footer Three CSS
* Footer Four CSS
* Footer Five CSS
* Footer Copyright CSS
* 404 CSS

/*==========================================================================
* Google Fonts
==========================================================================*/
@import url("https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;700&display=swap");
/*==========================================================================
* Common CSS
==========================================================================*/
body {
  font-family: var(--body-font);
  color: var(--body-color);
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  text-transform: capitalize;
}

img {
  max-width: 100%;
  height: auto;
  transition: 0.4s;
}

a {
  outline: none;
  color: inherit;
  text-decoration: none;
}

a,
button,
i {
  text-decoration: none;
  color: inherit;
}

a:focus,
a:hover {
  text-decoration: none;
  color: inherit;
}

.section-padding {
  padding: 120px 0px;
}

@media (max-width: 991px) {
  .section-padding {
    padding: 80px 0px;
  }
}
@media (max-width: 575px) {
  .section-padding {
    padding: 60px 0px;
  }
}
.display-none {
  display: none;
}

.display-block {
  display: block;
}

.filter img {
  filter: grayscale(100%);
}

.img__full {
  width: 100%;
}

/*==========================================================================
* Form CSS
==========================================================================*/
button,
input[type=button],
input[type=reset],
input[type=submit] {
  border: 1px solid var(--border-color-2);
  border-color: transparent;
  border-radius: 6px;
  background: var(--primary-color-1);
  color: var(--text-white);
  padding: 17px 40px;
}

button:hover,
input[type=button]:hover,
input[type=reset]:hover,
input[type=submit]:hover {
  border-color: transparent;
}

button:active,
button:focus,
input[type=button]:active,
input[type=button]:focus,
input[type=reset]:active,
input[type=reset]:focus,
input[type=submit]:active,
input[type=submit]:focus {
  border-color: transparent;
}

input[type=text],
input[type=email],
input[type=url],
input[type=password],
input[type=search],
input[type=number],
input[type=tel],
input[type=range],
input[type=date],
input[type=month],
input[type=week],
input[type=time],
input[type=datetime],
input[type=datetime-local],
input[type=color],
textarea {
  color: var(--body-color);
  border-radius: 5px;
  width: 100%;
  height: 60px;
  border: 1px solid var(--color-4);
  padding: 0 15px;
  background: var(--bg-white);
}

input[type=text]:focus,
input[type=email]:focus,
input[type=url]:focus,
input[type=password]:focus,
input[type=search]:focus,
input[type=number]:focus,
input[type=tel]:focus,
input[type=range]:focus,
input[type=date]:focus,
input[type=month]:focus,
input[type=week]:focus,
input[type=time]:focus,
input[type=datetime]:focus,
input[type=datetime-local]:focus,
input[type=color]:focus,
textarea:focus {
  color: var(--text-heading-color);
  outline: none;
  box-shadow: none;
  border-color: var(--primary-color-1);
}

select {
  border: 1px solid var(--border-color-2);
}

textarea {
  width: 100%;
  height: 150px;
  padding-top: 15px;
}

button,
button:hover,
button:focus {
  box-shadow: none;
  border: none;
  outline: none;
}

/*==========================================================================
* Hedging CSS
==========================================================================*/
h1 {
  font-size: 70px;
  line-height: 1.3;
  padding: 0;
  margin: 0;
  color: var(--text-heading-color);
  font-family: var(--heading-font);
  font-weight: 700;
}

h2 {
  font-size: 60px;
  line-height: 1.13;
  padding: 0;
  margin: 0;
  color: var(--text-heading-color);
  font-family: var(--heading-font);
  font-weight: 700;
}

h3 {
  font-size: 32px;
  line-height: 1.25;
  padding: 0;
  margin: 0;
  color: var(--text-heading-color);
  font-family: var(--heading-font);
  font-weight: 700;
}

h4 {
  font-size: 22px;
  line-height: 1.63;
  padding: 0;
  margin: 0;
  color: var(--text-heading-color);
  font-family: var(--heading-font);
  font-weight: 700;
}

h5 {
  font-size: 18px;
  line-height: 1.66;
  padding: 0;
  margin: 0;
  color: var(--text-heading-color);
  font-family: var(--heading-font);
  font-weight: 700;
}

h6 {
  font-size: 22px;
  line-height: 32px;
  padding: 0;
  margin: 0;
  color: var(--text-heading-color);
  font-family: var(--heading-font);
  font-weight: 700;
}

p {
  font-size: 18px;
  line-height: 30px;
  padding: 0;
  margin: 0;
}

.t-left {
  text-align: left;
}

.t-center {
  text-align: center;
}

.t-right {
  text-align: right;
}

@media (max-width: 1399px) {
  h2 {
    font-size: 45px;
    line-height: 55px;
  }
}
@media (max-width: 767px) {
  h2 {
    font-size: 38px;
    line-height: 50px;
  }
}
@media (max-width: 420px) {
  h2 {
    font-size: 31px;
    line-height: 41px;
  }
  h3 {
    font-size: 28px;
    line-height: 38px;
  }
}
@media (max-width: 359px) {
  h2 {
    font-size: 27px;
    line-height: 37px;
  }
  h3 {
    font-size: 24px;
    line-height: 34px;
  }
  h4 {
    font-size: 21px;
    line-height: 31px;
  }
  h5 {
    font-size: 20px;
    line-height: 30px;
  }
  h6 {
    font-size: 18px;
    line-height: 28px;
  }
}
/*==========================================================================
* Bantec Button And Title Styles CSS
==========================================================================*/
.subtitle-one,
.subtitle-two,
.subtitle-three,
.subtitle-four {
  position: relative;
  text-transform: capitalize;
  font-family: var(--heading-font);
  color: var(--primary-color-1);
  font-weight: 500;
  font-size: 16px;
  line-height: 1.87;
  display: inline-block;
  margin-bottom: 14px;
  background: rgba(14, 89, 242, 0.1019607843);
  padding: 1px 25px;
  border-radius: 10px;
}

.subtitle-two {
  background: rgba(14, 89, 242, 0.2392156863);
}

.subtitle-three::before {
  content: "";
  width: 25px;
  height: 1px;
  position: absolute;
  background: var(--primary-color-1);
  right: -35px;
  top: 15px;
}

.subtitle-four::after {
  content: "";
  width: 25px;
  height: 1px;
  position: absolute;
  background: var(--primary-color-1);
  left: -35px;
  top: 15px;
}
.subtitle-four::before {
  content: "";
  width: 25px;
  height: 1px;
  position: absolute;
  background: var(--primary-color-1);
  right: -35px;
  top: 15px;
}

.btn-one,
.btn-two,
.btn-three,
.btn-four,
.btn-five,
.btn-six,
.btn-seven,
.btn-eight,
.btn-nine {
  background: transparent;
  color: var(--text-heading-color);
  display: inline-flex;
  align-items: center;
  font-size: 18px;
  line-height: 26px;
  padding: 16px 28px;
  text-align: center;
  font-weight: 700;
  font-family: var(--heading-font);
  z-index: 3;
  position: relative;
  transition: 0.4s;
  text-transform: capitalize;
  overflow: hidden;
  border-radius: 50px;
  border: 1px solid var(--primary-color-1);
}
.btn-one i,
.btn-two i,
.btn-three i,
.btn-four i,
.btn-five i,
.btn-six i,
.btn-seven i,
.btn-eight i,
.btn-nine i {
  position: relative;
  top: 0px;
  margin-left: 10px;
  font-size: 15px;
  transition: 0.2s;
}
.btn-one:hover,
.btn-two:hover,
.btn-three:hover,
.btn-four:hover,
.btn-five:hover,
.btn-six:hover,
.btn-seven:hover,
.btn-eight:hover,
.btn-nine:hover {
  background-color: var(--primary-color-1);
  color: var(--text-white);
}
.btn-one:hover i,
.btn-two:hover i,
.btn-three:hover i,
.btn-four:hover i,
.btn-five:hover i,
.btn-six:hover i,
.btn-seven:hover i,
.btn-eight:hover i,
.btn-nine:hover i {
  transform: translateX(2px);
  color: var(--text-white);
}

.btn-two {
  background: var(--primary-color-1);
  color: var(--text-white);
  text-transform: none;
}
.btn-two:hover {
  background-color: transparent;
  color: var(--text-heading-color);
}
.btn-two:hover i {
  color: var(--text-heading-color);
}
.btn-three {
  background: transparent;
  border: 0;
  padding: 0;
  padding-right: 10px;
  border-radius: 0;
}
.btn-three:hover {
  background-color: transparent;
  color: var(--primary-color-1);
}
.btn-three:hover i {
  color: var(--primary-color-1);
}
.btn-four {
  background: var(--btn-heading-color);
}
.btn-four::before {
  background: var(--btn-white);
}
.btn-four:hover {
  color: var(--btn-heading-color);
}
.btn-five {
  background: var(--primary-color-2);
  color: var(--text-white);
  border-radius: 30px;
}
.btn-five::before {
  background: var(--btn-white);
}
.btn-five:hover {
  color: var(--btn-heading-color);
}
.btn-five:focus {
  color: var(--btn-heading-color);
}
.btn-six {
  background: transparent;
  border: 1px solid var(--primary-color-2);
  color: var(--primary-color-2);
  border-radius: 30px;
}
.btn-six:hover {
  border-color: var(--text-white);
}
.btn-six::before {
  background: var(--btn-white);
}
.btn-six:focus {
  color: var(--primary-color-2);
}
.btn-seven {
  background: var(--primary-color-3);
}
.btn-seven::before {
  background: var(--btn-heading-color);
}
.btn-eight {
  background: transparent;
  color: var(--primary-color-3);
  border: 1px solid var(--primary-color-3);
  padding: 16px 42px;
}
.btn-eight:hover {
  border-color: var(--btn-heading-color);
}
.btn-eight::before {
  background: var(--btn-heading-color);
}
.btn-eight:focus {
  color: var(--primary-color-3);
}
.btn-nine {
  background: var(--btn-white);
  color: var(--text-white);
}
.btn-nine:hover {
  color: var(--text-white);
}
.btn-nine::before {
  background: var(--btn-heading-color);
}
.btn-nine:focus {
  color: var(--primary-color-3);
}

.simple-btn-1,
.simple-btn-2,
.simple-btn-3 {
  color: var(--primary-color-1);
  display: inline-block;
  font-size: 16px;
  line-height: 26px;
  font-weight: 700;
  font-family: var(--heading-font);
  transition: 0.4s;
  text-transform: uppercase;
}
.simple-btn-1 i,
.simple-btn-2 i,
.simple-btn-3 i {
  position: relative;
  top: 0;
  margin-left: 15px;
  font-size: 13px;
}
.simple-btn-1:hover,
.simple-btn-2:hover,
.simple-btn-3:hover {
  color: var(--btn-heading-color);
}
.simple-btn-1:focus,
.simple-btn-2:focus,
.simple-btn-3:focus {
  color: var(--btn-heading-color);
}

.simple-btn-2 {
  color: var(--text-heading-color);
}
.simple-btn-2:hover {
  color: var(--primary-color-1);
}
.simple-btn-3 {
  color: var(--text-white);
}
.simple-btn-3:hover {
  color: var(--primary-color-1);
}

/*==========================================================================
* Highlighted Text Styles
==========================================================================*/
.highlighted {
  position: relative;
  z-index: 1;
  display: inline-block;
}
.highlighted::before {
  content: "";
  width: 100%;
  height: 8px;
  background: var(--primary-color-2);
  position: absolute;
  bottom: 13%;
  left: 5px;
  z-index: -1;
  animation: highlightAnimation 4s infinite;
}

@keyframes highlightAnimation {
  0% {
    width: 0;
  }
  15% {
    width: 100%;
  }
  85% {
    opacity: 1;
  }
  90% {
    width: 100%;
    opacity: 0;
  }
  to {
    width: 0;
    opacity: 0;
  }
}
.highlighted-two {
  position: relative;
  display: inline-block;
  color: var(--primary-color-1);
}
.highlighted-two::before {
  content: "";
  width: 108%;
  height: 60px;
  position: absolute;
  border: 2px solid transparent;
  border-top: 2px solid var(--primary-color-1);
  border-radius: 50%;
  top: 58px;
  left: -10px;
  z-index: 1;
}

@media (max-width: 480px) {
  .highlighted-two::before {
    top: 40px;
  }
}
/*==========================================================================
* Scroll Top CSS
==========================================================================*/
.scroll-up {
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px var(--color-8);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  position: fixed;
  right: 20px;
  bottom: 20px;
  height: 50px;
  width: 50px;
  transition: all 200ms linear;
}
.scroll-up::after {
  position: absolute;
  font-family: "Font Awesome 5 Pro";
  content: "\f176";
  text-align: center;
  line-height: 50px;
  font-size: 24px;
  color: var(--primary-color-1);
  left: 0;
  top: 0;
  font-size: 20px;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 200ms linear;
}
.scroll-up.active-scroll {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.scroll-up svg path {
  fill: none;
}
.scroll-up svg.scroll-circle path {
  stroke: var(--primary-color-1);
  stroke-width: 4;
  box-sizing: border-box;
  transition: all 200ms linear;
}

.scroll-two::after {
  color: var(--primary-color-2);
}
.scroll-two svg.scroll-circle path {
  stroke: var(--primary-color-2);
}

.scroll-three::after {
  color: var(--primary-color-3);
}
.scroll-three svg.scroll-circle path {
  stroke: var(--primary-color-3);
}

/*==========================================================================
* Preloader CSS
==========================================================================*/
.loader {
  z-index: 9999999;
  position: fixed;
  width: 100%;
  height: 100%;
  background: #dbe5f1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loader-container, .loader-container:before, .loader-container:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  animation-fill-mode: both;
  animation: bblFadInOut 1.8s infinite ease-in-out;
}

.loader-container {
  color: var(--primary-color-1);
  font-size: 7px;
  position: relative;
  text-indent: -9999em;
  transform: translateZ(0);
  animation-delay: -0.16s;
}

.loader-container:before,
.loader-container:after {
  content: "";
  position: absolute;
  top: 0;
}

.loader-container:before {
  left: -4.5em;
  animation-delay: -0.32s;
}

.loader-container:after {
  left: 4.5em;
}

@keyframes bblFadInOut {
  0%, 80%, 100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
/*==========================================================================
* Animation CSS
==========================================================================*/
.video {
  position: relative;
  text-align: center;
  display: inline-block;
  z-index: 4;
}
.video a {
  position: relative;
  color: var(--primary-color-2);
  font-size: 20px;
  z-index: 1;
  background: var(--bg-white);
  width: 90px;
  height: 90px;
  line-height: 90px;
  border-radius: 50%;
  display: block;
}

.video-pulse::after, .video-pulse::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  border: 1px solid var(--border-white);
  opacity: 0.3;
  left: 0;
  top: 0;
  border-radius: 50%;
  animation-duration: 2.5s;
  animation-timing-function: linear;
  animation-name: video-animation;
  animation-iteration-count: infinite;
}

.video-pulse::before {
  animation-delay: 1s;
}

@keyframes video-animation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}
/*============** Custom Animation **============*/
.animate-y-axis {
  animation-name: y-axis;
  animation-duration: 11s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
@keyframes y-axis {
  0% {
    transform: translateY(-30px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(-30px);
  }
}
.animate-y-axis-slider {
  animation-name: y-axis-slider;
  animation-duration: 3s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
@keyframes y-axis-slider {
  0% {
    transform: translateY(-40px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(-40px);
  }
}
/*==== Animation x-axis ==== */
.animate-x-axis {
  animation-name: x-axis;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
@keyframes x-axis {
  0% {
    transform: translateX(-20px);
  }
  50% {
    transform: translateX(-10px);
  }
  100% {
    transform: translateX(-20px);
  }
}
/*==== Animation Rotate ==== */
.animate-rotate {
  animation-name: rotate;
  animation-duration: 24s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  -webkit-animation-name: rotate;
  -webkit-animation-duration: 24s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  -moz-animation-name: rotate;
  -moz-animation-duration: 24s;
  -moz-animation-iteration-count: infinite;
  -moz-animation-timing-function: linear;
  -ms-animation-name: rotate;
  -ms-animation-duration: 24s;
  -ms-animation-iteration-count: infinite;
  -ms-animation-timing-function: linear;
  -o-animation-name: rotate;
  -o-animation-duration: 24s;
  -o-animation-iteration-count: infinite;
  -o-animation-timing-function: linear;
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
/*==== Animate zoom-in-out  ====*/
.animate-zoom-in-out {
  animation-name: zoomInOut;
  animation-duration: 3s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
  -webkit-animation-name: zoomInOut;
  -webkit-animation-duration: 3s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: ease-in-out;
  -moz-animation-name: zoomInOut;
  -moz-animation-duration: 3s;
  -moz-animation-iteration-count: infinite;
  -moz-animation-timing-function: ease-in-out;
  -ms-animation-name: zoomInOut;
  -ms-animation-duration: 3s;
  -ms-animation-iteration-count: infinite;
  -ms-animation-timing-function: ease-in-out;
  -o-animation-name: zoomInOut;
  -o-animation-duration: 3s;
  -o-animation-iteration-count: infinite;
  -o-animation-timing-function: ease-in-out;
}
@keyframes zoomInOut {
  0% {
    transform: rotate(0deg) scale(0.7);
  }
  50% {
    transform: rotate(180deg) scale(1);
  }
  100% {
    transform: rotate(360deg) scale(0.7);
  }
}
.halfRotationAnimation {
  animation: halfRotationAnimation 5s infinite ease-in-out;
}

@keyframes halfRotationAnimation {
  0% {
    transform: rotate(-10deg);
  }
  50% {
    transform: rotate(-15deg);
  }
  100% {
    transform: rotate(-10deg);
  }
}
.swiper-button-prev::after {
  display: none;
}

.swiper-button-next::after {
  display: none;
}

/*==========================================================================
* Bantec Color CSS
==========================================================================*/
:root {
  --body-font: "Space Grotesk", sans-serif;
  --heading-font: "Space Grotesk", sans-serif;
  --text-white: #FFFFFF;
  --bg-white: #FFFFFF;
  --btn-white: #FFFFFF;
  --border-white: #FFFFFF;
  --black: #000000;
  --primary-color-1: #0E59F2;
  --primary-color-2: #F8E559;
  --primary-color-3: #406AFF;
  --bg-heading-color: #051634;
  --text-heading-color: #051634;
  --btn-heading-color: #131313;
  --body-color: #343434;
  --dark-one: #1C1E22;
  --dark-two: #222429;
  --color-1: #F4F7FB;
  --color-2: #737373;
  --color-3: #0B46BF;
  --color-4: #CCCCCC;
  --color-5: #12223e;
  --color-6: #864af942;
  --color-7: #07204D;
  --border-color-1: #E7E7E7;
  --border-color-2: #E9E9E8;
  --border-color-3: rgba(19, 19, 19, 0.1);
  --border-color-4: rgba(19, 19, 19, 0.06);
  --box-shadow-1: 0px 25px 70px rgba(0, 0, 0, 0.08);
  --box-shadow-2: 0px 10px 70px rgba(0, 0, 0, 0.1);
}

/*==========================================================================
* Space And Container CSS
==========================================================================*/
.custom__container {
  max-width: 1520px;
  margin: 0 auto;
  padding: 0 12px;
}

.auto__container {
  max-width: 1720px;
  margin: 0 auto;
  padding: 0 12px;
}

.mt-0 {
  margin-top: 0px;
}

.mb-0 {
  margin-bottom: 0px;
}

.ml-0 {
  margin-left: 0px;
}

.mr-0 {
  margin-right: 0px;
}

.pt-0 {
  padding-top: 0px;
}

.pb-0 {
  padding-bottom: 0px;
}

.pl-0 {
  padding-left: 0px;
}

.pr-0 {
  padding-right: 0px;
}

.mt-5 {
  margin-top: 5px;
}

.mb-5 {
  margin-bottom: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.mr-5 {
  margin-right: 5px;
}

.pt-5 {
  padding-top: 5px;
}

.pb-5 {
  padding-bottom: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.pr-5 {
  padding-right: 5px;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.pt-10 {
  padding-top: 10px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pl-10 {
  padding-left: 10px;
}

.pr-10 {
  padding-right: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mb-15 {
  margin-bottom: 15px;
}

.ml-15 {
  margin-left: 15px;
}

.mr-15 {
  margin-right: 15px;
}

.pt-15 {
  padding-top: 15px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pl-15 {
  padding-left: 15px;
}

.pr-15 {
  padding-right: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.ml-20 {
  margin-left: 20px;
}

.mr-20 {
  margin-right: 20px;
}

.pt-20 {
  padding-top: 20px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pl-20 {
  padding-left: 20px;
}

.pr-20 {
  padding-right: 20px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-25 {
  margin-bottom: 25px;
}

.ml-25 {
  margin-left: 25px;
}

.mr-25 {
  margin-right: 25px;
}

.pt-25 {
  padding-top: 25px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pl-25 {
  padding-left: 25px;
}

.pr-25 {
  padding-right: 25px;
}

.mt-30 {
  margin-top: 30px;
}

.mb-30 {
  margin-bottom: 30px;
}

.ml-30 {
  margin-left: 30px;
}

.mr-30 {
  margin-right: 30px;
}

.pt-30 {
  padding-top: 30px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pl-30 {
  padding-left: 30px;
}

.pr-30 {
  padding-right: 30px;
}

.mt-35 {
  margin-top: 35px;
}

.mb-35 {
  margin-bottom: 35px;
}

.ml-35 {
  margin-left: 35px;
}

.mr-35 {
  margin-right: 35px;
}

.pt-35 {
  padding-top: 35px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pl-35 {
  padding-left: 35px;
}

.pr-35 {
  padding-right: 35px;
}

.mt-40 {
  margin-top: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.ml-40 {
  margin-left: 40px;
}

.mr-40 {
  margin-right: 40px;
}

.pt-40 {
  padding-top: 40px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pl-40 {
  padding-left: 40px;
}

.pr-40 {
  padding-right: 40px;
}

.mt-45 {
  margin-top: 45px;
}

.mb-45 {
  margin-bottom: 45px;
}

.ml-45 {
  margin-left: 45px;
}

.mr-45 {
  margin-right: 45px;
}

.pt-45 {
  padding-top: 45px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pl-45 {
  padding-left: 45px;
}

.pr-45 {
  padding-right: 45px;
}

.mt-50 {
  margin-top: 50px;
}

.mb-50 {
  margin-bottom: 50px;
}

.ml-50 {
  margin-left: 50px;
}

.mr-50 {
  margin-right: 50px;
}

.pt-50 {
  padding-top: 50px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pl-50 {
  padding-left: 50px;
}

.pr-50 {
  padding-right: 50px;
}

.mt-55 {
  margin-top: 55px;
}

.mb-55 {
  margin-bottom: 55px;
}

.ml-55 {
  margin-left: 55px;
}

.mr-55 {
  margin-right: 55px;
}

.pt-55 {
  padding-top: 55px;
}

.pb-55 {
  padding-bottom: 55px;
}

.pl-55 {
  padding-left: 55px;
}

.pr-55 {
  padding-right: 55px;
}

.mt-60 {
  margin-top: 60px;
}

.mb-60 {
  margin-bottom: 60px;
}

.ml-60 {
  margin-left: 60px;
}

.mr-60 {
  margin-right: 60px;
}

.pt-60 {
  padding-top: 60px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pl-60 {
  padding-left: 60px;
}

.pr-60 {
  padding-right: 60px;
}

.mt-65 {
  margin-top: 65px;
}

.mb-65 {
  margin-bottom: 65px;
}

.ml-65 {
  margin-left: 65px;
}

.mr-65 {
  margin-right: 65px;
}

.pt-65 {
  padding-top: 65px;
}

.pb-65 {
  padding-bottom: 65px;
}

.pl-65 {
  padding-left: 65px;
}

.pr-65 {
  padding-right: 65px;
}

.mt-70 {
  margin-top: 70px;
}

.mb-70 {
  margin-bottom: 70px;
}

.ml-70 {
  margin-left: 70px;
}

.mr-70 {
  margin-right: 70px;
}

.pt-70 {
  padding-top: 70px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pl-70 {
  padding-left: 70px;
}

.pr-70 {
  padding-right: 70px;
}

.mt-75 {
  margin-top: 75px;
}

.mb-75 {
  margin-bottom: 75px;
}

.ml-75 {
  margin-left: 75px;
}

.mr-75 {
  margin-right: 75px;
}

.pt-75 {
  padding-top: 75px;
}

.pb-75 {
  padding-bottom: 75px;
}

.pl-75 {
  padding-left: 75px;
}

.pr-75 {
  padding-right: 75px;
}

.mt-80 {
  margin-top: 80px;
}

.mb-80 {
  margin-bottom: 80px;
}

.ml-80 {
  margin-left: 80px;
}

.mr-80 {
  margin-right: 80px;
}

.pt-80 {
  padding-top: 80px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pl-80 {
  padding-left: 80px;
}

.pr-80 {
  padding-right: 80px;
}

.mt-85 {
  margin-top: 85px;
}

.mb-85 {
  margin-bottom: 85px;
}

.ml-85 {
  margin-left: 85px;
}

.mr-85 {
  margin-right: 85px;
}

.pt-85 {
  padding-top: 85px;
}

.pb-85 {
  padding-bottom: 85px;
}

.pl-85 {
  padding-left: 85px;
}

.pr-85 {
  padding-right: 85px;
}

.mt-90 {
  margin-top: 90px;
}

.mb-90 {
  margin-bottom: 90px;
}

.ml-90 {
  margin-left: 90px;
}

.mr-90 {
  margin-right: 90px;
}

.pt-90 {
  padding-top: 90px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pl-90 {
  padding-left: 90px;
}

.pr-90 {
  padding-right: 90px;
}

.mt-95 {
  margin-top: 95px;
}

.mb-95 {
  margin-bottom: 95px;
}

.ml-95 {
  margin-left: 95px;
}

.mr-95 {
  margin-right: 95px;
}

.pt-95 {
  padding-top: 95px;
}

.pb-95 {
  padding-bottom: 95px;
}

.pl-95 {
  padding-left: 95px;
}

.pr-95 {
  padding-right: 95px;
}

.mt-100 {
  margin-top: 100px;
}

.mb-100 {
  margin-bottom: 100px;
}

.ml-100 {
  margin-left: 100px;
}

.mr-100 {
  margin-right: 100px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pl-100 {
  padding-left: 100px;
}

.pr-100 {
  padding-right: 100px;
}

.mt-105 {
  margin-top: 105px;
}

.mb-105 {
  margin-bottom: 105px;
}

.ml-105 {
  margin-left: 105px;
}

.mr-105 {
  margin-right: 105px;
}

.pt-105 {
  padding-top: 105px;
}

.pb-105 {
  padding-bottom: 105px;
}

.pl-105 {
  padding-left: 105px;
}

.pr-105 {
  padding-right: 105px;
}

.mt-110 {
  margin-top: 110px;
}

.mb-110 {
  margin-bottom: 110px;
}

.ml-110 {
  margin-left: 110px;
}

.mr-110 {
  margin-right: 110px;
}

.pt-110 {
  padding-top: 110px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pl-110 {
  padding-left: 110px;
}

.pr-110 {
  padding-right: 110px;
}

.mt-115 {
  margin-top: 115px;
}

.mb-115 {
  margin-bottom: 115px;
}

.ml-115 {
  margin-left: 115px;
}

.mr-115 {
  margin-right: 115px;
}

.pt-115 {
  padding-top: 115px;
}

.pb-115 {
  padding-bottom: 115px;
}

.pl-115 {
  padding-left: 115px;
}

.pr-115 {
  padding-right: 115px;
}

.mt-120 {
  margin-top: 120px;
}

.mb-120 {
  margin-bottom: 120px;
}

.ml-120 {
  margin-left: 120px;
}

.mr-120 {
  margin-right: 120px;
}

.pt-120 {
  padding-top: 120px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pl-120 {
  padding-left: 120px;
}

.pr-120 {
  padding-right: 120px;
}

.display-n {
  display: none !important;
}

@media (max-width: 1199px) {
  .xl-pb-0 {
    padding-bottom: 0px;
  }
  .xl-pl-0 {
    padding-left: 0px;
  }
  .xl-pr-0 {
    padding-right: 0px;
  }
  .xl-mb-0 {
    margin-bottom: 0px;
  }
  .xl-ml-0 {
    margin-left: 0px;
  }
  .xl-mr-0 {
    margin-right: 0px;
  }
  .xl-pb-5 {
    padding-bottom: 5px;
  }
  .xl-pl-5 {
    padding-left: 5px;
  }
  .xl-pr-5 {
    padding-right: 5px;
  }
  .xl-mb-5 {
    margin-bottom: 5px;
  }
  .xl-ml-5 {
    margin-left: 5px;
  }
  .xl-mr-5 {
    margin-right: 5px;
  }
  .xl-pb-10 {
    padding-bottom: 10px;
  }
  .xl-pl-10 {
    padding-left: 10px;
  }
  .xl-pr-10 {
    padding-right: 10px;
  }
  .xl-mb-10 {
    margin-bottom: 10px;
  }
  .xl-ml-10 {
    margin-left: 10px;
  }
  .xl-mr-10 {
    margin-right: 10px;
  }
  .xl-pb-15 {
    padding-bottom: 15px;
  }
  .xl-pl-15 {
    padding-left: 15px;
  }
  .xl-pr-15 {
    padding-right: 15px;
  }
  .xl-mb-15 {
    margin-bottom: 15px;
  }
  .xl-ml-15 {
    margin-left: 15px;
  }
  .xl-mr-15 {
    margin-right: 15px;
  }
  .xl-pb-20 {
    padding-bottom: 20px;
  }
  .xl-pl-20 {
    padding-left: 20px;
  }
  .xl-pr-20 {
    padding-right: 20px;
  }
  .xl-mb-20 {
    margin-bottom: 20px;
  }
  .xl-ml-20 {
    margin-left: 20px;
  }
  .xl-mr-20 {
    margin-right: 20px;
  }
  .xl-pb-25 {
    padding-bottom: 25px;
  }
  .xl-pl-25 {
    padding-left: 25px;
  }
  .xl-pr-25 {
    padding-right: 25px;
  }
  .xl-mb-25 {
    margin-bottom: 25px;
  }
  .xl-ml-25 {
    margin-left: 25px;
  }
  .xl-mr-25 {
    margin-right: 25px;
  }
  .xl-pb-30 {
    padding-bottom: 30px;
  }
  .xl-pl-30 {
    padding-left: 30px;
  }
  .xl-pr-30 {
    padding-right: 30px;
  }
  .xl-mb-30 {
    margin-bottom: 30px;
  }
  .xl-ml-30 {
    margin-left: 30px;
  }
  .xl-mr-30 {
    margin-right: 30px;
  }
  .xl-pb-35 {
    padding-bottom: 35px;
  }
  .xl-pl-35 {
    padding-left: 35px;
  }
  .xl-pr-35 {
    padding-right: 35px;
  }
  .xl-mb-35 {
    margin-bottom: 35px;
  }
  .xl-ml-35 {
    margin-left: 35px;
  }
  .xl-mr-35 {
    margin-right: 35px;
  }
  .xl-pb-40 {
    padding-bottom: 40px;
  }
  .xl-pl-40 {
    padding-left: 40px;
  }
  .xl-pr-40 {
    padding-right: 40px;
  }
  .xl-mb-40 {
    margin-bottom: 40px;
  }
  .xl-ml-40 {
    margin-left: 40px;
  }
  .xl-mr-40 {
    margin-right: 40px;
  }
  .xl-pb-45 {
    padding-bottom: 45px;
  }
  .xl-pl-45 {
    padding-left: 45px;
  }
  .xl-pr-45 {
    padding-right: 45px;
  }
  .xl-mb-45 {
    margin-bottom: 45px;
  }
  .xl-ml-45 {
    margin-left: 45px;
  }
  .xl-mr-45 {
    margin-right: 45px;
  }
  .xl-pb-50 {
    padding-bottom: 50px;
  }
  .xl-pl-50 {
    padding-left: 50px;
  }
  .xl-pr-50 {
    padding-right: 50px;
  }
  .xl-mb-50 {
    margin-bottom: 50px;
  }
  .xl-ml-50 {
    margin-left: 50px;
  }
  .xl-mr-50 {
    margin-right: 50px;
  }
  .xl-pb-55 {
    padding-bottom: 55px;
  }
  .xl-pl-55 {
    padding-left: 55px;
  }
  .xl-pr-55 {
    padding-right: 55px;
  }
  .xl-mb-55 {
    margin-bottom: 55px;
  }
  .xl-ml-55 {
    margin-left: 55px;
  }
  .xl-mr-55 {
    margin-right: 55px;
  }
  .xl-pb-60 {
    padding-bottom: 60px;
  }
  .xl-pl-60 {
    padding-left: 60px;
  }
  .xl-pr-60 {
    padding-right: 60px;
  }
  .xl-mb-60 {
    margin-bottom: 60px;
  }
  .xl-ml-60 {
    margin-left: 60px;
  }
  .xl-mr-60 {
    margin-right: 60px;
  }
  .xl-pb-65 {
    padding-bottom: 65px;
  }
  .xl-pl-65 {
    padding-left: 65px;
  }
  .xl-pr-65 {
    padding-right: 65px;
  }
  .xl-mb-65 {
    margin-bottom: 65px;
  }
  .xl-ml-65 {
    margin-left: 65px;
  }
  .xl-mr-65 {
    margin-right: 65px;
  }
  .xl-pb-70 {
    padding-bottom: 70px;
  }
  .xl-pl-70 {
    padding-left: 70px;
  }
  .xl-pr-70 {
    padding-right: 70px;
  }
  .xl-mb-70 {
    margin-bottom: 70px;
  }
  .xl-ml-70 {
    margin-left: 70px;
  }
  .xl-mr-70 {
    margin-right: 70px;
  }
  .xl-pb-75 {
    padding-bottom: 75px;
  }
  .xl-pl-75 {
    padding-left: 75px;
  }
  .xl-pr-75 {
    padding-right: 75px;
  }
  .xl-mb-75 {
    margin-bottom: 75px;
  }
  .xl-ml-75 {
    margin-left: 75px;
  }
  .xl-mr-75 {
    margin-right: 75px;
  }
  .xl-pb-80 {
    padding-bottom: 80px;
  }
  .xl-pl-80 {
    padding-left: 80px;
  }
  .xl-pr-80 {
    padding-right: 80px;
  }
  .xl-mb-80 {
    margin-bottom: 80px;
  }
  .xl-ml-80 {
    margin-left: 80px;
  }
  .xl-mr-80 {
    margin-right: 80px;
  }
  .xl-pb-85 {
    padding-bottom: 85px;
  }
  .xl-pl-85 {
    padding-left: 85px;
  }
  .xl-pr-85 {
    padding-right: 85px;
  }
  .xl-mb-85 {
    margin-bottom: 85px;
  }
  .xl-ml-85 {
    margin-left: 85px;
  }
  .xl-mr-85 {
    margin-right: 85px;
  }
  .xl-pb-90 {
    padding-bottom: 90px;
  }
  .xl-pl-90 {
    padding-left: 90px;
  }
  .xl-pr-90 {
    padding-right: 90px;
  }
  .xl-mb-90 {
    margin-bottom: 90px;
  }
  .xl-ml-90 {
    margin-left: 90px;
  }
  .xl-mr-90 {
    margin-right: 90px;
  }
  .xl-pb-95 {
    padding-bottom: 95px;
  }
  .xl-pl-95 {
    padding-left: 95px;
  }
  .xl-pr-95 {
    padding-right: 95px;
  }
  .xl-mb-95 {
    margin-bottom: 95px;
  }
  .xl-ml-95 {
    margin-left: 95px;
  }
  .xl-mr-95 {
    margin-right: 95px;
  }
  .xl-t-left {
    text-align: left;
  }
  .xl-t-center {
    text-align: center;
  }
  .xl-t-right {
    text-align: right;
  }
  .xl-display-n {
    display: none !important;
  }
  .xl-display-b {
    display: block !important;
  }
}
@media (max-width: 991px) {
  .lg-mb-0 {
    margin-bottom: 0px;
  }
  .lg-mt-0 {
    margin-top: 0px;
  }
  .lg-ml-0 {
    margin-left: 0px;
  }
  .lg-pt-0 {
    padding-top: 0px;
  }
  .lg-pb-0 {
    padding-bottom: 0px;
  }
  .lg-pl-0 {
    padding-left: 0px;
  }
  .lg-pr-0 {
    padding-right: 0px;
  }
  .lg-mb-5 {
    margin-bottom: 5px;
  }
  .lg-mt-5 {
    margin-top: 5px;
  }
  .lg-ml-5 {
    margin-left: 5px;
  }
  .lg-pt-5 {
    padding-top: 5px;
  }
  .lg-pb-5 {
    padding-bottom: 5px;
  }
  .lg-pl-5 {
    padding-left: 5px;
  }
  .lg-pr-5 {
    padding-right: 5px;
  }
  .lg-mb-10 {
    margin-bottom: 10px;
  }
  .lg-mt-10 {
    margin-top: 10px;
  }
  .lg-ml-10 {
    margin-left: 10px;
  }
  .lg-pt-10 {
    padding-top: 10px;
  }
  .lg-pb-10 {
    padding-bottom: 10px;
  }
  .lg-pl-10 {
    padding-left: 10px;
  }
  .lg-pr-10 {
    padding-right: 10px;
  }
  .lg-mb-15 {
    margin-bottom: 15px;
  }
  .lg-mt-15 {
    margin-top: 15px;
  }
  .lg-ml-15 {
    margin-left: 15px;
  }
  .lg-pt-15 {
    padding-top: 15px;
  }
  .lg-pb-15 {
    padding-bottom: 15px;
  }
  .lg-pl-15 {
    padding-left: 15px;
  }
  .lg-pr-15 {
    padding-right: 15px;
  }
  .lg-mb-20 {
    margin-bottom: 20px;
  }
  .lg-mt-20 {
    margin-top: 20px;
  }
  .lg-ml-20 {
    margin-left: 20px;
  }
  .lg-pt-20 {
    padding-top: 20px;
  }
  .lg-pb-20 {
    padding-bottom: 20px;
  }
  .lg-pl-20 {
    padding-left: 20px;
  }
  .lg-pr-20 {
    padding-right: 20px;
  }
  .lg-mb-25 {
    margin-bottom: 25px;
  }
  .lg-mt-25 {
    margin-top: 25px;
  }
  .lg-ml-25 {
    margin-left: 25px;
  }
  .lg-pt-25 {
    padding-top: 25px;
  }
  .lg-pb-25 {
    padding-bottom: 25px;
  }
  .lg-pl-25 {
    padding-left: 25px;
  }
  .lg-pr-25 {
    padding-right: 25px;
  }
  .lg-mb-30 {
    margin-bottom: 30px;
  }
  .lg-mt-30 {
    margin-top: 30px;
  }
  .lg-ml-30 {
    margin-left: 30px;
  }
  .lg-pt-30 {
    padding-top: 30px;
  }
  .lg-pb-30 {
    padding-bottom: 30px;
  }
  .lg-pl-30 {
    padding-left: 30px;
  }
  .lg-pr-30 {
    padding-right: 30px;
  }
  .lg-mb-35 {
    margin-bottom: 35px;
  }
  .lg-mt-35 {
    margin-top: 35px;
  }
  .lg-ml-35 {
    margin-left: 35px;
  }
  .lg-pt-35 {
    padding-top: 35px;
  }
  .lg-pb-35 {
    padding-bottom: 35px;
  }
  .lg-pl-35 {
    padding-left: 35px;
  }
  .lg-pr-35 {
    padding-right: 35px;
  }
  .lg-mb-40 {
    margin-bottom: 40px;
  }
  .lg-mt-40 {
    margin-top: 40px;
  }
  .lg-ml-40 {
    margin-left: 40px;
  }
  .lg-pt-40 {
    padding-top: 40px;
  }
  .lg-pb-40 {
    padding-bottom: 40px;
  }
  .lg-pl-40 {
    padding-left: 40px;
  }
  .lg-pr-40 {
    padding-right: 40px;
  }
  .lg-mb-45 {
    margin-bottom: 45px;
  }
  .lg-mt-45 {
    margin-top: 45px;
  }
  .lg-ml-45 {
    margin-left: 45px;
  }
  .lg-pt-45 {
    padding-top: 45px;
  }
  .lg-pb-45 {
    padding-bottom: 45px;
  }
  .lg-pl-45 {
    padding-left: 45px;
  }
  .lg-pr-45 {
    padding-right: 45px;
  }
  .lg-mb-50 {
    margin-bottom: 50px;
  }
  .lg-mt-50 {
    margin-top: 50px;
  }
  .lg-ml-50 {
    margin-left: 50px;
  }
  .lg-pt-50 {
    padding-top: 50px;
  }
  .lg-pb-50 {
    padding-bottom: 50px;
  }
  .lg-pl-50 {
    padding-left: 50px;
  }
  .lg-pr-50 {
    padding-right: 50px;
  }
  .lg-mb-55 {
    margin-bottom: 55px;
  }
  .lg-mt-55 {
    margin-top: 55px;
  }
  .lg-ml-55 {
    margin-left: 55px;
  }
  .lg-pt-55 {
    padding-top: 55px;
  }
  .lg-pb-55 {
    padding-bottom: 55px;
  }
  .lg-pl-55 {
    padding-left: 55px;
  }
  .lg-pr-55 {
    padding-right: 55px;
  }
  .lg-mb-60 {
    margin-bottom: 60px;
  }
  .lg-mt-60 {
    margin-top: 60px;
  }
  .lg-ml-60 {
    margin-left: 60px;
  }
  .lg-pt-60 {
    padding-top: 60px;
  }
  .lg-pb-60 {
    padding-bottom: 60px;
  }
  .lg-pl-60 {
    padding-left: 60px;
  }
  .lg-pr-60 {
    padding-right: 60px;
  }
  .lg-mb-65 {
    margin-bottom: 65px;
  }
  .lg-mt-65 {
    margin-top: 65px;
  }
  .lg-ml-65 {
    margin-left: 65px;
  }
  .lg-pt-65 {
    padding-top: 65px;
  }
  .lg-pb-65 {
    padding-bottom: 65px;
  }
  .lg-pl-65 {
    padding-left: 65px;
  }
  .lg-pr-65 {
    padding-right: 65px;
  }
  .lg-mb-70 {
    margin-bottom: 70px;
  }
  .lg-mt-70 {
    margin-top: 70px;
  }
  .lg-ml-70 {
    margin-left: 70px;
  }
  .lg-pt-70 {
    padding-top: 70px;
  }
  .lg-pb-70 {
    padding-bottom: 70px;
  }
  .lg-pl-70 {
    padding-left: 70px;
  }
  .lg-pr-70 {
    padding-right: 70px;
  }
  .lg-t-left {
    text-align: left;
  }
  .lg-t-center {
    text-align: center;
  }
  .lg-t-right {
    text-align: right;
  }
  .lg-display-n {
    display: none !important;
  }
  .lg-display-b {
    display: block !important;
  }
}
@media (max-width: 767px) {
  .md-mb-0 {
    margin-bottom: 0px;
  }
  .md-mt-0 {
    margin-top: 0px;
  }
  .md-pt-0 {
    padding-top: 0px;
  }
  .md-pb-0 {
    padding-bottom: 0px;
  }
  .md-pl-0 {
    padding-left: 0px;
  }
  .md-pr-0 {
    padding-right: 0px;
  }
  .md-mb-5 {
    margin-bottom: 5px;
  }
  .md-mt-5 {
    margin-top: 5px;
  }
  .md-pt-5 {
    padding-top: 5px;
  }
  .md-pb-5 {
    padding-bottom: 5px;
  }
  .md-pl-5 {
    padding-left: 5px;
  }
  .md-pr-5 {
    padding-right: 5px;
  }
  .md-mb-10 {
    margin-bottom: 10px;
  }
  .md-mt-10 {
    margin-top: 10px;
  }
  .md-pt-10 {
    padding-top: 10px;
  }
  .md-pb-10 {
    padding-bottom: 10px;
  }
  .md-pl-10 {
    padding-left: 10px;
  }
  .md-pr-10 {
    padding-right: 10px;
  }
  .md-mb-15 {
    margin-bottom: 15px;
  }
  .md-mt-15 {
    margin-top: 15px;
  }
  .md-pt-15 {
    padding-top: 15px;
  }
  .md-pb-15 {
    padding-bottom: 15px;
  }
  .md-pl-15 {
    padding-left: 15px;
  }
  .md-pr-15 {
    padding-right: 15px;
  }
  .md-mb-20 {
    margin-bottom: 20px;
  }
  .md-mt-20 {
    margin-top: 20px;
  }
  .md-pt-20 {
    padding-top: 20px;
  }
  .md-pb-20 {
    padding-bottom: 20px;
  }
  .md-pl-20 {
    padding-left: 20px;
  }
  .md-pr-20 {
    padding-right: 20px;
  }
  .md-mb-25 {
    margin-bottom: 25px;
  }
  .md-mt-25 {
    margin-top: 25px;
  }
  .md-pt-25 {
    padding-top: 25px;
  }
  .md-pb-25 {
    padding-bottom: 25px;
  }
  .md-pl-25 {
    padding-left: 25px;
  }
  .md-pr-25 {
    padding-right: 25px;
  }
  .md-mb-30 {
    margin-bottom: 30px;
  }
  .md-mt-30 {
    margin-top: 30px;
  }
  .md-pt-30 {
    padding-top: 30px;
  }
  .md-pb-30 {
    padding-bottom: 30px;
  }
  .md-pl-30 {
    padding-left: 30px;
  }
  .md-pr-30 {
    padding-right: 30px;
  }
  .md-mb-35 {
    margin-bottom: 35px;
  }
  .md-mt-35 {
    margin-top: 35px;
  }
  .md-pt-35 {
    padding-top: 35px;
  }
  .md-pb-35 {
    padding-bottom: 35px;
  }
  .md-pl-35 {
    padding-left: 35px;
  }
  .md-pr-35 {
    padding-right: 35px;
  }
  .md-mb-40 {
    margin-bottom: 40px;
  }
  .md-mt-40 {
    margin-top: 40px;
  }
  .md-pt-40 {
    padding-top: 40px;
  }
  .md-pb-40 {
    padding-bottom: 40px;
  }
  .md-pl-40 {
    padding-left: 40px;
  }
  .md-pr-40 {
    padding-right: 40px;
  }
  .md-mb-45 {
    margin-bottom: 45px;
  }
  .md-mt-45 {
    margin-top: 45px;
  }
  .md-pt-45 {
    padding-top: 45px;
  }
  .md-pb-45 {
    padding-bottom: 45px;
  }
  .md-pl-45 {
    padding-left: 45px;
  }
  .md-pr-45 {
    padding-right: 45px;
  }
  .md-t-left {
    text-align: left;
  }
  .md-t-center {
    text-align: center;
  }
  .md-t-right {
    text-align: right;
  }
  .md-display-n {
    display: none !important;
  }
  .md-display-b {
    display: block !important;
  }
}
@media (max-width: 575px) {
  .sm-mb-0 {
    margin-bottom: 0px;
  }
  .sm-mt-0 {
    margin-top: 0px;
  }
  .sm-ml-0 {
    margin-left: 0px;
  }
  .sm-pt-0 {
    padding-top: 0px;
  }
  .sm-pb-0 {
    padding-bottom: 0px;
  }
  .sm-pl-0 {
    padding-left: 0px;
  }
  .sm-pr-0 {
    padding-right: 0px;
  }
  .sm-mb-5 {
    margin-bottom: 5px;
  }
  .sm-mt-5 {
    margin-top: 5px;
  }
  .sm-ml-5 {
    margin-left: 5px;
  }
  .sm-pt-5 {
    padding-top: 5px;
  }
  .sm-pb-5 {
    padding-bottom: 5px;
  }
  .sm-pl-5 {
    padding-left: 5px;
  }
  .sm-pr-5 {
    padding-right: 5px;
  }
  .sm-mb-10 {
    margin-bottom: 10px;
  }
  .sm-mt-10 {
    margin-top: 10px;
  }
  .sm-ml-10 {
    margin-left: 10px;
  }
  .sm-pt-10 {
    padding-top: 10px;
  }
  .sm-pb-10 {
    padding-bottom: 10px;
  }
  .sm-pl-10 {
    padding-left: 10px;
  }
  .sm-pr-10 {
    padding-right: 10px;
  }
  .sm-mb-15 {
    margin-bottom: 15px;
  }
  .sm-mt-15 {
    margin-top: 15px;
  }
  .sm-ml-15 {
    margin-left: 15px;
  }
  .sm-pt-15 {
    padding-top: 15px;
  }
  .sm-pb-15 {
    padding-bottom: 15px;
  }
  .sm-pl-15 {
    padding-left: 15px;
  }
  .sm-pr-15 {
    padding-right: 15px;
  }
  .sm-mb-20 {
    margin-bottom: 20px;
  }
  .sm-mt-20 {
    margin-top: 20px;
  }
  .sm-ml-20 {
    margin-left: 20px;
  }
  .sm-pt-20 {
    padding-top: 20px;
  }
  .sm-pb-20 {
    padding-bottom: 20px;
  }
  .sm-pl-20 {
    padding-left: 20px;
  }
  .sm-pr-20 {
    padding-right: 20px;
  }
  .sm-mb-25 {
    margin-bottom: 25px;
  }
  .sm-mt-25 {
    margin-top: 25px;
  }
  .sm-ml-25 {
    margin-left: 25px;
  }
  .sm-pt-25 {
    padding-top: 25px;
  }
  .sm-pb-25 {
    padding-bottom: 25px;
  }
  .sm-pl-25 {
    padding-left: 25px;
  }
  .sm-pr-25 {
    padding-right: 25px;
  }
  .sm-mb-30 {
    margin-bottom: 30px;
  }
  .sm-mt-30 {
    margin-top: 30px;
  }
  .sm-ml-30 {
    margin-left: 30px;
  }
  .sm-pt-30 {
    padding-top: 30px;
  }
  .sm-pb-30 {
    padding-bottom: 30px;
  }
  .sm-pl-30 {
    padding-left: 30px;
  }
  .sm-pr-30 {
    padding-right: 30px;
  }
  .sm-mb-35 {
    margin-bottom: 35px;
  }
  .sm-mt-35 {
    margin-top: 35px;
  }
  .sm-ml-35 {
    margin-left: 35px;
  }
  .sm-pt-35 {
    padding-top: 35px;
  }
  .sm-pb-35 {
    padding-bottom: 35px;
  }
  .sm-pl-35 {
    padding-left: 35px;
  }
  .sm-pr-35 {
    padding-right: 35px;
  }
  .sm-mb-40 {
    margin-bottom: 40px;
  }
  .sm-mt-40 {
    margin-top: 40px;
  }
  .sm-ml-40 {
    margin-left: 40px;
  }
  .sm-pt-40 {
    padding-top: 40px;
  }
  .sm-pb-40 {
    padding-bottom: 40px;
  }
  .sm-pl-40 {
    padding-left: 40px;
  }
  .sm-pr-40 {
    padding-right: 40px;
  }
  .sm-mb-45 {
    margin-bottom: 45px;
  }
  .sm-mt-45 {
    margin-top: 45px;
  }
  .sm-ml-45 {
    margin-left: 45px;
  }
  .sm-pt-45 {
    padding-top: 45px;
  }
  .sm-pb-45 {
    padding-bottom: 45px;
  }
  .sm-pl-45 {
    padding-left: 45px;
  }
  .sm-pr-45 {
    padding-right: 45px;
  }
  .sm-mb-50 {
    margin-bottom: 50px;
  }
  .sm-mt-50 {
    margin-top: 50px;
  }
  .sm-ml-50 {
    margin-left: 50px;
  }
  .sm-pt-50 {
    padding-top: 50px;
  }
  .sm-pb-50 {
    padding-bottom: 50px;
  }
  .sm-pl-50 {
    padding-left: 50px;
  }
  .sm-pr-50 {
    padding-right: 50px;
  }
  .sm-mb-55 {
    margin-bottom: 55px;
  }
  .sm-mt-55 {
    margin-top: 55px;
  }
  .sm-ml-55 {
    margin-left: 55px;
  }
  .sm-pt-55 {
    padding-top: 55px;
  }
  .sm-pb-55 {
    padding-bottom: 55px;
  }
  .sm-pl-55 {
    padding-left: 55px;
  }
  .sm-pr-55 {
    padding-right: 55px;
  }
  .sm-t-left {
    text-align: left;
  }
  .sm-t-center {
    text-align: center;
  }
  .sm-t-right {
    text-align: right;
  }
  .sm-display-n {
    display: none !important;
  }
  .sm-display-b {
    display: block !important;
  }
}
/*==========================================================================
* Top Bar CSS
==========================================================================*/
.top__bar {
  background: var(--bg-heading-color);
  padding: 13px 0;
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.top__bar-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: start;
}
.top__bar-left a {
  font-size: 18px;
  margin-right: 27px;
  text-transform: lowercase;
  color: var(--text-heading-color);
  color: var(--text-white);
}
.top__bar-left a i {
  font-size: 16px;
  margin-right: 5px;
  color: var(--primary-color-1);
}
.top__bar-right {
  text-align: right;
}
.top__bar-right a {
  font-size: 18px;
  text-transform: capitalize;
  color: var(--text-white);
}
.top__bar-right a i {
  font-size: 16px;
  color: var(--primary-color-1);
  margin-right: 5px;
}
.top__bar.two {
  background: linear-gradient(135deg, var(--primary-color-1) 39%, var(--color-7) 0%);
}
.top__bar.two .top__bar-left {
  justify-content: end;
}
.top__bar.two .top__bar-left span {
  font-size: 18px;
  margin-right: 27px;
  text-transform: capitalize;
  color: var(--text-heading-color);
  color: var(--text-white);
}
.top__bar.two .top__bar-left span i {
  font-size: 16px;
  margin-right: 5px;
}
.top__bar.two .top__bar-left a i {
  color: var(--text-white);
}
.top__bar.two .top__bar-right {
  text-align: left;
}
.top__bar.two .top__bar-right a i {
  color: var(--text-white);
}
.top__bar.four {
  background: var(--primary-color-1);
}
.top__bar.four .top__bar-left span {
  font-size: 18px;
  margin-right: 27px;
  text-transform: capitalize;
  color: var(--text-heading-color);
  color: var(--text-white);
}
.top__bar.four .top__bar-left span i {
  font-size: 16px;
  margin-right: 5px;
}
.top__bar.four .top__bar-left a i {
  color: var(--text-white);
}
.top__bar.four .top__bar-right a i {
  color: var(--text-white);
}

@media (max-width: 991px) {
  .top__bar-left a:last-child {
    margin: 0;
  }
}
@media (max-width: 767px) {
  .top__bar-left {
    justify-content: center;
    margin-bottom: 15px;
  }
  .top__bar-left a {
    font-size: 16px;
  }
  .top__bar-left a i {
    font-size: 14px;
  }
  .top__bar-right {
    text-align: center;
  }
  .top__bar-right a {
    font-size: 16px;
    text-transform: capitalize;
  }
  .top__bar-right a i {
    font-size: 14px;
  }
  .top__bar.two .top__bar-right {
    text-align: center;
  }
  .top__bar.two .top__bar-left {
    justify-content: center;
  }
}
@media (max-width: 480px) {
  .top__bar-left {
    margin-bottom: 5px;
  }
  .top__bar-left a {
    margin-bottom: 3px;
  }
}
/*==========================================================================
* Menu Bar Sticky CSS
==========================================================================*/
.header__sticky-sticky-menu {
  position: fixed !important;
  left: 0;
  top: 0;
  right: 0;
  width: 100%;
  box-shadow: var(--box-shadow-1) !important;
  animation: header_sticky 1.1s;
  background: var(--bg-white);
  display: block;
}
@keyframes header_sticky {
  0% {
    top: -250px;
  }
  100% {
    top: 0;
  }
}

.header__sticky.header__sticky-sticky-menu .header__area {
  background: var(--bg-heading-color);
}

/*==========================================================================
* Menu Bar CSS
==========================================================================*/
.header__area {
  padding: 0 30px;
  position: relative;
  z-index: 999;
  /* =============== Header Two Style =============== */
  /* =============== Header Three Style =============== */
  /* =============== Header Four Style =============== */
  /* =============== Header Five Style =============== */
}
.header__area-menubar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}
.header__area-menubar-left-logo a {
  display: inline-block;
}
.header__area-menubar-left-logo a img {
  max-width: 165px;
  position: relative;
  z-index: 9999;
}
.header__area-menubar-center-menu ul {
  padding: 0;
  margin: 0;
}
.header__area-menubar-center-menu ul li {
  display: inline-block;
  position: relative;
  list-style: none;
}
.header__area-menubar-center-menu ul li:hover > a {
  color: var(--primary-color-1);
}
.header__area-menubar-center-menu ul li:hover > a i {
  transform: rotate(-180deg);
}
.header__area-menubar-center-menu ul li a {
  color: var(--text-heading-color);
  display: block;
  font-size: 18px;
  line-height: 24px;
  transition: all 0.4s ease-out 0s;
  text-transform: capitalize;
  padding: 38px 10px;
  font-family: var(--heading-font);
}
.header__area-menubar-center-menu ul li:hover > .sub-menu {
  transform: scale(1, 1);
  opacity: 1;
  visibility: visible;
}
.header__area-menubar-center-menu ul li .sub-menu {
  position: absolute;
  background: var(--bg-white);
  min-width: 240px;
  transition: all 0.3s ease-out 0s;
  top: 100%;
  opacity: 0;
  box-shadow: var(--box-shadow-1);
  visibility: hidden;
  z-index: 99;
  text-align: left;
  transform: scale(1, 0);
  transform-origin: 0 0;
}
.header__area-menubar-center-menu ul li .sub-menu li {
  display: block;
  margin: 0;
  border-bottom: 1px solid var(--border-color-2);
  position: relative;
}
.header__area-menubar-center-menu ul li .sub-menu li::after {
  position: absolute;
  content: "";
  background: var(--primary-color-1);
  width: 2px;
  transition: 0.4s;
  height: 100%;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
}
.header__area-menubar-center-menu ul li .sub-menu li a {
  color: var(--text-heading-color) !important;
  padding: 12px 20px;
  transition: all 0.4s ease-out 0s;
  font-size: 16px;
  text-transform: capitalize;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header__area-menubar-center-menu ul li .sub-menu li .sub-menu {
  left: 100%;
  top: -2px;
}
.header__area-menubar-center-menu ul li .sub-menu li:hover > a {
  color: var(--primary-color-1) !important;
  padding-left: 25px;
}
.header__area-menubar-center-menu ul li .sub-menu li:hover::after {
  opacity: 1;
}
.header__area-menubar-center-menu ul li .sub-menu li:last-child {
  border: none;
}
.header__area-menubar-center-menu ul li ul .sub-menu li .sub-menu {
  color: var(--text-heading-color);
  cursor: pointer;
}
.header__area-menubar-center-menu ul li.menu-item-has-children > a {
  display: flex;
  align-items: end;
}
.header__area-menubar-center-menu ul li.menu-item-has-children > a i {
  font-size: 14px;
  margin-left: 7px;
  font-weight: 500;
  margin-bottom: 2px;
  transition: 0.3s;
}
.header__area-menubar-right {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header__area-menubar-right-box {
  display: flex;
  align-items: center;
}
.header__area-menubar-right-box-search-icon i {
  cursor: pointer;
  position: relative;
  z-index: 9;
  display: block;
  color: var(--text-heading-color);
  font-size: 20px;
  font-weight: 400;
  border-right: 1px solid var(--color-4);
  padding: 12px 25px 12px 0;
  font-weight: 700;
}
.header__area-menubar-right-box-search-box {
  position: fixed;
  bottom: 0;
  width: 100%;
  left: 0;
  right: 0;
  height: 0;
  background: var(--bg-heading-color);
  z-index: 9999;
  transition: all 0.5s ease-out;
  overflow: hidden;
}
.header__area-menubar-right-box-search-box form {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  width: 55%;
  transition: all 0.5s ease-out;
}
.header__area-menubar-right-box-search-box input {
  background: var(--bg-white);
  color: var(--text-heading-color);
  border: 0;
}
.header__area-menubar-right-box-search-box button {
  position: absolute;
  right: 0;
  top: 0;
  background-color: transparent;
  font-size: 22px;
  color: var(--primary-color-1);
  padding: 0;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
}
.header__area-menubar-right-box-search-box.active {
  height: 100%;
  top: 0;
}
.header__area-menubar-right-box-search-box.active.header__area-menubar-right-box-search-box form {
  transform: translate(-50%, -50%) scale(1);
}
.header__area-menubar-right-box-search-box-icon {
  position: absolute;
  right: 50px;
  top: 50px;
  font-size: 22px;
  color: var(--text-white);
  cursor: pointer;
  transform: rotate(0deg);
}
.header__area-menubar-right-box-search-box-icon:hover {
  animation: rotate 0.4s ease 0s;
}
.header__area-menubar-right-box-search-box-icon i {
  cursor: pointer;
  position: relative;
  z-index: 9;
}
.header__area-menubar-right-box-search-box-icon i::before {
  display: block;
}
.header__area-menubar-right-box-sidebar-popup-icon {
  cursor: pointer;
  text-align: right;
  text-align: -webkit-right;
  text-align: -moz-right;
  margin-left: 25px;
}
.header__area-menubar-right-box-sidebar-popup-icon:hover span {
  background-color: var(--primary-color-1);
  width: 30px;
}
.header__area-menubar-right-box-sidebar-popup-icon span {
  height: 3px;
  background-color: var(--text-heading-color);
  display: block;
  margin: 6px 0;
  transition: 0.4s;
}
.header__area-menubar-right-box-sidebar-popup-icon .bar-1 {
  width: 20px;
  margin-top: 0;
}
.header__area-menubar-right-box-sidebar-popup-icon .bar-2 {
  width: 30px;
}
.header__area-menubar-right-box-sidebar-popup-icon .bar-3 {
  width: 25px;
  margin-bottom: 0;
}
.header__area-menubar-right-box-btn {
  margin-left: 30px;
}
.header__area-menubar-right-sidebar-popup {
  position: fixed;
  width: 460px;
  height: 100%;
  right: 0;
  overflow: auto;
  transform: translateX(100%);
  top: 0;
  background: var(--color-5);
  opacity: 0;
  visibility: hidden;
  z-index: 999999;
  transition: 0.5s;
  padding: 100px 40px;
  scrollbar-width: none;
}
.header__area-menubar-right-sidebar-popup::-webkit-scrollbar {
  display: none;
}
.header__area-menubar-right-sidebar-popup.active {
  opacity: 1;
  visibility: visible;
  transform: translateX(0%);
  right: 0;
}
.header__area-menubar-right-sidebar-popup .sidebar-close-btn {
  position: absolute;
  top: 40px;
  right: 40px;
  transform: rotate(0);
}
.header__area-menubar-right-sidebar-popup .sidebar-close-btn i::before {
  background: var(--primary-color-1);
  width: 40px;
  color: var(--text-white);
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  cursor: pointer;
  display: block;
}
.header__area-menubar-right-sidebar-popup .sidebar-close-btn:hover {
  animation: rotate 0.4s ease 0s;
}
.header__area-menubar-right-sidebar-popup-logo {
  margin-bottom: 30px;
}
.header__area-menubar-right-sidebar-popup-logo a {
  display: inline-block;
}
.header__area-menubar-right-sidebar-popup-logo a img {
  max-width: 150px;
  position: relative;
  z-index: 999;
}
.header__area-menubar-right-sidebar-popup p {
  color: var(--color-4);
}
.header__area-menubar-right-sidebar-popup-contact {
  margin: 40px 0;
  padding: 40px 0;
  border-top: 1px solid var(--color-19);
  border-bottom: 1px solid var(--color-19);
}
.header__area-menubar-right-sidebar-popup-contact-item {
  display: flex;
  margin-bottom: 25px;
  gap: 25px;
}
.header__area-menubar-right-sidebar-popup-contact-item-icon {
  margin-top: 8px;
  width: 30px;
}
.header__area-menubar-right-sidebar-popup-contact-item-icon i {
  color: var(--primary-color-1);
  font-size: 30px;
}
.header__area-menubar-right-sidebar-popup-contact-item-content span {
  color: var(--color-4);
  display: inline-block;
  margin-bottom: 5px;
}
.header__area-menubar-right-sidebar-popup-contact-item-content h6 {
  max-width: 240px;
  font-size: 18px;
  line-height: 28px;
}
.header__area-menubar-right-sidebar-popup-contact-item-content h6 a {
  transition: all 0.4s ease-out;
  color: var(--text-white);
}
.header__area-menubar-right-sidebar-popup-contact-item-content h6 a:hover {
  color: var(--primary-color-1);
}
.header__area-menubar-right-sidebar-popup-contact-item:last-child {
  margin: 0;
}
.header__area-menubar-right-sidebar-popup-contact h4 {
  color: var(--text-white);
}
.header__area-menubar-right-sidebar-popup-social ul {
  padding: 0;
  margin: 0;
}
.header__area-menubar-right-sidebar-popup-social ul li {
  list-style: none;
  display: inline-block;
  margin-right: 10px;
}
.header__area-menubar-right-sidebar-popup-social ul li:last-child {
  margin: 0;
}
.header__area-menubar-right-sidebar-popup-social ul li a i {
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 10px;
  color: var(--primary-color-1);
  transition: all 0.4s ease-out;
  background: var(--color-1);
}
.header__area-menubar-right-sidebar-popup-social ul li a i:hover {
  color: var(--text-white);
  background: var(--primary-color-1);
}
.header__area.two {
  box-shadow: none;
}
.header__area.two .header__area-menubar-right-box-help {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 30px;
}
.header__area.two .header__area-menubar-right-box-help i {
  font-size: 35px;
  background: var(--primary-color-1);
  color: var(--text-white);
  text-align: center;
  width: 55px;
  height: 55px;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.header__area.two .header__area-menubar-right-box-help-info {
  margin-left: 15px;
}
.header__area.two .header__area-menubar-right-box-help-info > * {
  display: block;
  font-size: 18px;
}
.header__area.two .header__area-menubar-right-box-help-info span {
  color: var(--primary-color-1);
  font-size: 16px;
  text-transform: none;
}
.header__area.two .header__area-menubar-right-box-help-info span i {
  margin-right: 5px;
}
.header__area.two .header__area-menubar-right-box-help-info a {
  font-weight: 700;
}
.header__area.three {
  padding: 10px 30px;
}
.header__area.three .header__area-menubar-right-box-btn {
  margin-left: 0;
}
.header__area.four .header__area-menubar-right-box-search-icon i {
  border: 0;
  padding: 0;
}
.header__area.four .btn-two {
  background: var(--color-5);
  border: 1px solid var(--color-5);
  margin-right: 25px;
}
.header__area.four .btn-two:hover {
  background: transparent;
}
.header__area.five {
  position: absolute;
  left: 0;
  right: 0;
  box-shadow: none;
  border-bottom: 1px solid rgba(5, 22, 52, 0.1607843137);
}

@media (max-width: 1500px) {
  .header__area.two .header__area-menubar-right-box-social {
    display: none;
  }
}
@media (max-width: 1255px) {
  .header__area.two .header__area-menubar-right-box-help {
    display: none;
  }
}
@media (max-width: 1050px) {
  .header__area.two .header__area-menubar-center-menu ul li a {
    font-size: 17px;
    padding: 38px 7px;
  }
}
@media (max-width: 1399px) {
  .header__area.three .header__area-menubar-right-box-btn {
    display: block;
  }
}
@media (max-width: 1199px) {
  .header__area.three .header__area-menubar-right-box-btn {
    display: none;
  }
}
/*==========================================================================
* Menu Sidebar CSS
==========================================================================*/
.sidebar-overlay {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  transition: all 0.4s ease-out;
  opacity: 0;
  visibility: hidden;
  z-index: 99999;
  background: rgba(24, 24, 24, 0.6);
}
.sidebar-overlay.show {
  visibility: visible;
  opacity: 1;
}

@media (max-width: 1399px) {
  .header__area-menubar-right-box-btn {
    display: none;
  }
  .header__area-menubar-center-menu ul li a {
    font-size: 17px;
    padding: 38px 7px;
  }
}
@media (max-width: 991px) {
  .header__area {
    padding: 10px 0;
  }
  .header__area-menubar-right .responsive-menu .mean-bar {
    right: 0;
    position: absolute;
    top: 10px;
    background: transparent;
    padding: 0;
    z-index: 99;
  }
  .header__area-menubar-right .responsive-menu .mean-bar span {
    background: #000;
  }
  .header__area-menubar-right-box-sidebar {
    display: none;
  }
  .header__area-menubar-right-box-search-icon i {
    border: 0;
    padding: 0;
  }
  .header__area-menubar-right-box-search .search {
    margin-right: 58px;
    margin-top: 6px;
    z-index: 9999;
    position: relative;
  }
}
@media (max-width: 359px) {
  .header__area-menubar-right-box-search .search {
    display: none;
  }
}
/*==========================================================================
* Banner One CSS
==========================================================================*/
.banner__one {
  padding: 95px 0;
  background-image: linear-gradient(75deg, #fefefe 52%, var(--color-1) 48%);
  position: relative;
  overflow: hidden;
}
.banner__one .banner-shape .shape {
  position: absolute;
}
.banner__one .banner-shape-1 {
  width: 86px;
  height: 115px;
  background-color: var(--primary-color-1);
  top: 0;
  left: 46%;
  transform: skew(18deg);
}
.banner__one .banner-shape-2 {
  width: 28px;
  height: 185px;
  background-color: var(--primary-color-1);
  bottom: 0;
  left: 52.5%;
  transform: skew(15deg);
}
.banner__one .banner-shape-3 {
  width: 300px;
  height: 300px;
  background-color: transparent;
  border: 4px solid rgba(5, 22, 52, 0.1607843137);
  border-radius: 50%;
  bottom: -21%;
  left: 49%;
}
.banner__one .banner-shape-4 {
  width: 420px;
  height: 370px;
  background-color: var(--primary-color-1);
  filter: blur(195px);
  border-radius: 50%;
  left: 25px;
  bottom: -180px;
}
.banner__one .banner-shape-5 {
  top: 0;
  right: 0;
}
.banner__one .banner-shape-6 {
  width: 300px;
  height: 340px;
  background-color: transparent;
  border: 4px solid var(--bg-white);
  border-radius: 50%;
  top: 8%;
  right: -12%;
}
.banner__one .banner-shape-7 {
  width: 60px;
  height: 280px;
  background-color: #4b83f4;
  top: 8%;
  right: -1%;
  transform: skew(19deg);
}
.banner__one-content {
  z-index: 3;
  position: relative;
}
.banner__one-content h2 {
  font-size: 72px;
  line-height: 83px;
  margin-bottom: 20px;
  text-transform: none;
}
.banner__one-content h2 span {
  color: var(--primary-color-1);
}
.banner__one-content p {
  font-size: 18px;
  text-transform: none;
  margin-bottom: 40px;
  width: 93%;
}
.banner__one-image-wrapper {
  position: relative;
  border-radius: 50%;
}
.banner__one-image-wrapper-shapes {
  width: 100%;
  height: 100%;
  position: absolute;
  border-radius: 50%;
  z-index: 4;
}
.banner__one-image-wrapper-shapes .shape {
  position: absolute;
}
.banner__one-image-wrapper-shapes .shape-1 {
  width: 35px;
  height: 35px;
  background-color: var(--bg-white);
  border-radius: 50%;
  outline: 8px solid var(--primary-color-1);
  top: 14%;
  left: 7%;
}
.banner__one-image-wrapper-shapes .shape-2 {
  width: 45px;
  height: 45px;
  background-color: var(--bg-white);
  border-radius: 50%;
  outline: 10px solid var(--primary-color-1);
  bottom: 15%;
  right: 20px;
}
.banner__one-image-wrapper img {
  z-index: 3;
  position: relative;
  border-radius: 50%;
  outline: 20px solid var(--bg-white);
}

@media (max-width: 1399px) {
  .banner__one-content p {
    font-size: 16px;
  }
}
@media (max-width: 991px) {
  .banner__one-content {
    margin-bottom: 30px;
  }
  .banner__one-content h2 {
    max-width: 585px;
  }
}
@media (max-width: 767px) {
  .banner__one-content h2 {
    font-size: 60px;
  }
}
@media (max-width: 535px) {
  .banner__one-content h2 {
    font-size: 51px;
    line-height: 66px;
  }
}
@media (max-width: 575px) {
  .banner__one-image-wrapper-shapes {
    display: none;
  }
}
@media (max-width: 480px) {
  .banner__one .banner-shape-2 {
    display: none;
  }
  .banner__one .banner-shape-5 {
    display: none;
  }
  .banner__one .banner-shape-6 {
    display: none;
  }
  .banner__one .banner-shape-7 {
    display: none;
  }
  .banner__one-content {
    margin-bottom: 0;
  }
  .banner__one-content p {
    font-size: 15px;
  }
  .banner__one-image {
    display: none;
  }
}
/*==========================================================================
* Banner Two CSS
==========================================================================*/
.banner__two-single-slider {
  padding: 155px 0;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  background-position: center center;
}
.banner__two .banner-two-shape .shape {
  position: absolute;
}
.banner__two .banner-two-shape-1 {
  bottom: 12%;
  right: 23%;
  z-index: 2;
  position: absolute;
}
.banner__two .banner-two-shape-1 svg {
  position: absolute;
  left: 0;
  fill: var(--primary-color-1);
  width: 50px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.banner__two .banner-two-shape-2 {
  top: 215px;
  left: 45%;
  width: 150px;
}
.banner__two .banner-two-shape-3 {
  top: 0px;
  left: 46%;
}
.banner__two .banner-two-shape-4 {
  right: 22%;
  bottom: 0;
}
.banner__two-content {
  z-index: 3;
  position: relative;
}
.banner__two-content h2 {
  font-size: 74px;
  color: var(--text-white);
  font-weight: 700;
  line-height: 1.13;
  margin-bottom: 25px;
}
.banner__two-content h2 .highlighted::before {
  content: "";
  width: 111%;
  height: 4px;
  bottom: 14%;
  left: 0;
  border-radius: 3px;
}
.banner__two-content p {
  color: var(--text-white);
  text-transform: none;
  margin-bottom: 42px;
  opacity: 0.7;
}
.banner__two-content .btn-two:hover {
  color: var(--text-white);
}
.banner__two-content .btn-two:hover i {
  color: var(--text-white);
}

@media (max-width: 1399px) {
  .banner__two-content h2 {
    font-size: 68px;
  }
}
@media (max-width: 1199px) {
  .banner-two-shape-2 {
    display: none;
  }
  .banner-two-shape-3 {
    display: none;
  }
  .banner-two-shape-4 {
    display: none;
  }
}
@media (max-width: 767px) {
  .banner-two-shape-1 {
    display: none;
  }
  .banner__two-single-slider {
    padding: 100px 0;
    background-color: var(--color-7);
    background-image: none !important;
  }
  .banner__two-content h2 {
    font-size: 58px;
  }
}
@media (max-width: 575px) {
  .banner__two {
    background-image: none;
    background-color: var(--color-7);
  }
}
@media (max-width: 480px) {
  .banner__two-content h2 {
    font-size: 45px;
  }
}
/*==========================================================================
* Banner Three CSS
==========================================================================*/
.banner__three-single-slide {
  padding: 180px 0;
  background-repeat: no-repeat;
  background-position: center center;
  position: relative;
  overflow: hidden;
  background-size: cover;
}
.banner__three-single-slide::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: var(--text-heading-color);
  z-index: 1;
  opacity: 0.8;
}
.banner__three-bg-shape-overlay {
  width: 100%;
  height: 100%;
  z-index: 3;
  position: absolute;
  top: 0;
  left: 0;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.banner__three-content {
  z-index: 3;
  position: relative;
  text-align: center;
}
.banner__three-content h2 {
  font-size: 74px;
  color: var(--text-white);
  font-weight: 700;
  line-height: 1.15;
  margin-bottom: 25px;
}
.banner__three-content h2 .text-bordered {
  color: transparent;
  -webkit-text-stroke-width: 0.5px;
  -webkit-text-stroke-color: var(--text-white);
}
.banner__three-content p {
  color: var(--text-white);
  text-transform: none;
  margin: auto;
  margin-bottom: 42px;
  width: 61%;
}
.banner__three-content .btn-two:hover {
  color: var(--text-white);
}
.banner__three-content .btn-two:hover i {
  color: var(--text-white);
}

@media (max-width: 1399px) {
  .banner__three-content h2 {
    font-size: 71px;
  }
}
@media (max-width: 991px) {
  .banner__three {
    padding: 131px 0;
  }
  .banner__three-content h2 {
    font-size: 68px;
  }
  .banner__three-content p {
    width: auto;
  }
}
@media (max-width: 767px) {
  .banner__three-content h2 {
    font-size: 50px;
  }
}
@media (max-width: 359px) {
  .banner__three-content h2 {
    font-size: 43px;
  }
}
/*==========================================================================
* Banner Four CSS
==========================================================================*/
.banner__four {
  padding: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  overflow: hidden;
}
.banner__four-content {
  z-index: 3;
  position: relative;
}
.banner__four-content h2 {
  font-size: 72px;
  line-height: 1.13;
  margin-bottom: 20px;
  text-transform: none;
  color: var(--text-white);
}
.banner__four-content p {
  font-size: 18px;
  text-transform: none;
  margin-bottom: 40px;
  color: var(--text-white);
}
.banner__four-content a:hover {
  color: var(--text-white);
}
.banner__four-content a:hover i {
  color: var(--text-white);
}
.banner__four-content-bottom {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  row-gap: 20px;
  -moz-column-gap: 30px;
       column-gap: 30px;
}
.banner__four-content-call {
  display: flex;
  align-items: center;
}
.banner__four-content-call i {
  width: 50px;
  height: 50px;
  background-color: var(--bg-white);
  font-size: 30px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  color: var(--primary-color-1);
  margin-right: 15px;
}
.banner__four-content-call-right span {
  display: block;
  color: var(--text-white);
}
.banner__four-content-call-right a {
  color: var(--text-white);
  font-size: 18px;
  transition: 0.4s;
  font-weight: 500;
}
.banner__four-content-call-right a:hover {
  color: var(--primary-color-1);
}
.banner__four-image {
  padding-left: 73px;
}
.banner__four-image-wrapper img {
  z-index: 3;
}

@media (max-width: 991px) {
  .banner__four {
    padding: 80px 0;
    padding-bottom: 0;
  }
}
@media (max-width: 575px) {
  .banner__four-content h2 {
    font-size: 60px;
  }
}
@media (max-width: 480px) {
  .banner__four-content h2 {
    font-size: 50px;
  }
}
@media (max-width: 359px) {
  .banner__four-image-wrapper img {
    display: none;
  }
  .banner__four {
    padding: 50px 0;
    padding-bottom: 20px;
  }
  .banner__four-content h2 {
    font-size: 45px;
  }
}
/*==========================================================================
* Banner Five CSS
==========================================================================*/
.banner__five {
  padding: 165px 0px 135px 0px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  overflow: hidden;
}
.banner__five-content {
  z-index: 3;
  position: relative;
}
.banner__five-content h2 {
  font-size: 72px;
  line-height: 1.15;
  margin-bottom: 20px;
  text-transform: none;
}
.banner__five-content p {
  font-size: 18px;
  text-transform: none;
  margin-bottom: 40px;
  width: 83%;
}
.banner__five-content-bottom {
  display: flex;
  align-items: center;
}
.banner__five-content-video-btn {
  margin-left: 30px;
  display: flex;
  align-items: center;
}
.banner__five-content-video-btn i {
  width: 58px;
  height: 58px;
  background-color: var(--primary-color-1);
  font-size: 18px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  color: var(--text-white);
  margin-right: 15px;
}
.banner__five-image {
  padding-left: 60px;
}
.banner__five-image-wrapper {
  position: relative;
}
.banner__five-image-wrapper img {
  z-index: 3;
}
.banner__five-image-wrapper .img-2 {
  position: absolute;
  left: -50px;
  top: 65px;
}

@media (max-width: 1399px) {
  .banner__five-content h2 {
    font-size: 68px;
  }
}
@media (max-width: 767px) {
  .banner__five-content h2 {
    font-size: 50px;
  }
}
@media (max-width: 359px) {
  .banner__three-content h2 {
    font-size: 40px;
  }
}
/*==========================================================================
* Page Banner CSS
==========================================================================*/
.page__banner {
  background-color: var(--bg-heading-color);
  position: relative;
  overflow: hidden;
}
.page__banner-shape {
  position: absolute;
  right: 0;
  z-index: 1;
}
.page__banner-content {
  z-index: 3;
  position: relative;
}
.page__banner-content h2 {
  color: var(--text-white);
  margin-bottom: 25px;
  font-size: 64px;
  line-height: 1.13;
}
.page__banner-content span {
  font-size: 18px;
  color: var(--primary-color-1);
}
.page__banner-content span a {
  color: var(--text-white);
  transition: 0.4s;
}
.page__banner-content span a:hover {
  color: var(--primary-color-1);
}
.page__banner-content span span {
  margin: 0 8px;
}
.page__banner-img {
  z-index: 2;
  position: relative;
}

@media (max-width: 991px) {
  .page__banner-content {
    padding: 85px 0;
  }
  .page__banner-img {
    display: none;
  }
}
@media (max-width: 767px) {
  .page__banner-content h2 {
    margin-bottom: 20px;
    font-size: 64px;
    line-height: 80px;
  }
}
@media (max-width: 535px) {
  .page__banner-content h2 {
    margin-bottom: 18px;
    font-size: 40px;
    line-height: 60px;
  }
  .page__banner-content {
    padding: 60px 0;
  }
  .page__banner-shape {
    display: none;
  }
}
/*==========================================================================
* About Area One CSS
==========================================================================*/
.about__one-image {
  position: relative;
}
.about__one-image .experience-bar {
  display: flex;
  align-items: center;
  background-color: var(--primary-color-1);
  position: absolute;
  padding: 25px 50px 25px 25px;
  border-radius: 10px;
  right: 110px;
  top: 80px;
}
.about__one-image .experience-bar i {
  color: var(--text-white);
  font-size: 70px;
  margin-right: 30px;
}
.about__one-image .experience-bar span {
  font-size: 18px;
  color: var(--text-white);
  font-weight: 500;
}
.about__one-image .experience-bar-counter {
  display: flex;
  align-items: center;
  color: var(--text-white);
}
.about__one-image .experience-bar-counter h4 {
  font-size: 45px;
  color: var(--text-white);
  line-height: 1.2;
}
.about__one-image .experience-bar-counter span {
  font-size: 45px;
  font-weight: 700;
}
.about__one-image img {
  border-radius: 10px;
}
.about__one-image .image-1 {
  width: 370px;
  height: 485px;
}
.about__one-image .image-2 {
  margin-left: 45%;
  margin-top: -40%;
  border: 10px solid var(--bg-white);
}
.about__one-content h2 {
  text-transform: none;
  margin-bottom: 30px;
}
.about__one-content p {
  color: var(--color-2);
  margin-bottom: 30px;
}
.about__one-content-service {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 30px;
}
.about__one-content-service .service {
  margin-bottom: 20px;
}
.about__one-content-service .service span {
  font-size: 18px;
  color: var(--color-2);
}
.about__one-content-service .service i {
  color: var(--color-3);
  margin-right: 10px;
  font-weight: 300;
}

@media (max-width: 991px) {
  .about__one-content-service {
    display: block;
  }
}
@media (max-width: 575px) {
  .about__one-image .experience-bar {
    right: 35px;
    top: 145px;
  }
  .about__one-image .image-2 {
    margin-left: 25%;
    margin-top: -51%;
  }
}
@media (max-width: 480px) {
  .about__one-image-shapes {
    display: none;
  }
  .about__one-content p {
    font-size: 15px;
  }
  .about__one-image .image-2 {
    display: none;
  }
  .about__one-image .image-1 {
    width: 100%;
  }
  .about__one-image .experience-bar {
    right: 0;
    top: 32px;
  }
}
/*==========================================================================
* About Area Two CSS
==========================================================================*/
.about__two-content {
  padding-left: 54px;
}
.about__two-content h2 {
  text-transform: none;
  margin-bottom: 17px;
}
.about__two-content p {
  color: var(--color-2);
  margin-bottom: 30px;
  text-transform: none;
}
.about__two-content-service {
  display: block;
  margin-bottom: 40px;
}
.about__two-content-service .service {
  margin-bottom: 13px;
}
.about__two-content-service .service span {
  font-size: 18px;
  color: var(--color-2);
}
.about__two-content-service .service i {
  color: var(--primary-color-1);
  margin-right: 10px;
}
.about__two-left-right-image {
  border-radius: 0 10px 10px 0;
  overflow: hidden;
  margin-bottom: 30px;
  height: 214px;
}
.about__two-left-right-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.about__two-left-progressbar {
  background-color: var(--color-1);
  width: 100%;
  height: 230px;
  border-radius: 0 10px 10px 0;
  padding: 18px;
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 10px;
}
.about__two-left-progressbar-wrapper {
  width: 100%;
  height: 100%;
  border: 2px dashed var(--primary-color-1);
  border-radius: 0 10px 10px 0;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 10px;
}
.about__two-left-progressbar-value {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: conic-gradient(var(--primary-color-1) 270deg, var(--color-4) 0deg);
  display: flex;
  justify-content: center;
  align-items: center;
}
.about__two-left-progressbar-value span {
  position: relative;
  font-size: 20px;
  color: var(--color-2);
}
.about__two-left-progressbar-value::before {
  content: "";
  width: 88px;
  height: 88px;
  background-color: var(--color-1);
  border-radius: 50%;
  position: absolute;
}
.about__two-left-progressbar-title {
  font-size: 18px;
  color: var(--color-2);
  font-weight: 400;
}
.about__two-left-image-left-side {
  border-radius: 40px 0 0 40px;
  overflow: hidden;
}

@media (max-width: 1199px) {
  .about__two-left-progressbar-value {
    width: 80px;
    height: 80px;
  }
  .about__two-left-progressbar-value::before {
    width: 70px;
    height: 70px;
  }
}
@media (max-width: 992px) {
  .about__two-content {
    padding-left: 0;
  }
}
@media (max-width: 575px) {
  .about__two-right-progressbar {
    width: 60%;
    margin-top: -115px;
    margin-left: 170px;
    margin-bottom: 30px;
    position: relative;
    z-index: 3;
  }
  .about__two-left-image-left-side {
    margin-bottom: 20px;
  }
  .about__two-right-image-right-side {
    display: none;
  }
}
@media (max-width: 480px) {
  .about__two-right-left-image {
    margin: auto;
    margin-bottom: 30px;
    text-align: center;
    width: 100%;
  }
  .about__two-right-left-image img {
    width: 100%;
  }
  .about__two-right-progressbar {
    margin: 0;
    margin: auto;
    width: 100%;
  }
}
/*==========================================================================
* About Area Three CSS
==========================================================================*/
.about__three-content h2 {
  text-transform: none;
  margin-bottom: 17px;
}
.about__three-content p {
  color: var(--color-2);
  margin-bottom: 30px;
  text-transform: none;
}
.about__three-content-service-single {
  display: flex;
  justify-content: space-between;
  box-shadow: 0px 0px 50px 0px rgba(0, 0, 0, 0.06);
  align-items: center;
  margin-bottom: 20px;
  padding: 22px 30px;
  border-radius: 10px;
}
.about__three-content-service-single i {
  margin-right: 18px;
  background-color: var(--primary-color-1);
  border-radius: 50%;
  font-size: 42px;
  padding: 17px 17px;
  color: var(--text-white);
}
.about__three-content-service-single h4 {
  font-size: 22px;
  margin-bottom: 10px;
}
.about__three-content-service-single p {
  margin: 0;
}
.about__three-content-service-single:last-child {
  margin-bottom: 0;
}
.about__three-right {
  position: relative;
}
.about__three-right-counter {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 258px;
  background-color: var(--primary-color-1);
  padding: 24px 25px;
  border-radius: 10px;
}
.about__three-right-counter h4 {
  color: var(--text-white);
  font-size: 60px;
  margin-right: 6px;
  width: 58%;
}
.about__three-right-counter span {
  color: var(--text-white);
  font-size: 18px;
  opacity: 0.7;
}
.about__three-right-image-left-side {
  border-radius: 30px 0 0 30px;
  overflow: hidden;
  height: 600px;
}
.about__three-right-image-left-side img {
  width: 100%;
  height: 100%;
}
.about__three-right-image {
  margin-bottom: 30px;
  border-radius: 0px 30px 0px 0px;
  overflow: hidden;
  height: 285px;
}
.about__three-right-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.about__three-right-image:last-child {
  margin-bottom: 0;
  border-radius: 0px 0px 30px 0px;
}

@media (max-width: 480px) {
  .about__three-content-service-single {
    flex-direction: column;
    text-align: center;
  }
  .about__three-content-service-single i {
    margin-bottom: 10px;
  }
}
@media (max-width: 575px) {
  .about__three-right-image-left-side {
    display: none;
  }
  .about__three-right-image {
    width: 100%;
  }
  .about__three-right-image img {
    width: 100%;
  }
}
/*==========================================================================
* About Area Four CSS
==========================================================================*/
.about__four {
  background-color: var(--color-1);
}
.about__four-image {
  position: relative;
}
.about__four-image .experience-bar {
  background-color: var(--primary-color-1);
  position: absolute;
  padding: 30px 40px 30px 40px;
  top: 200px;
  right: 160px;
  border-radius: 15px;
}
.about__four-image .experience-bar span {
  font-size: 20px;
  color: var(--text-white);
  font-weight: 500;
}
.about__four-image .experience-bar-counter {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-white);
}
.about__four-image .experience-bar-counter h4 {
  font-size: 52px;
  color: var(--text-white);
  line-height: 1.3;
}
.about__four-image .experience-bar-counter span {
  font-size: 52px;
  font-weight: 700;
}
.about__four-image img {
  border-radius: 10px;
}
.about__four-image .image-1 {
  width: 440px;
  height: 470px;
}
.about__four-image .image-2 {
  margin-left: 51%;
  margin-top: -30%;
  border: 10px solid var(--bg-white);
  border-radius: 20px;
  width: 285px;
}
.about__four-content h2 {
  text-transform: none;
  margin-bottom: 30px;
}
.about__four-content p {
  color: var(--color-2);
  margin-bottom: 30px;
}
.about__four-content-service {
  margin-bottom: 30px;
}
.about__four-content-service .service {
  margin-bottom: 12px;
}
.about__four-content-service .service span {
  font-size: 18px;
}
.about__four-content-service .service i {
  color: var(--color-3);
  margin-right: 5px;
}
.about__four-content a {
  border-color: var(--color-4);
}

@media (max-width: 575px) {
  .about__four-image .image-2 {
    margin-left: 0;
  }
  .about__four-image .experience-bar {
    padding: 15px 25px 18px 25px;
    left: 28px;
    width: -moz-fit-content;
    width: fit-content;
  }
}
/*==========================================================================
* About Area Five CSS
==========================================================================*/
.about__five {
  background-repeat: no-repeat;
  background-position: center top;
  background-size: contain;
}
.about__five-image {
  position: relative;
}
.about__five-image-wrapper {
  padding: 0 35px;
}
.about__five-image .image-2 {
  position: absolute;
  width: 135px;
  right: 158px;
  top: 90px;
}
.about__five-content h2 {
  text-transform: none;
  margin-bottom: 30px;
}
.about__five-content p {
  color: var(--color-2);
  margin-bottom: 30px;
}
.about__five-content-service {
  margin-bottom: 30px;
}
.about__five-content-service .single-service {
  margin-bottom: 12px;
  display: flex;
  align-items: baseline;
}
.about__five-content-service .single-service span {
  font-size: 18px;
}
.about__five-content-service .single-service i {
  color: var(--color-3);
  margin-right: 15px;
  font-size: 22px;
}

@media (max-width: 767px) {
  .about__five-image .image-2 {
    width: 90px;
    right: 100px;
    top: 57px;
  }
}
@media (max-width: 359px) {
  .about__five-image .image-2 {
    display: none;
  }
}
/*==========================================================================
* Brand Area CSS
==========================================================================*/
@media (max-width: 991px) {
  .brand__area {
    padding-top: 80px;
  }
  .brand__area .swiper-wrapper {
    transition-timing-function: linear;
  }
}
/*==========================================================================
* Skill Style 1 Area Style
==========================================================================*/
.skill-area__one-left {
  position: relative;
}
.skill-area__one-left-video {
  position: absolute;
  right: 70px;
  bottom: 0;
  z-index: 2;
  width: 350px;
  height: 280px;
}
.skill-area__one-left-video img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.skill-area__one-left-video::before {
  content: "";
  position: absolute;
  top: 1px;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-color-1);
  z-index: -1;
  border-radius: 20px;
  transform: rotate(-10deg);
  animation: halfRotationAnimation 5s infinite ease-in-out;
}
.skill-area__one-left-video-play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 65px;
  height: 65px;
  text-align: center;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background: var(--primary-color-1);
  border-radius: 50%;
  font-size: 21px;
  color: var(--bg-white);
  transition: 0.4s;
}
.skill-area__one-left-video-play-btn:hover {
  color: var(--primary-color-1);
  background: var(--bg-white);
}
.skill-area__one-right h2 {
  margin-bottom: 18px;
}
.skill-area__one-right p {
  color: var(--color-2);
  text-transform: none;
  margin-bottom: 35px;
}
.skill-area__one-right-skill-item {
  margin-bottom: 30px;
}
.skill-area__one-right-skill-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 22px;
}
.skill-area__one-right-skill-item-content-title {
  font-size: 22px;
  font-weight: 700;
}
.skill-area__one-right-skill-item-content-count {
  font-size: 18px;
}
.skill-area__one-right-skill-item-content-count .count {
  width: 50px;
}
.skill-area__one-right-skill-item-inner {
  height: 3px;
  background: var(--color-4);
  border-radius: 5px;
}
.skill-area__one-right-skill-item-bar {
  height: 3px;
  width: 0;
  border-radius: 5px;
  background: var(--primary-color-1);
  transition: all 3.5s ease-out 0s;
}

@media (max-width: 767px) {
  .skill-area__one-left-video {
    right: 0;
  }
}
/*==========================================================================
* Skill Style 1 Area Style
==========================================================================*/
.skill__two .tab-content > .active {
  display: flex;
}
.skill__two-tab {
  background: var(--bg-white);
  border: 1px solid var(--color-4);
  padding: 45px 60px 40px 60px;
  border-radius: 10px;
  text-align: left;
  transition: 0.5s;
  position: relative;
}
.skill__two-tab::before {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  bottom: -15%;
  left: 50%;
  transform: translateX(-50%);
  border: 17px solid transparent;
  border-top: 22px solid var(--primary-color-1);
  opacity: 0;
  visibility: hidden;
  transition: 0.5s;
  z-index: -1;
}
.skill__two-tab-icon {
  color: var(--primary-color-1) !important;
  background: rgba(14, 89, 242, 0.1019607843);
  font-size: 40px !important;
  width: 65px;
  height: 65px;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  line-height: 0 !important;
}
.skill__two-tab span {
  font-size: 22px;
  line-height: 1.63;
  font-weight: 600;
  color: var(--text-heading-color);
}
.skill__two-tab:hover {
  border: 1px solid var(--color-1);
  background-color: var(--color-1);
}
.skill__two-tab.active {
  border: 1px solid var(--primary-color-1);
  background: var(--primary-color-1);
}
.skill__two-tab.active::before {
  opacity: 1;
  visibility: visible;
}
.skill__two-tab.active .skill__two-tab-icon {
  color: var(--text-white) !important;
  background: rgba(255, 255, 255, 0.1098039216) !important;
}
.skill__two-tab.active span {
  color: var(--text-white);
}
.skill__two-tab-details-image {
  position: relative;
}
.skill__two-tab-details-image .experience-bar {
  display: flex;
  align-items: center;
  background: var(--primary-color-1);
  position: absolute;
  padding: 25px 50px 25px 25px;
  border-radius: 10px;
  right: 110px;
  top: 80px;
}
.skill__two-tab-details-image .experience-bar i {
  color: var(--text-white);
  font-size: 70px;
  margin-right: 30px;
}
.skill__two-tab-details-image .experience-bar span {
  font-size: 18px;
  color: var(--text-white);
  font-weight: 500;
}
.skill__two-tab-details-image .image-1 {
  border-radius: 20px;
}
.skill__two-tab-details-content {
  padding-right: 55px;
}
.skill__two-tab-details-content h2 {
  margin-bottom: 30px;
}
.skill__two-tab-details-content p {
  color: var(--color-2);
  text-transform: none;
  margin-bottom: 30px;
}
.skill__two-tab-details-content-service {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 30px;
}
.skill__two-tab-details-content-service .service {
  margin-bottom: 20px;
}
.skill__two-tab-details-content-service .service span {
  font-size: 18px;
  color: var(--color-2);
}
.skill__two-tab-details-content-service .service i {
  color: var(--color-3);
  margin-right: 10px;
}

@media (max-width: 1399px) {
  .skill__two-tab {
    padding: 35px 0 30px 40px;
  }
}
@media (max-width: 575px) {
  .skill__two-tab {
    padding: 35px 40px;
    margin: auto;
    width: 55%;
  }
}
@media (max-width: 480px) {
  .skill__two-tab {
    width: 100%;
  }
}
/*==========================================================================
* Services One Style
==========================================================================*/
.services__one-title h2 {
  margin: auto;
  margin-bottom: 30px;
  text-transform: none;
}
.services__one .single-service {
  background: var(--bg-white);
  padding: 30px 30px 33px 30px;
  padding-top: 0;
  border-radius: 8px;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 5px 15px 0px;
  border-radius: 20px;
  transition: 0.4s;
  margin-top: 60px;
}
.services__one .single-service .services__one-single-service-icon {
  transition: 0.3s;
}
.services__one .single-service .services__one-single-service-icon i {
  background-color: var(--color-1);
  padding: 14px;
  border-radius: 5px;
  margin-top: -30px;
  margin-bottom: 20px;
  font-size: 40px;
  display: inline-block;
  color: var(--primary-color-1);
}
.services__one .single-service:hover {
  transform: translateY(-5px);
}
.services__one .single-service:hover .services__one-single-service-icon {
  transform: translateY(-3px);
}
.services__one .single-service:last-child {
  margin-bottom: 0;
}
.services__one-single-service-content h4 {
  font-size: 22px;
  margin-bottom: 12px;
}
.services__one-single-service-content p {
  color: var(--color-2);
  margin-bottom: 20px;
}
.services__one .services-image-wrapper {
  text-align: center;
}

@media (max-width: 1399px) {
  .services__one .single-service {
    padding: 20px 18px 26px 23px;
    padding-top: 0;
  }
  .services__one .single-service h4 {
    font-size: 20px;
    margin-bottom: 13px;
  }
  .services__one .single-service p {
    font-size: 16px;
  }
}
@media (max-width: 767px) {
  .services__one .single-service h4 {
    font-size: 19px;
    margin-bottom: 13px;
  }
  .services__one .single-service p {
    font-size: 15px;
  }
}
@media (max-width: 535px) {
  .services__one-left {
    flex-wrap: wrap;
    justify-content: center;
  }
  .services__one-right {
    flex-wrap: wrap;
    justify-content: center;
  }
  .services__one .single-service {
    margin-right: 0;
    margin-bottom: 80px;
  }
  .services__one .single-service:last-child {
    margin-bottom: 0;
  }
}
/*==========================================================================
* Services Two Style
==========================================================================*/
.services__two {
  background: var(--color-1);
}
.services__two-title {
  margin-bottom: 58px;
}
.services__two-title h2 {
  text-transform: none;
  max-width: 541px;
  margin: auto;
}
.services__two-single-service {
  background: var(--bg-white);
  padding: 40px 45px;
  border-radius: 20px;
  transition: 0.4s;
  overflow: hidden;
}
.services__two-single-service-icon i {
  background-color: rgba(204, 204, 204, 0.1411764706);
  border-radius: 5px;
  margin-bottom: 25px;
  text-align: center;
  z-index: 3;
  position: relative;
  font-size: 58px;
  display: inline-block;
  padding: 20px 20px;
  color: var(--primary-color-1);
  transition: 0.4s;
}
.services__two-single-service-content {
  z-index: 3;
  position: relative;
}
.services__two-single-service-content h4 {
  font-size: 22px;
  margin-bottom: 32px;
  text-transform: none;
  transition: 0.4s;
}
.services__two-single-service-content a i {
  color: var(--primary-color-1);
}
.services__two-single-service::before {
  content: "";
  width: 100%;
  height: 0%;
  position: absolute;
  background: var(--primary-color-1);
  bottom: 0;
  left: 0;
  opacity: 0;
  visibility: hidden;
  transition: 0.5s;
}
.services__two-single-service:hover {
  transform: translateY(-5px);
}
.services__two-single-service:hover::before {
  opacity: 1;
  visibility: visible;
  height: 100%;
}
.services__two-single-service:hover .services__two-single-service-icon i {
  color: var(--text-white);
}
.services__two-single-service:hover .services__two-single-service-content h4 {
  color: var(--text-white);
}
.services__two-single-service:hover .services__two-single-service-content a {
  color: var(--text-white);
}
.services__two-single-service:hover .services__two-single-service-content a i {
  color: var(--text-white);
}

@media (max-width: 1399px) {
  .services__two-single-service {
    padding: 20px 18px 26px 23px;
  }
  .services__two-single-service-content h4 {
    font-size: 19px;
  }
  .services__two-single-service-content p {
    font-size: 16px;
  }
}
/*==========================================================================
* Services Three Style
==========================================================================*/
.services__three-title {
  margin-bottom: 75px;
}
.services__three-items {
  background: var(--color-1);
  padding: 15px 27px 40px 27px;
  width: 100%;
  border-radius: 20px;
  margin: auto;
}
.services__three-single-service {
  background: var(--color-1);
  padding: 37px 35px 37px 40px;
  border-radius: 8px;
  border-radius: 20px;
  transition: 0.5s;
}
.services__three-single-service-icon {
  background-color: rgba(14, 89, 242, 0.1019607843);
  display: table;
  border-radius: 5px;
  margin-bottom: 25px;
  text-align: center;
  transition: 0.5s;
  font-size: 40px;
  padding: 14px 13px;
  color: var(--primary-color-1);
}
.services__three-single-service-content h4 {
  font-size: 22px;
  margin-bottom: 20px;
}
.services__three-single-service-content p {
  color: var(--color-2);
  margin-bottom: 20px;
  text-transform: none;
  font-size: 17px;
}
.services__three-single-service.active {
  background-color: var(--bg-white);
}
.services__three-single-service:hover {
  background-color: var(--bg-white);
}

@media (max-width: 1399px) {
  .services__three-items {
    padding: 0 0 25px 0;
  }
  .services__three-single-service {
    padding: 25px 10px 25px 25px;
  }
}
/*==========================================================================
* Service One Style
==========================================================================*/
.services__four-single-service {
  background: var(--bg-white);
  padding: 30px 27px 33px 30px;
  border-radius: 8px;
  box-shadow: 0px 6px 60px 0px rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  transition: 0.4s;
}
.services__four-single-service-icon i {
  background-color: var(--color-1);
  padding: 14px;
  border-radius: 5px;
  margin-bottom: 20px;
  font-size: 40px;
  display: inline-block;
  color: var(--primary-color-1);
}
.services__four-single-service-content h4 {
  font-size: 22px;
  margin-bottom: 20px;
}
.services__four-single-service-content p {
  color: var(--color-2);
  margin-bottom: 20px;
  text-transform: none;
}
.services__four-single-service:hover {
  transform: translateY(-5px);
}

@media (max-width: 1399px) {
  .services__four-single-service {
    padding: 20px 18px 26px 23px;
  }
  .services__four-single-service-content h4 {
    font-size: 20px;
    margin-bottom: 13px;
  }
  .services__four-single-service-content p {
    font-size: 16px;
  }
}
/*==========================================================================
* Service Five Style
==========================================================================*/
.services__five {
  background: var(--color-1);
}
.services__five-single-service {
  background: var(--bg-white);
  padding: 40px 30px 40px 30px;
  border-radius: 8px;
  box-shadow: 0px 6px 60px 0px rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  transition: 0.4s;
  border-bottom: 4px solid var(--primary-color-1);
}
.services__five-single-service-icon {
  width: 100px;
  height: 100px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: var(--color-1);
  transition: 0.4s;
  margin-bottom: 20px;
}
.services__five-single-service-icon i {
  font-size: 60px;
  display: inline-block;
  color: var(--primary-color-1);
  transition: 0.4s;
}
.services__five-single-service-content h4 {
  font-size: 22px;
  margin-bottom: 20px;
  transition: 0.4s;
}
.services__five-single-service-content p {
  color: var(--color-2);
  margin-bottom: 20px;
  text-transform: none;
  transition: 0.4s;
}
.services__five-single-service:hover {
  transform: translateY(-5px);
  background: var(--primary-color-1);
}
.services__five-single-service:hover .services__five-single-service-icon {
  background-color: rgba(255, 255, 255, 0.1019607843);
}
.services__five-single-service:hover .services__five-single-service-icon i {
  color: var(--text-white);
}
.services__five-single-service:hover .services__five-single-service-content h4 {
  color: var(--text-white);
}
.services__five-single-service:hover .services__five-single-service-content p {
  color: var(--text-white);
}
.services__five-single-service:hover .services__five-single-service-content a {
  color: var(--text-white);
}
.services__five-single-service:hover .services__five-single-service-content a i {
  color: var(--text-white);
}

/*==========================================================================
* Service Details CSS
==========================================================================*/
.service__details-thumb {
  position: relative;
  margin-bottom: 50px;
}
.service__details-thumb-icon {
  position: absolute;
  top: 0;
  left: 60px;
  background: var(--primary-color-1);
  padding: 15px 19px;
}
.service__details-thumb-icon-wrapper {
  position: relative;
}
.service__details-thumb-icon-wrapper::before {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  border: 51px solid transparent;
  border-top: 23px solid var(--primary-color-1);
  bottom: -89px;
  left: -19px;
}
.service__details-content h2 {
  margin-bottom: 25px;
}
.service__details-content p {
  color: var(--color-2);
  text-transform: none;
  margin-bottom: 30px;
}
.service__details-content h3 {
  font-size: 32px;
  margin-bottom: 15px;
}
.service__details-content-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.service__details-content-box-single {
  background: var(--color-1);
  padding: 37px 43px;
  width: 48.5%;
  border-radius: 10px;
  margin-bottom: 32px;
}
.service__details-content-box-single h4 {
  margin-bottom: 15px;
}
.service__details-content-box-single p {
  font-size: 17px;
  margin-bottom: 25px;
}
.service__details-content-box-single .icon {
  margin-bottom: 25px;
}
.service__details-content-box-single .service-qualities {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.service__details-content-box-single .service-qualities li {
  width: 50%;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
}
.service__details-content-box-single .service-qualities li:last-child {
  margin: 0;
}
.service__details-content-box-single .service-qualities li:nth-last-child(2) {
  margin: 0;
}
.service__details-content-box-single .service-qualities li::before {
  content: "";
  width: 15px;
  height: 15px;
  display: block;
  background: var(--color-4);
  margin-right: 15px;
  border-radius: 50%;
  border: 3px solid var(--color-1);
  outline: 1px solid var(--color-4);
  transition: 0.4s;
}
.service__details-content-box-single .service-qualities li:hover::before {
  outline: 1px solid var(--primary-color-1);
  background: var(--primary-color-1);
}
.service__details-content > *:last-child {
  margin: 0;
}

@media (max-width: 1399px) {
  .service__details-content-box-single {
    padding: 30px 38px;
  }
}
@media (max-width: 1199px) {
  .service__details-content-box {
    flex-wrap: wrap;
  }
  .service__details-content-box-single {
    width: auto;
  }
}
@media (max-width: 767px) {
  .service__details-thumb-icon {
    left: 30px;
    padding: 12px 15px;
  }
  .service__details-thumb-icon-wrapper::before {
    border: 49px solid transparent;
    border-top: 23px solid var(--primary-color-1);
    bottom: -83px;
    left: -16px;
  }
}
@media (max-width: 480px) {
  .service__details-content-box-single .service-qualities li {
    font-size: 16px;
  }
  .service__details-content-box-single .service-qualities li::before {
    width: 14px;
    height: 14px;
    margin-right: 10px;
  }
  .service__details-content-box-single {
    padding: 22px 25px;
  }
  .service__details-thumb {
    margin-bottom: 25px;
  }
  .service__details-thumb-icon-wrapper img {
    width: 45px;
  }
  .service__details-thumb-icon-wrapper::before {
    border: 38px solid transparent;
    border-top: 18px solid var(--primary-color-1);
    bottom: -68px;
    left: -16px;
  }
}
/*==========================================================================
* Portfolio One Style
==========================================================================*/
.portfolio__one {
  background: var(--color-1);
}
.portfolio__one .custom__container {
  max-width: 1745px;
}
.portfolio__one-content-left h2 {
  margin: auto;
  text-transform: none;
}
.portfolio__one-single-portfolio {
  position: relative;
  border-radius: 30px;
  overflow: hidden;
  transition: all 0.3s linear;
}
.portfolio__one-single-portfolio img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.portfolio__one-single-portfolio-content {
  position: absolute;
  z-index: 3;
  bottom: 4%;
  left: 50%;
  transform: translateX(-50%);
  width: 94%;
  background: white;
  border-radius: 19px;
  border-bottom: 4px solid var(--primary-color-1);
  padding: 17px 30px;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s all ease-in-out;
}
.portfolio__one-single-portfolio-content h4 {
  font-size: 28px;
  font-weight: 600;
}
.portfolio__one-single-portfolio-content span {
  font-size: 18px;
  color: var(--primary-color-1);
}
.portfolio__one .swiper-slide {
  height: 365px;
  transform: scale(0.8);
}
.portfolio__one .swiper-slide.swiper-slide-active {
  transform: scale(1.2);
}
.portfolio__one .swiper-slide.swiper-slide-active .portfolio__one-single-portfolio-content {
  visibility: visible;
  opacity: 1;
}

@media (max-width: 1399px) {
  .portfolio__one-content-right p {
    margin-left: 0;
  }
  .portfolio__one-content-right a {
    margin-left: 0;
  }
  .portfolio__one-single-portfolio-content h4 {
    font-size: 28px;
  }
}
@media (max-width: 1199px) {
  .portfolio__one-content-left {
    margin-bottom: 0;
  }
  .portfolio__one-content-left h2 {
    margin: 0;
  }
}
@media (max-width: 992px) {
  .portfolio__one .swiper-slide {
    transform: scale(1);
  }
  .portfolio__one .swiper-slide.swiper-slide-active {
    transform: scale(1);
  }
}
@media (max-width: 767px) {
  .portfolio__one-single-portfolio {
    width: auto;
    margin-bottom: 30px;
  }
  .portfolio__one-single-portfolio.active {
    width: auto;
    height: 365px;
  }
  .portfolio__one-single-portfolio:last-child {
    margin: 0;
  }
  .portfolio__one-single-portfolio-content h4 {
    font-size: 25px;
  }
}
@media (max-width: 480px) {
  .portfolio__one-content-right p {
    font-size: 15px;
  }
}
/*==========================================================================
* Portfolio Two CSS
==========================================================================*/
.portfolio__two-title {
  margin-bottom: 60px;
}
.portfolio__two-title h2 {
  text-transform: none;
}
.portfolio__two-items {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.portfolio__two-items:last-child .portfolio__two-single-item:last-child {
  margin-bottom: 0;
}
.portfolio__two-single-item {
  height: 345px;
  overflow: hidden;
  margin-bottom: 30px;
  border-radius: 30px;
  transition: 0.5s;
  margin-right: 30px;
  position: relative;
}
.portfolio__two-single-item:last-child {
  margin-right: 0;
}
.portfolio__two-single-item-img-wrapper {
  width: 100%;
  height: 100%;
}
.portfolio__two-single-item-img-wrapper img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.portfolio__two-single-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--bg-white);
  padding: 30px;
  position: absolute;
  bottom: 0;
  left: 48%;
  width: 88%;
  transform: translateX(-50%);
  border-radius: 10px;
  opacity: 0;
  transition: 0.3s;
  visibility: hidden;
  border-left: 4px solid var(--primary-color-1);
}
.portfolio__two-single-item-content-left h4 {
  font-size: 28px;
  margin-bottom: 3px;
}
.portfolio__two-single-item-content-left p {
  font-size: 17px;
  text-transform: none;
  color: var(--primary-color-1);
}
.portfolio__two-single-item-content-right {
  margin-right: -13%;
  border: 3px solid var(--bg-white);
  border-radius: 50%;
}
.portfolio__two-single-item-content-right a {
  font-size: 20px;
  transform: rotate(-45deg);
  width: 60px;
  height: 60px;
  display: block;
  text-align: center;
  line-height: 59px;
  border-radius: 50%;
  border: 1px solid var(--primary-color-1);
  transition: 0.4s;
  background-color: var(--primary-color-1);
  color: var(--text-white);
}
.portfolio__two-single-item-content-right a:hover {
  color: var(--text-heading-color);
  background-color: var(--bg-white);
  transform: rotate(0);
}
.portfolio__two-single-item:hover .portfolio__two-single-item-content {
  opacity: 1;
  bottom: 20px;
  visibility: visible;
}
.portfolio__two.three-columns .portfolio__two-single-item {
  height: 500px;
  border-radius: 20px;
}
.portfolio__two.three-columns .portfolio__two-single-item h3 {
  font-size: 24px;
}

@media (max-width: 991px) {
  .portfolio__two-items {
    flex-direction: column;
  }
  .portfolio__two-single-item {
    width: 100%;
    margin-right: 0;
  }
}
@media (max-width: 767px) {
  .portfolio__two-single-item-content-left {
    width: 80%;
  }
}
@media (max-width: 480px) {
  .portfolio__two-single-item-content-left {
    width: 100%;
    text-align: center;
    margin-bottom: 15px;
  }
  .portfolio__two-single-item-content {
    flex-direction: column;
  }
}
/*==========================================================================
* Project Details CSS
==========================================================================*/
.project__details .project-feature {
  display: flex;
  justify-content: space-between;
  background: var(--color-1);
  padding: 38px 40px 68px 40px;
  border-radius: 10px;
  position: relative;
}
.project__details .project-feature h4 {
  width: 54%;
}
.project__details .project-feature::before {
  content: "";
  width: 81%;
  height: 1px;
  position: absolute;
  background: var(--color-2);
  bottom: 38px;
  left: 50%;
  transform: translateX(-50%);
}
.project__details-thumb {
  position: relative;
}
.project__details-thumb img {
  width: 100%;
}
.project__details-thumb .project-info {
  position: absolute;
  bottom: -44%;
  right: 0;
  width: 410px;
  border-radius: 10px 0 0 10px;
  overflow: hidden;
}
.project__details-thumb .project-info .project-info-top {
  background: var(--primary-color-1);
  text-align: center;
  padding: 7px 0;
}
.project__details-thumb .project-info .project-info-top h4 {
  color: var(--text-white);
  font-size: 18px;
}
.project__details-thumb .project-info ul {
  background: var(--bg-white);
  border: 1px solid var(--color-4);
  border-top: 0;
  padding: 23px 30px;
  border-radius: 0 0 0 10px;
  list-style-type: none;
  margin: 0;
}
.project__details-thumb .project-info ul li {
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}
.project__details-thumb .project-info ul li span {
  color: var(--color-2);
  margin-left: 10px;
  font-weight: 400;
}
.project__details-thumb .project-info ul li .value {
  color: var(--primary-color-1);
  font-size: 22px;
  font-weight: 600;
}
.project__details-thumb .project-info ul .project-rating i {
  color: #FBA758;
  margin-right: 5px;
}
.project__details .project__details-content-mid {
  margin-top: 30px;
  margin-bottom: 48px;
}
.project__details .project__details-content-mid p {
  margin-bottom: 30px;
}
.project__details .project__details-content h3 {
  margin-bottom: 15px;
}
.project__details .project__details-content p {
  font-size: 17px;
  text-transform: none;
  color: var(--color-2);
}
.project__details-images {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 40px;
  gap: 30px;
}

@media (max-width: 1399px) {
  .project__details-thumb .project-info {
    position: unset;
    border-radius: 10px;
    overflow: hidden;
    margin-top: 30px;
    width: 100%;
  }
}
/*==========================================================================
* Team One Style
==========================================================================*/
.team__one {
  background: linear-gradient(var(--bg-white) 60%, var(--color-1) 40%);
}
.team__one.bg-color-2 {
  background: var(--color-1);
}
.team__one-title {
  margin-bottom: 60px;
}
.team__one-title h2 {
  text-transform: none;
}
.team__one-team-item {
  position: relative;
  border-radius: 30px;
  transition: 0.4s;
}
.team__one-team-item img {
  width: 100%;
  height: 100%;
}
.team__one-team-item-content {
  background: var(--bg-white);
  width: 85%;
  border-radius: 20px;
  padding: 18px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 3;
  position: relative;
  margin: auto;
  margin-top: -4pc;
}
.team__one-team-item-content h3 {
  font-size: 22px;
}
.team__one-team-item-content span {
  color: var(--color-2);
}
.team__one-team-item:hover {
  transform: translateY(-4px);
}
.team__one-social-wrapper {
  background: transparent;
  border: 1px solid var(--primary-color-1);
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  color: var(--primary-color-1);
  cursor: pointer;
  transition: 0.3s;
}
.team__one-social-wrapper:hover {
  background-color: var(--primary-color-1);
  color: var(--text-white);
}
.team__one-social-wrapper:hover .share-links {
  opacity: 1;
  visibility: visible;
  color: var(--text-heading-color);
}
.team__one-social-wrapper .share-links {
  position: absolute;
  top: -150px;
  right: 22px;
  opacity: 0;
  transition: 0.3s linear;
  visibility: hidden;
}
.team__one-social-wrapper .share-links .inner-link {
  background: var(--color-1);
  height: 35px;
  width: 35px;
  font-size: 14px;
  text-align: center;
  line-height: 35px;
  border-radius: 5px;
  color: var(--text-heading-color);
  display: block;
  margin-bottom: 10px;
  transition: 0.4s;
}
.team__one-social-wrapper .share-links .inner-link:hover {
  color: var(--primary-color-1);
}

@media (max-width: 1399px) {
  .team__one-team-item-content span {
    font-size: 15px;
  }
}
@media (max-width: 991px) {
  .team__one-team-item-content {
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  }
}
/*==========================================================================
* Team Two Style
==========================================================================*/
.team__two-title h2 {
  text-transform: none;
}
.team__two-team-item {
  position: relative;
  border-radius: 30px;
  transition: 0.4s;
}
.team__two-team-item img {
  width: 100%;
  height: 100%;
}
.team__two-team-item-content {
  background: var(--bg-white);
  padding: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 3;
  position: relative;
  border: 1px solid var(--color-4);
  border-radius: 0 0 20px 19px;
  margin-top: -30px;
}
.team__two-team-item-content h3 {
  font-size: 22px;
}
.team__two-team-item-content span {
  color: var(--color-2);
}
.team__two-team-item-content .share-link-wrapper {
  background: var(--primary-color-1);
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 5px;
  color: var(--text-white);
  cursor: pointer;
  transition: 0.3s;
}
.team__two-team-item-content .share-link-wrapper:hover {
  border-radius: 0 0 5px 5px;
}
.team__two-team-item-content .share-link-wrapper:hover .share-links {
  opacity: 1;
  visibility: visible;
  color: var(--text-heading-color);
}
.team__two-team-item-content .share-link-wrapper .share-links {
  position: absolute;
  bottom: 77px;
  right: 30px;
  opacity: 0;
  transition: 0.3s linear;
  visibility: hidden;
  background: rgba(255, 255, 255, 0.8392156863);
  border-radius: 5px 5px 0 0;
  padding: 7px 0;
}
.team__two-team-item-content .share-link-wrapper .share-links .inner-link {
  height: 35px;
  width: 40px;
  font-size: 14px;
  text-align: center;
  line-height: 35px;
  color: var(--text-heading-color);
  display: block;
  transition: 0.3s;
}
.team__two-team-item-content .share-link-wrapper .share-links .inner-link:hover {
  color: var(--primary-color-1);
}
.team__two-team-item:hover {
  transform: translateY(-4px);
}

@media (max-width: 1399px) {
  .team__one-team-item-content span {
    font-size: 15px;
  }
}
@media (max-width: 1399px) {
  .team__two-team-item-content {
    padding: 15px 25px;
  }
  .team__two-team-item-content > i .share-links {
    bottom: 68px;
    right: 25px;
  }
}
/*==========================================================================
* Horizontal Scroll Animation
==========================================================================*/
.horizontal-scroll {
  padding: 30px 0;
  background: var(--primary-color-1);
  margin-bottom: -1px;
}
.horizontal-scroll-active {
  padding: 0;
}
.horizontal-scroll-icon {
  margin: 0 45px;
  font-size: 45px;
  color: var(--text-white);
}
.horizontal-scroll-single-item {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  list-style: none;
}
.horizontal-scroll-single-item h3 {
  font-size: 42px;
  color: var(--text-white);
}
.horizontal-scroll .slick-slide:nth-child(even) .horizontal-scroll-single-item h3 {
  color: transparent;
  -webkit-text-stroke: 1.5px var(--text-white);
}

/*==========================================================================
* Contact One CSS
==========================================================================*/
.contact__one {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}
.contact__one-title {
  margin-bottom: 45px;
}
.contact__one-title .subtitle-three {
  color: var(--text-white);
}
.contact__one-title .subtitle-three::before {
  background-color: var(--bg-white);
}
.contact__one-title h2 {
  color: var(--text-white);
}
.contact__one-form input {
  background-color: transparent;
  border-radius: 100px;
  color: var(--text-white);
  transition: 0.4s;
  padding: 33px 30px;
  font-size: 18px;
}
.contact__one-form input::-moz-placeholder {
  color: var(--text-white);
}
.contact__one-form input::placeholder {
  color: var(--text-white);
}
.contact__one-form input:focus {
  border: 1px solid var(--color-1);
  color: var(--text-white);
}
.contact__one-form button {
  justify-content: center;
  margin-top: 30px;
  border: 1px solid var(--primary-color-1);
  color: var(--text-white) !important;
}
.contact__one-form button i {
  color: var(--text-white) !important;
}
.contact__one-form-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}
.contact__one-form-top input {
  margin-right: 30px;
}
.contact__one-form-top input:last-child {
  margin: 0;
}
.contact__one-counter {
  background-color: rgba(255, 255, 255, 0.1019607843);
  width: 145px;
  padding: 15px 20px;
  border-radius: 20px;
  color: var(--text-white);
}
.contact__one-counter span {
  text-transform: none;
  font-size: 13px;
  line-height: 1.76;
}
.contact__one-counter .counter-only {
  display: flex;
  align-items: center;
}
.contact__one-counter .counter-only h3 {
  color: var(--text-white);
  font-size: 36px;
}
.contact__one-counter-img {
  margin-left: -80px;
  width: 213px;
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 15px;
}

@media (max-width: 575px) {
  .contact__one-right img {
    display: none;
  }
  .contact__one-counter {
    width: 100%;
    text-align: center;
    padding: 35px 30px;
  }
  .contact__one-counter span {
    font-size: 16px;
  }
  .contact__one-counter .counter-only {
    justify-content: center;
  }
  .contact__one-counter .counter-only h3 {
    font-size: 45px;
  }
  .contact__one-form-top {
    flex-direction: column;
    row-gap: 25px;
  }
  .contact__one-form-top input {
    margin-right: 0;
  }
}
/*==========================================================================
* Contact Two CSS
==========================================================================*/
.contact__two-title h2 {
  text-transform: none;
  margin-bottom: 30px;
}
.contact__two-title p {
  color: var(--color-2);
  margin-bottom: 35px;
  text-transform: none;
}
.contact__two-form textarea {
  margin-bottom: 30px;
  height: 130px;
}
.contact__two-form .btn-two {
  border-radius: 5px;
  border: 1px solid var(--primary-color-1);
}
.contact__two-contact-info {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-left: 70px;
}
.contact__two-single-info {
  background-color: var(--color-1);
  padding: 30px 5px;
  border-radius: 5px;
  transition: 0.4s;
  text-align: center;
  width: 43%;
  margin-right: 28px;
  margin-bottom: 28px;
}
.contact__two-single-info-icon {
  background-color: var(--bg-white);
  width: 65px;
  height: 65px;
  border-radius: 5px;
  margin: auto;
  margin-bottom: 15px;
  text-align: center;
  line-height: 65px;
}
.contact__two-single-info-content h4 {
  font-size: 22px;
  margin-bottom: 7px;
}
.contact__two-single-info-content span {
  color: var(--color-2);
  display: block;
  font-size: 14px;
  text-transform: none;
  margin-bottom: 7px;
}
.contact__two-single-info-content span:last-child {
  margin-bottom: 0;
}
.contact__two-single-info:last-child {
  margin-bottom: 0;
}
.contact__two-single-info:nth-last-child(2) {
  margin-bottom: 0;
}
.contact__two-single-info:hover {
  transform: translateY(-5px);
}

@media (max-width: 1399px) {
  .contact__two-single-info {
    width: 46%;
    margin-right: 15px;
    margin-bottom: 15px;
  }
}
@media (max-width: 1199px) {
  .contact__two-contact-info {
    margin-left: 0;
  }
  .contact__two-single-info {
    width: 35%;
    padding: 30px 46px;
  }
}
@media (max-width: 991px) {
  .contact__two-single-info {
    width: 45%;
  }
}
@media (max-width: 767px) {
  .contact__two-single-info:nth-last-child(2) {
    margin-bottom: 15px;
  }
  .contact__two-single-info {
    width: 100%;
  }
}
/*==========================================================================
* Contact Location Map 
==========================================================================*/
.location-map {
  height: 651px;
  margin-bottom: -6.5%;
  filter: grayscale(1);
}
.location-map iframe {
  width: 100%;
  height: 100%;
}

.request-quote__area form label {
  font-size: 18px;
}
.request-quote__area form input::-moz-placeholder {
  font-size: 18px;
  margin-bottom: 10px;
}
.request-quote__area form input::placeholder {
  font-size: 18px;
  margin-bottom: 10px;
}
.request-quote__area form button:hover, .request-quote__area form input[type=button]:hover, .request-quote__area form input[type=reset]:hover, .request-quote__area form input[type=submit]:hover {
  border-color: var(--primary-color-1);
}
.request-quote__area-inputs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  row-gap: 30px;
}
.request-quote__area-input-field {
  width: 49%;
}
.request-quote__area-input-field label {
  margin-bottom: 10px;
}
.request-quote__area-service-input {
  display: flex;
  flex-wrap: wrap;
  -moz-column-gap: 20px;
       column-gap: 20px;
  row-gap: 20px;
  margin: 30px 0;
}
.request-quote__area-service-input span {
  width: 100%;
  font-size: 18px;
  margin-bottom: 10px;
}

@media (max-width: 575px) {
  .request-quote__area-input-field {
    width: 100%;
  }
}
/*==========================================================================
* Why Choose Us Style 
==========================================================================*/
.why-choose-us__one {
  background-color: var(--color-1);
}
.why-choose-us__one-title h2 {
  text-transform: none;
  margin-bottom: 30px;
}
.why-choose-us__one-title p {
  color: var(--color-2);
  margin-bottom: 35px;
  text-transform: none;
  width: 93%;
}
.why-choose-us__one-quality-single {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}
.why-choose-us__one-quality-single .icon i {
  margin-right: 20px;
  background: var(--bg-white);
  text-align: center;
  border-radius: 50%;
  font-size: 40px;
  padding: 20px 20px;
  display: block;
  color: var(--primary-color-1);
}
.why-choose-us__one-quality-single-content h4 {
  font-size: 22px;
  margin-bottom: 10px;
}
.why-choose-us__one-quality-single-content p {
  font-size: 18px;
  color: var(--color-2);
  text-transform: none;
  width: 92%;
}
.why-choose-us__one-image {
  position: relative;
}
.why-choose-us__one-image-shape .shape {
  position: absolute;
  z-index: 0;
}
.why-choose-us__one-image-shape .shape-1 {
  width: 185px;
  height: 150px;
  background: var(--primary-color-1);
  top: -34px;
  left: -40px;
  border-radius: 100px 8px 8px 100px;
  opacity: 0.2;
}
.why-choose-us__one-image-shape .shape-2 {
  width: 185px;
  height: 150px;
  background: var(--primary-color-1);
  bottom: -34px;
  right: -13px;
  border-radius: 100px 8px 8px 100px;
  opacity: 0.2;
}
.why-choose-us__one-image-shape .shape-3 {
  bottom: -21px;
  left: -48px;
}
.why-choose-us__one-image > img {
  border: 8px solid var(--bg-white);
  border-radius: 30px;
  z-index: 2;
  position: relative;
}

@media (max-width: 1399px) {
  .why-choose-us__one-quality-single .icon i {
    margin-right: 15px;
  }
  .why-choose-us__one-quality-single-content h4 {
    font-size: 20px;
    margin-bottom: 8px;
  }
  .why-choose-us__one-quality-single-content p {
    font-size: 16px;
  }
}
@media (max-width: 1199px) {
  .why-choose-us__one-left {
    margin-bottom: 60px;
  }
}
@media (max-width: 535px) {
  .why-choose-us__one-quality-single {
    flex-wrap: wrap;
  }
  .why-choose-us__one-quality-single .icon i {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
/*==========================================================================
* Why Choose Us two CSS
==========================================================================*/
.why-choose-us__two-content h2 {
  text-transform: none;
  margin-bottom: 30px;
}
.why-choose-us__two-content p {
  color: var(--color-2);
  margin-bottom: 30px;
  font-size: 17px;
}
.why-choose-us__two-content-service {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 30px;
}
.why-choose-us__two-content-service .service {
  margin-bottom: 20px;
  margin-right: 50px;
}
.why-choose-us__two-content-service .service span {
  font-size: 18px;
  color: var(--color-2);
}
.why-choose-us__two-content-service .service i {
  color: var(--primary-color-1);
  margin-right: 10px;
}
.why-choose-us__two-image {
  position: relative;
  text-align: right;
}

/*==========================================================================
* Why Choose Us three Style 
==========================================================================*/
.why-choose-us__three {
  background-color: var(--color-1);
}
.why-choose-us__three-title h2 {
  text-transform: none;
  margin-bottom: 30px;
}
.why-choose-us__three-title p {
  color: var(--color-2);
  margin-bottom: 35px;
  text-transform: none;
  font-size: 17px;
}
.why-choose-us__three-bottom {
  display: flex;
  align-items: center;
}
.why-choose-us__three-bottom-card {
  padding: 25px 35px;
  background: var(--bg-white);
  border-radius: 15px;
  margin-right: 24px;
}
.why-choose-us__three-bottom-card-content {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 15px;
}
.why-choose-us__three-bottom-card-content i {
  font-size: 40px;
  color: var(--primary-color-1);
}
.why-choose-us__three-bottom-card-content h4 {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.4;
  color: var(--color-2);
}
.why-choose-us__three-bottom-card-counter {
  display: flex;
  align-items: center;
}
.why-choose-us__three-bottom-card-counter h4 {
  font-size: 48px;
  line-height: 1.2;
  font-weight: 500;
  text-transform: none;
}
.why-choose-us__three-image {
  position: relative;
  text-align: right;
}
.why-choose-us__three-image img {
  border-radius: 20px;
  z-index: 2;
  position: relative;
}
.why-choose-us__three-floating-image {
  position: absolute !important;
  top: 30%;
  z-index: 3 !important;
  left: -13%;
  border: 8px solid var(--bg-white);
  width: 260px;
}

@media (max-width: 1199px) {
  .why-choose-us__three-bottom-card {
    padding: 20px 18px;
    background: var(--bg-white);
    border-radius: 15px;
    margin-right: 24px;
  }
  .why-choose-us__three-floating-image {
    left: 0;
  }
}
@media (max-width: 480px) {
  .why-choose-us__three-bottom {
    display: flex;
    align-items: center;
    flex-direction: column;
  }
  .why-choose-us__three-bottom-card {
    margin-bottom: 25px;
  }
  .why-choose-us__three-bottom-card:last-child {
    margin-bottom: 0;
  }
}
/*==========================================================================
* Why Choose Us four Style 
==========================================================================*/
.why-choose-us__four-title h2 {
  text-transform: none;
  margin-bottom: 30px;
}
.why-choose-us__four-title p {
  color: var(--color-2);
  margin-bottom: 35px;
  text-transform: none;
  font-size: 17px;
}
.why-choose-us__four-image {
  position: relative;
}
.why-choose-us__four-image img {
  width: 510px;
  z-index: 2;
  position: relative;
}
.why-choose-us__four-progress-bar {
  display: flex;
  -moz-column-gap: 90px;
       column-gap: 90px;
}
.why-choose-us__four-progress-bar-wrapper h3 {
  font-size: 22px;
  font-weight: 500;
  margin-top: 18px;
}
.why-choose-us__four-progress-bar .single-progress-bar {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: conic-gradient(var(--primary-color-1) 270deg, #f1f1f1 0deg);
  display: flex;
  justify-content: center;
  align-items: center;
}
.why-choose-us__four-progress-bar .single-progress-bar span {
  position: relative;
  font-size: 22px;
  font-weight: 600;
}
.why-choose-us__four-progress-bar .single-progress-bar::before {
  content: "";
  width: 86px;
  height: 86px;
  background: var(--bg-white);
  border-radius: 50%;
  position: absolute;
}
.why-choose-us__four-floating-image {
  position: absolute !important;
  top: 10%;
  z-index: 3 !important;
  left: 8%;
  width: 230px !important;
}

@media (max-width: 575px) {
  .why-choose-us__four-floating-image {
    width: 150px !important;
  }
}
@media (max-width: 359px) {
  .why-choose-us__four-floating-image {
    width: 110px !important;
  }
}
/*==========================================================================
* Pricing Plan One CSS
==========================================================================*/
.pricing-plan__one-title {
  margin-bottom: 50px;
}
.pricing-plan__one-title h2 {
  text-transform: none;
}
.pricing-plan__one-single-pricing-wrapper {
  border-radius: 5px;
  transition: 0.4s;
  box-shadow: 0px 0px 60px 0px rgba(0, 0, 0, 0.05);
}
.pricing-plan__one-single-pricing-wrapper:hover {
  transform: translateY(-5px);
}
.pricing-plan__one-single-pricing-plan {
  background: var(--bg-white);
  padding: 45px 35px 38px 35px;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
}
.pricing-plan__one-single-pricing-plan::before {
  content: "";
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background: var(--color-1);
  position: absolute;
  top: -23pc;
  left: -10pc;
  z-index: 0;
  transition: 0.5s;
}
.pricing-plan__one-single-pricing-plan.active::before {
  background: var(--primary-color-1);
}
.pricing-plan__one-single-pricing-plan.active .pricing-plan__one-single-pricing-plan-title {
  color: var(--text-white);
}
.pricing-plan__one-single-pricing-plan-title {
  margin-bottom: 58px;
  z-index: 1;
  position: relative;
}
.pricing-plan__one-single-pricing-plan-price {
  margin-bottom: 15px;
}
.pricing-plan__one-single-pricing-plan-price span {
  text-transform: lowercase;
  font-size: 18px;
  color: var(--color-2);
}
.pricing-plan__one-single-pricing-plan p {
  color: var(--color-2);
  text-transform: none;
  margin-bottom: 27px;
  width: 86%;
}
.pricing-plan__one-single-pricing-plan-benefits {
  margin-bottom: 45px;
}
.pricing-plan__one-single-pricing-plan-benefits span {
  display: block;
  font-size: 18px;
  margin-bottom: 13px;
  color: var(--color-2);
}
.pricing-plan__one-single-pricing-plan-benefits span i {
  color: var(--primary-color-1);
  margin-right: 10px;
  transition: 0.3s;
}
.pricing-plan__one-single-pricing-plan:hover::before {
  top: -22pc;
  left: -9pc;
}
.pricing-plan__one .btn-one {
  border-color: var(--color-4);
  justify-content: center;
}
.pricing-plan__one .tab-content > .active {
  display: flex;
}
.pricing-plan__one .nav-pills .nav-link {
  padding: 7px 30px;
  font-size: 18px;
  font-weight: 600;
  background: var(--color-1);
  color: var(--text-heading-color);
  margin-left: 20px;
}
.pricing-plan__one .nav-pills .nav-link.active {
  background-color: var(--primary-color-1);
  color: var(--text-white);
}

/*==========================================================================
* Pricing Plan Two CSS
==========================================================================*/
.pricing-plan__two {
  background: linear-gradient(var(--color-1) 60%, var(--bg-white) 40%);
}
.pricing-plan__two-title {
  margin-bottom: 70px;
}
.pricing-plan__two-title h2 {
  text-transform: none;
}
.pricing-plan__two-single-pricing-wrapper {
  background: linear-gradient(var(--color-6), transparent);
  padding: 2px;
  border-radius: 5px;
  transition: 0.4s;
  box-shadow: rgba(17, 17, 26, 0.1) 0px 0px 16px;
}
.pricing-plan__two-single-pricing-wrapper:hover {
  transform: translateY(-5px);
}
.pricing-plan__two-single-pricing-plan {
  background: var(--bg-white);
  padding: 45px 35px 38px 35px;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  box-shadow: rgba(14, 30, 37, 0.02) 0px 2px 4px 0px, rgba(14, 30, 37, 0.03) 0px 2px 16px 0px;
}
.pricing-plan__two-single-pricing-plan-title {
  margin-bottom: 18px;
}
.pricing-plan__two-single-pricing-plan-price {
  margin-bottom: 40px;
}
.pricing-plan__two-single-pricing-plan-price span {
  text-transform: lowercase;
  font-size: 18px;
  color: var(--color-2);
}
.pricing-plan__two-single-pricing-plan p {
  color: var(--color-2);
  max-width: 261px;
  text-transform: none;
  margin-bottom: 27px;
}
.pricing-plan__two-single-pricing-plan-benefits {
  margin-bottom: 95px;
}
.pricing-plan__two-single-pricing-plan-benefits span {
  display: block;
  font-size: 18px;
  margin-bottom: 18px;
  color: var(--color-2);
}
.pricing-plan__two-single-pricing-plan-benefits span i {
  color: var(--primary-color-1);
  margin-right: 10px;
  transition: 0.3s;
}
.pricing-plan__two-single-pricing-plan .btn-one {
  justify-content: center;
}
.pricing-plan__two-single-pricing-plan .btn-one i {
  color: var(--primary-color-1);
}
.pricing-plan__two-single-pricing-plan .btn-one:hover {
  background: var(--primary-color-2);
  border-color: var(--primary-color-2);
  color: var(--text-heading-color);
}
.pricing-plan__two-single-pricing-plan .btn-one:hover i {
  color: var(--text-heading-color);
}

/*==========================================================================
* Work Process One Style
==========================================================================*/
.work-process-area__one {
  background-position: center center;
  background-size: cover;
  overflow: hidden;
}
.work-process-area__one-title h2 {
  max-width: 94%;
  text-transform: none;
  color: var(--bg-white);
  margin-bottom: 59px;
}
.work-process-area__one-title p {
  text-transform: none;
  margin-bottom: 60px;
  color: var(--bg-white);
  opacity: 0.6;
  width: 98%;
}
.work-process-area__one-single-work {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  border: 2px solid var(--color-5);
  padding: 29px 29px;
  border-radius: 20px;
}
.work-process-area__one-single-work:last-child {
  margin-bottom: 0;
}
.work-process-area__one-single-work span {
  background: var(--primary-color-1);
  color: var(--text-white);
  width: 65px;
  font-size: 32px;
  height: 65px;
  text-align: center;
  border-radius: 50%;
  margin-right: 18px;
  font-weight: 700;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.work-process-area__one-single-work h4 {
  color: var(--bg-white);
  font-size: 22px;
  margin-bottom: 15px;
}
.work-process-area__one-single-work p {
  color: var(--bg-white);
  text-transform: none;
  opacity: 0.6;
}
.work-process-area__one-single-work-content {
  width: calc(100% - 65px);
}
.work-process-area__one-right-counter-img {
  display: flex;
  margin-top: 30px;
  margin-left: 35px;
}
.work-process-area__one-right-counter-img .img-counter {
  background: var(--color-3);
  padding: 32px 35px;
  border-radius: 20px 0 0 20px;
  width: 35%;
}
.work-process-area__one-right-counter-img .img-counter .counter-only {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}
.work-process-area__one-right-counter-img .img-counter h2 {
  color: var(--bg-white);
}
.work-process-area__one-right-counter-img .img-counter span {
  font-size: 22px;
  color: var(--bg-white);
  text-transform: lowercase;
  line-height: 1.63;
}
.work-process-area__one-right-counter-img img {
  width: 65%;
}
.work-process-area__one-right-img {
  margin-left: 36px;
  max-width: 100%;
}
.work-process-area__one-right-img img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

@media (max-width: 1399px) {
  .works-process-area__one-single-work {
    padding: 16px 10px;
  }
}
@media (max-width: 1199px) {
  .work-process-area__one-right-img {
    margin-left: 0;
  }
  .work-process-area__one-right-counter-img {
    margin-left: 0;
  }
}
@media (max-width: 991px) {
  .works-process-area__one {
    padding-top: 100px;
    padding-bottom: 170px;
  }
  .work-process-area__one-single-work {
    flex-direction: column;
    text-align: center;
  }
  .work-process-area__one-single-work span {
    margin-bottom: 20px;
  }
  .work-process-area__one-title h2 {
    margin-bottom: 20px;
  }
}
@media (max-width: 535px) {
  .work-process-area__one-right-counter-img {
    flex-direction: column;
  }
  .work-process-area__one-right-counter-img .img-counter {
    width: 100%;
    border-radius: 20px;
    margin-bottom: 30px;
  }
  .work-process-area__one-right-counter-img img {
    width: 100%;
    border-radius: 30px;
  }
}
/*==========================================================================
* Work Process Two style 
==========================================================================*/
.work-process__two-title {
  margin-bottom: 60px;
}
.work-process__two-title h2 {
  text-transform: none;
  margin-bottom: 60px;
}
.work-process__two-cards {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.work-process__two-cards-single {
  border: 1px solid var(--color-4);
  margin-right: 25px;
  padding: 35px 38px;
  border-radius: 20px;
  position: relative;
  outline: 7px solid var(--text-white);
  background-color: var(--bg-white);
  transition: 0.4s;
}
.work-process__two-cards-single:last-child {
  margin: 0;
}
.work-process__two-cards-single-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}
.work-process__two-cards-single-title-left h4 {
  font-size: 22px;
}
.work-process__two-cards-single-title-left span {
  margin-bottom: 12px;
  display: inline-block;
  font-size: 18px;
  font-weight: 700;
  color: var(--color-4);
}
.work-process__two-cards-single-title-right {
  font-size: 40px;
  color: var(--primary-color-1);
}
.work-process__two-cards-single p {
  color: var(--color-2);
  text-transform: none;
  margin-bottom: 28px;
}
.work-process__two-cards-single a i {
  color: var(--primary-color-1);
}
.work-process__two-cards-single .card-arrow-wrapper {
  position: absolute;
  right: -69px;
  z-index: -1;
  bottom: -45px;
}
.work-process__two-cards-single .card-arrow-wrapper .card-arrow-ingredient {
  position: relative;
}
.work-process__two-cards-single .card-arrow-wrapper .card-arrow-ingredient .arrow-head {
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-bottom: 12px solid var(--primary-color-1);
  position: absolute;
  top: 60px;
  right: -1px;
  transform: rotate(37deg);
}
.work-process__two-cards-single .card-arrow-wrapper .card-arrow-ingredient .arrow-body {
  width: 160px;
  height: 103px;
  background: transparent;
  border-radius: 50%;
  border: 2px dashed var(--primary-color-1);
  border-top: 2px solid var(--text-white);
  transition: 0.4s;
  z-index: -1;
}
.work-process__two-cards-single:hover {
  border: 1px solid var(--primary-color-1);
}

@media (max-width: 991px) {
  .work-process__two-cards {
    display: block;
  }
  .work-process__two-cards-single {
    margin-right: 0;
    margin-bottom: 30px;
  }
  .work-process__two-cards-single:last-child {
    margin-bottom: 0;
  }
  .work-process__two-cards-single .card-arrow-wrapper {
    display: none;
  }
}
/*==========================================================================
* Work Process Three style 
==========================================================================*/
.work-process__three {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  padding: 140px 0;
}
.work-process__three h2 {
  color: var(--text-white);
}
.work-process__three-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 5px solid var(--primary-color-1);
  padding-top: 75px;
  position: relative;
}
.work-process__three-card-single {
  background: var(--color-5);
  border: 1px solid var(--primary-color-1);
  padding: 35px 30px;
  border-radius: 20px;
  text-align: center;
  width: 28%;
}
.work-process__three-card-single.middle {
  width: 34%;
}
.work-process__three-card-single h3 {
  color: var(--text-white);
  font-size: 60px;
  margin-bottom: 10px;
}
.work-process__three-card-single h5 {
  color: var(--text-white);
  font-size: 22px;
  margin-bottom: 8px;
}
.work-process__three-card-single p {
  color: rgba(255, 255, 255, 0.8);
  text-transform: none;
}
.work-process__three-card-arrows img {
  position: absolute;
  top: 0;
}
.work-process__three-card-arrows .arrow-1 {
  top: -10px;
  left: 15%;
}
.work-process__three-card-arrows .arrow-2 {
  top: -10px;
  left: 50%;
}
.work-process__three-card-arrows .arrow-3 {
  top: -10px;
  right: 13%;
}

@media (max-width: 991px) {
  .work-process__three-card-arrows img {
    display: none;
  }
  .work-process__three-card {
    border-top: 0;
  }
  .work-process__three-card {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
  }
  .work-process__three-card-single {
    width: 75%;
    margin-bottom: 30px;
  }
  .work-process__three-card-single:last-child {
    margin-bottom: 0;
  }
  .work-process__three-card-single.middle {
    width: 75%;
  }
}
@media (max-width: 480px) {
  .work-process__three-card-single {
    width: 100%;
  }
  .work-process__three-card-single.middle {
    width: 100%;
  }
}
/*==========================================================================
* Work Process Four style 
==========================================================================*/
.work-process__four {
  background-color: var(--primary-color-1);
  padding-bottom: 110px;
  background-repeat: no-repeat;
  background-position: bottom left;
}
.work-process__four-title .subtitle-one {
  color: var(--text-white);
  background-color: rgba(255, 255, 255, 0.1490196078);
}
.work-process__four-title h2 {
  color: var(--text-white);
  margin-bottom: 30px;
}
.work-process__four-title p {
  color: var(--color-4);
  width: 85%;
  text-transform: none;
  margin-bottom: 40px;
}
.work-process__four .btn-one {
  color: var(--text-white);
  border-color: var(--bg-white);
}
.work-process__four .btn-one:hover {
  background-color: var(--bg-white);
  color: var(--text-heading-color);
}
.work-process__four .btn-one:hover i {
  color: var(--text-heading-color);
}
.work-process__four .call-us-box {
  display: inline-flex;
  margin-left: 30px;
  align-items: center;
}
.work-process__four .call-us-box-icon {
  display: inline-flex;
  background: rgba(255, 255, 255, 0.1019607843);
  width: 60px;
  height: 60px;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 30px;
  color: var(--text-white);
  margin-right: 30px;
}
.work-process__four .call-us-box span {
  font-size: 16px;
  color: var(--text-white);
  display: block;
  margin-bottom: 5px;
}
.work-process__four .call-us-box a {
  display: block;
  font-size: 22px;
  color: var(--text-white);
  font-weight: 600;
}
.work-process__four-single-item {
  position: relative;
}
.work-process__four-single-item i {
  background: rgba(255, 255, 255, 0.1019607843);
  width: 80px;
  height: 80px;
  font-size: 40px;
  color: var(--text-white);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  margin-bottom: 20px;
}
.work-process__four-single-item h3 {
  position: absolute;
  top: 15px;
  right: 65px;
  font-size: 80px;
  opacity: 0.1;
  color: var(--text-white);
}
.work-process__four-single-item h4 {
  color: var(--text-white);
  margin-bottom: 10px;
}
.work-process__four-single-item p {
  color: var(--text-white);
  width: 90%;
  text-transform: none;
}

@media (max-width: 535px) {
  .work-process__four .call-us-box {
    display: flex;
    margin-left: 0;
    align-items: center;
    margin-top: 20px;
  }
}
/*==========================================================================
* Counter One Style
==========================================================================*/
.counter-up__one-counters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  background-color: var(--primary-color-1);
  padding: 60px 0;
  border-radius: 20px;
  margin-top: -103px;
  z-index: 3;
  position: relative;
}
.counter-up__one-counters .main-counter {
  display: flex;
  align-items: center;
  justify-content: center;
}
.counter-up__one-single-counter {
  position: relative;
  padding: 8px 0;
  width: 25%;
  border-right: 1px solid rgba(204, 204, 204, 0.4392156863);
}
.counter-up__one-single-counter:last-child {
  border: 0;
}
.counter-up__one-single-counter h1 {
  color: var(--text-white);
  opacity: 0.1;
  font-size: 84px;
}
.counter-up__one-single-counter span {
  font-size: 18px;
  color: var(--text-white);
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

@media (max-width: 1199px) {
  .counter-up__one-counters {
    padding: 30px 45px;
  }
  .counter-up__one-single-counter h1 {
    font-size: 75px;
  }
}
@media (max-width: 1199px) and (max-width: 991px) {
  .counter-up__one-single-counter {
    width: 50%;
  }
  .counter-up__one-single-counter:nth-child(2) {
    border-right: 0;
  }
}
@media (max-width: 535px) {
  .counter-up__one-single-counter h1 {
    font-size: 60px;
  }
  .counter-up__one-single-counter span {
    font-size: 16px;
  }
}
@media (max-width: 439px) {
  .counter-up__one-single-counter {
    width: 100%;
    border-bottom: 1px solid rgba(204, 204, 204, 0.4392156863);
    border-right: 0;
    padding: 18px 0;
  }
  .counter-up__one-single-counter h1 {
    font-size: 72px;
  }
  .counter-up__one-single-counter span {
    font-size: 18px;
  }
  .counter-up__one-counters {
    padding: 0px 60px;
  }
}
/*==========================================================================
* FAQ Style One
==========================================================================*/
.faq__one {
  padding-top: 120px;
  padding-bottom: 87px;
}
.faq__one-title {
  margin-bottom: 60px;
}
.faq__one-title h2 {
  text-transform: none;
  max-width: 91%;
}
.faq__one-image {
  position: relative;
}
.faq__one-image-wrapper {
  border: 35px solid transparent;
  outline: 2px dashed var(--primary-color-1);
  border-radius: 50%;
  margin-left: 53px;
}
.faq__one-image-wrapper img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.faq__one-image-shape .shape {
  position: absolute;
}
.faq__one-image-shape .shape-1 {
  width: 40px;
  height: 40px;
  background-color: var(--primary-color-1);
  border-radius: 50%;
  right: 27px;
  bottom: 100px;
}
.faq__one-image-shape .shape-2 {
  right: 26px;
  top: 0;
}
.faq__one-image-shape .shape-3 {
  bottom: 65px;
  left: 74px;
  z-index: -1;
}
.faq__one-image-shape .counter-shape {
  position: absolute;
  background-color: var(--primary-color-1);
  color: var(--text-white);
  width: 130px;
  height: 130px;
  border-radius: 50%;
  text-align: center;
  top: 5%;
  left: 13%;
}
.faq__one-image-shape .counter-shape span {
  display: inline-block;
  margin-top: 35px;
  font-size: 30px;
  font-weight: 600;
  text-transform: lowercase;
}
.faq__one-image-shape .counter-shape p {
  color: var(--color-4);
}
.faq__one .faq-collapse-item {
  margin-bottom: 30px;
}
.faq__one .faq-collapse-item-card {
  border: 1px solid var(--color-4);
  border-radius: 5px;
  transition: 0.5s;
  border-left: 3px solid var(--color-4);
}
.faq__one .faq-collapse-item-card.active {
  border-color: transparent;
  border-left: 3px solid var(--primary-color-1);
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
}
.faq__one .faq-collapse-item-card.active:hover {
  border-left: 3px solid var(--primary-color-1);
}
.faq__one .faq-collapse-item-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 22px 27px;
  cursor: pointer;
}
.faq__one .faq-collapse-item-card-header h6 {
  font-size: 22px;
}
.faq__one .faq-collapse-item-card-header .fa-minus-circle {
  color: var(--primary-color-1);
}
.faq__one .faq-collapse-item-card-header-content {
  text-transform: none;
  padding: 0 26px;
  color: var(--color-2);
}
.faq__one .faq-collapse-item-card-header-content p {
  margin-bottom: 27px;
}

@media (max-width: 1199px) {
  .faq__one-image-wrapper {
    margin-left: 0;
  }
}
@media (max-width: 991px) {
  .faq__one {
    padding-top: 80px;
  }
}
@media (max-width: 767px) {
  .faq__one-image-wrapper {
    border: 10px solid transparent;
  }
}
@media (max-width: 575px) {
  .faq__one {
    padding-top: 60px;
  }
}
/*==========================================================================
* FAQ Style Two
==========================================================================*/
.faq__two {
  background-color: var(--color-1);
}
.faq__two-title {
  margin-bottom: 60px;
}
.faq__two-title h2 {
  text-transform: none;
  margin-bottom: 35px;
}
.faq__two-title p {
  color: var(--color-2);
  text-transform: none;
  font-size: 17px;
}
.faq__two .award {
  background-color: var(--text-white);
  display: table;
  position: relative;
  margin-left: 40px;
}
.faq__two .award-wrapper {
  display: flex;
  align-items: center;
  padding: 25px 47px;
  border-left: 2px solid var(--primary-color-1);
  border-radius: 10px;
}
.faq__two .award-wrapper span {
  font-size: 18px;
}
.faq__two .award-count {
  display: flex;
  align-items: center;
  margin-right: 15px;
}
.faq__two .award-count h3 {
  font-size: 60px;
}
.faq__two .award-icon {
  position: absolute;
  left: -10%;
  background-color: var(--primary-color-1);
  width: 64px;
  height: 64px;
  text-align: center;
  line-height: 64px;
  border-radius: 50%;
  color: var(--text-white);
  font-size: 30px;
}
.faq__two .faq-collapse-item {
  margin-bottom: 30px;
}
.faq__two .faq-collapse-item-card {
  border-radius: 5px;
  transition: 0.5s;
  background-color: var(--bg-white);
}
.faq__two .faq-collapse-item-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 27px;
  cursor: pointer;
}
.faq__two .faq-collapse-item-card-header h6 {
  font-size: 22px;
}
.faq__two .faq-collapse-item-card-header i {
  color: var(--color-4);
  font-weight: 300;
  font-size: 18px;
}
.faq__two .faq-collapse-item-card-header .fa-minus-circle {
  color: var(--primary-color-1);
}
.faq__two .faq-collapse-item-card-header-content {
  text-transform: none;
  padding: 0 26px;
  color: var(--color-2);
}
.faq__two .faq-collapse-item-card-header-content p {
  padding-bottom: 27px;
}

@media (max-width: 359px) {
  .faq__two .award {
    margin-left: 0;
  }
  .award-icon {
    display: none;
  }
}
/*==========================================================================
* FAQ Style Three
==========================================================================*/
.faq__three {
  padding-top: 120px;
  padding-bottom: 87px;
}
.faq__three-title {
  margin-bottom: 60px;
}
.faq__three-title h2 {
  text-transform: none;
  margin-bottom: 35px;
}
.faq__three-title p {
  color: var(--color-2);
  text-transform: none;
  font-size: 17px;
}
.faq__three .faq-collapse-item {
  margin-bottom: 25px;
  box-shadow: rgba(100, 100, 111, 0.1) 0px 7px 29px 0px;
}
.faq__three .faq-collapse-item-card {
  border-radius: 5px;
  transition: 0.5s;
  background-color: var(--bg-white);
}
.faq__three .faq-collapse-item-card-header {
  display: flex;
  align-items: center;
  padding: 20px 18px;
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 5px;
  margin-bottom: 20px;
  transition: 0.4s;
}
.faq__three .faq-collapse-item-card-header h6 {
  font-size: 18px;
}
.faq__three .faq-collapse-item-card-header-content {
  text-transform: none;
  padding: 0 26px;
  color: var(--color-2);
}
.faq__three .faq-collapse-item-card-header-content p {
  padding-bottom: 27px;
}
.faq__three .faq-collapse-item-card-header i {
  background-color: var(--primary-color-1);
  color: var(--text-white);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 14px;
  text-align: center;
  line-height: 30px;
  margin-right: 20px;
}
.faq__three .faq-collapse-item-card.active .faq-collapse-item-card-header {
  border: 2px solid var(--color-4);
  padding: 25px 18px;
}

/*==========================================================================
* Blog One Style
==========================================================================*/
.blog__one-title {
  margin-bottom: 50px;
}
.blog__one-title h2 {
  text-transform: none;
}
.blog__one-single-blog {
  position: relative;
}
.blog__one-single-blog-image {
  border-radius: 15px 5px 15px 15px;
  overflow: hidden;
  height: 260px;
}
.blog__one-single-blog-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.blog__one-single-blog-date {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--color-1);
  text-align: center;
  padding: 11px 14px;
  border-radius: 0 5px 0 5px;
}
.blog__one-single-blog-date span {
  display: block;
}
.blog__one-single-blog-date .date {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 3px;
}
.blog__one-single-blog-date .month {
  font-size: 18px;
  color: var(--color-2);
}
.blog__one-single-blog-content {
  position: relative;
  width: 90%;
  background-color: var(--bg-white);
  padding: 30px;
  border-radius: 15px;
  margin: auto;
  margin-top: -58px;
  transition: 0.5s;
  box-shadow: 0px 5px 60px 0px rgba(0, 0, 0, 0.0509803922);
  border-bottom: 3px solid transparent;
}
.blog__one-single-blog-content-top {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
}
.blog__one-single-blog-content-top span {
  font-size: 14px;
  color: var(--color-2);
  margin-right: 27px;
}
.blog__one-single-blog-content-top span i {
  color: var(--primary-color-1);
  margin-right: 8px;
}
.blog__one-single-blog-content .blog-heading {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 30px;
  line-height: 32px;
  transition: 0.4s;
}
.blog__one-single-blog-content .blog-heading:hover {
  color: var(--primary-color-1);
}
.blog__one-single-blog-content a {
  display: block;
  text-align: left;
}
.blog__one-single-blog-content .btn-three i {
  margin-left: 5px;
  font-size: 17px;
  font-weight: 500;
}
.blog__one-single-blog:hover .blog__one-single-blog-content {
  border-bottom: 3px solid var(--primary-color-1);
}

@media (max-width: 1399px) {
  .blog__one-single-blog-content-top span {
    margin-right: 15px;
  }
  .blog__one-single-blog-content .blog-heading {
    font-size: 20px;
  }
}
@media (max-width: 480px) {
  .blog__one-single-blog-content {
    width: 100%;
    margin-top: -58px;
    margin-left: 0;
  }
}
/*==========================================================================
* Blog Two Style
==========================================================================*/
.blog__two-title {
  margin-bottom: 62px;
}
.blog__two-title h2 {
  text-transform: none;
  margin: auto;
}
.blog__two-single-blog-content {
  box-shadow: 0px 5px 60px 0px rgba(0, 0, 0, 0.0509803922);
  padding: 20px 40px 40px;
  border-radius: 0 0 20px 20px;
}
.blog__two-single-blog-content-top {
  background-color: var(--color-1);
  padding: 10px 20px 9px 14px;
  display: table;
  margin-bottom: 10px;
  border-radius: 5px;
}
.blog__two-single-blog-content-top span {
  font-size: 14px;
  margin-right: 18px;
  color: var(--color-2);
}
.blog__two-single-blog-content-top span i {
  font-size: 13px;
  color: var(--primary-color-1);
  margin-right: 10px;
}
.blog__two-single-blog-content-title {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 36px;
  text-transform: none;
  display: block;
  transition: 0.4s;
}
.blog__two-single-blog-content-title:hover {
  color: var(--primary-color-1);
}
.blog__two-single-blog-content .btn-three a i {
  font-size: 14px;
  color: var(--primary-color-1);
}
.blog__two-single-blog-img {
  position: relative;
}
.blog__two-single-blog-img img {
  width: 100%;
  border-radius: 20px 20px 0 0;
}
.blog__two-single-blog-date {
  position: absolute;
  bottom: 20px;
  right: 30px;
  background-color: var(--primary-color-1);
  text-align: center;
  padding: 10px 18px;
  border-radius: 10px;
}
.blog__two-single-blog-date span {
  display: block;
}
.blog__two-single-blog-date .date {
  font-size: 22px;
  color: var(--text-white);
  font-weight: 700;
  margin-bottom: 3px;
}
.blog__two-single-blog-date .month {
  font-size: 18px;
  color: var(--text-white);
}

@media (max-width: 535px) {
  .blog__two-left-sidebar-item-content a {
    font-size: 18px;
  }
  .blog__two-left-sidebar-item-content-top {
    margin-bottom: 10px;
  }
  .blog__two-left-sidebar {
    padding: 18px 16px;
  }
  .blog__two-left-sidebar-item-content-top span {
    margin-right: 10px;
  }
}
@media (max-width: 480px) {
  .blog__two-left-sidebar-item {
    flex-direction: column;
  }
  .blog__two-left-sidebar-item-img {
    width: 100%;
    margin-bottom: 19px;
  }
  .blog__two-left-sidebar-item-img img {
    width: 100%;
  }
  .blog__two-left-sidebar-item-content {
    margin-left: 0;
    width: 100%;
  }
}
@media (max-width: 359px) {
  .blog__two-right-content-top span {
    margin-right: 10px;
  }
  .blog__two-right-content {
    padding: 20px 20px;
  }
}
/*==========================================================================
* Blog Three Style
==========================================================================*/
.blog__three {
  background-color: var(--color-1);
}
.blog__three-title h2 {
  text-transform: none;
  margin: auto;
}
.blog__three-title-btn a i {
  color: var(--primary-color-1);
}
.blog__three-single-blog {
  display: flex;
  align-items: center;
}
.blog__three-single-blog-content {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.5);
  padding: 25px 33px;
  border-radius: 20px;
  margin-left: -10%;
  z-index: 153;
  background-color: var(--bg-white);
  transition: 0.4s;
}
.blog__three-single-blog-content-top {
  margin-bottom: 18px;
  display: flex;
  justify-content: start;
  align-items: center;
  flex-wrap: wrap;
}
.blog__three-single-blog-content-top span {
  font-size: 14px;
  margin-right: 18px;
  color: var(--color-2);
}
.blog__three-single-blog-content-top span i {
  font-size: 14px;
  color: var(--primary-color-1);
  margin-right: 5px;
}
.blog__three-single-blog-content-title {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 28px;
  line-height: 36px;
  text-transform: none;
  display: block;
  transition: 0.4s;
}
.blog__three-single-blog-content-title:hover {
  color: var(--primary-color-1);
}
.blog__three-single-blog-content p {
  text-transform: none;
  font-size: 17px;
  color: var(--color-2);
  margin-bottom: 30px;
}
.blog__three-single-blog-content .btn-three a i {
  font-size: 14px;
  color: var(--primary-color-1);
}
.blog__three-single-blog-img {
  width: 75%;
  height: 310px;
  overflow: hidden;
  border-radius: 21px;
  position: relative;
  transition: 0.4s;
}
.blog__three-single-blog-img-date {
  position: absolute;
  top: 0;
  left: 0;
  background-color: var(--bg-white);
  padding: 8px 15px 8px 15px;
  border-radius: 20px 0 10px 0;
  font-size: 16px;
  line-height: 1.62;
  text-align: center;
}
.blog__three-single-blog-img-date span {
  color: var(--color-2);
  font-weight: 300;
}
.blog__three-single-blog-img img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.blog__three-single-blog:hover .blog__three-single-blog-img img {
  transform: scale(1.1);
}
.blog__three-single-blog:hover .blog__three-single-blog-content {
  transform: translateX(-8px);
}

@media (max-width: 767px) {
  .blog__three-title {
    margin-bottom: 0;
  }
  .blog__three-right-img {
    width: 90%;
  }
}
@media (max-width: 575px) {
  .blog__three-left-sidebar-item {
    flex-direction: column;
  }
  .blog__three-left-sidebar-item-img {
    width: 100%;
  }
  .blog__three-left-sidebar-item-content {
    width: 100%;
    padding: 20px 23px;
  }
}
@media (max-width: 535px) {
  .blog__three-right {
    flex-direction: column;
  }
  .blog__three-right-content {
    margin-left: 0;
    margin-top: -20%;
  }
  .blog__three-right-img {
    width: 100%;
  }
}
@media (max-width: 480px) {
  .blog__three-single-blog {
    flex-direction: column;
  }
  .blog__three-single-blog-img {
    width: 95%;
  }
  .blog__three-single-blog-content {
    margin-left: 0;
    margin-top: -10%;
  }
}
/*==========================================================================
* Blog Four Style
==========================================================================*/
.blog__four-title {
  margin-bottom: 62px;
}
.blog__four-title h2 {
  text-transform: none;
  margin: auto;
}
.blog__four-single-blog {
  margin-bottom: 35px;
}
.blog__four-single-blog-content {
  border: 2px solid var(--color-4);
  border-top: 0;
  padding: 20px 41px;
  padding-bottom: 15px;
  border-radius: 0 0 20px 20px;
  text-align: center;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.5);
}
.blog__four-single-blog-content-top {
  background-color: var(--color-1);
  padding: 10px 25px;
  transform: translateY(-40px);
  margin: auto;
  margin-bottom: -20px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  width: 290px;
  flex-wrap: wrap;
  -moz-column-gap: 24px;
       column-gap: 24px;
}
.blog__four-single-blog-content-top span {
  font-size: 14px;
  color: var(--color-2);
}
.blog__four-single-blog-content-top span i {
  font-size: 13px;
  color: var(--primary-color-1);
  margin-right: 10px;
}
.blog__four-single-blog-content-title {
  font-size: 22px;
  font-weight: 700;
  line-height: 36px;
  text-transform: none;
  display: block;
  transition: 0.4s;
}
.blog__four-single-blog-content-title:hover {
  color: var(--primary-color-1);
}
.blog__four-single-blog-content .btn-one {
  border-color: var(--color-4);
  transform: translateY(45px);
  background-color: var(--bg-white);
  padding: 15px 32px;
}
.blog__four-single-blog-content .btn-one:hover {
  border-color: var(--primary-color-1);
  background-color: var(--primary-color-1);
}
.blog__four-single-blog-img {
  border-radius: 15px 15px 0 0;
  overflow: hidden;
  position: relative;
}
.blog__four-single-blog-img img {
  width: 100%;
}
.blog__four-single-blog-date {
  position: absolute;
  top: 0;
  left: 0;
  background: var(--primary-color-1);
  color: var(--color-1);
  padding: 10px 20px;
  font-size: 20px;
  font-weight: 600;
  border-radius: 0 0 10px 0;
}

@media (max-width: 575px) {
  .skill-area__one-left-video {
    width: 284px;
    height: 200px;
  }
}
@media (max-width: 359px) {
  .blog__four-right-content-top {
    width: 100%;
    justify-content: center;
    row-gap: 3px;
  }
}
/*==========================================================================
* Blog With Sidebar Style
==========================================================================*/
.blog__sidebar-single-blog {
  position: relative;
  margin-bottom: 60px;
}
.blog__sidebar-single-blog:last-child {
  margin-bottom: 0;
}
.blog__sidebar-single-blog-image {
  border-radius: 10px 10px 0 10px;
  overflow: hidden;
}
.blog__sidebar-single-blog-image img {
  width: 100%;
  height: 552px;
  -o-object-fit: cover;
     object-fit: cover;
}
.blog__sidebar-single-blog-date {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--color-1);
  text-align: center;
  padding: 8px 15px;
  border-radius: 0 5px 0 5px;
}
.blog__sidebar-single-blog-date span {
  display: block;
  font-size: 12px;
  color: var(--color-2);
}
.blog__sidebar-single-blog-content {
  position: relative;
  width: 90%;
  background-color: var(--bg-white);
  padding: 30px 41px;
  border-radius: 0 10px 10px 10px;
  margin-top: -80px;
  transition: 0.5s;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 50px;
}
.blog__sidebar-single-blog-content-top {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.blog__sidebar-single-blog-content-top span {
  font-size: 14px;
  color: var(--color-2);
  margin-right: 27px;
}
.blog__sidebar-single-blog-content-top span i {
  color: var(--text-heading-color);
  margin-right: 3px;
}
.blog__sidebar-single-blog-content p {
  text-transform: none;
  color: var(--color-2);
  font-size: 17px;
  margin-bottom: 40px;
}
.blog__sidebar-single-blog-content .blog-heading {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.25;
  text-transform: none;
  transition: 0.4s;
  display: block;
}
.blog__sidebar-single-blog-content .blog-heading:hover {
  color: var(--primary-color-1);
}
.blog__sidebar-single-blog-content .btn-one {
  display: table;
  text-align: left;
  border-radius: 5px;
}
.blog__sidebar.no-sidebar .blog__sidebar-single-blog-content {
  border-radius: 10px;
  margin-left: 50%;
  transform: translateX(-50%);
}

@media (max-width: 991px) {
  .blog__sidebar-single-blog-content {
    width: 100%;
  }
}
@media (max-width: 575px) {
  .blog__sidebar-single-blog-content {
    padding: 18px 15px;
  }
  .blog__sidebar-single-blog-content .blog-heading {
    font-size: 24px;
  }
}
/*==========================================================================
* Blog Sidebar Style
==========================================================================*/
.sidebar-item {
  margin-left: 8px;
}
.sidebar-item-single {
  background-color: var(--color-1);
  padding: 35px 40px;
  border-radius: 5px;
  margin-bottom: 35px;
}
.sidebar-item-single:last-child {
  margin-bottom: 0;
}
.sidebar-item-single h3 {
  margin-bottom: 15px;
}
.sidebar-item-single.sidebar-search form {
  display: flex;
  margin-top: 28px;
}
.sidebar-item-single.sidebar-search form input {
  border: 0;
  height: 58px;
  padding: 10px 20px;
  border-radius: 10px 0 0 10px;
}
.sidebar-item-single.sidebar-search form input::-moz-placeholder {
  font-size: 18px;
}
.sidebar-item-single.sidebar-search form input::placeholder {
  font-size: 18px;
}
.sidebar-item-single.sidebar-search form button {
  padding: 10px 21px;
  border-radius: 0 10px 10px 0;
}
.sidebar-item-single.sidebar-category .single-category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--bg-white);
  padding: 9px 23px;
  margin-bottom: 20px;
  border-radius: 10px;
}
.sidebar-item-single.sidebar-category .single-category:last-child {
  margin-bottom: 0;
}
.sidebar-item-single.sidebar-category .single-category-name {
  display: flex;
  align-items: center;
}
.sidebar-item-single.sidebar-category .single-category-name i {
  color: var(--primary-color-1);
  margin-right: 15px;
}
.sidebar-item-single.sidebar-category .single-category .category-count {
  font-size: 12px;
  color: var(--color-2);
}
.sidebar-item-single.sidebar-item-single .blog-post-single {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.sidebar-item-single.sidebar-item-single .blog-post-single:last-child {
  margin: 0;
}
.sidebar-item-single.sidebar-item-single .blog-post-single-img {
  width: 60%;
}
.sidebar-item-single.sidebar-item-single .blog-post-single-content {
  background-color: var(--bg-white);
  padding: 10px 13px;
  border-radius: 0 5px 5px 0;
}
.sidebar-item-single.sidebar-item-single .blog-post-single-content-top {
  margin-bottom: 8px;
}
.sidebar-item-single.sidebar-item-single .blog-post-single-content-top span {
  font-size: 12px;
  color: var(--color-2);
  margin-right: 20px;
}
.sidebar-item-single.sidebar-item-single .blog-post-single-content-top span i {
  color: var(--primary-color-1);
  margin-right: 5px;
}
.sidebar-item-single.sidebar-item-single .blog-post-single-content a {
  font-size: 18px;
  font-weight: 700;
  transition: 0.4s;
  text-transform: none;
}
.sidebar-item-single.sidebar-item-single .blog-post-single-content a:hover {
  color: var(--primary-color-1);
}
.sidebar-item-single.tags a {
  font-size: 18px;
  background-color: var(--bg-white);
  padding: 8px 12px;
  display: inline-block;
  margin: 8px 6px;
  color: var(--color-2);
  border-radius: 5px;
}

@media (max-width: 575px) {
  .sidebar-item-single {
    padding: 20px 22px;
  }
}
/*==========================================================================
* Blog Five Style
==========================================================================*/
.blog__five-title {
  margin-bottom: 62px;
}
.blog__five-title h2 {
  text-transform: none;
  margin: auto;
}
.blog__five-single-blog-content {
  box-shadow: 0px 5px 60px 0px rgba(0, 0, 0, 0.0509803922);
  padding: 20px 40px 40px;
  border-radius: 0 0 20px 20px;
}
.blog__five-single-blog-content-top {
  margin-bottom: 10px;
}
.blog__five-single-blog-content-top span {
  font-size: 14px;
  margin-right: 18px;
  color: var(--color-2);
}
.blog__five-single-blog-content-top span i {
  font-size: 13px;
  color: var(--primary-color-1);
  margin-right: 10px;
}
.blog__five-single-blog-content-title {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 36px;
  text-transform: none;
  display: block;
  transition: 0.4s;
}
.blog__five-single-blog-content-title:hover {
  color: var(--primary-color-1);
}
.blog__five-single-blog-content .btn-three a i {
  font-size: 14px;
  color: var(--primary-color-1);
}
.blog__five-single-blog-img {
  position: relative;
}
.blog__five-single-blog-img img {
  width: 100%;
  border-radius: 20px 20px 0 0;
}
.blog__five-single-blog-date {
  position: absolute;
  bottom: 20px;
  left: 30px;
  background-color: var(--primary-color-1);
  text-align: center;
  padding: 8px 20px;
  border-radius: 5px;
}
.blog__five-single-blog-date span {
  display: block;
}
.blog__five-single-blog-date .date {
  font-size: 22px;
  color: var(--text-white);
  font-weight: 700;
  margin-bottom: 3px;
}
.blog__five-single-blog-date .month {
  font-size: 18px;
  color: var(--text-white);
}

@media (max-width: 535px) {
  .blog__five-left-sidebar-item-content a {
    font-size: 18px;
  }
  .blog__five-left-sidebar-item-content-top {
    margin-bottom: 10px;
  }
  .blog__five-left-sidebar {
    padding: 18px 16px;
  }
  .blog__five-left-sidebar-item-content-top span {
    margin-right: 10px;
  }
}
@media (max-width: 480px) {
  .blog__five-left-sidebar-item {
    flex-direction: column;
  }
  .blog__five-left-sidebar-item-img {
    width: 100%;
    margin-bottom: 19px;
  }
  .blog__five-left-sidebar-item-img img {
    width: 100%;
  }
  .blog__five-left-sidebar-item-content {
    margin-left: 0;
    width: 100%;
  }
}
@media (max-width: 359px) {
  .blog__five-right-content-top span {
    margin-right: 10px;
  }
  .blog__five-right-content {
    padding: 20px 20px;
  }
}
/*==========================================================================
* Blog Details Style
==========================================================================*/
.blog__details-thumb {
  position: relative;
}
.blog__details-thumb .date {
  position: absolute;
  color: var(--text-white);
  background-color: var(--primary-color-1);
  font-size: 18px;
  right: 0;
  padding: 10px 20px;
  border-radius: 0 5px 0 5px;
}
.blog__details-thumb img {
  width: 100%;
}
.blog__details-content-top {
  margin-top: 32px;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.blog__details-content-top span {
  font-size: 14px;
  color: var(--color-2);
  margin-right: 27px;
}
.blog__details-content-top span i {
  color: var(--text-heading-color);
  margin-right: 3px;
}
.blog__details-content h2 {
  font-size: 32px;
  margin-bottom: 25px;
  text-transform: none;
}
.blog__details-content p {
  text-transform: none;
  color: var(--color-2);
  font-size: 17px;
  margin-bottom: 28px;
}
.blog__details-quote {
  background-color: var(--color-1);
  padding: 38px 33px;
  margin-bottom: 38px;
}
.blog__details-quote-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
}
.blog__details-quote-avatar {
  display: flex;
  align-items: center;
}
.blog__details-quote-avatar-wrapper {
  width: 45px;
  height: 45px;
  border-radius: 5px;
  overflow: hidden;
}
.blog__details-quote-avatar-wrapper img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.blog__details-quote-avatar h4 {
  font-size: 18px;
  color: var(--primary-color-1);
  margin-left: 20px;
  text-transform: none;
}
.blog__details-quote p {
  margin-bottom: 0;
}
.blog__details-portfolio {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 35px;
  flex-wrap: wrap;
  row-gap: 25px;
}
.blog__details-portfolio-middle {
  margin: 0;
  padding: 0;
}
.blog__details-portfolio-middle li {
  list-style: none;
  margin-bottom: 10px;
  font-size: 18px;
  color: var(--color-2);
  margin-bottom: 18px;
}
.blog__details-portfolio-middle li:last-child {
  margin-bottom: 0;
}
.blog__details-portfolio-middle li i {
  color: var(--primary-color-1);
  margin-right: 3px;
  font-size: 15px;
}
.blog__details-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 50px;
  flex-wrap: wrap;
  row-gap: 25px;
}
.blog__details-pagination-prev {
  text-align: left;
}
.blog__details-pagination-prev .pagination-btn {
  margin-right: 30px;
}
.blog__details-pagination-next {
  text-align: right;
}
.blog__details-pagination-next .pagination-btn {
  margin-left: 30px;
}
.blog__details-pagination-text span {
  font-size: 18px;
  text-transform: none;
  display: block;
  color: var(--color-2);
}
.blog__details-pagination-text span:last-child {
  color: var(--text-heading-color);
  font-weight: 700;
}
.blog__details-pagination-btn {
  display: flex;
  align-items: center;
}
.blog__details-pagination-btn .pagination-btn {
  background-color: transparent;
  width: 60px;
  height: 60px;
  border: 1px solid var(--color-4);
  text-align: center;
  line-height: 60px;
  border-radius: 5px;
  transition: 0.4s;
  font-size: 19px;
}
.blog__details-pagination-btn .pagination-btn:hover {
  border-color: var(--primary-color-1);
  color: var(--primary-color-1);
}
.blog__details-comments {
  margin-top: 55px;
}
.blog__details-comments h3 {
  margin-bottom: 18px;
}
.blog__details-single-comment {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 20px;
}
.blog__details-single-comment:last-child {
  margin-bottom: 0;
}
.blog__details-single-comment-user-pic img {
  height: 85px;
  width: 85px;
  min-width: 85px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}
.blog__details-single-comment-body {
  border: 1px solid var(--color-4);
  border-radius: 5px;
  padding: 34px 33px;
}
.blog__details-single-comment-body-top {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: wrap;
  row-gap: 8px;
}
.blog__details-single-comment-body-top > * {
  margin-right: 20px;
}
.blog__details-single-comment-body-top span {
  font-size: 18px;
  color: var(--color-2);
}
.blog__details-single-comment-body-top .user-socials {
  display: flex;
  justify-content: space-between;
  align-items: center;
  list-style-type: none;
  margin: 0;
  padding: 0;
  margin-left: 10px;
}
.blog__details-single-comment-body-top .user-socials li {
  margin-right: 15px;
  font-size: 13px;
  transition: 0.4s;
}
.blog__details-single-comment-body-top .user-socials li:last-child {
  margin: 0;
}
.blog__details-single-comment-body-top .user-socials li:hover {
  color: var(--primary-color-1);
}
.blog__details-single-comment-body p {
  text-transform: none;
  color: var(--color-2);
  margin-bottom: 20px;
  font-size: 17px;
}
.blog__details-single-comment-body .comment-reply-btn {
  font-size: 18px;
  font-weight: 700;
  transition: 0.4s;
}
.blog__details-single-comment-body .comment-reply-btn:hover {
  color: var(--primary-color-1);
}
.blog__details-comment-form {
  margin-top: 55px;
  padding: 55px 58px;
  background-color: var(--color-1);
  border-radius: 5px;
}
.blog__details-comment-form h3 {
  text-transform: none;
  margin-bottom: 30px;
}
.blog__details-comment-form p {
  color: var(--color-2);
  text-transform: none;
  margin-bottom: 25px;
}
.blog__details-comment-form input {
  border: 1px solid transparent;
  width: 100%;
  transition: 0.5s;
}
.blog__details-comment-form input:focus {
  border-color: var(--primary-color-1);
}
.blog__details-comment-form input[type=text] {
  margin-bottom: 30px;
}
.blog__details-comment-form textarea {
  border: 1px solid transparent;
  margin-bottom: 25px;
  height: 130px;
}
.blog__details-comment-form textarea:focus {
  border-color: var(--primary-color-1);
}

@media (max-width: 991px) {
  .blog__details-single-comment {
    flex-direction: column;
    row-gap: 10px;
    margin-bottom: 45px;
  }
}
@media (max-width: 767px) {
  .blog__details-comment-form {
    padding: 20px 30px;
  }
}
@media (max-width: 575px) {
  .blog__details-single-comment-body {
    padding: 20px 20px;
  }
}
@media (max-width: 359px) {
  .blog__details-content h2 {
    font-size: 25px;
  }
}
/*==========================================================================
* Testimonial One Style
==========================================================================*/
.testimonial__one {
  overflow: hidden;
  padding-bottom: 0;
}
.testimonial__one-left {
  background-size: cover;
  background-position: center center;
  padding: 145px 55px;
  padding-right: 80px;
  border-radius: 30px;
  width: 800px;
  position: relative;
  overflow: hidden;
}
.testimonial__one-left::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: var(--primary-color-1);
  top: 0;
  left: 0;
  opacity: 0.9;
}
.testimonial__one-left-title {
  z-index: 4;
  position: relative;
}
.testimonial__one-left-title .subtitle-one {
  color: var(--text-white);
  background: rgba(255, 255, 255, 0.168627451);
}
.testimonial__one-left-title h2 {
  color: var(--text-white);
  margin-bottom: 45px;
  text-transform: none;
}
.testimonial__one-left-title h2 .highlighted::before {
  background-color: var(--color-3);
}
.testimonial__one-left-title .btn-one {
  color: var(--text-white);
  border-color: var(--bg-white);
}
.testimonial__one-left-title .btn-one:hover {
  background-color: var(--bg-white);
  color: var(--text-heading-color);
}
.testimonial__one-left-title .btn-one:hover i {
  color: var(--text-heading-color);
}
.testimonial__one-right {
  background: var(--bg-white);
  z-index: 3;
  position: relative;
  padding: 65px 57px;
  padding-bottom: 135px;
  margin-top: 44%;
  transform: translateY(-50%);
  border-radius: 30px;
  box-shadow: rgba(99, 99, 99, 0.09) 0px 2px 8px 0px;
}
.testimonial__one-right .single-slider-user {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}
.testimonial__one-right .single-slider-user-name h4 {
  font-size: 22px;
}
.testimonial__one-right .single-slider-user-name span {
  font-size: 18px;
  color: var(--color-2);
}
.testimonial__one-right .single-slider-user-rating i {
  color: #F3DC65;
  font-size: 20px;
  margin: 0 1px;
}
.testimonial__one-right .single-slider-user-rating .not-rated {
  color: var(--color-4);
}
.testimonial__one-right .single-slider p {
  font-size: 22px;
  color: var(--color-2);
  text-transform: none;
}
.testimonial__one-right .slider-quote {
  position: absolute;
  right: 60px;
  bottom: 40px;
}
.testimonial__one .swiper-button-prev, .testimonial__one .swiper-button-next {
  padding: 20px 23px;
  border-radius: 5px;
  transition: 0.4s;
  top: 84%;
  background: var(--color-1);
  font-size: 16px;
  font-weight: 300;
  color: var(--color-5);
}
.testimonial__one .swiper-button-prev:hover, .testimonial__one .swiper-button-next:hover {
  background: var(--primary-color-1);
  border-color: var(--primary-color-1);
  color: var(--text-white);
}
.testimonial__one .swiper-button-prev {
  left: 61px;
}
.testimonial__one .swiper-button-next {
  left: 120px;
}

@media (max-width: 1399px) {
  .testimonial__one-right {
    margin-top: 50%;
    padding: 35px 40px;
    padding-bottom: 125px;
  }
  .testimonial__one-right-bottom {
    bottom: 25px;
  }
}
@media (max-width: 1399px) {
  .testimonial__one-left {
    width: 634px;
  }
}
@media (max-width: 1199px) {
  .testimonial__one-right {
    margin-top: 60%;
  }
}
@media (max-width: 991px) {
  .testimonial__one-left {
    width: 100%;
    padding: 60px 55px 145px 55px;
  }
  .testimonial__one-right {
    margin-top: -100px;
    transform: translate(0);
    border-bottom: 1px solid var(--color-4);
  }
}
@media (max-width: 767px) {
  .testimonial__one-right .single-slider p {
    font-size: 18px;
  }
  .testimonial__one-right-bottom {
    right: 48px;
    bottom: 28px;
  }
}
@media (max-width: 480px) {
  .single-slider-user-rating {
    margin-top: 12px;
  }
  .slider-quote {
    display: none;
  }
  .slider-arrow {
    position: absolute;
    bottom: 50px;
    left: 25%;
  }
  .testimonial__one-left {
    padding: 145px 35px;
  }
  .testimonial__one-right {
    padding: 30px 30px;
    padding-bottom: 100px;
  }
}
/*==========================================================================
* Testimonial Two Style
==========================================================================*/
.testimonial__two {
  overflow: hidden;
  background: var(--color-1);
  padding-bottom: 0;
}
.testimonial__two-title {
  margin-bottom: 62px;
}
.testimonial__two-title h2 {
  text-transform: none;
  margin: auto;
}
.testimonial__two-left {
  background-size: cover;
  background-position: center center;
  border-radius: 30px;
  width: 800px;
  position: relative;
  overflow: hidden;
  height: 589px;
}
.testimonial__two-right {
  background: var(--bg-white);
  z-index: 3;
  position: relative;
  padding: 65px 57px;
  padding-bottom: 110px;
  margin-top: 45%;
  transform: translateY(-50%);
  border-radius: 20px;
  box-shadow: rgba(99, 99, 99, 0.09) 0px 2px 8px 0px;
}
.testimonial__two-right .slider-quote {
  position: absolute;
  top: -22px;
  right: 60px;
  transform: rotate(180deg);
}
.testimonial__two-right .single-slider-user {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.testimonial__two-right .single-slider-user-name h4 {
  font-size: 22px;
}
.testimonial__two-right .single-slider-user-name span {
  font-size: 18px;
  color: var(--color-2);
}
.testimonial__two-right .single-slider-user-rating i {
  color: #F3DC65;
  font-size: 20px;
  margin: 0 1px;
}
.testimonial__two-right .single-slider-user-rating .not-rated {
  color: var(--color-4);
}
.testimonial__two-right .single-slider p {
  font-size: 22px;
  color: var(--color-2);
  text-transform: none;
}
.testimonial__two .swiper-button-prev, .testimonial__two .swiper-button-next {
  border: 1px solid var(--color-4);
  color: var(--primary-color-1);
  padding: 20px 21px;
  border-radius: 5px;
  transition: 0.4s;
  top: 84%;
  border-radius: 50%;
  background: var(--color-1);
  font-weight: 300;
}
.testimonial__two .swiper-button-prev:hover, .testimonial__two .swiper-button-next:hover {
  background: var(--primary-color-1);
  border-color: var(--primary-color-1);
  color: var(--text-white);
}
.testimonial__two .swiper-button-prev {
  left: 61px;
}
.testimonial__two .swiper-button-next {
  left: 120px;
}

@media (max-width: 1399px) {
  .testimonial__two-right {
    margin-top: 50%;
    padding: 35px 40px;
    padding-bottom: 100px;
  }
}
@media (max-width: 1199px) {
  .testimonial__two-right {
    margin-top: 60%;
  }
}
@media (max-width: 991px) {
  .testimonial__two-left {
    width: 100%;
  }
  .testimonial__two-right {
    margin-top: -100px;
    transform: translate(0);
    border-bottom: 1px solid var(--color-4);
  }
  .testimonial__four-card {
    margin-left: -50% !important;
  }
}
@media (max-width: 767px) {
  .testimonial__four-card {
    width: 100% !important;
    padding: 35px !important;
  }
}
@media (max-width: 480px) {
  .testimonial__two-right {
    padding: 30px 30px;
    padding-bottom: 75px;
  }
}
/*==========================================================================
* Testimonial Three Style
==========================================================================*/
.testimonial__three {
  overflow: hidden;
  background: linear-gradient(var(--bg-white) 50%, var(--primary-color-1) 50%);
}
.testimonial__three-title {
  margin-bottom: 62px;
}
.testimonial__three-title h2 {
  text-transform: none;
  margin: auto;
}
.testimonial__three-left {
  background-size: cover;
  background-position: center center;
  border-radius: 30px 0 0 30px;
  height: 449px;
  position: relative;
  overflow: hidden;
}
.testimonial__three-right {
  background: var(--bg-white);
  z-index: 3;
  position: relative;
  padding: 50px 57px;
  margin-top: 5%;
  border-radius: 0 30px 30px 0;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
}
.testimonial__three-right .single-slider-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 35px;
}
.testimonial__three-right .single-slider-rating i {
  color: #F3DC65;
  font-size: 20px;
  margin: 0 1px;
}
.testimonial__three-right .single-slider-rating .not-rated {
  color: var(--color-4);
}
.testimonial__three-right .single-slider p {
  font-size: 22px;
  color: var(--color-2);
  text-transform: none;
  margin-bottom: 40px;
}
.testimonial__three-right .single-slider-user {
  display: flex;
  align-items: center;
}
.testimonial__three-right .single-slider-user-img {
  margin-right: 20px;
}

@media (max-width: 1399px) {
  .testimonial__three-right {
    margin-top: 3%;
  }
}
@media (max-width: 1199px) {
  .testimonial__three-right {
    margin-top: -15%;
    border-radius: 30px;
  }
}
@media (max-width: 767px) {
  .testimonial__three-right {
    padding: 27px 30px;
  }
}
@media (max-width: 575px) {
  .testimonial__four-card {
    width: 140% !important;
    padding: 35px !important;
  }
}
/*==========================================================================
* Testimonial Four Style
==========================================================================*/
.testimonial__four {
  background-image: url(../img/testimonial/testimonial-three.png);
  background-size: cover;
  background-position: top right;
  background-repeat: no-repeat;
  padding: 95px 0;
  width: 63%;
  display: block;
  position: relative;
  margin-left: 37%;
}
.testimonial__four::before {
  content: "";
  position: absolute;
  top: 0;
  left: -59%;
  width: 59%;
  height: 100%;
  background: var(--primary-color-1);
}
.testimonial__four-card {
  width: 650px;
  background: var(--bg-white);
  padding: 50px 100px;
  text-align: center;
  border-radius: 20px;
  margin-left: -33%;
  z-index: 3;
  position: relative;
}
.testimonial__four-card-profile {
  width: 100px;
  height: 100px;
  margin: auto;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 20px;
}
.testimonial__four-card-profile img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.testimonial__four-card p {
  margin: 15px 0;
}
.testimonial__four-card-rating i {
  color: #f0ad4e;
  font-size: 18px;
  margin: 0 2px;
}

/*==========================================================================
* Testimonial Five Style
==========================================================================*/
.testimonial__five {
  background: var(--color-1);
}
.testimonial__five .testimonial__five-card {
  background: var(--bg-white);
  padding: 55px 50px;
  border-radius: 20px;
  box-shadow: 0px 0px 60px 0px rgba(0, 0, 0, 0.06);
}
.testimonial__five .testimonial__five-card-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.testimonial__five .testimonial__five-card-top img {
  width: 50px;
}
.testimonial__five .testimonial__five-card-top .testimonial-rating {
  display: flex;
  justify-content: end;
  align-items: center;
  -moz-column-gap: 2px;
       column-gap: 2px;
  font-size: 17px;
  color: #f0ad4e;
}
.testimonial__five .testimonial__five-card p {
  font-size: 22px;
  color: var(--color-2);
  text-transform: none;
  margin-bottom: 50px;
}
.testimonial__five .testimonial__five-card-profile {
  display: flex;
  align-items: center;
  -moz-column-gap: 20px;
       column-gap: 20px;
  row-gap: 20px;
  flex-wrap: wrap;
}
.testimonial__five .testimonial__five-card-profile img {
  width: 65px;
  height: 65px;
  border-radius: 50%;
}

/*==========================================================================
* Testimonial Six Style
==========================================================================*/
.testimonial__six-card {
  background: var(--color-1);
  padding: 40px 35px;
  border-radius: 20px;
}
.testimonial__six-card h4 {
  font-size: 24px;
}
.testimonial__six-card span {
  font-size: 18px;
  margin-bottom: 20px;
  color: var(--color-2);
  display: block;
}
.testimonial__six-card p {
  font-size: 20px;
  color: var(--color-2);
  text-transform: none;
  margin-bottom: 20px;
}
.testimonial__six-card .testimonial-rating {
  display: flex;
  align-items: center;
  -moz-column-gap: 5px;
       column-gap: 5px;
  font-size: 17px;
  color: #f0ad4e;
}

/*==========================================================================
* Video One Start Here
==========================================================================*/
.video__one {
  background-image: url(../img/video/video.jpg);
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  padding: 310px 0;
  width: 100%;
  display: block;
  position: relative;
}
.video__one::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #051635;
  opacity: 0.5;
}
.video__one .video-play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--primary-color-1);
  color: var(--text-white);
  width: 100px;
  height: 100px;
  font-size: 28px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  z-index: 3;
}

/*==========================================================================
* About Area Five CSS
==========================================================================*/
.technology-solution__one-image {
  position: relative;
}
.technology-solution__one-image-wrapper {
  padding: 0 20px;
}
.technology-solution__one-content h2 {
  text-transform: none;
  margin-bottom: 30px;
}
.technology-solution__one-content p {
  color: var(--color-2);
  margin-bottom: 30px;
  width: 90%;
}
.technology-solution__one-content .satisfied-customer-counter {
  display: flex;
  align-items: center;
  background: var(--color-1);
  width: -moz-fit-content;
  width: fit-content;
  padding: 15px 0 15px 25px;
  border-radius: 20px;
}
.technology-solution__one-content .satisfied-customer-counter .counter-wrapper {
  display: flex;
  margin-right: 10px;
}
.technology-solution__one-content .satisfied-customer-counter .counter-wrapper h3 {
  font-size: 48px;
  color: var(--primary-color-1);
}
.technology-solution__one-content .satisfied-customer-counter span {
  font-size: 18px;
  width: 28%;
  font-weight: 500;
}

/*==========================================================================
* Subscribe One, Two Three CSS
==========================================================================*/
.subscribe__one {
  margin-bottom: -128px;
}
.subscribe__one-content {
  background-color: var(--primary-color-1);
  background-repeat: no-repeat;
  background-position: top left;
  background-size: 20%;
  padding: 50px 0;
  border-radius: 30px 30px 0 0;
  z-index: 4;
  position: relative;
  width: 100%;
  margin: auto;
}
.subscribe__one-title {
  margin-bottom: 35px;
}
.subscribe__one-title h3 {
  color: var(--text-white);
  font-size: 32px;
  font-weight: 500;
  text-transform: none;
}
.subscribe__one-form {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  position: relative;
}
.subscribe__one-form input {
  background-color: var(--bg-white);
  border-radius: 5px;
  width: 600px;
  height: 75px;
  border-color: var(--color-4);
  padding: 0 221px 0 30px;
  color: var(--text-heading-color);
}
.subscribe__one-form input::-moz-placeholder {
  color: var(--color-2);
}
.subscribe__one-form input::placeholder {
  color: var(--color-2);
}
.subscribe__one-form .btn-two {
  position: absolute;
  border-radius: 5px;
  border: 1px solid var(--primary-color-1);
  right: 72px;
  top: 6px;
  color: var(--text-white);
  text-transform: capitalize;
  padding: 18px 25px;
}
.subscribe__one-form .btn-two:hover {
  color: var(--text-heading-color);
}
.subscribe__one.two .subscribe__one-content {
  border-radius: 30px;
}
.subscribe__one.two .subscribe__one-content::before {
  content: "";
  background-image: url(../img/subscribe/subscribe-two-shape-1.png);
  width: 181px;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  right: 0;
}
.subscribe__one.two .subscribe__one-title h3 {
  font-size: 48px;
  line-height: 1.12;
  font-weight: 600;
  z-index: 3;
  position: relative;
}
.subscribe__one.two .subscribe-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  -moz-column-gap: 20px;
       column-gap: 20px;
  row-gap: 18px;
}
.subscribe__one.two .subscribe-bottom .btn-two {
  background-color: var(--color-5);
  color: var(--text-white);
  text-transform: capitalize;
  margin-right: 20px;
}
.subscribe__one.two .subscribe-bottom .btn-two:hover {
  color: var(--text-white);
  background-color: var(--color-3);
}
.subscribe__one.two .subscribe-bottom .btn-two:hover i {
  color: var(--text-white);
}
.subscribe__one.two .subscribe-bottom .call-box-item {
  display: flex;
  align-items: center;
  gap: 15px;
  -moz-text-align-last: left;
       text-align-last: left;
  z-index: 3;
  position: relative;
}
.subscribe__one.two .subscribe-bottom .call-box-item-icon i {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  font-size: 28px;
  border-radius: 50%;
  background-color: var(--bg-white);
  color: var(--primary-color-1);
}
.subscribe__one.two .subscribe-bottom .call-box-item-info span {
  font-size: 16px;
  color: var(--text-white);
  font-weight: 400;
  display: block;
  text-transform: none;
}
.subscribe__one.two .subscribe-bottom .call-box-item-info a {
  color: var(--text-white);
  font-weight: 700;
  font-size: 18px;
  line-height: 26px;
  display: block;
}
.subscribe__one.three {
  margin-bottom: -8%;
  position: relative;
}
.subscribe__one.three .subscribe__three-shape {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
}
.subscribe__one.three .subscribe__one-content {
  background-color: inherit;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  border-radius: 20px;
  padding: 50px 0;
}
.subscribe__one.three .subscribe__one-title h3 {
  font-size: 48px;
  line-height: 1.2;
  margin-bottom: 25px;
}
.subscribe__one.three .subscribe__one-title p {
  color: var(--text-white);
  text-transform: none;
  width: 80%;
  margin: auto;
}

@media (max-width: 1399px) {
  .subscribe__one-form .btn-two {
    right: 27px;
  }
}
@media (max-width: 1199px) {
  .subscribe__one-form input {
    width: 100%;
  }
  .subscribe__one-form .btn-two {
    right: 5px;
  }
}
@media (max-width: 767px) {
  .subscribe__one-form .email-input {
    margin-bottom: 20px;
  }
  .subscribe__one-form .email-input input {
    max-width: 100%;
  }
  .subscribe__one-content {
    padding: 45px 0 45px 0;
  }
}
@media (max-width: 359px) {
  .subscribe__one-form input {
    height: 70px;
    padding: 0 170px 0 15px;
  }
  .subscribe__one-form .btn-two {
    top: 7px;
    padding: 14px 15px;
  }
}
/*==========================================================================
* Footer One CSS
==========================================================================*/
.footer__one {
  position: relative;
  z-index: 1;
  background-color: var(--bg-heading-color);
  padding-top: 190px;
}
.footer__one-shape {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%);
  z-index: -1;
}
.footer__one-widget h4 {
  color: var(--text-white);
  margin-bottom: 40px;
  font-size: 22px;
}
.footer__one-widget-about a img {
  max-width: 165px;
  margin-bottom: 20px;
}
.footer__one-widget-about p {
  color: var(--text-white);
  text-transform: none;
  font-size: 17px;
  margin-bottom: 20px;
}
.footer__one-widget-about-social h4 {
  font-size: 18px;
  margin-bottom: 18px;
}
.footer__one-widget-about-social ul {
  padding: 0;
  margin: 0;
}
.footer__one-widget-about-social ul li {
  list-style: none;
  display: inline-block;
  margin-right: 10px;
}
.footer__one-widget-about-social ul li:last-child {
  margin: 0;
}
.footer__one-widget-about-social ul li a i {
  display: inline-block;
  width: 38px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: transparent;
  font-size: 14px;
  border-radius: 4px;
  color: var(--text-white);
  transition: 0.4s;
  border: 1px solid var(--color-2);
}
.footer__one-widget-about-social ul li a i:hover {
  background-color: var(--primary-color-1);
  border: 1px solid var(--primary-color-1);
}
.footer__one-widget-solution ul {
  padding: 0;
  margin: 0;
}
.footer__one-widget-solution ul li {
  padding: 0;
  list-style: none;
  margin-bottom: 21px;
}
.footer__one-widget-solution ul li:last-child {
  margin: 0;
}
.footer__one-widget-solution ul li a {
  color: var(--color-4);
  transition: 0.4s;
  position: relative;
  font-size: 18px;
}
.footer__one-widget-solution ul li a i {
  color: var(--primary-color-1);
  font-size: 14px;
  margin-right: 10px;
}
.footer__one-widget-solution ul li a:hover {
  color: var(--primary-color-1);
  padding-left: 10px;
}
.footer__one-widget-location h6 {
  color: var(--text-white);
  margin: 18px 0;
}
.footer__one-widget-location-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}
.footer__one-widget-location-item-icon i {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  font-size: 23px;
  border-radius: 50%;
  background-color: var(--primary-color-1);
  color: var(--text-white);
}
.footer__one-widget-location-item-info span {
  font-size: 18px;
  color: var(--color-4);
  font-weight: 400;
  display: block;
  margin-bottom: 8px;
}
.footer__one-widget-location-item-info a {
  color: var(--color-4);
  font-weight: 700;
  font-size: 18px;
  line-height: 26px;
  display: block;
  transition: 0.4s;
  width: 90%;
}
.footer__one-widget-location-item-info a:hover {
  color: var(--primary-color-1);
}
.footer__one-widget-location .email a {
  text-transform: lowercase;
}
.footer__one-widget-subscribe p {
  color: var(--color-4);
  font-weight: 500;
  font-size: 16px;
  line-height: 26px;
  max-width: 265px;
}
.footer__one-widget-subscribe form {
  position: relative;
  margin-top: 30px;
}
.footer__one-widget-subscribe form input {
  background-color: transparent;
  color: var(--color-1);
  border-color: var(--color-1);
  height: 60px;
  border-radius: 50px;
  padding: 0 22px;
}
.footer__one-widget-subscribe form input:focus {
  color: var(--color-1);
}
.footer__one-widget-subscribe form input::-moz-placeholder {
  color: var(--text-white);
}
.footer__one-widget-subscribe form input::placeholder {
  color: var(--text-white);
}
.footer__one-widget-subscribe form button {
  padding: 0;
  position: absolute;
  right: 4px;
  top: 4px;
  width: 50px;
  height: 50px;
  font-size: 20px;
  text-align: center;
  border-radius: 50%;
}

@media (max-width: 991px) {
  .footer__one {
    padding-top: 145px;
  }
  .footer__one-widget h4 {
    margin-bottom: 20px;
  }
  .footer__one .copyright__one-container-area {
    padding: 25px 0;
    margin-top: 45px;
  }
  .footer__one .copyright__one-left {
    margin-bottom: 13px;
    text-align: center;
  }
  .footer__one .copyright__one-right {
    text-align: center;
    justify-content: center;
  }
}
@media (max-width: 535px) {
  .footer__one {
    padding-top: 150px;
  }
}
/*==========================================================================
* Footer Two CSS
==========================================================================*/
.footer__two {
  position: relative;
  z-index: 1;
  background-color: var(--bg-heading-color);
  padding-top: 195px;
}
.footer__two-shape {
  position: absolute;
  top: 0;
  z-index: -1;
  height: 88%;
}
.footer__two-widget h4 {
  color: var(--text-white);
  margin-bottom: 30px;
  font-size: 22px;
}
.footer__two-widget-social h4 {
  font-size: 18px;
  margin-bottom: 18px;
}
.footer__two-widget-social ul {
  padding: 0;
  margin-top: 35px;
}
.footer__two-widget-social ul li {
  list-style: none;
  display: inline-block;
  margin-right: 12px;
}
.footer__two-widget-social ul li:last-child {
  margin: 0;
}
.footer__two-widget-social ul li a i {
  font-size: 18px;
  color: var(--text-white);
  transition: 0.4s;
  border: 1px solid var(--color-2);
  width: 35px;
  height: 35px;
  border-radius: 5px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.footer__two-widget-social ul li a i:hover {
  border-color: var(--primary-color-1);
  background-color: var(--primary-color-1);
}
.footer__two-widget-about a img {
  max-width: 165px;
  margin-bottom: 20px;
}
.footer__two-widget-about p {
  color: var(--text-white);
  text-transform: none;
  font-size: 17px;
  margin-bottom: 20px;
}
.footer__two-widget-about-location h6 {
  color: var(--text-white);
  margin: 18px 0;
}
.footer__two-widget-about-location-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}
.footer__two-widget-about-location-item-icon i {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  font-size: 25px;
  border-radius: 50%;
  background-color: var(--primary-color-1);
  color: var(--text-white);
}
.footer__two-widget-about-location-item-info span {
  font-size: 18px;
  color: var(--color-4);
  font-weight: 400;
  display: block;
  margin-bottom: 8px;
}
.footer__two-widget-about-location-item-info a {
  color: var(--color-4);
  font-weight: 700;
  font-size: 18px;
  line-height: 26px;
  display: block;
  transition: 0.4s;
}
.footer__two-widget-about-location-item-info a:hover {
  color: var(--primary-color-1);
}
.footer__two-widget-about-location .email a {
  text-transform: lowercase;
}
.footer__two-widget-solution ul {
  padding: 0;
  margin: 0;
}
.footer__two-widget-solution ul li {
  padding: 0;
  list-style: none;
  margin-bottom: 21px;
}
.footer__two-widget-solution ul li:last-child {
  margin: 0;
}
.footer__two-widget-solution ul li a {
  color: var(--color-4);
  transition: 0.4s;
  position: relative;
  font-size: 18px;
}
.footer__two-widget-solution ul li a i {
  color: var(--primary-color-1);
  font-size: 14px;
  margin-right: 10px;
}
.footer__two-widget-solution ul li a:hover {
  color: var(--primary-color-1);
  padding-left: 10px;
}
.footer__two-widget-subscribe p {
  color: var(--color-4);
  font-weight: 500;
  font-size: 16px;
  line-height: 26px;
  max-width: 265px;
}

@media (max-width: 1199px) {
  .footer__two-widget h4 {
    margin-bottom: 18px;
  }
}
@media (max-width: 991px) {
  .footer__two .copyright__two-container-area {
    padding: 25px 0;
    margin-top: 45px;
  }
  .footer__two .copyright__two-left {
    margin-bottom: 13px;
    text-align: center;
  }
  .footer__two .copyright__two-right {
    text-align: center;
    justify-content: center;
  }
}
@media (max-width: 575px) {
  .footer__two-widget {
    margin-left: 0;
  }
}
/*==========================================================================
* Footer Three CSS
==========================================================================*/
.footer__three {
  position: relative;
  z-index: 1;
  background-color: var(--bg-heading-color);
}
.footer__three-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-5);
  padding: 45px 40px 45px 40px;
  border-radius: 15px;
  margin-bottom: 100px;
  flex-wrap: wrap;
  row-gap: 20px;
}
.footer__three-top h3 {
  color: var(--text-white);
  line-height: 1.26;
  font-size: 38px;
}
.footer__three-top-social {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  -moz-column-gap: 12px;
       column-gap: 12px;
  row-gap: 10px;
}
.footer__three-top-social a {
  width: 48px;
  height: 48px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--color-4);
  color: var(--text-white);
  border-radius: 50%;
  font-size: 18px;
  transition: 0.4s;
}
.footer__three-top-social a:last-child {
  margin-right: 0;
}
.footer__three-top-social a:hover {
  background-color: var(--primary-color-1);
  border-color: var(--primary-color-1);
}
.footer__three-widget h4 {
  color: var(--text-white);
  margin-bottom: 25px;
  font-size: 22px;
}
.footer__three-widget-about a img {
  max-width: 165px;
  margin-bottom: 20px;
}
.footer__three-widget-about p {
  color: var(--text-white);
  text-transform: none;
  font-size: 17px;
  margin-bottom: 20px;
}
.footer__three-widget-about form {
  position: relative;
  margin-top: 30px;
}
.footer__three-widget-about form input {
  background-color: transparent;
  color: var(--color-1);
  border-color: var(--color-1);
  height: 60px;
  border-radius: 50px;
  padding: 0 22px;
}
.footer__three-widget-about form input:focus {
  color: var(--color-1);
}
.footer__three-widget-about form input::-moz-placeholder {
  color: var(--text-white);
}
.footer__three-widget-about form input::placeholder {
  color: var(--text-white);
}
.footer__three-widget-about form button {
  padding: 0;
  position: absolute;
  right: 4px;
  top: 4px;
  width: 50px;
  height: 50px;
  font-size: 20px;
  text-align: center;
  border-radius: 50%;
}
.footer__three-widget-solution {
  margin-left: 60px;
}
.footer__three-widget-solution ul {
  padding: 0;
  margin: 0;
}
.footer__three-widget-solution ul li {
  padding: 0;
  list-style: none;
  margin-bottom: 21px;
}
.footer__three-widget-solution ul li:last-child {
  margin: 0;
}
.footer__three-widget-solution ul li a {
  color: var(--color-4);
  transition: 0.4s;
  position: relative;
  font-size: 18px;
}
.footer__three-widget-solution ul li a i {
  color: var(--primary-color-1);
  font-size: 14px;
  margin-right: 10px;
}
.footer__three-widget-solution ul li a:hover {
  color: var(--primary-color-1);
  padding-left: 10px;
}
.footer__three-widget-location h6 {
  color: var(--text-white);
  margin: 18px 0;
}
.footer__three-widget-location-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}
.footer__three-widget-location-item-icon i {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  font-size: 23px;
  border-radius: 50%;
  background-color: var(--primary-color-1);
  color: var(--text-white);
}
.footer__three-widget-location-item-info span {
  font-size: 18px;
  color: var(--color-4);
  font-weight: 400;
  display: block;
  margin-bottom: 8px;
}
.footer__three-widget-location-item-info a {
  color: var(--color-4);
  font-weight: 700;
  font-size: 18px;
  line-height: 26px;
  display: block;
  transition: 0.4s;
  width: 90%;
}
.footer__three-widget-location-item-info a:hover {
  color: var(--primary-color-1);
}
.footer__three-widget-location .email a {
  text-transform: lowercase;
}

@media (max-width: 1199px) {
  .footer__three-widget h4 {
    margin-left: 0;
  }
  .footer__three-widget-solution {
    margin-left: 0;
  }
}
/*==========================================================================
* Footer Four CSS
==========================================================================*/
.footer__four {
  position: relative;
  z-index: 1;
  background-color: var(--bg-heading-color);
  padding-top: 100px;
}
.footer__four-shape {
  position: absolute;
  top: 0;
  z-index: -1;
  height: 88%;
}
.footer__four-widget h4 {
  color: var(--text-white);
  margin-bottom: 30px;
  font-size: 22px;
}
.footer__four-widget-about a img {
  max-width: 165px;
  margin-bottom: 20px;
}
.footer__four-widget-about p {
  color: var(--text-white);
  text-transform: none;
  font-size: 17px;
  margin-bottom: 20px;
}
.footer__four-widget-about form {
  position: relative;
  margin-top: 30px;
}
.footer__four-widget-about form input {
  background-color: transparent;
  color: var(--color-1);
  border-color: var(--color-1);
  height: 60px;
  border-radius: 50px;
  padding: 0 22px;
}
.footer__four-widget-about form input:focus {
  color: var(--color-1);
}
.footer__four-widget-about form input::-moz-placeholder {
  color: var(--text-white);
}
.footer__four-widget-about form input::placeholder {
  color: var(--text-white);
}
.footer__four-widget-about form button {
  padding: 0;
  position: absolute;
  right: 4px;
  top: 4px;
  width: 50px;
  height: 50px;
  font-size: 20px;
  text-align: center;
  border-radius: 50%;
}
.footer__four-widget-solution ul {
  padding: 0;
  margin: 0;
}
.footer__four-widget-solution ul li {
  padding: 0;
  list-style: none;
  margin-bottom: 21px;
}
.footer__four-widget-solution ul li:last-child {
  margin: 0;
}
.footer__four-widget-solution ul li a {
  color: var(--color-4);
  transition: 0.4s;
  position: relative;
  font-size: 18px;
}
.footer__four-widget-solution ul li a i {
  color: var(--primary-color-1);
  font-size: 14px;
  margin-right: 10px;
}
.footer__four-widget-solution ul li a:hover {
  color: var(--primary-color-1);
  padding-left: 10px;
}

@media (max-width: 1199px) {
  .footer__five-widget-solution {
    margin-left: 0;
  }
  .footer__five-widget h4 {
    margin-left: 0;
  }
}
/*==========================================================================
* Footer Five CSS
==========================================================================*/
.footer__five {
  position: relative;
  z-index: 1;
  background-color: var(--color-1);
  padding-top: 200px;
}
.footer__five-shape {
  position: absolute;
  top: 0;
  z-index: -1;
  height: 88%;
}
.footer__five-widget h4 {
  color: var(--text-heading-color);
  margin-bottom: 25px;
  font-size: 22px;
}
.footer__five-widget-about a img {
  max-width: 165px;
  margin-bottom: 20px;
}
.footer__five-widget-about p {
  color: var(--color-2);
  text-transform: none;
  font-size: 17px;
  margin-bottom: 20px;
}
.footer__five-widget-about-social ul {
  padding: 0;
  margin: 0;
}
.footer__five-widget-about-social ul li {
  list-style: none;
  display: inline-block;
  margin-right: 10px;
}
.footer__five-widget-about-social ul li:last-child {
  margin: 0;
}
.footer__five-widget-about-social ul li a i {
  display: inline-block;
  width: 38px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: var(--bg-white);
  font-size: 18px;
  border-radius: 4px;
  color: var(--text-heading-color);
  transition: 0.4s;
  border: 1px solid var(--color-4);
}
.footer__five-widget-about-social ul li a i:hover {
  background-color: var(--primary-color-1);
  border: 1px solid var(--primary-color-1);
  color: var(--text-white);
}
.footer__five-widget-solution ul {
  padding: 0;
  margin: 0;
}
.footer__five-widget-solution ul li {
  padding: 0;
  list-style: none;
  margin-bottom: 18px;
}
.footer__five-widget-solution ul li:last-child {
  margin: 0;
}
.footer__five-widget-solution ul li a {
  color: var(--color-2);
  transition: 0.4s;
  position: relative;
  font-size: 18px;
}
.footer__five-widget-solution ul li a i {
  color: var(--primary-color-1);
  font-size: 14px;
  margin-right: 10px;
}
.footer__five-widget-solution ul li a:hover {
  color: var(--primary-color-1);
  padding-left: 10px;
}
.footer__five-widget-location p {
  color: var(--color-2);
  text-transform: none;
  font-size: 17px;
  margin-bottom: 20px;
  width: 94%;
}
.footer__five-widget-location-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}
.footer__five-widget-location-item-icon i {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  font-size: 23px;
  border-radius: 50%;
  background-color: var(--primary-color-1);
  color: var(--text-white);
}
.footer__five-widget-location-item-info span {
  font-size: 16px;
  color: var(--primary-color-1);
  font-weight: 400;
  display: block;
}
.footer__five-widget-location-item-info a {
  color: var(--text-heading-color);
  font-weight: 700;
  font-size: 18px;
  line-height: 26px;
  display: block;
  transition: 0.4s;
}
.footer__five-widget-location-item-info a:hover {
  color: var(--primary-color-1);
}
.footer__five-widget-location .email a {
  text-transform: lowercase;
}
.footer__five .copyright__one.two {
  border-top: 1px solid rgba(204, 204, 204, 0.2901960784);
}
.footer__five .copyright__one-left p {
  color: var(--color-2);
}
.footer__five .copyright__one-right a {
  color: var(--color-2);
}

/*==========================================================================
* Footer Copyright CSS
==========================================================================*/
.copyright__one-container-area {
  background-color: var(--color-5);
  padding: 20px 15px;
  border-radius: 10px 10px 0 0;
  margin-top: 68px;
}
.copyright__one-left {
  color: var(--text-white);
}
.copyright__one-left p {
  font-size: 18px;
}
.copyright__one-right {
  color: var(--text-white);
  display: flex;
  justify-content: end;
  align-items: center;
  flex-wrap: wrap;
}
.copyright__one-right a {
  margin: 0 8px;
  font-size: 18px;
  transition: 0.3s;
}
.copyright__one-right a:hover {
  color: var(--primary-color-1);
}
.copyright__one.two {
  border-top: 1px solid var(--color-5);
}
.copyright__one.two .copyright__one-container-area {
  background-color: transparent;
}
.copyright__one.two .copyright__one-container-area {
  padding: 30px 0;
  margin-top: 0;
}

@media (max-width: 991px) {
  .copyright__one-left {
    text-align: center;
  }
  .copyright__one-right {
    justify-content: center;
  }
}
/*==========================================================================
* 404 CSS
==========================================================================*/
.error-area {
  background-color: var(--color-1);
  height: 100vh;
}/*# sourceMappingURL=style.css.map */