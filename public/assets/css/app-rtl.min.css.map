{"version": 3, "sources": ["app.scss", "custom/fonts/_fonts.scss", "app.css", "custom/structure/_topbar.scss", "_variables.scss", "custom/structure/_page-head.scss", "custom/structure/_footer.scss", "custom/structure/_right-sidebar.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "custom/structure/_vertical.scss", "custom/structure/_horizontal-nav.scss", "custom/structure/_layouts.scss", "custom/components/_root.scss", "custom/components/_waves.scss", "custom/components/_avatar.scss", "custom/components/_accordion.scss", "custom/components/_helper.scss", "custom/components/_preloader.scss", "custom/components/_forms.scss", "custom/components/_widgets.scss", "custom/components/_demos.scss", "custom/components/_print.scss", "custom/plugins/_custom-scrollbar.scss", "custom/plugins/_calendar.scss", "custom/plugins/_color-picker.scss", "custom/plugins/_session-timeout.scss", "custom/plugins/_round-slider.scss", "custom/plugins/_range-slider.scss", "custom/plugins/_sweatalert2.scss", "custom/plugins/_rating.scss", "custom/plugins/_toastr.scss", "custom/plugins/_parsley.scss", "custom/plugins/_select2.scss", "custom/plugins/_switch.scss", "custom/plugins/_datepicker.scss", "custom/plugins/_bootstrap-touchspin.scss", "custom/plugins/_datatable.scss", "custom/plugins/_form-editors.scss", "custom/plugins/_form-upload.scss", "custom/plugins/_form-wizard.scss", "custom/plugins/_responsive-table.scss", "custom/plugins/_table-editable.scss", "custom/plugins/_apexcharts.scss", "../../../node_modules/bootstrap/scss/_variables.scss", "custom/plugins/_flot.scss", "custom/plugins/_sparkline-chart.scss", "custom/plugins/_google-map.scss", "custom/plugins/_vector-maps.scss", "custom/plugins/_x-editable.scss", "custom/pages/_authentication.scss", "custom/pages/_ecommerce.scss", "custom/pages/_email.scss", "custom/pages/_chat.scss", "custom/pages/_kanbanboard.scss", "custom/pages/_coming-soon.scss", "custom/pages/_timeline.scss", "custom/pages/_extras-pages.scss"], "names": [], "mappings": "AAAA;;;;;;CAAA;ACKQ,4FAAA;AAER;EACI,oBAAA;EACA,kBAAA;EACA,gBAAA;EACA,kBAAA;EACA,qHAAA;ACEJ;ADEA;EACI,oBAAA;EACA,kBAAA;EACA,gBAAA;EACA,kBAAA;EACA,yHAAA;ACAJ;ADIA;EACI,oBAAA;EACA,kBAAA;EACA,gBAAA;EACA,kBAAA;EACA,uHAAA;ACFJ;ADMA;EACI,oBAAA;EACA,kBAAA;EACA,gBAAA;EACA,kBAAA;EACA,kHAAA;ACJJ;AC/BA;EACI,eAAA;EACA,MAAA;EACA,QAAA;EACA,OAAA;EACA,aAAA;EACA,qCAAA;EACA,iDCsnB0B;UDtnB1B,yCCsnB0B;AFrlB9B;;AC9BA;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,sBAAA;EACA,yBAAA;UAAA,8BAAA;EACA,yBAAA;MAAA,sBAAA;UAAA,mBAAA;EACA,cAAA;EACA,YCMY;EDLZ,+BAAA;ADiCJ;AC9BQ;EACI,wCAAA;ADgCZ;;AC3BA;EACI,iBAAA;EACA,YC2BqB;AFGzB;;AC3BA;EACI,iBAAA;AD8BJ;AC5BI;EACI,aAAA;AD8BR;;AC1BA;EACI,aAAA;AD6BJ;;AC1BA,WAAA;AAEA;EACI,yBAAA;AD4BJ;AC1BI;EACI,YAAA;EACA,YAAA;EACA,kBAAA;EACA,mBAAA;EACA,4CCnBW;EDoBX,wBAAA;UAAA,gBAAA;EACA,mBAAA;AD4BR;AC1BI;EACI,kBAAA;EACA,WAAA;EACA,eAAA;EACA,iBAAA;EACA,UAAA;EACA,MAAA;EACA,cC2BG;AFCX;;ACrBI;EACI,kBAAA;EACA,gBAAA;ADwBR;ACvBQ;EACI,2BCqtCgC;AF5rC5C;;ACpBA;EACI;IACI,WAAA;EDuBN;EClBM;IACI,aAAA;EDoBV;ECjBM;IACI,qBAAA;EDmBV;AACF;ACfA;EACI,mEAAA;ADiBJ;;ACdA;EACI,YChFY;EDiFZ,mCAAA;UAAA,2BAAA;EACA,kCAAA;EACA,SAAA;EACA,kBAAA;ADiBJ;ACfI;EACI,kCAAA;ADiBR;;ACXA;EACI,YAAA;EACA,WAAA;EACA,wCAAA;EACA,YAAA;ADcJ;;ACTQ;EACI,qBAAA;ADYZ;;ACNI;EACI,eAAA;EACA,kCAAA;ADSR;ACNI;EACI,kBAAA;EACA,qBAAA;EACA,WAAA;EACA,UAAA;EACA,yBCvBE;EDwBF,kBAAA;EACA,SAAA;EACA,WAAA;ADQR;;ACHI;EACI,qBAAA;ADMR;ACJQ;EACI,uCAAA;ADMZ;;ACAA;EACI,cAAA;EACA,kBAAA;EACA,iBAAA;EACA,kBAAA;EACA,mBAAA;EACA,cAAA;EACA,6BAAA;EACA,cC5EO;AF+EX;ACDI;EACI,YAAA;ADGR;ACAI;EACI,cAAA;EACA,gBAAA;EACA,uBAAA;EACA,mBAAA;ADER;ACCI;EACI,oCAAA;ADCR;;ACMQ;EACI,gBAAA;ADHZ;;ACSI;EACI,0CAAA;ADNR;ACUY;EACI,2CAAA;ADRhB;ACYQ;EACI,oCAAA;ADVZ;ACcI;EACI,uCAAA;ADZR;ACcQ;EACI,uCAAA;ADZZ;ACgBI;EACI,2CAAA;ADdR;ACkBQ;EACI,uCAAA;ADhBZ;ACoBI;EACI,aAAA;ADlBR;ACqBI;EACI,cAAA;ADnBR;ACwBQ;EACI,4CAAA;EACA,WC3JJ;AFqIR;ACwBQ;;EAEI,+BAAA;ADtBZ;;AC6BI;EACI,qCAAA;AD1BR;AC6BI;EACI,aAAA;AD3BR;AC8BI;EACI,cAAA;AD5BR;;ACgCA;EAEQ;IACI,gBAAA;ED9BV;ECgCU;IACI,qBAAA;IACA,sBAAA;ED9Bd;AACF;ACmCA;EACI;IACI,aAAA;EDjCN;AACF;ACqCI;EACI,WAAA;ADnCR;ACqCI;EACI,gBC1QQ;ED2QR,mEAAA;ADnCR;;ACuCA;EAEQ;IACI,gBAAA;EDrCV;AACF;AC4CI;EACI,6CAAA;AD1CR;AC8CY;EACI,2CAAA;AD5ChB;ACgDQ;EACI,oCAAA;AD9CZ;ACkDI;EACI,uCAAA;ADhDR;ACkDQ;EACI,uCAAA;ADhDZ;ACoDI;EACI,2CAAA;ADlDR;ACsDQ;EACI,uCAAA;ADpDZ;ACwDI;EACI,aAAA;ADtDR;ACyDI;EACI,cAAA;ADvDR;AC4DQ;EACI,4CAAA;EACA,WCxQJ;AF8MR;AC4DQ;;EAEI,+BAAA;AD1DZ;;AGvSA;EACI,sBDmlB0B;AFzS9B;AGxSI;EACI,6BAAA;EACA,UAAA;AH0SR;AGvSI;EACI,eAAA;EACA,yBAAA;EACA,gBAAA;AHySR;;AIpTA;EACI,SAAA;EACA,8BAAA;EACA,kBAAA;EACA,QAAA;EACA,cFkCW;EEjCX,WFGY;EEFZ,YF8BY;EE7BZ,iDFqnB0B;UErnB1B,yCFqnB0B;EEpnB1B,qCF6BQ;AF0RZ;;AIpTA;EACI;IACI,OAAA;EJuTN;AACF;AIlTI;EACI,UFXkB;AF+T1B;;AI/SI;EACI,kBAAA;AJkTR;;AK7UA;EACI,wCHk5CM;EGj5CN,iFAAA;UAAA,yEAAA;EACA,cAAA;EACA,eAAA;EACA,sCAAA;EAAA,8BAAA;EACA,YH0Ca;EGzCb,aAAA;EACA,uBAAA;EACA,aAAA;EACA,MAAA;EACA,SAAA;ALgVJ;AK9UI;EACI,yBAAA;EACA,YAAA;EACA,WAAA;EACA,iBAAA;EACA,cHqEG;EGpEH,kBAAA;EACA,kBAAA;ALgVR;AK9UQ;EACI,yBAAA;ALgVZ;;AK1UA;EACI,wCAAA;EACA,kBAAA;EACA,OAAA;EACA,QAAA;EACA,MAAA;EACA,SAAA;EACA,aAAA;EACA,aAAA;EACA,qCAAA;EAAA,6BAAA;AL6UJ;;AKzUI;EACI,QAAA;AL4UR;AK1UI;EACI,cAAA;AL4UR;;AMlTI;EDrBA;IACI,cAAA;EL2UN;EK1UM;IACI,uBAAA;EL4UV;AACF;AOnYA;EACI,SAAA;APqYJ;AOnYI;EACI,cAAA;EACA,WAAA;APqYR;AOlYI;EACI,aAAA;APoYR;AOlYQ;EACI,aAAA;APoYZ;AOjYQ;EACI,cAAA;APmYZ;AO/XI;EACI,kBAAA;EACA,SAAA;EACA,gBAAA;EACA,wCAAA;UAAA,gCAAA;EACA,kCAAA;UAAA,0BAAA;EACA,+CAAA;EAAA,uCAAA;APiYR;;AO5XA;EACI,YLvBY;EKwBZ,aAAA;EACA,gCAAA;EACA,SAAA;EACA,aAAA;EACA,eAAA;EACA,SLhBY;EKiBZ,iDLslB0B;UKtlB1B,yCLslB0B;AFvN9B;;AO5XA;EACI,kBLlCY;EKmCZ,gBAAA;AP+XJ;AO7XI;EACI,yBAAA;EACA,gBL1BQ;AFyZhB;;AO1XA;EACI,sBAAA;AP6XJ;AOzXY;EACI,kCAAA;UAAA,0BAAA;AP2XhB;AOrXQ;EACI,iBAAA;EACA,oCAAA;EACA,cAAA;EACA,YAAA;EACA,0CAAA;EAAA,kCAAA;EAAA,0BAAA;EAAA,kDAAA;EACA,eAAA;APuXZ;AOjXY;EACI,cAAA;EACA,wBAAA;EACA,wCAAA;EACA,kBAAA;EACA,iBAAA;EACA,4BAAA;EAAA,oBAAA;EACA,gCLXQ;EKYR,gBAAA;APmXhB;AOjXgB;EACI,qBAAA;EACA,iBAAA;EACA,uBAAA;EACA,iBAAA;EACA,uBAAA;EACA,sBAAA;EACA,6CAAA;EACA,4BAAA;EAAA,oBAAA;EACA,aAAA;APmXpB;AOhXgB;EACI,8CAAA;APkXpB;AOhXoB;EACI,8CAAA;APkXxB;AO7WY;EACI,eAAA;AP+WhB;AO5WY;EACI,UAAA;AP8WhB;AO1WoB;EACI,oCAAA;EACA,eAAA;EACA,4CAAA;AP4WxB;AOzWoB;EACI,UAAA;AP2WxB;AOxW4B;EACI,oCAAA;EACA,iBAAA;AP0WhC;;AO/VA;EACI,6BAAA;EACA,sBAAA;EACA,oBAAA;EACA,eAAA;EACA,eAAA;EACA,yBAAA;EACA,6CAAA;EACA,gBLuiBmB;EKtiBnB,gCL5EoB;EK6EpB,YAAA;APkWJ;;AO9VA;EACI,0DAAA;APiWJ;AOhWI;EACI,0DAAA;APkWR;AOjWQ;EACI,0DAAA;APmWZ;AOhWI;EACI,0DAAA;APkWR;AOhWI;EACI,0DAAA;APkWR;AOhWQ;EACI,0DAAA;APkWZ;;AO7VA;EACI;IACI,aAAA;EPgWN;EO7VE;IACI,yBAAA;EP+VN;EO3VM;IACI,cAAA;EP6VV;AACF;AOtVI;EACI,iBLxLkB;AFghB1B;AOrVI;EACI,sBAAA;APuVR;AOnVQ;EACI,aAAA;APqVZ;AOlVQ;EACI,cAAA;APoVZ;AO/UI;EACI,kBAAA;EACA,sBAAA;EACA,UAAA;APiVR;AO/UQ;;EAEI,4BAAA;APiVZ;AO9UQ;EACI,wBAAA;APgVZ;AO7UQ;EACI,oBAAA;AP+UZ;AOzUY;;;EAGI,wBAAA;AP2UhB;AOxUY;EACI,0BAAA;AP0UhB;AOtUgB;EACI,aAAA;APwUpB;AOnUgB;EACI,kBAAA;EACA,mBAAA;APqUpB;AOnUoB;EACI,kBAAA;EACA,gBAAA;EACA,wBAAA;EAAA,gBAAA;APqUxB;AOnUwB;EAGI,8CAAA;APmU5B;AOhUwB;EACI,eAAA;EACA,gBAAA;APkU5B;AO/TwB;EACI,aAAA;EACA,kBAAA;APiU5B;AO5TwB;EACI,kBAAA;EACA,yBAAA;EACA,cLtKlB;EKwKkB,wBAAA;EAAA,gBAAA;AP6T5B;AO3T4B;EACI,cL3KtB;AFweV;AO1T4B;EACI,eAAA;AP4ThC;AOxTwB;EACI,cAAA;EACA,UL7RF;EK8RE,kBAAA;EACA,YAAA;EACA,uBAAA;EACA,2DAAA;UAAA,mDAAA;AP0T5B;AOxT4B;EACI,2DAAA;UAAA,mDAAA;AP0ThC;AOvT4B;EACI,wBAAA;UAAA,gBAAA;EACA,iBAAA;EACA,kBAAA;EACA,YAAA;EACA,UAAA;EACA,4CAAA;APyThC;AOvTgC;EACI,8CAAA;APyTpC;AOlTgB;EACI,cAAA;EACA,aAAA;EACA,aAAA;EACA,sCAAA;APoTpB;AOhT4B;EACI,cAAA;EACA,WAAA;EACA,uBAAA;EACA,iBAAA;EACA,kBAAA;EACA,YAAA;APkThC;AO7S4B;EACI,kBAAA;EACA,WAAA;EACA,SAAA;EACA,iCAAA;UAAA,yBAAA;AP+ShC;AOzSwB;EACI,cLzQjB;AFojBX;;AO7RI;EACI,qCAAA;APgSR;AOzRgB;EACI,6CAAA;AP2RpB;AOzRoB;EACI,6CAAA;AP2RxB;AOxRoB;EACI,mDAAA;AP0RxB;AOxRwB;EACI,mDAAA;AP0R5B;AOlRwB;EACI,iDAAA;APoR5B;AOlR4B;EACI,mDAAA;APoRhC;AO3QI;EACI,kBAAA;AP6QR;AOjQ4B;EACI,2CAAA;EACA,mDAAA;APmQhC;AOlQgC;EACI,mDAAA;APoQpC;AO/PgC;EACI,iDAAA;APiQpC;AOhQoC;EACI,8CAAA;APkQxC;AO3PoB;EACI,wCAAA;AP6PxB;AOpP4B;EACI,+DAAA;APsPhC;AO9O+B;EACK,0DAAA;APgPpC;AO3OgC;EACI,0DAAA;AP6OpC;AO7NI;EACI,+DAAA;AP+NR;AO9NQ;EACI,+DAAA;APgOZ;AO/NY;EACI,+DAAA;APiOhB;AO9NQ;EACI,+DAAA;APgOZ;AO9NQ;EACI,+DAAA;APgOZ;AO9NY;EACI,+DAAA;APgOhB;AO3NI;EACI,6CAAA;AP6NR;;AOvNI;EACI,yBAAA;AP0NR;;AOnNI;EACI,YLxfW;AF8sBnB;AOpNQ;EAHJ;IAIQ,WAAA;EPuNV;AACF;AOrNI;EACI,YL/fW;EKggBX,kBAAA;APuNR;AOrNQ;;EAEI,wBAAA;APuNZ;AOpNI;EACI,kBLxgBW;AF8tBnB;AOpNI;EACI,WL3gBW;AFiuBnB;AOrNQ;EAFJ;IAGQ,OAAA;EPwNV;AACF;AO/MgB;EACI,cAAA;APiNpB;AO5MoB;EACI,oBAAA;AP8MxB;AOzM4B;EACI,oBAAA;AP2MhC;AOlMQ;EACI,iBL/iBc;AFmvB1B;AOjMY;EACI,gBAAA;APmMhB;AO/L4B;EACI,qBAAA;APiMhC;AO1LQ;EACI,ULhkBc;AF4vB1B;;AOpLI;EACI,mBLjeE;AFwpBV;AOrLI;EACI,yBLpeE;AF2pBV;AOtLQ;EACI,aAAA;APwLZ;AOtLQ;EACI,cAAA;APwLZ;AOjLgB;EACI,+BAAA;APmLpB;AOhLgB;EACI,+BAAA;APkLpB;AOjLoB;EACI,+BAAA;APmLxB;AOhLwB;EACE,oCAAA;APkL1B;AO3KwB;EACI,+BAAA;AP6K5B;AO/JwB;EACI,yBAAA;EACA,WLhjBpB;AFitBR;AOhK4B;EACI,WLljBxB;AFotBR;AOvJ4B;EACI,+DAAA;APyJhC;AOjJ+B;EACK,0DAAA;APmJpC;AO9IgC;EACI,0DAAA;APgJpC;AOlII;EACI,sBAAA;APoIR;AOnIQ;EACI,sBAAA;APqIZ;AOpIY;EACI,sBAAA;APsIhB;AOnIQ;EACI,sBAAA;APqIZ;AOnIQ;EACI,sBAAA;APqIZ;AOnIY;EACI,sBAAA;APqIhB;AOhII;EACI,sBAAA;APkIR;;AQv0BA;EACI,+BAAA;EACA,2BAAA;EACA,iDN0nB0B;UM1nB1B,yCN0nB0B;EMznB1B,gBNkBY;EMjBZ,eAAA;EACA,OAAA;EACA,QAAA;EACA,YAAA;AR00BJ;AQx0BI;EACI,SAAA;EACA,UAAA;AR00BR;AQr0BQ;EACI,eAAA;EACA,kBAAA;EACA,oBAAA;EACA,gCNuBM;EMtBN,gCNmDY;AFoxBxB;AQr0BY;EACI,eAAA;EACA,sBAAA;EACA,qBAAA;ARu0BhB;AQp0BY;EAEI,uCNaS;EMZT,6BAAA;ARq0BhB;AQj0BQ;EACI,gCNMM;AF6zBlB;AQj0BY;EAEI,uCNGS;AF+zBzB;AQ7zBY;EACI,uCNHS;AFk0BzB;AQzzBgB;EACI,uCNVK;EMWL,6BAAA;AR2zBpB;;AMxzBI;EEQI;;IAEI,cAAA;ERozBV;AACF;AM/zBI;EEoBgB;IACI,eAAA;ER8yBtB;EQxyBM;IACI,sBAAA;IACA,gBAAA;ER0yBV;EQnyBc;IACI,SAAA;IACA,WAAA;ERqyBlB;EQjyBU;IACI,aAAA;IACA,kEAAA;ERmyBd;EQhyBkB;IACI,WAAA;IACA,mDAAA;YAAA,2CAAA;IACA,kBAAA;ERkyBtB;EQ7xBkB;IACI,kBAAA;IACA,iBAAA;IACA,UAAA;IACA,aAAA;ER+xBtB;EQzxBc;IACI,cAAA;ER2xBlB;EQtxBM;IACI,cAAA;ERwxBV;EQpxBE;IACI,aAAA;ERsxBN;AACF;AQnxBA;EACI,qBAAA;ARqxBJ;AQnxBI;EACI,qBAAA;EACA,mBAAA;EACA,yBAAA;EACA,WAAA;EACA,aAAA;EACA,qBAAA;EACA,UAAA;EACA,QAAA;EACA,iBAAA;EACA,kDAAA;UAAA,0CAAA;EACA,6BAAA;UAAA,qBAAA;EACA,qCAAA;EAAA,6BAAA;EACA,YAAA;ARqxBR;;AMz2BI;EE+FoB;IACI,WAAA;IACA,UAAA;ER8wB1B;AACF;AMh3BI;EE6GI;IACI,cNlII;EFw4Bd;EQpwBU;IACI,cNrIA;EF24Bd;EQlwBM;IACI,aN3IG;EF+4Bb;EQhwBE;IACI,iBAAA;IACA,gBAAA;IACA,UAAA;ERkwBN;EQ/vBU;IACI,uBAAA;ERiwBd;EQ5vBU;IACI,6BAAA;IACA,YAAA;IACA,wBAAA;YAAA,gBAAA;IACA,kBAAA;ER8vBd;EQ5vBc;IACI,WAAA;ER8vBlB;EQ5vBkB;IACI,WAAA;ER8vBtB;EQzvBU;IACI,kBAAA;IACA,6BAAA;ER2vBd;EQzvBc;IAEI,cNjHV;EF22BR;EQpvBU;IACI,WAAA;IACA,kBAAA;ERsvBd;AACF;AMv6BI;EE2LQ;IACI,cNnMA;EFk7Bd;EQ5uBU;IACI,aNxMD;EFs7Bb;EQ1uBM;IACI,0CAAA;ER4uBV;EQxuBc;IACI,+BAAA;ER0uBlB;EQxuBkB;IAEI,+BAAA;ERyuBtB;EQnuBsB;IACI,0CAAA;ERquB1B;AACF;AQ3tBQ;EACI,aNzOG;AFs8Bf;AQ1tBQ;EACI,cN5OI;AFw8BhB;;AS5/BA;EACI,yCAAA;AT+/BJ;AS9/BI;EACI,mCAAA;EACA,iBPsDa;EOrDb,cAAA;EACA,iDPunBsB;UOvnBtB,yCPunBsB;AFyY9B;AS7/BI;EACI,iBPgDa;EO/Cb,cAAA;AT+/BR;AS5/BI;EACI,cAAA;EACA,+BAAA;AT8/BR;AS1/BQ;EACI,8BAAA;AT4/BZ;;ASn/BI;EACI,iBP2Ba;EO1Bb,iBAAA;EACA,kBAAA;ATs/BR;;AU9/BA;;EAEI,uBAAA;EACA,mDAAA;EACA,qDAAA;EACA,wBAAA;EAEA,0BAAA;EAEA,+BAAA;EAEA,wBAAA;EACA,uBAAA;EAMA,4BAAA;EAEA,6BAAA;EACA,oCAAA;EAGA,gCAAA;EAGA,oBAAA;EACA,+BAAA;EACA,gDAAA;EAGA,qCAAA;EACA,4BAAA;EAEA,+BAAA;EAEA,2BAAA;EAmBA,wBAAA;EACA,qCAAA;EACA,yCAAA;EACA,0CAAA;EACA,2CAAA;EACA,4CAAA;EACA,yBAAA;EACA,kCAAA;EACA,4BAAA;AV89BJ;AUhhCI;;EACI,8BAAA;AVmhCR;AU3/BI;;EACI,uBAAA;EACA,+BAAA;EACA,oCAAA;AV8/BR;AU3/BI;;EACK,+BAAA;EACA,iDAAA;EACA,oCAAA;AV8/BT;AU3/BI;;EACI,8BAAA;AV8/BR;AUh/BI;;EACI,wBAAA;EACA,qCAAA;EACA,yCAAA;EACA,0CAAA;EACA,2CAAA;EACA,4CAAA;AVm/BR;AUh/BI;;EACI,6BAAA;EACA,0CAAA;EACA,8CAAA;EACA,+CAAA;EACA,gDAAA;EACA,iDAAA;EACA,8BAAA;AVm/BR;;AU5kCQ;EAiGA,mBAAA;EACA,0BAAA;EACA,kBAAA;EACA,yBAAA;EAGA,uBAAA;EACA,4BAAA;EACA,+BAAA;EACA,gCAAA;EA4BA,uBAAA;EAEA,qDAAA;EAEA,uBAAA;EAEA,2BAAA;EAEA,wBAAA;EACA,qCAAA;EACA,yCAAA;EACA,0CAAA;EACA,2CAAA;EACA,4CAAA;AV88BR;AUr/BQ;EACK,uBAAA;EACA,oCAAA;EACA,+CAAA;EACA,4CAAA;EACA,+BAAA;EACA,2CAAA;EACA,8BAAA;EACA,0CAAA;EACA,6BAAA;AVu/Bb;AUp/BQ;EACI,+BAAA;EACA,gCAAA;AVs/BZ;AUn/BQ;EACI,8BAAA;AVq/BZ;AUl/BQ;EACI,8BAAA;AVo/BZ;AUj+BQ;EACA,8BAAA;EACA,0CAAA;EACA,8CAAA;EACA,+CAAA;EACA,gDAAA;EACA,iDAAA;EACA,8BAAA;AVm+BR;;AWppCA;;;;;;uDAAA;AAOC;EACG,kBAAA;EACA,eAAA;EACA,qBAAA;EACA,gBAAA;EACA,yBAAA;EACA,sBAAA;EACA,qBAAA;EACA,iBAAA;EACA,wCAAA;AXupCJ;;AWrpCE;EACE,kBAAA;EACA,kBAAA;EACA,YAAA;EACA,aAAA;EACA,iBAAA;EACA,kBAAA;EACA,UAAA;EACA,8BAAA;EAIA,qJAAA;EACA,qCAAA;EAGA,6BAAA;EACA,uDAAA;EAGA,uDAAA;EAAA,+CAAA;EAAA,uCAAA;EAAA,0DAAA;EACA,2CAAA;EAIA,mCAAA;EACA,oBAAA;AXwpCJ;;AWtpCE;EACE,oCAAA;EAIA,6KAAA;AXypCJ;;AWvpCE;EACE,8BAAA;AX0pCJ;;AWxpCE;EACE,oCAAA;AX2pCJ;;AWzpCE;EACE,mCAAA;EAGA,2BAAA;AX4pCJ;;AW1pCE;;EAEE,gCAAA;EAIA,wBAAA;EACA,2EAAA;AX6pCJ;;AW3pCE;;;;EAIE,mBAAA;EACA,sBAAA;EACA,eAAA;EACA,YAAA;EACA,aAAA;EACA,cAAA;EACA,kCAAA;EACA,cAAA;EACA,gBAAA;EACA,kBAAA;EACA,qBAAA;EACA,UAAA;AX8pCJ;;AW5pCE;EACE,qBAAA;EACA,oBAAA;AX+pCJ;;AW7pCE;EACE,SAAA;EACA,qBAAA;AXgqCJ;;AW9pCE;EACE,oBAAA;EACA,sBAAA;AXiqCJ;;AW/pCE;EACE,UAAA;AXkqCJ;;AWhqCE;EACE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,UAAA;AXmqCJ;;AWjqCE;EACE,kBAAA;EACA,YAAA;EACA,aAAA;EACA,kBAAA;EACA,kBAAA;AXoqCJ;;AWlqCE;EACE,wBAAA;EACA,yDAAA;EACA,iDAAA;EACA,6BAAA;EAGA,qBAAA;AXqqCJ;;AWnqCE;EACE,uDAAA;EACA,+CAAA;AXsqCJ;;AWpqCE;EACE,cAAA;AXuqCJ;;AWnqCI;EACI,0CAAA;AXsqCR;;AWjqCI;EACI,yCAAA;AXoqCR;;AWhqCI;EACI,yCAAA;AXmqCR;;AW/pCI;EACI,yCAAA;AXkqCR;;AW9pCI;EACI,yCAAA;AXiqCR;;AW7pCI;EACI,wCAAA;AXgqCR;;AYj0CA;EACE,YAAA;EACA,WAAA;AZo0CF;;AYj0CA;EACE,YAAA;EACA,WAAA;AZo0CF;;AYj0CA;EACE,cAAA;EACA,aAAA;AZo0CF;;AYj0CA;EACE,YAAA;EACA,WAAA;AZo0CF;;AYj0CA;EACE,cAAA;EACA,aAAA;AZo0CF;;AYj0CA;EACE,yBAAA;MAAA,sBAAA;UAAA,mBAAA;EACA,yBVuFQ;EUtFR,WVyDM;EUxDN,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,gBV4pBmB;EU3pBnB,YAAA;EACA,wBAAA;MAAA,qBAAA;UAAA,uBAAA;EACA,WAAA;AZo0CF;;AY/zCA;EACE,kBAAA;EACA,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,mBAAA;MAAA,eAAA;AZk0CF;AYj0CE;EACE,kBAAA;EACA,wCAAA;EACA,kBAAA;EACA,4BAAA;EAAA,oBAAA;AZm0CJ;AYl0CI;EACE,kBAAA;EACA,mCAAA;UAAA,2BAAA;AZo0CN;;Aal3CQ;EACI,kBAAA;Abq3CZ;Aa92CgB;EACI,iBAAA;Abg3CpB;Aa12CI;EACI,kBAAA;Ab42CR;;Aav2CI;EACI,wCAAA;EACA,wBAAA;UAAA,gBAAA;Ab02CR;Aax2CI;EACI,kBAAA;EACA,kBAAA;Ab02CR;Aax2CQ;EACI,kBAAA;EACA,qBAAA;EACA,WAAA;EACA,YAAA;EACA,iBAAA;EACA,eAAA;EACA,yBX2EF;EW1EE,WX6CJ;EW5CI,kBAAA;EACA,kBAAA;EACA,UAAA;EACA,QAAA;EACA,mCAAA;UAAA,2BAAA;Ab02CZ;Aan2CgB;EACI,iBAAA;Abq2CpB;;Acz5CA;EACI,gCZuEoB;AFq1CxB;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acz5CA;EACI,0BAAA;Ad45CJ;;Acr5CA;EACI,YAAA;EACA,WAAA;EACA,6BAAA;EACA,cAAA;EACA,yBAAA;EACA,kBAAA;EACA,cZ0BO;EYzBP,kBAAA;EACA,4BAAA;EAAA,oBAAA;Adw5CJ;Act5CI;EACI,cZsBG;EYrBH,yBZiBG;AFu4CX;;Acn5CA;EACI,eAAA;Ads5CJ;;Acn5CA;EACI,eAAA;Ads5CJ;;Acn5CA;EACI,gBAAA;Ads5CJ;;Acn5CA;EACI,gBAAA;Ads5CJ;;Acn5CA;EACI,gBAAA;Ads5CJ;;Acj5CA;EACI,kBAAA;EACA,YAAA;EACA,WAAA;EACA,QAAA;EACA,SAAA;EACA,OAAA;EACA,MAAA;EACA,YAAA;EACA,sBZXI;AF+5CR;;Ac/4CA;EACI,mBAAA;MAAA,WAAA;UAAA,OAAA;Adk5CJ;;Ac14CI;EACI,eAAA;EACA,wBAAA;EACA,2WAAA;Ad64CR;;AexgDA;EACI,eAAA;EACA,MAAA;EACA,OAAA;EACA,QAAA;EACA,SAAA;EACA,wCb64CM;Ea54CN,aAAA;Af2gDJ;;AexgDA;EACI,kBAAA;EACA,SAAA;EACA,QAAA;EACA,mCAAA;UAAA,2BAAA;EACA,uBAAA;Af2gDJ;;AevgDI;EACI,eAAA;EACA,cb6FE;Ea5FF,kBAAA;EACA,qBAAA;EACA,4CAAA;UAAA,oCAAA;Af0gDR;;AetgDA;EACI;IACE,+BAAA;YAAA,uBAAA;EfygDJ;EevgDE;IACE,iCAAA;YAAA,yBAAA;EfygDJ;AACF;;Ae/gDA;EACI;IACE,+BAAA;YAAA,uBAAA;EfygDJ;EevgDE;IACE,iCAAA;YAAA,yBAAA;EfygDJ;AACF;AgBxiDA;EACE,eAAA;EACA,qBAAA;EACA,oBdk/BkC;AFwjBpC;AgBziDE;EACE,YAAA;EACA,cAAA;EACA,oBAAA;AhB2iDJ;AgBziDE;EACE,cAAA;AhB2iDJ;;AgBviDA;EACE,kBAAA;EACA,gBAAA;AhB0iDF;;AgBtiDA;EACE,eAAA;EACA,gBAAA;AhByiDF;;AiB9jDA;EACI,eAAA;EACA,iBAAA;AjBikDJ;AiB/jDI;EACI,kBAAA;EACA,sBAAA;AjBikDR;AiB/jDQ;EACI,WAAA;EACA,gDAAA;EACA,kBAAA;EACA,OAAA;EACA,SAAA;EACA,SAAA;AjBikDZ;AiB/jDQ;EACI,kBAAA;EACA,WAAA;EACA,MAAA;EACA,UAAA;AjBikDZ;AiB9jDQ;EACI,mBAAA;AjBgkDZ;;AkB3lDA;EACI,iBAAA;EACA,oBAAA;AlB8lDJ;AkB5lDI;EACI,mBAAA;EACA,gBAAA;AlB8lDR;;AkBxlDA;EACI,iBAAA;AlB2lDJ;;AkBtlDA;EACI,kBAAA;EACA,SAAA;EACA,WAAA;EACA,YAAA;EACA,UAAA;EACA,UAAA;EACA,cAAA;AlBylDJ;;AkBplDA;EACE,chB0DS;AF6hDX;AkBrlDE;EACE,qBAAA;EACA,WAAA;EACA,YAAA;EACA,iBAAA;EACA,eAAA;EACA,chBmDO;EgBlDP,wCAAA;EACA,kBAAA;EACA,4BAAA;EAAA,oBAAA;EACA,kBAAA;EACA,kBAAA;EACA,sBAAA;AlBulDJ;AkBplDE;EACE,gBAAA;AlBslDJ;AkBnlDM;EACE,WhB+BA;EgB9BA,yBhB2DE;EgB1DF,qBhB0DE;AF2hDV;;AkB3kDI;EACI,yBhBmBG;EgBlBH,gBAAA;EACA,iBAAA;EACA,gBhBonBa;EgBnnBb,kBAAA;AlB8kDR;;AkBvkDA;EACE,wChBo0CQ;EgBn0CR,wCAAA;EACA,uBhB+hBc;EgB9hBd,aAAA;EACA,gBAAA;EACA,uBAAA;EACA,mBAAA;EACA,cAAA;AlB0kDF;AkBxkDE;EACE,eAAA;AlB0kDJ;;AkBtkDA;EACE,cAAA;AlBykDF;;AkBrkDA;EACE,aAAA;AlBwkDF;AkBvkDE;EACE,gCAAA;AlBykDJ;;AkBpkDE;EACI,gBAAA;AlBukDN;AkBtkDM;EACI,WAAA;EACA,YAAA;EACA,4BAAA;EACA,0BAAA;EACA,6BAAA;EACA,qDAAA;EACA,yBAAA;EACA,0BAAA;AlBwkDV;;AmB3rDA;EACI;;;;;IAKI,wBAAA;EnB8rDN;EmB5rDE;;;;;IAKI,UAAA;IACA,SAAA;EnB8rDN;EmB3rDE;IACI,SAAA;EnB6rDN;AACF;AoBrtDA;EACE,kBAAA;EACA,4BAAA;EAAA,6BAAA;MAAA,0BAAA;UAAA,sBAAA;EACA,mBAAA;MAAA,eAAA;EACA,uBAAA;MAAA,oBAAA;UAAA,2BAAA;EACA,yBAAA;MAAA,yBAAA;EACA,wBAAA;MAAA,qBAAA;UAAA,uBAAA;ApButDF;;AoBptDA;EACE,gBAAA;EACA,cAAA;EACA,eAAA;EACA,kBAAA;EACA,mBAAA;ApButDF;;AoBptDA;EACE,kBAAA;EACA,kBAAA;EACA,gBAAA;EACA,UAAA;EACA,SAAA;EACA,OAAA;EACA,MAAA;EACA,SAAA;EACA,QAAA;EACA,sBAAA;EACA,uBAAA;EACA,UAAA;ApButDF;;AoBptDA;EACE,6BAAA;EACA,sCAAA;UAAA,8BAAA;EACA,uBAAA;EACA,kBAAA;EACA,MAAA;EACA,kBAAA;EACA,SAAA;EACA,mBAAA;EACA,UAAA;EACA,SAAA;EACA,iCAAA;ApButDF;;AoBptDA;EACE,kBAAA;EACA,yCAAA;UAAA,iCAAA;EACA,kBAAA;EACA,cAAA;EACA,YAAA,EAAA,mGAAA;EACA,WAAA;EACA,mBAAA;EACA,cAAA,EAAA,mFAAA;EACA,eAAA,EAAA,kDAAA;EACA,gBAAA,EAAA,0CAAA;EACA,qBAAA;EACA,uBAAA;ApButDF;;AoBptDA;;EAEE,aAAA;ApButDF;;AoBptDA;;EAEE,YAAA;EACA,cAAA;ApButDF;;AoBptDA;EACE,gBAAA;EACA,eAAA;EACA,WAAA;EACA,oBAAA;ApButDF;;AoBptDA;EACE,sCAAA;UAAA,8BAAA;EACA,YAAA;EACA,WAAA;EACA,cAAA;EACA,kBAAA;EACA,WAAA;EACA,eAAA;EACA,gBAAA;EACA,WAAA;EACA,UAAA;EACA,SAAA;EACA,oBAAA;EACA,yBAAA;MAAA,0BAAA;UAAA,kBAAA;EACA,oBAAA;MAAA,cAAA;EACA,0BAAA;MAAA,aAAA;ApButDF;;AoBptDA;EACE,2BAAA;UAAA,mBAAA;EACA,cAAA;EACA,UAAA;EACA,kBAAA;EACA,MAAA;EACA,OAAA;EACA,aAAA;EACA,YAAA;EACA,eAAA;EACA,cAAA;EACA,gBAAA;EACA,oBAAA;EACA,WAAA;ApButDF;;AoBptDA;EACE,UAAA;EACA,kBAAA;EACA,QAAA;EACA,SAAA;EACA,oBAAA;EACA,gBAAA;ApButDF;;AoBptDA;EACE,oBAAA;EACA,sBAAA;GAAA,qBAAA;OAAA,iBAAA;EACA,yBAAA;ApButDF;;AoBptDA;EACE,mBAAA;ApButDF;;AoBptDA;EACE,kBAAA;EACA,UAAA;EACA,UAAA;EACA,gBAAA;ApButDF;;AoBptDA;EACE,kBAAA;EACA,WAAA;EACA,mBAAA;EACA,kBAAA;EACA,OAAA;EACA,QAAA;EACA,UAAA;EACA,uCAAA;EAAA,+BAAA;ApButDF;;AoBptDA;EACE,0DAAA;EACA,YAAA;EACA,qCAAA;EAAA,6BAAA;ApButDF;;AoBptDA;EACE,MAAA;EACA,WAAA;ApButDF;;AoBptDA;EACE,QAAA;EACA,WAAA;ApButDF;;AoBptDA;EACE,OAAA;EACA,YAAA;ApButDF;;AoBptDA;EACE,YAAA;EACA,SAAA;EACA,UAAA;ApButDF;;AoBptDA;EACE,WAAA;EACA,OAAA;EACA,QAAA;EACA,WAAA;EACA,aAAA;EACA,eAAA;EACA,WAAA;ApButDF;;AoBptDA,gBAAA;AACA;EACE,WAAA;EACA,OAAA;ApButDF;;AoBptDA;EACE,cAAA;EACA,eAAA;EACA,UAAA;EACA,kBAAA;EACA,aAAA;EACA,YAAA;EACA,kBAAA;EACA,kBAAA;ApButDF;;AoBptDA;EACE,eAAA;EACA,OAAA;EACA,kBAAA;EACA,kBAAA;EACA,qBAAA;ApButDF;;AoBptDA;EACE,YAAA;ApButDF;;AqBv6DE;EACI,eAAA;EACA,iBAAA;EACA,yBAAA;ArB06DN;;AqBr6DE;EACI,mBnB4EK;EmB3EL,eAAA;EACA,iBAAA;EACA,eAAA;EACA,yBAAA;EACA,gBnB4qBiB;AF4vCvB;;AqBn6DE;;;;;;;;;;EAUI,oCAAA;ArBs6DN;AqBp6DE;EACI,mBAAA;ArBs6DN;;AqBl6DA;EACE,kCnB62CQ;EmB52CR,oCAAA;EACA,cnBoDS;EmBnDT,0BAAA;EACA,wBAAA;UAAA,gBAAA;EACA,4BAAA;EACA,uBAAA;ArBq6DF;;AqBl6DA;;;EAGE,yBnBgEQ;EmB/DR,WnBkCM;EmBjCN,iBAAA;ArBq6DF;;AqBl6DA;EACE,kBAAA;EACA,YAAA;EACA,YAAA;EACA,oBAAA;EACA,eAAA;EACA,gBAAA;EACA,kBAAA;ArBq6DF;;AqBl6DA;EACE,2BAAA;EACA,iBAAA;ArBq6DF;;AqBl6DA;EACE,yBnB2CQ;AF03DV;;AqBl6DA;EACE,WnBUM;AF25DR;;AqBh6DI;EACE,oCAAA;ArBm6DN;AqB95DI;EADF;IAEI,cAAA;ErBi6DJ;AACF;AqB/5DM;EACI,eAAA;EACA,iBAAA;EACA,yBAAA;ArBi6DV;AqB95DM;EAEI;;;IAGI,WAAA;IACA,cAAA;IACA,kBAAA;IACA,WAAA;IACA,cAAA;ErB+5DZ;EqB55DQ;IACI,WAAA;ErB85DZ;EqB35DQ;IACI,aAAA;ErB65DZ;AACF;AqB15DM;EACI,0BAAA;ArB45DV;;AqBv5DA;EACE,uCAAA;ArB05DF;;AqBv5DA;EACE,iCAAA;ArB05DF;;AqBt5DA;EACE,yBAAA;ArBy5DF;;AqBt5DA;EACE,mBAAA;ArBy5DF;;AsBjiEA;EACE,wCpB+xC0C;AFqwB5C;AsBniEE;EACE,uBAAA;EACE,qBAAA;EACA,qBAAA;EACA,gBAAA;EACA,cpBsFK;AF+8DX;AsBniEM;EACE,yBpB2EG;AF09DX;AsBliEM;EACE,yBpB0GE;EoBzGF,gBAAA;EACA,eAAA;AtBoiER;;AsB/hEA;EACE,8CAAA;AtBkiEF;;AsB/hEA;EACE,wCpBo7BkC;EoBn7BlC,2DAAA;EACA,2BpBu7BkC;AF2mCpC;AsBjiEE;EACE,aAAA;AtBmiEJ;;AsB5hEE;EACE,cAAA;AtB+hEJ;AsB3hEI;EACE,qCAAA;EACA,wCAAA;EACA,sCAAA;EACA,yCAAA;AtB6hEN;AsBzhEE;EACE,oDAAA;EACA,cAAA;EACA,yBAAA;EACA,4BAAA;EACA,gDpBi6BgC;EoBh6BhC,mDpBg6BgC;AF2nCpC;;AuBnlEI;EACI,aAAA;AvBslER;AuBnlEI;EACI,crBgHE;EqB/GF,gBrBmrBa;AFk6CrB;AuBllEI;EACI,sBrB0EA;EqBzEA,crB0GE;EqBzGF,wBAAA;UAAA,gBAAA;AvBolER;;AwBhmEA;EACE,gBAAA;AxBmmEF;;AwBhmEA;EACE,yBtBkFS;AFihEX;;AwBhmEA;EACE,8CAAA;AxBmmEF;;AwBhmEA;EACE,yBAAA;AxBmmEF;;AwBhmEA;EACE,yBtB0ES;AFyhEX;;AwB/lEE;EACE,yBAAA;AxBkmEJ;;AwB9lEA;EACE,UAAA;AxBimEF;;AwB3lEE;EACE,iBAAA;AxB8lEJ;AwB7lEI;EACE,0BAAA;AxB+lEN;AwB5lEE;EACE,yBAAA;AxB8lEJ;AwB5lEE;EACE,6BAAA;AxB8lEJ;;AwBvlEE;EACE,mBAAA;AxB0lEJ;AwBxlEE;EACE,yBAAA;AxB0lEJ;;AwBnlEQ;EACE,yBtBoUK;AFkxDf;AwBnlEQ;EACE,yBAAA;EACA,qBtB+TK;AFsxDf;AwBplEU;EACE,yBtB6TG;AFyxDf;AwBjlEU;EACE,yBtBuTG;AF4xDf;AwB9kEU;EACE,qBAAA;AxBglEZ;;AwBpmEQ;EACE,yBtBoUK;AFmyDf;AwBpmEQ;EACE,yBAAA;EACA,qBtB+TK;AFuyDf;AwBrmEU;EACE,yBtB6TG;AF0yDf;AwBlmEU;EACE,yBtBuTG;AF6yDf;AwB/lEU;EACE,qBAAA;AxBimEZ;;AwBrnEQ;EACE,yBtBoUK;AFozDf;AwBrnEQ;EACE,yBAAA;EACA,qBtB+TK;AFwzDf;AwBtnEU;EACE,yBtB6TG;AF2zDf;AwBnnEU;EACE,yBtBuTG;AF8zDf;AwBhnEU;EACE,qBAAA;AxBknEZ;;AwBtoEQ;EACE,yBtBoUK;AFq0Df;AwBtoEQ;EACE,yBAAA;EACA,qBtB+TK;AFy0Df;AwBvoEU;EACE,yBtB6TG;AF40Df;AwBpoEU;EACE,yBtBuTG;AF+0Df;AwBjoEU;EACE,qBAAA;AxBmoEZ;;AwBvpEQ;EACE,yBtBoUK;AFs1Df;AwBvpEQ;EACE,yBAAA;EACA,qBtB+TK;AF01Df;AwBxpEU;EACE,yBtB6TG;AF61Df;AwBrpEU;EACE,yBtBuTG;AFg2Df;AwBlpEU;EACE,qBAAA;AxBopEZ;;AwBxqEQ;EACE,yBtBoUK;AFu2Df;AwBxqEQ;EACE,yBAAA;EACA,qBtB+TK;AF22Df;AwBzqEU;EACE,yBtB6TG;AF82Df;AwBtqEU;EACE,yBtBuTG;AFi3Df;AwBnqEU;EACE,qBAAA;AxBqqEZ;;AwBzrEQ;EACE,yBtBoUK;AFw3Df;AwBzrEQ;EACE,uBAAA;EACA,qBtB+TK;AF43Df;AwB1rEU;EACE,yBtB6TG;AF+3Df;AwBvrEU;EACE,yBtBuTG;AFk4Df;AwBprEU;EACE,mBAAA;AxBsrEZ;;AwB1sEQ;EACE,yBtBoUK;AFy4Df;AwB1sEQ;EACE,yBAAA;EACA,qBtB+TK;AF64Df;AwB3sEU;EACE,yBtB6TG;AFg5Df;AwBxsEU;EACE,yBtBuTG;AFm5Df;AwBrsEU;EACE,qBAAA;AxBusEZ;;AwB9rEI;EACE,6BAAA;EACA,6BAAA;EACA,2BtBFK;EsBGL,oCAAA;EACA,+BAAA;AxBisEN;AwBhsEM;EACE,cAAA;EACA,YAAA;EACA,kBAAA;EACA,YAAA;EACA,WAAA;EACA,mBtBXG;EsBYH,YAAA;EACA,aAAA;EACA,oBAAA;AxBksER;;AwB3rEE;EACE,6BAAA;EACA,6BAAA;EACA,2BtBxBO;EsByBP,gCAAA;AxB8rEJ;AwB7rEI;EACE,cAAA;EACA,YAAA;EACA,kBAAA;EACA,YAAA;EACA,WAAA;EACA,mBtBhCK;EsBiCL,WAAA;EACA,YAAA;EACA,mBAAA;AxB+rEN;AwB7rEE;EACE,cAAA;EACA,YAAA;EACA,WAAA;EACA,kBAAA;EACA,SAAA;EACA,UAAA;EACA,8BAAA;AxB+rEJ;;AyBv0EA;EACI,sCvB4qBe;AF8pDnB;;AyBr0EI;;;;EAII,8BAAA;EACA,eAAA;AzBw0ER;AyBl0EQ;;;EACI,aAAA;AzBs0EZ;AyBl0EI;EACI,iCAAA;EACA,oCAAA;AzBo0ER;AyBj0EI;EACI,eAAA;EACA,cvB6DG;AFswEX;AyBh0EI;;EAEI,cvBwDG;EuBvDH,iCAAA;EACA,eAAA;AzBk0ER;AyB/zEI;EACI,yBAAA;EACA,WAAA;EACA,YAAA;EACA,SAAA;EACA,mDAAA;AzBi0ER;;A0B32EE;EACE,eAAA;EACA,gBxBsrBiB;AFwrDrB;;A0B12EA;EACE,eAAA;A1B62EF;;A0Bz2EE;EACE,qBxB6GM;EwB5GN,cxB4GM;AFgwEV;A0Bz2EI;EACE,yBxBsGI;AFqwEV;A0Bx2EI;EACE,qCAAA;A1B02EN;A0Bv2EE;EACE,qBxB6FM;EwB5FN,cxB4FM;AF6wEV;;A0Bp2EE;EACE,wBAAA;UAAA,gBAAA;A1Bu2EJ;;A0Bl2EE;EACE,mBxB0EM;AF2xEV;A0Bp2EI;EACE,mBxBwEI;AF8xEV;A0Br2EM;EACE,mCAAA;A1Bu2ER;A0Bl2EE;EACE,mBxBgEM;AFoyEV;;A0Bh2EA;EACE,qDAAA;A1Bm2EF;;A0Bh2EA;EACE,mDAAA;EACA,sCAAA;A1Bm2EF;;A0Bj2EA;EACE,mDAAA;EACE,2CAAA;A1Bo2EJ;;A0Bh2EA;EACE,2CAAA;A1Bm2EF;;A2Bv6EA;EACE,oCzBi5CQ;AFyhCV;;A2Bv6EA;EACE,eAAA;A3B06EF;;A2Bv6EA;EACE,QAAA;A3B06EF;;A2Bt6EE;EACE,qBAAA;EACA,sBAAA;A3By6EJ;A2Bv6EI;EACE,gBAAA;A3By6EN;;A4B17EA;;eAAA;AAII;EACI,iD1BsnBsB;U0BtnBtB,yC1BsnBsB;E0BrnBtB,UAAA;A5B47ER;A4B37EQ;EACI,iD1BmnBkB;U0BnnBlB,yC1BmnBkB;E0BlnBlB,YAAA;A5B67EZ;A4Bx7EQ;EACE,cAAA;EACA,gBAAA;A5B07EV;;A4Bl7EI;EACI,oCAAA;EACA,oDAAA;A5Bq7ER;;A4Bv7EI;EACI,oCAAA;EACA,qDAAA;A5B07ER;;A4B57EI;EACI,oCAAA;EACA,oDAAA;A5B+7ER;;A4Bj8EI;EACI,oCAAA;EACA,oDAAA;A5Bo8ER;;A4Bt8EI;EACI,oCAAA;EACA,oDAAA;A5By8ER;;A4B38EI;EACI,oCAAA;EACA,mDAAA;A5B88ER;;A4Bh9EI;EACI,oCAAA;EACA,qDAAA;A5Bm9ER;;A4Br9EI;EACI,oCAAA;EACA,kDAAA;A5Bw9ER;;A4Bj9EA;EACI,wCAAA;EACA,yBAAA;A5Bo9EJ;;A4Bj9EA;EACI,aAAA;EACA,uCAAA;EACA,gBAAA;EACA,wCAAA;A5Bo9EJ;;A6B//EA;EACE,c3BoHQ;AF84EV;;A6B//EA;EACE,qB3BgHQ;AFk5EV;;A6B//EA;EACE,aAAA;EACA,SAAA;EACA,UAAA;A7BkgFF;A6BjgFE;EACE,cAAA;A7BmgFJ;A6BjgFE;EACE,eAAA;EACA,gBAAA;EACA,c3BmGM;E2BlGN,eAAA;A7BmgFJ;;A8BthFA;EACE,cAAA;A9ByhFF;A8BxhFE;EACE,wC5B28BgC;E4B18BhC,oDAAA;EACA,YAAA;A9B0hFJ;A8BzhFI;EACE,aAAA;A9B2hFN;A8BxhFI;EACE,iBAAA;EACA,kBAAA;EACA,2B5Bs8B8B;AFolDpC;A8BvhFI;EACE,YAAA;EACA,WAAA;EACA,UAAA;A9ByhFN;A8BvhFM;EACE,yDAAA;EACA,2BAAA;A9ByhFR;A8BrhFI;EACE,2BAAA;A9BuhFN;;A8B7gFM;EACE,oEAAA;EACA,sCAAA;A9BghFR;;A8BzgFE;EACI,aAAA;EACA,wC5B+uCsC;AF6xC5C;A8B3gFM;EACI,oDAAA;EACA,wC5B05B0B;E4Bz5B1B,c5BqCC;E4BpCD,aAAA;A9B6gFV;A8B1gFE;EACI,yB5BuDI;AFq9EV;A8B1gFE;EACI,uC5BivCsC;E4BhvCtC,gC5B+uCsC;AF6xC5C;A8B3gFM;EACI,yB5BiDA;E4BhDA,W5BmBF;AF0/ER;;A8BxgFA;EACE,iBAAA;A9B2gFF;;A8BxgFA;EACE,wCAAA;EACA,wC5BotC0C;E4BntC1C,iD5B+iB4B;U4B/iB5B,yC5B+iB4B;AF49D9B;;A8BvgFE;EACE,wCAAA;A9B0gFJ;;A8BrgFE;EACE,gBAAA;EACA,wC5Bq3BgC;E4Bp3BhC,+DAAA;A9BwgFJ;A8BtgFI;EACE,iBAAA;A9BwgFN;A8BtgFI;EACE,SAAA;EACA,2B5Bk3B8B;AFspDpC;A8BvgFM;EACI,2B5Bg3B0B;AFypDpC;A8B1gFM;EACI,2B5Bg3B0B;AFypDpC;A8B1gFM;EACI,2B5Bg3B0B;AFypDpC;A8B1gFM;EACI,2B5Bg3B0B;AFypDpC;A8B1gFM;EACI,2B5Bg3B0B;AFypDpC;A8BtgFI;EACE,uCAAA;EACA,wCAAA;EACA,kBAAA;EACA,cAAA;A9BwgFN;;A8BjgFI;EACE,oCAAA;A9BogFN;A8BhgFE;EACE,gB5BmkBmB;AF+7DvB;;A8B5/EA;EACI,WAAA;EACA,WAAA;EACA,kBAAA;A9B+/EJ;A8B9/EE;EACE,WAAA;EACA,YAAA;EACA,kBAAA;A9BggFJ;;A8B5/EA;EACE,eAAA;A9B+/EF;;A8B5/EA;;;EAGE,qBAAA;EACA,eAAA;EACA,iBAAA;EACA,c5BzDS;AFwjFX;A8B7/EE;;;EACE,iBAAA;A9BigFJ;A8B9/EM;;;EACE,gBAAA;EACA,kCAAA;A9BkgFR;;A8B3/EE;;;EAGA,+BAAA;A9B8/EF;;A8B1/EA;EACE,gBAAA;A9B6/EF;;A8Bv/EA;EACE,iBAAA;EACA,YAAA;EACA,WAAA;A9B0/EF;;A+B7qFA,eAAA;AACA;EACE,aAAA;A/BgrFF;A+B/qFE;EACE,cAAA;EACA,cAAA;EACA,WAAA;EACA,YAAA;EACA,yB7BiFO;E6BhFP,sBAAA;EACA,mBAAA;EACA,mBAAA;EACA,eAAA;EACA,qBAAA;EACA,kBAAA;EACA,kBAAA;EACA,gB7B0qBiB;E6BzqBjB,wCAAA;EAAA,gCAAA;A/BirFJ;A+BhrFI;EACE,c7B2EK;E6B1EL,6BAAA;EACA,cAAA;EACA,oBAAA;EACA,gBAAA;EACA,eAAA;EACA,iBAAA;EACA,kBAAA;EACA,UAAA;EACA,WAAA;EACA,SAAA;EACA,kBAAA;EACA,qBAAA;EACA,gBAAA;EACA,wCAAA;EAAA,gCAAA;A/BkrFN;A+B/qFI;EACE,WAAA;EACA,kBAAA;EACA,SAAA;EACA,yB7B+CK;E6B9CL,wBAAA;UAAA,gBAAA;EACA,mBAAA;EACA,YAAA;EACA,WAAA;EACA,QAAA;EACA,wCAAA;EAAA,gCAAA;A/BirFN;A+B7qFE;EACE,yB7B+DM;AFgnFV;;A+B3qFA;EACE,yB7B0DQ;AFonFV;A+B7qFE;EACE,W7B2BI;E6B1BJ,4BAAA;EACA,WAAA;EACA,SAAA;A/B+qFJ;A+B5qFE;EACE,UAAA;EACA,yB7BqBO;AFypFX;;A+B1qFA;EACE,yB7B+CQ;AF8nFV;;A+B3qFA;;EAEE,W7BUM;AFoqFR;;A+B3qFA;EACE,yB7B0CQ;AFooFV;;A+B3qFA;EACE,yBAAA;A/B8qFF;;A+B3qFA;EACE,yB7B2BQ;AFmpFV;;A+B3qFA;EACE,yB7B8BQ;AFgpFV;;A+B3qFA;EACE,yB7B4BQ;AFkpFV;;A+B3qFA;EACE,yB7BqBQ;AFypFV;;A+B3qFA;EACE,yB7BeQ;AF+pFV;;A+B3qFA;EACE,yB7BbS;AF2rFX;;A+B3qFA;EACE,iBAAA;A/B8qFF;A+B7qFE;EACE,kBAAA;A/B+qFJ;;AgC/xFA;EACE,yBAAA;EACA,YAAA;EACA,uBAAA;AhCkyFF;AgC/xFM;EACE,gBAAA;AhCiyFR;AgC9xFQ;EAGE,oCAAA;EACA,sBAAA;EACA,wBAAA;UAAA,gBAAA;EACA,sBAAA;AhC8xFV;AgC3xFQ;;;EAII,mB9B+DD;AF6tFX;AgCzxFQ;;;EAII,c9B2DD;E8B1DC,YAAA;AhC0xFZ;AgCvxFQ;EACI,yB9BoDD;AFquFX;;AgCjxFE;EACE,YAAA;AhCoxFJ;;AiC5zFQ;EACA,0BAAA;EACA,6BAAA;AjC+zFR;AiCxzFQ;EACE,yBAAA;EACA,4BAAA;AjC0zFV;;AkCz0FE;EACE,UAAA;AlC40FJ;;AkCv0FE;EACE,iBAAA;AlC00FJ;AkCx0FI;EAHF;IAII,kBAAA;ElC20FJ;AACF;AkCx0FI;EACE,kBAAA;EACA,eAAA;AlC00FN;;AkCn0FI;EACE,aAAA;AlCs0FN;;AkCh0FE;EACE,eAAA;AlCm0FJ;AkCh0FE;EACE,gBAAA;AlCk0FJ;;AkC7zFA;EACE,oCAAA;EACA,8BAAA;AlCg0FF;AkCtzFM;;;;;EACE,UAAA;EACA,aAAA;EACA,iBAAA;EACA,oCAAA;EACA,eAAA;EACA,QAAA;AlC4zFR;AkCvzFM;;;;;EACE,UAAA;EACA,YAAA;EACA,iBAAA;EACA,oCAAA;EACA,SAAA;EACA,eAAA;AlC6zFR;AkCpzFQ;;;;EAGE,kBAAA;EACA,mBAAA;AlCuzFV;AkC9yFI;EACI,yCAAA;AlCgzFR;AkC9yFQ;EACI,qCAAA;EACA,chCiBF;AF+xFV;AkC5yFQ;EACI,wBAAA;AlC8yFZ;AkC1yFI;EACI,qCAAA;EACA,oBAAA;EACA,0CAAA;AlC4yFR;;AkCvyFA;EACE,gBhCwkBqB;AFkuEvB;;AkC9xFU;;EACE,iDhC8fkB;UgC9flB,yChC8fkB;EgC7flB,yBhCRF;EgCSE,YAAA;AlCkyFZ;AkCvxFU;;EACE,yBhCxBF;AFkzFV;;AkChxFA;EACE,yBhCvCQ;EgCwCR,YAAA;EACA,WhCtEM;EgCuEN,wBAAA;UAAA,gBAAA;EACA,kBAAA;EACA,kBAAA;EACA,WAAA;AlCmxFF;AkCjxFE;EACE,mBAAA;EACA,0CAAA;EACA,WhC/EI;AFk2FR;;AM/2FI;E4BkGF;;IAEE,qBAAA;IACA,iBAAA;ElCixFF;EkC9wFA;IACE,aAAA;ElCgxFF;EkC5wFE;IACE,kBAAA;IACA,cAAA;IACA,2BAAA;ElC8wFJ;EkC1wFA;IACE,qBAAA;IACA,mBhCuSK;EFq+EP;AACF;AkCvwFE;EACE,yBhC/GO;AFw3FX;;AkChwFA;EACE,2DAAA;AlCmwFF;;AkC3vFI;;EACE,kBAAA;AlC+vFN;AkC7vFM;;EACE,kBAAA;AlCgwFR;AkC9vFQ;;EACE,QAAA;EACA,SAAA;EACA,YAAA;EACA,WAAA;EACA,iBAAA;EACA,cAAA;EACA,kBAAA;EACA,WhChJF;EgCiJE,sBAAA;EACA,mBAAA;EACA,+BAAA;UAAA,uBAAA;EACA,kBAAA;EACA,yBAAA;EACA,iBAAA;EACA,YAAA;EACA,yBhC3HA;AF43FV;;AmC5+FA;EACI,mDAAA;AnC++FJ;;AmC3+FI;EACI,uDAAA;AnC8+FR;AmC3+FI;;;EAGI,mDAAA;EACA,2BAAA;AnC6+FR;AmC1+FI;EACI,sCAAA;AnC4+FR;AmC1+FQ;EACI,kDAAA;AnC4+FZ;AmCv+FQ;EACI,kDAAA;AnCy+FZ;AmCr+FI;EACI,+CAAA;AnCu+FR;AmCp+FI;;;EAGI,4CAAA;AnCs+FR;AmCn+FI;EACI,sCAAA;AnCq+FR;AmCn+FQ;EACI,qCAAA;AnCq+FZ;AmCj+FI;;;EAGI,sCAAA;AnCm+FR;AmCh+FI;EACI,yDAAA;AnCk+FR;;AmC/9FA;EACI,wBAAA;AnCk+FJ;;AmC/9FA;EACI,kDAAA;AnCk+FJ;;AoCliGA,aAAA;AACA;EACE,iBAAA;EACA,yCAAA;EACA,kClC84CQ;EkC74CR,kBAAA;ApCqiGF;AoCniGE;EACE,eAAA;EACA,WAAA;ApCqiGJ;;AoCjiGA;EACE,uBAAA;ApCoiGF;;AqC/iGI;EACI,kBAAA;ArCkjGR;AqChjGQ;EACI,WAAA;EACA,WAAA;EACA,WAAA;EACA,wCAAA;EACA,kBAAA;EACA,OAAA;EACA,SAAA;ArCkjGZ;AqC/iGQ;EACI,qBAAA;EACA,WAAA;EACA,YAAA;EACA,iBAAA;EACA,yBAAA;EACA,cnC2FF;EmC1FE,kBAAA;EACA,kBAAA;EACA,kBAAA;EACA,wCnCw3CF;AFyrDV;AqC/iGY;EAZJ;IAaQ,cAAA;IACA,6BAAA;ErCkjGd;AACF;AqC9iGY;EACI,cAAA;EACA,eAAA;EACA,gBnCqpBG;AF25EnB;AqC9iGgB;EALJ;IAMQ,aAAA;ErCijGlB;AACF;AqC9iGY;EACI,6BAAA;EACA,2BAAA;ArCgjGhB;AqC9iGgB;EACI,yBnC+DV;EmC9DU,WnCiCZ;AF+gGR;AqC1iGI;EACI,iBAAA;EACA,eAAA;EACA,gBAAA;EACA,gBAAA;ArC4iGR;AqC1iGQ;EACI,qBAAA;ArC4iGZ;AqC1iGY;EACI,qBAAA;EACA,wBAAA;EACA,yBnC4CN;EmC3CM,WnCcR;EmCbQ,sBAAA;ArC4iGhB;AqCxiGgB;EACI,mBAAA;EACA,yBAAA;ArC0iGpB;AqCtiGY;EACI,YAAA;ArCwiGhB;;AqCliGA;EACI,iBAAA;EACA,iBAAA;ArCqiGJ;;AsC9nGE;EACE,cAAA;AtCioGJ;AsC/nGE;EACE,uBAAA;AtCioGJ;AsC9nGI;EACE,yBpC4EK;EoC3EL,cpCmFK;EoClFL,yBAAA;AtCgoGN;AsC/nGM;EACI,yBpCoGA;EoCnGA,qBpCmGA;EoClGA,WpCqEF;AF4jGR;AsC9nGI;EACE,YAAA;AtCgoGN;AsC/nGM;EACE,QAAA;EACA,kCAAA;UAAA,0BAAA;EACA,oBAAA;AtCioGR;AsC5nGI;EACE,eAAA;EACA,mBAAA;AtC8nGN;AsC1nGE;EACE,kBAAA;EACA,sCAAA;AtC4nGJ;AsC1nGI;EACE,oCAAA;AtC4nGN;AsCznGI;EACE,qBAAA;EACA,iBAAA;EACA,kBAAA;AtC2nGN;AsC1nGM;EACE,+BAAA;EACA,oCAAA;EACA,sBpCmCA;EoClCA,kBAAA;EACA,yBAAA;EACA,WAAA;EACA,qBAAA;EACA,YAAA;EACA,OAAA;EACA,kBAAA;EACA,kBAAA;EACA,4BAAA;EACA,WAAA;EACA,wBAAA;AtC4nGR;AsC1nGM;EACE,cpCuBG;EoCtBH,qBAAA;EACA,eAAA;EACA,YAAA;EACA,OAAA;EACA,kBAAA;EACA,iBAAA;EACA,gBAAA;EACA,kBAAA;EACA,SAAA;EACA,WAAA;AtC4nGR;AsCznGI;EACE,eAAA;EACA,UAAA;EACA,UAAA;EACA,wBAAA;AtC2nGN;AsCznGM;EACE,aAAA;AtC2nGR;AsCvnGM;EACE,oBAAA;EACA,aAAA;AtCynGR;AsCrnGM;EACE,gBAAA;EACA,kCAAA;EACA,gBAAA;AtCunGR;AsCnnGM;EACE,yBpChBG;EoCiBH,mBAAA;AtCqnGR;AsCjnGM;EACE,yBpCME;EoCLF,qBpCKE;AF8mGV;AsCjnGM;EACE,WpC3BA;AF8oGR;AsC7mGI;EACE,oBAAA;EACA,yBpCNI;AFqnGV;AsC9mGM;EACE,WpCrCA;AFqpGR;;AsCzmGE;EAEI;IACE,qBAAA;EtC2mGN;AACF;;AuC9uGE;EACE,+DrC0+BgC;EqCz+BhC,uBAAA;EACA,oDAAA;EACA,wCrCy8BgC;EqCx8BhC,2BrC68BgC;EqC58BhC,sCrCi9BgC;AFgyEpC;AuChvGI;EACE,aAAA;EACA,qBrCm9B8B;AF+xEpC;;AwC7vGA;EACI,2BAAA;AxCgwGJ;AwC/vGI;EACI,iDAAA;EACA,atCsFG;AF2qGX;AwC/vGI;EACI,cAAA;AxCiwGR;;AwC7vGA;EACI,8CAAA;EACA,mDAAA;AxCgwGJ;;AwC9vGA;EACI,8CAAA;EACA,0DAAA;AxCiwGJ;;AwC/vGA;;EAEI,iDAAA;AxCkwGJ;;AwC/vGA;EACI,gBtCiqBiB;AFimFrB;;AwC/vGA;EACI,oBAAA;EACA,eCvBO;AzCyxGX;;AwC/vGA;EACI,yBAAA;EACA,iDAAA;EACA,0BAAA;AxCkwGJ;;AwC/vGA;EACI,qBAAA;AxCkwGJ;;AwC7vGI;;EACI,iDAAA;EACA,atC2CG;AFstGX;;AwC7vGA;EACI,yCAAA;AxCgwGJ;;AwC7vGA;EACI,yCAAA;AxCgwGJ;;A0C1zGA,eAAA;AACA;EACE,aAAA;A1C6zGF;;A0C1zGA;EACE,iBAAA;EACA,uCAAA;EACA,YAAA;EACA,cxC+ES;EwC9ET,iDxCqnB4B;UwCrnB5B,yCxCqnB4B;EwCpnB5B,kBAAA;A1C6zGF;;A0C1zGA;EACE,cxC6ES;AFgvGX;;A2C10GA;EACE,+BAAA;UAAA,uBAAA;EACA,sBAAA;EACA,uBAAA;EACA,oCAAA;EACA,iDzCynB4B;UyCznB5B,yCzCynB4B;EyCxnB5B,4BAAA;EACA,kBAAA;EACA,gCAAA;A3C60GF;;A2C10GA;EACE,yBAAA;EACA,0BAAA;EACA,4BAAA;EACA,iDAAA;EACA,2BAAA;A3C60GF;;A4C51GA;EACE,aAAA;EACA,mB1CmFS;E0ClFT,kBAAA;A5C+1GF;;A4C51GA;EACE,cAAA;EACA,kBAAA;EACA,W1C2EM;E0C1EN,eAAA;EACA,iBAAA;EACA,mB1CqGQ;E0CpGR,kBAAA;EACA,kBAAA;A5C+1GF;;A4C51GA;EACE,SAAA;EACA,kBAAA;EACA,QAAA;EACA,SAAA;EACA,kBAAA;A5C+1GF;A4C91GE;EACE,aAAA;EACA,mCAAA;EACA,oCAAA;EACA,8BAAA;A5Cg2GJ;A4C91GE;EACE,UAAA;EACA,mCAAA;EACA,oCAAA;EACA,iCAAA;A5Cg2GJ;;A6Cl4GA;EACI,YAAA;EACA,mB3C2FO;E2C1FP,c3CmFO;E2ClFP,sC3CyqBe;E2CxqBf,iB3C8qB4B;E2C7qB5B,gBAAA;A7Cq4GJ;;A8C14GI;EACE,qBAAA;A9C64GN;;A8Cz4GE;EACE,gBAAA;A9C44GJ;A8C34GI;EACE,gBAAA;A9C64GN;;A+Ct5GA;EACI,wC7Ck5CM;AFugEV;;A+Ct5GA;EACI,wDAAA;EACA,aAAA;EACA,sBAAA;EACA,2BAAA;A/Cy5GJ;A+Cx5GI;EACI,yBAAA;A/C05GR;A+Cv5GI;EATJ;IAUQ,aAAA;E/C05GN;AACF;;A+Cv5GA;EACI,aAAA;EACA,oBAAA;EAAA,oBAAA;EAAA,aAAA;A/C05GJ;;A+Cv5GA;EACI,kBAAA;A/C05GJ;A+Cz5GI;EACI,YAAA;EACA,iBAAA;EACA,kBAAA;A/C25GR;A+Cx5GI;EACI,kBAAA;EACA,QAAA;EACA,UAAA;A/C05GR;A+Cv5GI;EACI,kBAAA;EACA,QAAA;EACA,mCAAA;UAAA,2BAAA;EACA,UAAA;EACA,eAAA;EACA,c7CuEE;AFk1GV;;A+Cp5GI;EACI,a7CEO;AFq5Gf;A+Cr5GI;EACI,cAAA;A/Cu5GR;;A+Cj5GQ;EACI,cAAA;A/Co5GZ;A+Cl5GQ;EACI,aAAA;A/Co5GZ;;AgD/8GI;EACI,mBAAA;EACA,kBAAA;AhDk9GR;AgDh9GI;EACI,eAAA;EACA,kBAAA;EACA,UAAA;EACA,MAAA;EACA,iBAAA;AhDk9GR;;AgD38GI;EACI,+CAAA;AhD88GR;AgD38GQ;EACI,SAAA;AhD68GZ;AgDz8GI;EACI,SAAA;EACA,cAAA;EACA,2BAAA;EACA,gB9C0pBa;E8CzpBb,iBAAA;AhD28GR;AgDr8GI;EACI,iBAAA;AhDu8GR;AgDp8GI;EACI,iBAAA;EACA,cAAA;EACA,oCAAA;EACA,eAAA;EACA,kBAAA;EACA,WAAA;EACA,gB9CwoBa;E8CvoBb,QAAA;EACA,sBAAA;EACA,mCAAA;UAAA,2BAAA;AhDs8GR;AgDn8GI;EACI,cAAA;EACA,SAAA;AhDq8GR;AgDl8GY;EACI,cAAA;EACA,iBAAA;EACA,2BAAA;AhDo8GhB;AgDh8GgB;EACI,c9C6CV;AFq5GV;;AgDr7GQ;EACI,eAAA;AhDw7GZ;AgDt7GY;EACI,uCAAA;AhDw7GhB;;AgDj7GI;EACI,qBAAA;EACA,kBAAA;EACA,2BAAA;AhDo7GR;AgDl7GQ;EACI,WAAA;EACA,wCAAA;EACA,kBAAA;AhDo7GZ;AgDl7GQ;EACI,c9CUF;AF06GV;AgDn7GY;EACI,gCAAA;AhDq7GhB;;AgD96GI;EACI,2BAAA;AhDi7GR;AgDh7GQ;EACI,2BAAA;EACA,YAAA;AhDk7GZ;AgD96GY;EACI,c9CRN;AFw7GV;;AgD16GA;EACI,aAAA;EACA,wCAAA;EACA,4BAAA;EAAA,oBAAA;AhD66GJ;AgD36GI;EACI,iD9CufsB;U8CvftB,yC9CufsB;AFs7F9B;;AgDz6GA;EACI,kBAAA;AhD46GJ;AgD16GI;EACI,kBAAA;EACA,MAAA;EACA,WAAA;EACA,gBAAA;EACA,gCAAA;AhD46GR;AgDz6GI;EACI,kBAAA;EACA,MAAA;EACA,QAAA;AhD26GR;AgD16GQ;EACI,qBAAA;EACA,WAAA;EACA,YAAA;EACA,wCAAA;EACA,iBAAA;EACA,kBAAA;EACA,kBAAA;EACA,c9CvED;AFm/GX;;AgDr6GQ;EACI,kBAAA;AhDw6GZ;AgDv6GY;EACI,uCAAA;AhDy6GhB;AgDr6GY;EACI,WAAA;AhDu6GhB;AgDl6GI;EACI,wCAAA;EACA,aAAA;AhDo6GR;;AgD/5GI;EACI,gBAAA;AhDk6GR;;AgD55GQ;EACI,c9CzGD;AFwgHX;AgD75GQ;EACI,kBAAA;AhD+5GZ;;AgDx5GA;EACI,wCAAA;EACA,wC9C+vBgC;E8C9vBhC,uB9C+ZY;AF4/FhB;AgD15GI;EACI,yBAAA;EACA,YAAA;AhD45GR;AgDz5GI;EACI,wCAAA;EACA,oCAAA;EACA,yBAAA;EACA,eAAA;EACA,iBAAA;EACA,wBAAA;UAAA,gBAAA;AhD25GR;;AgDp5GA;EACI,wBAAA;UAAA,gBAAA;AhDu5GJ;AgDt5GI;EACI,gCAAA;AhDw5GR;;AiDnoHA;;oBAAA;AAGA;EACE,YAAA;EACA,WAAA;EACA,aAAA;EACA,kBAAA;AjDsoHF;;AiDnoHA;EACE,kBAAA;AjDsoHF;;AiDloHE;EACE,2BAAA;EACA,gBAAA;AjDqoHJ;AiDnoHE;EACE,eAAA;AjDqoHJ;;AiDjoHA;EACE;IACE,WAAA;IACA,WAAA;EjDooHF;EiDloHA;IACE,SAAA;EjDooHF;AACF;AiD/nHE;EACE,cAAA;EACA,c/CyDO;E+CxDP,iBAAA;EACA,gBAAA;AjDioHJ;AiDhoHI;EACE,c/CgFI;E+C/EJ,gBAAA;AjDkoHN;;AiD7nHA;EACE,cAAA;EACA,eAAA;AjDgoHF;AiD9nHE;EACE,kBAAA;EACA,cAAA;EACA,YAAA;EACA,iBAAA;EACA,eAAA;EACA,iCAAA;UAAA,yBAAA;AjDgoHJ;AiD9nHI;EACE,c/CkCK;AF8lHX;AiD7nHI;EACE,iCAAA;EACA,kCAAA;UAAA,0BAAA;AjD+nHN;AiD5nHI;EACE,WAAA;EACA,kBAAA;AjD8nHN;AiD3nHI;EACE,YAAA;AjD6nHN;AiD3nHM;;;EAGE,cAAA;EACA,WAAA;AjD6nHR;AiD1nHM;EACE,6BAAA;EACA,oBAAA;EACA,mBAAA;EACA,SAAA;EACA,QAAA;EACA,cAAA;EACA,YAAA;AjD4nHR;AiDznHM;EACE,wBAAA;AjD2nHR;AiDxnHM;EACE,gBAAA;EACA,gBAAA;AjD0nHR;AiDvnHM;EACE,kBAAA;EACA,MAAA;EACA,WAAA;EACA,QAAA;EACA,uBAAA;EACA,gBAAA;EACA,mBAAA;EACA,gBAAA;AjDynHR;AiDrnHI;EACE,kBAAA;EACA,MAAA;EACA,WAAA;EACA,QAAA;EACA,SAAA;AjDunHN;AiDrnHM;;EAEE,kBAAA;EACA,MAAA;AjDunHR;AiDpnHM;EACE,OAAA;EACA,YAAA;EACA,uBAAA;EACA,gBAAA;EACA,mBAAA;AjDsnHR;AiDnnHM;EACE,QAAA;EACA,YAAA;EACA,kBAAA;AjDqnHR;AiDjnHI;EAEE,yCAAA;UAAA,iCAAA;AjDknHN;AiD9mHE;EACE,uCAAA;EACA,gBAAA;EACA,2BAAA;AjDgnHJ;AiD/mHM;EACE,2BAAA;EACA,gBAAA;AjDinHR;AiD3mHE;EACE,eAAA;EACA,YAAA;EACA,WAAA;EACA,kBAAA;EACA,qBAAA;EACA,2CAAA;UAAA,mCAAA;EACA,kBAAA;AjD6mHJ;AiD3mHI;EACE,UAAA;EACA,eAAA;AjD6mHN;AiD3mHI;EACE,UAAA;AjD6mHN;AiD1mHI;EACE,kBAAA;EACA,YAAA;EACA,WAAA;EACA,OAAA;EACA,eAAA;EACA,UAAA;EACA,gBAAA;EACA,kCAAA;UAAA,0BAAA;EACA,MAAA;AjD4mHN;AiD3mHM;EACE,iBAAA;EACA,oCAAA;EACA,MAAA;EACA,YAAA;EACA,cAAA;EACA,WAAA;EACA,kBAAA;EACA,iBAAA;EACA,SAAA;EACA,eAAA;AjD6mHR;;AiDvmHA;EACE;IACI,YAAA;EjD0mHJ;AACF;AkDrzHA;EACI,wChDk5CM;EgDj5CN,oCAAA;EACA,iDhD0nB0B;UgD1nB1B,yChD0nB0B;AF6rG9B;AkDrzHI;EALJ;IAMQ,gBAAA;ElDwzHN;AACF;AkDpzHQ;EACI,uCAAA;AlDszHZ;AkDnzHgB;EACI,wChDm4CV;EgDl4CU,chDiGV;AFotHV;;AkD3yHQ;EACI,WAAA;EACA,kBAAA;EACA,UAAA;EACA,WAAA;EACA,yBhDsFF;EgDrFE,kBAAA;EACA,QAAA;AlD8yHZ;AkD1yHI;EACI,YAAA;EACA,wBAAA;UAAA,gBAAA;EACA,eAAA;AlD4yHR;;AkDvyHI;EACI,SAAA;AlD0yHR;;AkDtyHA;EACI,SAAA;AlDyyHJ;AkDryHY;EACI,uCAAA;AlDuyHhB;AkDnyHQ;EACI,cAAA;EACA,kBAAA;EACA,chD2BD;EgD1BC,4BAAA;EAAA,oBAAA;EACA,4CAAA;EACA,kBAAA;AlDqyHZ;AkDnyHY;EACI,kDAAA;AlDqyHhB;AkDjyHQ;EACI,kBAAA;AlDmyHZ;AkDjyHY;EACI,WAAA;EACA,YAAA;EACA,yBhDUL;EgDTK,kBAAA;EACA,wCAAA;EACA,kBAAA;EACA,QAAA;EACA,SAAA;AlDmyHhB;AkD7xHgB;EACI,yBhD6BV;AFkwHV;AkD1xHgB;EACI,yBhDsBV;AFswHV;;AkDrxHA;EACI,wChDyyCM;EgDxyCN,iDhDkhB0B;UgDlhB1B,yChDkhB0B;AFswG9B;AkDvxHI;EACI,+CAAA;AlDyxHR;;AkDnxHQ;EACI,YAAA;EACA,WAAA;EACA,iBAAA;EACA,wBAAA;UAAA,gBAAA;EACA,UAAA;EACA,eAAA;EACA,kBAAA;AlDsxHZ;AkDnxHQ;EACI,iDhD+fkB;UgD/flB,yChD+fkB;EgD9flB,wCAAA;AlDqxHZ;;AkD9wHI;EACI,WAAA;AlDixHR;AkD9wHI;EACI,WAAA;EACA,iBAAA;AlDgxHR;AkD9wHQ;EACI,WAAA;EACA,YAAA;EACA,kBAAA;AlDgxHZ;AkD5wHI;EACI,kBAAA;EACA,kBAAA;EACA,mBAAA;EACA,gBAAA;AlD8wHR;AkD5wHQ;EACI,uCAAA;EACA,kBAAA;EACA,UAAA;EACA,iBAAA;EACA,mBAAA;AlD8wHZ;AkD3wHQ;EACI,WAAA;EACA,kBAAA;EACA,WAAA;EACA,WAAA;EACA,OAAA;EACA,QAAA;EACA,wCAAA;EACA,SAAA;AlD6wHZ;AkD1wHQ;EACI,eAAA;AlD4wHZ;AkDxwHI;EACI,mBAAA;EACA,qBAAA;EACA,kBAAA;AlD0wHR;AkDxwHQ;EACI,gBAAA;AlD0wHZ;AkDxwHY;EACI,gBhD+fO;EgD9fP,kBAAA;AlD0wHhB;AkDtwHQ;EACI,kBAAA;EACA,yBhDjFF;EgDkFE,uBhD4aI;EgD3aJ,WhDhHJ;AFw3HR;AkDrwHQ;EACI,eAAA;EACA,eAAA;EACA,iBAAA;AlDuwHZ;AkDlwHQ;EACI,YAAA;AlDowHZ;AkDlwHY;EACI,iBAAA;AlDowHhB;AkDjwHY;EACI,uCAAA;EACA,iBAAA;EACA,chD/HL;AFk4HX;AkDhwHY;EACI,gBAAA;AlDkwHhB;;AkDzvHA;EACI,wChD2qCM;EgD1qCN,uBhDuYY;AFq3GhB;;AkDzvHA;EACI,kDAAA;EACA,+CAAA;AlD4vHJ;;AkDzvHA;EACI,kBAAA;EACA,UAAA;EACA,QAAA;EACA,mCAAA;UAAA,2BAAA;AlD4vHJ;AkDzvHQ;EACI,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,qBAAA;AlD2vHZ;;AkDrvHI;EADJ;IAEQ,eAAA;ElDyvHN;AACF;;AmD7/HA;EACE,wCAAA;EACA,wBAAA;UAAA,gBAAA;AnDggIF;AmD7/HI;EACE,iBAAA;AnD+/HN;;AmD1/HA;EACI,oDAAA;EACA,oCAAA;AnD6/HJ;;AoD1gIA;EACI,eAAA;EACA,gBlD0rBe;EkDzrBf,gClDsEoB;EkDrEpB,kBAAA;EACA,oBAAA;EAAA,oBAAA;EAAA,aAAA;ApD6gIJ;AoD5gII;EACI,eAAA;EACA,gBlDirBa;EkDhrBb,cAAA;EACA,gBAAA;ApD8gIR;;AoD1gIA;EACI,UAAA;ApD6gIJ;;AoD1gIA;EACI,oDAAA;ApD6gIJ;;AqDjiIA,gDAAA;AAEA;EACI,kBAAA;EACA,mBAAA;ArDmiIJ;AqDliII;EACI,WAAA;EACA,kBAAA;EACA,UAAA;EACA,SAAA;EACA,OAAA;EACA,SAAA;EACA,uCAAA;ArDoiIR;AqDjiII;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;ArDmiIR;AqDhiII;EACI,WAAA;ArDkiIR;AqD/hII;EACI,mBAAA;ArDiiIR;AqD9hII;EACI,cAAA;EACA,kBAAA;EACA,iBAAA;ArDgiIR;AqD7hII;EACI,qBAAA;EACA,yBAAA;EACA,gCAAA;EACA,yBnD8EE;EmD7EF,WnDgDA;EmD/CA,kBAAA;EACA,WAAA;ArD+hIR;AqD7hIQ;EACI,iBAAA;ArD+hIZ;AqD3hII;EACI,kBAAA;EACA,WAAA;EACA,YAAA;EACA,kCnDi2CE;EmDh2CF,kBAAA;EACA,cAAA;EACA,yBAAA;EACA,WAAA;EACA,kBAAA;EACA,SAAA;EACA,UAAA;ArD6hIR;AqD5hIQ;EACI,WAAA;EACA,kBAAA;EACA,WAAA;EACA,WAAA;EACA,uCAAA;EACA,UAAA;EACA,QAAA;ArD8hIZ;AqD1hII;EACI,gBAAA;ArD4hIR;AqD3hIQ;EACI,qBAAA;EACA,iBAAA;ArD6hIZ;AqD3hIQ;EACI,YAAA;EACA,WAAA;EACA,kBAAA;ArD6hIZ;;AqDvhIA;EAIQ;IACI,UAAA;ErDuhIV;EqDphIM;IACI,SAAA;ErDshIV;EqDphIM;IACI,kBAAA;IACA,UAAA;IACA,iBAAA;ErDshIV;EqDjhIM;IACI,WAAA;IACA,cAAA;IACA,UAAA;ErDmhIV;EqDhhIM;IACI,iBAAA;ErDkhIV;EqDjhIU;IACI,WAAA;IACA,cAAA;IACA,UAAA;ErDmhId;EqDhhIU;IACI,aAAA;ErDkhId;EqD7gIU;IACI,cAAA;IACA,kBAAA;ErD+gId;EqD5gIU;IACI,yBAAA;IACA,gCAAA;IACA,UAAA;IACA,YAAA;ErD8gId;EqD5gIc;IACI,YAAA;IACA,iBAAA;IACA,gBAAA;ErD8gIlB;EqD1gIU;IACI,UAAA;IACA,YAAA;ErD4gId;EqD3gIc;IACI,UAAA;IACA,WAAA;ErD6gIlB;AACF;AqDpgII;EACI,SAAA;ArDsgIR;AqDpgII;EACI,aAAA;ArDsgIR;AqDngII;EACI,WAAA;ArDqgIR;AqDlgII;EACI,WAAA;ArDogIR;;AsDzqIA;EACI,qBAAA;EACA,wCpD84CM;EoD74CN,iDpDunB0B;UoDvnB1B,yCpDunB0B;EoDtnB1B,YAAA;EACA,kBAAA;AtD4qIJ;AsD3qII;EACI,qBAAA;AtD6qIR;;AsDtqIQ;EACI,gBAAA;AtDyqIZ;;AsDpqIA;;sBAAA;AAKI;EACI,eAAA;AtDqqIR;AsDnqII;EACI,kBAAA;EACA,kBAAA;EACA,wCAAA;EACA,2BAAA;AtDqqIR;AsDpqIQ;EACI,eAAA;EACA,kBAAA;EACA,cAAA;AtDsqIZ;AsDnqIQ;EACI,qBpDuEF;EoDtEE,6BAAA;EACA,2BAAA;AtDqqIZ;AsDnqIY;EACI,cpDkEN;AFmmIV;;AsD/pIA;EACI,gBAAA;AtDkqIJ;AsDjqII;EAFJ;IAGQ,eAAA;EtDoqIN;AACF;;AsDjqIA;EACI,cpDwDM;EoDvDN,kBAAA;AtDoqIJ;AsDlqII;EACI,kBAAA;EACA,YAAA;EACA,WAAA;EACA,QAAA;EACA,YAAA;AtDoqIR;AsDlqIQ;EAPJ;IAQQ,WAAA;IACA,WAAA;IACA,YAAA;EtDqqIV;AACF", "file": "app-rtl.min.css", "sourcesContent": ["/*\r\nTemplate Name: Nazox -  Admin & Dashboard Template\r\nAuthor: Themesdesign\r\nVersion: 2.3.0\r\nContact: <EMAIL>\r\nFile: Main Css File\r\n*/\r\n\r\n\r\n//Fonts\r\n@import \"custom/fonts/fonts\";\r\n\r\n//Core files\r\n@import \"./node_modules/bootstrap/scss/functions\";\r\n@import \"./node_modules/bootstrap/scss/variables\";\r\n@import \"variables\";\r\n@import \"variables-dark\";\r\n@import \"./node_modules/bootstrap/scss/mixins.scss\";\r\n\r\n// Structure\r\n@import \"custom/structure/general\";\r\n@import \"custom/structure/topbar\";\r\n@import \"custom/structure/page-head\";\r\n@import \"custom/structure/footer\";\r\n@import \"custom/structure/right-sidebar\";\r\n@import \"custom/structure/vertical\";\r\n@import \"custom/structure/horizontal-nav\";\r\n@import \"custom/structure/layouts\";\r\n\r\n// Components\r\n@import \"custom/components/root\";\r\n@import \"custom/components/waves\";\r\n@import \"custom/components/avatar\";\r\n@import \"custom/components/accordion\";\r\n@import \"custom/components/helper\";\r\n@import \"custom/components/preloader\";\r\n@import \"custom/components/forms\";\r\n@import \"custom/components/widgets\";\r\n@import \"custom/components/demos\";\r\n@import \"custom/components/print\";\r\n\r\n// Plugins\r\n@import \"custom/plugins/custom-scrollbar\";\r\n@import \"custom/plugins/calendar\";\r\n@import \"custom/plugins/color-picker\";\r\n@import \"custom/plugins/session-timeout\";\r\n@import \"custom/plugins/round-slider\";\r\n@import \"custom/plugins/range-slider\";\r\n@import \"custom/plugins/sweatalert2\";\r\n@import \"custom/plugins/rating\";\r\n@import \"custom/plugins/toastr\";\r\n@import \"custom/plugins/parsley\";\r\n@import \"custom/plugins/select2\";\r\n@import \"custom/plugins/switch\";\r\n@import \"custom/plugins/datepicker\";\r\n@import \"custom/plugins/bootstrap-touchspin\";\r\n@import \"custom/plugins/datatable\";\r\n@import \"custom/plugins/form-editors\";\r\n@import \"custom/plugins/form-upload\";\r\n@import \"custom/plugins/form-wizard\";\r\n@import \"custom/plugins/responsive-table\";\r\n@import \"custom/plugins/table-editable\";\r\n@import \"custom/plugins/apexcharts\";\r\n@import \"custom/plugins/flot\";\r\n@import \"custom/plugins/sparkline-chart\";\r\n@import \"custom/plugins/google-map\";\r\n@import \"custom/plugins/vector-maps\";\r\n@import \"custom/plugins/x-editable\";\r\n\r\n// Pages\r\n@import \"custom/pages/authentication\";\r\n@import \"custom/pages/ecommerce\";\r\n@import \"custom/pages/email\";\r\n@import \"custom/pages/chat\";\r\n@import \"custom/pages/kanbanboard\";\r\n@import \"custom/pages/coming-soon\";\r\n@import \"custom/pages/timeline\";\r\n@import \"custom/pages/extras-pages\";\r\n", "//\r\n// Fonts\r\n//\r\n\r\n// Nunito - Google Font\r\n@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600&display=swap');\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 300;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-light.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/inter-light.woff?v=3.13\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 400;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-regular.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/inter-regular.woff?v=3.13\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 500;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-medium.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/inter-medium.woff?v=3.13\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 700;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-bold.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/iter-bold.woff?v=3.13\") format(\"woff\");\r\n}", "/*\nTemplate Name: <PERSON>zox -  Admin & Dashboard Template\nAuthor: Themesdesign\nVersion: 2.3.0\nContact: <EMAIL>\nFile: Main Css File\n*/\n@import url(\"https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600&display=swap\");\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 300;\n  font-display: swap;\n  src: url(\"../fonts/inter-light.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/inter-light.woff?v=3.13\") format(\"woff\");\n}\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(\"../fonts/inter-regular.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/inter-regular.woff?v=3.13\") format(\"woff\");\n}\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(\"../fonts/inter-medium.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/inter-medium.woff?v=3.13\") format(\"woff\");\n}\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(\"../fonts/inter-bold.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/iter-bold.woff?v=3.13\") format(\"woff\");\n}\n#page-topbar {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1002;\n  background-color: var(--bs-header-bg);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n\n.navbar-header {\n  display: flex;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0 auto;\n  height: 70px;\n  padding: 0 calc(1.5rem / 2) 0 0;\n}\n.navbar-header .dropdown .show.header-item {\n  background-color: var(--bs-secondary-bg);\n}\n\n.navbar-brand-box {\n  padding: 0 1.5rem;\n  width: 240px;\n}\n\n.logo {\n  line-height: 70px;\n}\n.logo .logo-sm {\n  display: none;\n}\n\n.logo-light {\n  display: none;\n}\n\n/* Search */\n.app-search {\n  padding: calc(32px / 2) 0;\n}\n.app-search .form-control {\n  border: none;\n  height: 38px;\n  padding-left: 40px;\n  padding-right: 20px;\n  background-color: var(--bs-topbar-search-bg);\n  box-shadow: none;\n  border-radius: 30px;\n}\n.app-search span {\n  position: absolute;\n  z-index: 10;\n  font-size: 16px;\n  line-height: 38px;\n  left: 13px;\n  top: 0;\n  color: #74788d;\n}\n\n.megamenu-list li {\n  position: relative;\n  padding: 5px 0px;\n}\n.megamenu-list li a {\n  color: var(--bs-body-color);\n}\n\n@media (max-width: 991px) {\n  .navbar-brand-box {\n    width: auto;\n  }\n  .logo span.logo-lg {\n    display: none;\n  }\n  .logo span.logo-sm {\n    display: inline-block;\n  }\n}\n.page-content {\n  padding: calc(70px + 1.5rem) calc(1.5rem / 2) 60px calc(1.5rem / 2);\n}\n\n.header-item {\n  height: 70px;\n  box-shadow: none !important;\n  color: var(--bs-header-item-color);\n  border: 0;\n  border-radius: 0px;\n}\n.header-item:hover {\n  color: var(--bs-header-item-color);\n}\n\n.header-profile-user {\n  height: 36px;\n  width: 36px;\n  background-color: var(--bs-secondary-bg);\n  padding: 3px;\n}\n\n.user-dropdown .dropdown-item i {\n  display: inline-block;\n}\n\n.noti-icon i {\n  font-size: 22px;\n  color: var(--bs-header-item-color);\n}\n.noti-icon .noti-dot {\n  position: absolute;\n  display: inline-block;\n  height: 6px;\n  width: 6px;\n  background-color: #ff3d60;\n  border-radius: 50%;\n  top: 20px;\n  right: 14px;\n}\n\n.notification-item .d-flex {\n  padding: 0.75rem 1rem;\n}\n.notification-item .d-flex:hover {\n  background-color: var(--bs-tertiary-bg);\n}\n\n.dropdown-icon-item {\n  display: block;\n  border-radius: 3px;\n  line-height: 34px;\n  text-align: center;\n  padding: 15px 0 9px;\n  display: block;\n  border: 1px solid transparent;\n  color: #74788d;\n}\n.dropdown-icon-item img {\n  height: 24px;\n}\n.dropdown-icon-item span {\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.dropdown-icon-item:hover {\n  border-color: var(--bs-border-color);\n}\n\n.fullscreen-enable [data-toggle=fullscreen] .ri-fullscreen-line:before {\n  content: \"\\ed73\";\n}\n\nbody[data-topbar=dark] #page-topbar {\n  background-color: var(--bs-header-dark-bg);\n}\nbody[data-topbar=dark] .navbar-header .dropdown .show.header-item {\n  background-color: rgba(255, 255, 255, 0.05);\n}\nbody[data-topbar=dark] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\nbody[data-topbar=dark] .header-item {\n  color: var(--bs-header-dark-item-color);\n}\nbody[data-topbar=dark] .header-item:hover {\n  color: var(--bs-header-dark-item-color);\n}\nbody[data-topbar=dark] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25);\n}\nbody[data-topbar=dark] .noti-icon i {\n  color: var(--bs-header-dark-item-color);\n}\nbody[data-topbar=dark] .logo-dark {\n  display: none;\n}\nbody[data-topbar=dark] .logo-light {\n  display: block;\n}\nbody[data-topbar=dark] .app-search .form-control {\n  background-color: var(--bs-topbar-search-bg);\n  color: #fff;\n}\nbody[data-topbar=dark] .app-search span,\nbody[data-topbar=dark] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5);\n}\n\nbody[data-sidebar=dark] .navbar-brand-box {\n  background: var(--bs-sidebar-dark-bg);\n}\nbody[data-sidebar=dark] .logo-dark {\n  display: none;\n}\nbody[data-sidebar=dark] .logo-light {\n  display: block;\n}\n\n@media (max-width: 600px) {\n  .navbar-header .dropdown {\n    position: static;\n  }\n  .navbar-header .dropdown .dropdown-menu {\n    left: 10px !important;\n    right: 10px !important;\n  }\n}\n@media (max-width: 380px) {\n  .navbar-brand-box {\n    display: none;\n  }\n}\nbody[data-layout=horizontal] .navbar-brand-box {\n  width: auto;\n}\nbody[data-layout=horizontal] .page-content {\n  margin-top: 70px;\n  padding: calc(55px + 1.5rem) calc(1.5rem / 2) 60px calc(1.5rem / 2);\n}\n\n@media (max-width: 992px) {\n  body[data-layout=horizontal] .page-content {\n    margin-top: 15px;\n  }\n}\nbody[data-topbar=colored] #page-topbar {\n  background-color: var(--bs-header-colored-bg);\n}\nbody[data-topbar=colored] .navbar-header .dropdown .show.header-item {\n  background-color: rgba(255, 255, 255, 0.05);\n}\nbody[data-topbar=colored] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\nbody[data-topbar=colored] .header-item {\n  color: var(--bs-header-dark-item-color);\n}\nbody[data-topbar=colored] .header-item:hover {\n  color: var(--bs-header-dark-item-color);\n}\nbody[data-topbar=colored] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25);\n}\nbody[data-topbar=colored] .noti-icon i {\n  color: var(--bs-header-dark-item-color);\n}\nbody[data-topbar=colored] .logo-dark {\n  display: none;\n}\nbody[data-topbar=colored] .logo-light {\n  display: block;\n}\nbody[data-topbar=colored] .app-search .form-control {\n  background-color: var(--bs-topbar-search-bg);\n  color: #fff;\n}\nbody[data-topbar=colored] .app-search span,\nbody[data-topbar=colored] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.page-title-box {\n  padding-bottom: 1.5rem;\n}\n.page-title-box .breadcrumb {\n  background-color: transparent;\n  padding: 0;\n}\n.page-title-box h4 {\n  font-size: 15px;\n  text-transform: uppercase;\n  font-weight: 600;\n}\n\n.footer {\n  bottom: 0;\n  padding: 20px calc(1.5rem / 2);\n  position: absolute;\n  right: 0;\n  color: #74788d;\n  left: 240px;\n  height: 60px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  background-color: var(--bs-footer-bg);\n}\n\n@media (max-width: 992px) {\n  .footer {\n    left: 0;\n  }\n}\n.vertical-collpsed .footer {\n  left: 70px;\n}\n\nbody[data-layout=horizontal] .footer {\n  left: 0 !important;\n}\n\n.right-bar {\n  background-color: var(--bs-secondary-bg);\n  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n  display: block;\n  position: fixed;\n  transition: all 200ms ease-out;\n  width: 280px;\n  z-index: 9999;\n  float: right !important;\n  right: -290px;\n  top: 0;\n  bottom: 0;\n}\n.right-bar .right-bar-toggle {\n  background-color: #31373d;\n  height: 24px;\n  width: 24px;\n  line-height: 24px;\n  color: #eff2f7;\n  text-align: center;\n  border-radius: 50%;\n}\n.right-bar .right-bar-toggle:hover {\n  background-color: #383f45;\n}\n\n.rightbar-overlay {\n  background-color: rgba(33, 37, 41, 0.55);\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  display: none;\n  z-index: 9998;\n  transition: all 0.2s ease-out;\n}\n\n.right-bar-enabled .right-bar {\n  right: 0;\n}\n.right-bar-enabled .rightbar-overlay {\n  display: block;\n}\n\n@media (max-width: 767.98px) {\n  .right-bar {\n    overflow: auto;\n  }\n  .right-bar .slimscroll-menu {\n    height: auto !important;\n  }\n}\n.metismenu {\n  margin: 0;\n}\n.metismenu li {\n  display: block;\n  width: 100%;\n}\n.metismenu .mm-collapse {\n  display: none;\n}\n.metismenu .mm-collapse:not(.mm-show) {\n  display: none;\n}\n.metismenu .mm-collapse.mm-show {\n  display: block;\n}\n.metismenu .mm-collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  transition-timing-function: ease;\n  transition-duration: 0.35s;\n  transition-property: height, visibility;\n}\n\n.vertical-menu {\n  width: 240px;\n  z-index: 1001;\n  background: var(--bs-sidebar-bg);\n  bottom: 0;\n  margin-top: 0;\n  position: fixed;\n  top: 70px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n\n.main-content {\n  margin-left: 240px;\n  overflow: hidden;\n}\n.main-content .content {\n  padding: 0 15px 10px 15px;\n  margin-top: 70px;\n}\n\n#sidebar-menu {\n  padding: 10px 0 30px 0;\n}\n#sidebar-menu .mm-active > .has-arrow:after {\n  transform: rotate(-180deg);\n}\n#sidebar-menu .has-arrow:after {\n  content: \"\\f0140\";\n  font-family: \"Material Design Icons\";\n  display: block;\n  float: right;\n  transition: transform 0.2s;\n  font-size: 1rem;\n}\n#sidebar-menu ul li a {\n  display: block;\n  padding: 0.625rem 1.5rem;\n  color: var(--bs-sidebar-menu-item-color);\n  position: relative;\n  font-size: 13.3px;\n  transition: all 0.4s;\n  font-family: \"Inter\", sans-serif;\n  font-weight: 500;\n}\n#sidebar-menu ul li a i {\n  display: inline-block;\n  min-width: 1.5rem;\n  padding-bottom: 0.125em;\n  font-size: 1.1rem;\n  line-height: 1.40625rem;\n  vertical-align: middle;\n  color: var(--bs-sidebar-menu-item-icon-color);\n  transition: all 0.4s;\n  opacity: 0.75;\n}\n#sidebar-menu ul li a:hover {\n  color: var(--bs-sidebar-menu-item-hover-color);\n}\n#sidebar-menu ul li a:hover i {\n  color: var(--bs-sidebar-menu-item-hover-color);\n}\n#sidebar-menu ul li .badge {\n  margin-top: 4px;\n}\n#sidebar-menu ul li ul.sub-menu {\n  padding: 0;\n}\n#sidebar-menu ul li ul.sub-menu li a {\n  padding: 0.4rem 1.5rem 0.4rem 3.2rem;\n  font-size: 13px;\n  color: var(--bs-sidebar-menu-sub-item-color);\n}\n#sidebar-menu ul li ul.sub-menu li ul.sub-menu {\n  padding: 0;\n}\n#sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding: 0.4rem 1.5rem 0.4rem 4.2rem;\n  font-size: 13.5px;\n}\n\n.menu-title {\n  padding: 12px 20px !important;\n  letter-spacing: 0.05em;\n  pointer-events: none;\n  cursor: default;\n  font-size: 11px;\n  text-transform: uppercase;\n  color: var(--bs-sidebar-menu-item-icon-color);\n  font-weight: 600;\n  font-family: \"Inter\", sans-serif;\n  opacity: 0.5;\n}\n\n.mm-active {\n  color: var(--bs-sidebar-menu-item-active-color) !important;\n}\n.mm-active > a {\n  color: var(--bs-sidebar-menu-item-active-color) !important;\n}\n.mm-active > a i {\n  color: var(--bs-sidebar-menu-item-active-color) !important;\n}\n.mm-active > i {\n  color: var(--bs-sidebar-menu-item-active-color) !important;\n}\n.mm-active .active {\n  color: var(--bs-sidebar-menu-item-active-color) !important;\n}\n.mm-active .active i {\n  color: var(--bs-sidebar-menu-item-active-color) !important;\n}\n\n@media (max-width: 992px) {\n  .vertical-menu {\n    display: none;\n  }\n  .main-content {\n    margin-left: 0 !important;\n  }\n  body.sidebar-enable .vertical-menu {\n    display: block;\n  }\n}\n.vertical-collpsed .main-content {\n  margin-left: 70px;\n}\n.vertical-collpsed .navbar-brand-box {\n  width: 70px !important;\n}\n.vertical-collpsed .logo span.logo-lg {\n  display: none;\n}\n.vertical-collpsed .logo span.logo-sm {\n  display: block;\n}\n.vertical-collpsed .vertical-menu {\n  position: absolute;\n  width: 70px !important;\n  z-index: 5;\n}\n.vertical-collpsed .vertical-menu .simplebar-mask,\n.vertical-collpsed .vertical-menu .simplebar-content-wrapper {\n  overflow: visible !important;\n}\n.vertical-collpsed .vertical-menu .simplebar-scrollbar {\n  display: none !important;\n}\n.vertical-collpsed .vertical-menu .simplebar-offset {\n  bottom: 0 !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .menu-title,\n.vertical-collpsed .vertical-menu #sidebar-menu .badge,\n.vertical-collpsed .vertical-menu #sidebar-menu .collapse.in {\n  display: none !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .nav.collapse {\n  height: inherit !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .has-arrow:after {\n  display: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li {\n  position: relative;\n  white-space: nowrap;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a {\n  padding: 15px 20px;\n  min-height: 55px;\n  transition: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:hover, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:active, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:focus {\n  color: var(--bs-sidebar-menu-item-hover-color);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n  font-size: 20px;\n  margin-left: 4px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a span {\n  display: none;\n  padding-left: 25px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  position: relative;\n  width: calc(190px + 70px);\n  color: #5664d2;\n  transition: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: #5664d2;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a span {\n  display: inline;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul {\n  display: block;\n  left: 70px;\n  position: absolute;\n  width: 190px;\n  height: auto !important;\n  box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul ul {\n  box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n  box-shadow: none;\n  padding: 8px 20px;\n  position: relative;\n  width: 190px;\n  z-index: 6;\n  color: var(--bs-sidebar-menu-sub-item-color);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n  color: var(--bs-sidebar-menu-item-hover-color);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n  padding: 5px 0;\n  z-index: 9999;\n  display: none;\n  background-color: var(--bs-sidebar-bg);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li:hover > ul {\n  display: block;\n  left: 190px;\n  height: auto !important;\n  margin-top: -36px;\n  position: absolute;\n  width: 190px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li > a span.pull-right {\n  position: absolute;\n  right: 20px;\n  top: 12px;\n  transform: rotate(270deg);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li.active a {\n  color: #f8f9fa;\n}\n\nbody[data-sidebar=dark] .vertical-menu {\n  background: var(--bs-sidebar-dark-bg);\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a {\n  color: var(--bs-sidebar-dark-menu-item-color);\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a i {\n  color: var(--bs-sidebar-dark-menu-item-color);\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a:hover {\n  color: var(--bs-sidebar-dark-menu-item-hover-color);\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a:hover i {\n  color: var(--bs-sidebar-dark-menu-item-hover-color);\n}\nbody[data-sidebar=dark] #sidebar-menu ul li ul.sub-menu li a {\n  color: var(--bs-sidebar-dark-menu-sub-item-color);\n}\nbody[data-sidebar=dark] #sidebar-menu ul li ul.sub-menu li a:hover {\n  color: var(--bs-sidebar-dark-menu-item-hover-color);\n}\nbody[data-sidebar=dark].vertical-collpsed {\n  min-height: 1400px;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  background-color: var(--bs-sidebar-dark-bg);\n  color: var(--bs-sidebar-dark-menu-item-hover-color);\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: var(--bs-sidebar-dark-menu-item-hover-color);\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n  color: var(--bs-sidebar-dark-menu-sub-item-color);\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n  color: var(--bs-sidebar-menu-item-hover-color);\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n  background-color: var(--bs-secondary-bg);\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul > li > a.mm-active {\n  color: var(--bs-sidebar-dark-menu-item-active-color) !important;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li.mm-active > a, body[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li.active > a {\n  color: var(--bs-sidebar-menu-item-active-color) !important;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.mm-active, body[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.active {\n  color: var(--bs-sidebar-menu-item-active-color) !important;\n}\nbody[data-sidebar=dark] .mm-active {\n  color: var(--bs-sidebar-dark-menu-item-active-color) !important;\n}\nbody[data-sidebar=dark] .mm-active > a {\n  color: var(--bs-sidebar-dark-menu-item-active-color) !important;\n}\nbody[data-sidebar=dark] .mm-active > a i {\n  color: var(--bs-sidebar-dark-menu-item-active-color) !important;\n}\nbody[data-sidebar=dark] .mm-active > i {\n  color: var(--bs-sidebar-dark-menu-item-active-color) !important;\n}\nbody[data-sidebar=dark] .mm-active .active {\n  color: var(--bs-sidebar-dark-menu-item-active-color) !important;\n}\nbody[data-sidebar=dark] .mm-active .active i {\n  color: var(--bs-sidebar-dark-menu-item-active-color) !important;\n}\nbody[data-sidebar=dark] .menu-title {\n  color: var(--bs-sidebar-dark-menu-item-color);\n}\n\nbody[data-layout=horizontal] .main-content {\n  margin-left: 0 !important;\n}\n\nbody[data-sidebar-size=small] .navbar-brand-box {\n  width: 160px;\n}\n@media (max-width: 992px) {\n  body[data-sidebar-size=small] .navbar-brand-box {\n    width: auto;\n  }\n}\nbody[data-sidebar-size=small] .vertical-menu {\n  width: 160px;\n  text-align: center;\n}\nbody[data-sidebar-size=small] .vertical-menu .has-arrow:after,\nbody[data-sidebar-size=small] .vertical-menu .badge {\n  display: none !important;\n}\nbody[data-sidebar-size=small] .main-content {\n  margin-left: 160px;\n}\nbody[data-sidebar-size=small] .footer {\n  left: 160px;\n}\n@media (max-width: 991px) {\n  body[data-sidebar-size=small] .footer {\n    left: 0;\n  }\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li a i {\n  display: block;\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li ul.sub-menu li a {\n  padding-left: 1.5rem;\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding-left: 1.5rem;\n}\nbody[data-sidebar-size=small].vertical-collpsed .main-content {\n  margin-left: 70px;\n}\nbody[data-sidebar-size=small].vertical-collpsed .vertical-menu #sidebar-menu {\n  text-align: left;\n}\nbody[data-sidebar-size=small].vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n  display: inline-block;\n}\nbody[data-sidebar-size=small].vertical-collpsed .footer {\n  left: 70px;\n}\n\nbody[data-sidebar=colored] .vertical-menu {\n  background: #5664d2;\n}\nbody[data-sidebar=colored] .navbar-brand-box {\n  background-color: #5664d2;\n}\nbody[data-sidebar=colored] .navbar-brand-box .logo-dark {\n  display: none;\n}\nbody[data-sidebar=colored] .navbar-brand-box .logo-light {\n  display: block;\n}\nbody[data-sidebar=colored] #sidebar-menu ul li.menu-title {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a i {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a.waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.1);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li ul.sub-menu li a {\n  color: rgba(255, 255, 255, 0.5);\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  background-color: #5e6bd4;\n  color: #fff;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: #fff;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul > li > a.mm-active {\n  color: var(--bs-sidebar-dark-menu-item-active-color) !important;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li.mm-active > a, body[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li.active > a {\n  color: var(--bs-sidebar-menu-item-active-color) !important;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.mm-active, body[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.active {\n  color: var(--bs-sidebar-menu-item-active-color) !important;\n}\nbody[data-sidebar=colored] .mm-active {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > a {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > a i {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > i {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active .active {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active .active i {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .menu-title {\n  color: #fff !important;\n}\n\n.topnav {\n  background: var(--bs-topnav-bg);\n  padding: 0 calc(1.5rem / 2);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  margin-top: 70px;\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 100;\n}\n.topnav .topnav-menu {\n  margin: 0;\n  padding: 0;\n}\n.topnav .navbar-nav .nav-link {\n  font-size: 15px;\n  position: relative;\n  padding: 1rem 1.3rem;\n  color: var(--bs-menu-item-color);\n  font-family: \"Inter\", sans-serif;\n}\n.topnav .navbar-nav .nav-link i {\n  font-size: 15px;\n  vertical-align: middle;\n  display: inline-block;\n}\n.topnav .navbar-nav .nav-link:focus, .topnav .navbar-nav .nav-link:hover {\n  color: var(--bs-menu-item-active-color);\n  background-color: transparent;\n}\n.topnav .navbar-nav .dropdown-item {\n  color: var(--bs-menu-item-color);\n}\n.topnav .navbar-nav .dropdown-item.active, .topnav .navbar-nav .dropdown-item:hover {\n  color: var(--bs-menu-item-active-color);\n}\n.topnav .navbar-nav .nav-item .nav-link.active {\n  color: var(--bs-menu-item-active-color);\n}\n.topnav .navbar-nav .dropdown.active > a {\n  color: var(--bs-menu-item-active-color);\n  background-color: transparent;\n}\n\n@media (min-width: 1200px) {\n  body[data-layout=horizontal] .container-fluid,\n  body[data-layout=horizontal] .navbar-header {\n    max-width: 85%;\n  }\n}\n@media (min-width: 992px) {\n  .topnav .navbar-nav .nav-item:first-of-type .nav-link {\n    padding-left: 0;\n  }\n  .topnav .dropdown-item {\n    padding: 0.5rem 1.5rem;\n    min-width: 180px;\n  }\n  .topnav .dropdown.mega-dropdown .mega-dropdown-menu {\n    left: 0px;\n    right: auto;\n  }\n  .topnav .dropdown .dropdown-menu {\n    margin-top: 0;\n    border-radius: 0 0 var(--bs-border-radius) var(--bs-border-radius);\n  }\n  .topnav .dropdown .dropdown-menu .arrow-down::after {\n    right: 15px;\n    transform: rotate(-135deg) translateY(-50%);\n    position: absolute;\n  }\n  .topnav .dropdown .dropdown-menu .dropdown .dropdown-menu {\n    position: absolute;\n    top: 0 !important;\n    left: 100%;\n    display: none;\n  }\n  .topnav .dropdown:hover > .dropdown-menu {\n    display: block;\n  }\n  .topnav .dropdown:hover > .dropdown-menu > .dropdown:hover > .dropdown-menu {\n    display: block;\n  }\n  .navbar-toggle {\n    display: none;\n  }\n}\n.arrow-down {\n  display: inline-block;\n}\n.arrow-down:after {\n  border-color: initial;\n  border-style: solid;\n  border-width: 0 0 1px 1px;\n  content: \"\";\n  height: 0.4em;\n  display: inline-block;\n  right: 5px;\n  top: 50%;\n  margin-left: 10px;\n  transform: rotate(-45deg) translateY(-50%);\n  transform-origin: top;\n  transition: all 0.3s ease-out;\n  width: 0.4em;\n}\n\n@media (max-width: 1199.98px) {\n  .topnav-menu .navbar-nav li:last-of-type .dropdown .dropdown-menu {\n    right: 100%;\n    left: auto;\n  }\n}\n@media (max-width: 991.98px) {\n  .navbar-brand-box .logo-dark {\n    display: block;\n  }\n  .navbar-brand-box .logo-dark span.logo-sm {\n    display: block;\n  }\n  .navbar-brand-box .logo-light {\n    display: none;\n  }\n  .topnav {\n    max-height: 360px;\n    overflow-y: auto;\n    padding: 0;\n  }\n  .topnav .navbar-nav .nav-link {\n    padding: 0.75rem 1.1rem;\n  }\n  .topnav .dropdown .dropdown-menu {\n    background-color: transparent;\n    border: none;\n    box-shadow: none;\n    padding-left: 15px;\n  }\n  .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl {\n    width: auto;\n  }\n  .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl .row {\n    margin: 0px;\n  }\n  .topnav .dropdown .dropdown-item {\n    position: relative;\n    background-color: transparent;\n  }\n  .topnav .dropdown .dropdown-item.active, .topnav .dropdown .dropdown-item:active {\n    color: #5664d2;\n  }\n  .topnav .arrow-down::after {\n    right: 15px;\n    position: absolute;\n  }\n}\n@media (min-width: 992px) {\n  body[data-layout=horizontal][data-topbar=light] .navbar-brand-box .logo-dark {\n    display: block;\n  }\n  body[data-layout=horizontal][data-topbar=light] .navbar-brand-box .logo-light {\n    display: none;\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav {\n    background-color: var(--bs-header-dark-bg);\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link {\n    color: rgba(255, 255, 255, 0.6);\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link:focus, body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link:hover {\n    color: rgba(255, 255, 255, 0.9);\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav > .dropdown.active > a {\n    color: rgba(255, 255, 255, 0.9) !important;\n  }\n}\n[data-bs-theme=dark][data-layout=horizontal][data-topbar=light] .navbar-brand-box .logo-dark {\n  display: none;\n}\n[data-bs-theme=dark][data-layout=horizontal][data-topbar=light] .navbar-brand-box .logo-light {\n  display: block;\n}\n\nbody[data-layout-size=boxed] {\n  background-color: var(--bs-boxed-body-bg);\n}\nbody[data-layout-size=boxed] #layout-wrapper {\n  background-color: var(--bs-body-bg);\n  max-width: 1300px;\n  margin: 0 auto;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\nbody[data-layout-size=boxed] #page-topbar {\n  max-width: 1300px;\n  margin: 0 auto;\n}\nbody[data-layout-size=boxed] .footer {\n  margin: 0 auto;\n  max-width: calc(1300px - 240px);\n}\nbody[data-layout-size=boxed].vertical-collpsed .footer {\n  max-width: calc(1300px - 70px);\n}\n\nbody[data-layout=horizontal][data-layout-size=boxed] .topnav {\n  max-width: 1300px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n:root,\n[data-bs-theme=light] {\n  --bs-header-bg: #ffffff;\n  --bs-header-item-color: var(--bs-header-item-color);\n  --bs-header-item-sub-color: var(--bs-secondary-color);\n  --bs-header-height: 70px;\n  --bs-rightbar-width: 280px;\n  --bs-boxed-layout-width: 1300px;\n  --bs-footer-height: 60px;\n  --bs-footer-bg: #ffffff;\n  --bs-header-dark-bg: #252b3b;\n  --bs-menu-item-color: #74788d;\n  --bs-menu-item-active-color: #5664d2;\n  --bs-topbar-search-bg: #f1f5f712;\n  --bs-topnav-bg: #fff;\n  --bs-topnav-item-color: #5d6071;\n  --bs-topnav-item-color-active: var(--bs-primary);\n  --bs-twocolumn-menu-iconview-bg: #fff;\n  --bs-twocolumn-menu-bg: #fff;\n  --bs-header-item-color: #636e75;\n  --bs-boxed-body-bg: #dee7ec;\n  --bs-sidebar-bg: #ffffff;\n  --bs-sidebar-menu-item-color: #74788d;\n  --bs-sidebar-menu-sub-item-color: #7c8a96;\n  --bs-sidebar-menu-item-icon-color: #505d69;\n  --bs-sidebar-menu-item-hover-color: #383c40;\n  --bs-sidebar-menu-item-active-color: #556ee6;\n  --bs-sidebar-width: 240px;\n  --bs-sidebar-collapsed-width: 70px;\n  --bs-sidebar-width-sm: 160px;\n}\n:root [data-topbar=light],\n[data-bs-theme=light] [data-topbar=light] {\n  --bs-topbar-search-bg: #f1f5f7;\n}\n:root [data-topbar=dark],\n[data-bs-theme=light] [data-topbar=dark] {\n  --bs-header-bg: #252b3b;\n  --bs-header-item-color: #f8f9fa;\n  --bs-header-dark-item-color: #e9ecef;\n}\n:root [data-topbar=colored],\n[data-bs-theme=light] [data-topbar=colored] {\n  --bs-header-colored-bg: #556ee6;\n  --bs-topbar-search-bg: rgba(241, 245, 247, 0.071);\n  --bs-header-dark-item-color: #e9ecef;\n}\n:root [data-sidebar=colored],\n[data-bs-theme=light] [data-sidebar=colored] {\n  --bs-topbar-search-bg: #f1f5f7;\n}\n:root [data-sidebar=light],\n[data-bs-theme=light] [data-sidebar=light] {\n  --bs-sidebar-bg: #ffffff;\n  --bs-sidebar-menu-item-color: #74788d;\n  --bs-sidebar-menu-sub-item-color: #7c8a96;\n  --bs-sidebar-menu-item-icon-color: #505d69;\n  --bs-sidebar-menu-item-hover-color: #383c40;\n  --bs-sidebar-menu-item-active-color: #556ee6;\n}\n:root [data-sidebar=dark],\n[data-bs-theme=light] [data-sidebar=dark] {\n  --bs-sidebar-dark-bg: #252b3b;\n  --bs-sidebar-dark-menu-item-color: #8590a5;\n  --bs-sidebar-dark-menu-sub-item-color: #8590a5;\n  --bs-sidebar-dark-menu-item-icon-color: #8590a5;\n  --bs-sidebar-dark-menu-item-hover-color: #d7e4ec;\n  --bs-sidebar-dark-menu-item-active-color: #d7e4ec;\n  --bs-topbar-search-bg: #f1f5f7;\n}\n\n[data-bs-theme=dark] {\n  --bs-light: #2d3448;\n  --bs-light-rgb: 45, 52, 72;\n  --bs-dark: #2d3448;\n  --bs-dark-rgb: 45, 52, 72;\n  --bs-header-bg: #272d3e;\n  --bs-header-dark-bg: #556ee6;\n  --bs-header-item-color: #919bae;\n  --bs-topbar-search-bg: #2b324412;\n  --bs-topnav-bg: #282e3f;\n  --bs-header-item-sub-color: var(--bs-secondary-color);\n  --bs-footer-bg: #252b3b;\n  --bs-boxed-body-bg: #2d3447;\n  --bs-sidebar-bg: #ffffff;\n  --bs-sidebar-menu-item-color: #74788d;\n  --bs-sidebar-menu-sub-item-color: #7c8a96;\n  --bs-sidebar-menu-item-icon-color: #505d69;\n  --bs-sidebar-menu-item-hover-color: #d7e4ec;\n  --bs-sidebar-menu-item-active-color: #556ee6;\n}\n[data-bs-theme=dark] .table-light {\n  --bs-table-color: white;\n  --bs-table-bg: var(--bs-tertiary-bg);\n  --bs-table-border-color: var(--bs-border-color);\n  --bs-table-striped-bg: var(--bs-tertiary-bg);\n  --bs-table-striped-color: white;\n  --bs-table-active-bg: var(--bs-tertiary-bg);\n  --bs-table-active-color: white;\n  --bs-table-hover-bg: var(--bs-tertiary-bg);\n  --bs-table-hover-color: white;\n}\n[data-bs-theme=dark][data-topbar=colored] {\n  --bs-header-colored-bg: #556ee6;\n  --bs-topbar-search-bg: #2b324412;\n}\n[data-bs-theme=dark][data-topbar=light] {\n  --bs-topbar-search-bg: #2b3244;\n}\n[data-bs-theme=dark][data-sidebar=colored] {\n  --bs-topbar-search-bg: #2b3244;\n}\n[data-bs-theme=dark][data-sidebar=dark] {\n  ---bs-sidebar-dark-bg: #252b3b;\n  --bs-sidebar-dark-menu-item-color: #8590a5;\n  --bs-sidebar-dark-menu-sub-item-color: #8590a5;\n  --bs-sidebar-dark-menu-item-icon-color: #8590a5;\n  --bs-sidebar-dark-menu-item-hover-color: #d7e4ec;\n  --bs-sidebar-dark-menu-item-active-color: #d7e4ec;\n  --bs-topbar-search-bg: #2b3244;\n}\n\n/*!\n * Waves v0.7.6\n * http://fian.my.id/Waves \n * \n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \n * Released under the MIT license \n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  -moz-transition: all 0.5s ease-out;\n  -o-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -moz-transition-property: -moz-transform, opacity;\n  -o-transition-property: -o-transform, opacity;\n  transition-property: transform, opacity;\n  -webkit-transform: scale(0) translate(0, 0);\n  -moz-transform: scale(0) translate(0, 0);\n  -ms-transform: scale(0) translate(0, 0);\n  -o-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n}\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  -moz-transition: none !important;\n  -o-transition: none !important;\n  transition: none !important;\n}\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\n}\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1;\n}\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em;\n}\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em;\n}\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom;\n}\n\n.waves-input-wrapper.waves-button {\n  padding: 0;\n}\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1;\n}\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%;\n}\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  -moz-transition: all 300ms;\n  -o-transition: all 300ms;\n  transition: all 300ms;\n}\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n}\n\n.waves-block {\n  display: block;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background-color: rgba(255, 255, 255, 0.4);\n}\n\n.waves-effect.waves-primary .waves-ripple {\n  background-color: rgba(86, 100, 210, 0.4);\n}\n\n.waves-effect.waves-success .waves-ripple {\n  background-color: rgba(28, 187, 140, 0.4);\n}\n\n.waves-effect.waves-info .waves-ripple {\n  background-color: rgba(74, 163, 255, 0.4);\n}\n\n.waves-effect.waves-warning .waves-ripple {\n  background-color: rgba(252, 185, 44, 0.4);\n}\n\n.waves-effect.waves-danger .waves-ripple {\n  background-color: rgba(255, 61, 96, 0.4);\n}\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem;\n}\n\n.avatar-sm {\n  height: 3rem;\n  width: 3rem;\n}\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: #5664d2;\n  color: #fff;\n  display: flex;\n  font-weight: 500;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n.avatar-group {\n  padding-left: 12px;\n  display: flex;\n  flex-wrap: wrap;\n}\n.avatar-group .avatar-group-item {\n  margin-left: -12px;\n  border: 2px solid var(--bs-secondary-bg);\n  border-radius: 50%;\n  transition: all 0.2s;\n}\n.avatar-group .avatar-group-item:hover {\n  position: relative;\n  transform: translateY(-2px);\n}\n\n.custom-accordion .card + .card {\n  margin-top: 0.5rem;\n}\n.custom-accordion a.collapsed i.accor-plus-icon:before {\n  content: \"\\f0415\";\n}\n.custom-accordion .card-header {\n  border-radius: 7px;\n}\n\n.custom-accordion-arrow .card {\n  border: 1px solid var(--bs-border-color);\n  box-shadow: none;\n}\n.custom-accordion-arrow .card-header {\n  padding-left: 45px;\n  position: relative;\n}\n.custom-accordion-arrow .card-header .accor-arrow-icon {\n  position: absolute;\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  line-height: 24px;\n  font-size: 16px;\n  background-color: #5664d2;\n  color: #fff;\n  border-radius: 50%;\n  text-align: center;\n  left: 10px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.custom-accordion-arrow a.collapsed i.accor-arrow-icon:before {\n  content: \"\\f0142\";\n}\n\n.font-family-secondary {\n  font-family: \"Inter\", sans-serif;\n}\n\n.font-size-10 {\n  font-size: 10px !important;\n}\n\n.font-size-11 {\n  font-size: 11px !important;\n}\n\n.font-size-12 {\n  font-size: 12px !important;\n}\n\n.font-size-13 {\n  font-size: 13px !important;\n}\n\n.font-size-14 {\n  font-size: 14px !important;\n}\n\n.font-size-15 {\n  font-size: 15px !important;\n}\n\n.font-size-16 {\n  font-size: 16px !important;\n}\n\n.font-size-17 {\n  font-size: 17px !important;\n}\n\n.font-size-18 {\n  font-size: 18px !important;\n}\n\n.font-size-20 {\n  font-size: 20px !important;\n}\n\n.font-size-22 {\n  font-size: 22px !important;\n}\n\n.font-size-24 {\n  font-size: 24px !important;\n}\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 2px);\n  display: block;\n  border: 1px solid #adb5bd;\n  border-radius: 50%;\n  color: #adb5bd;\n  text-align: center;\n  transition: all 0.4s;\n}\n.social-list-item:hover {\n  color: #74788d;\n  background-color: #eff2f7;\n}\n\n.w-xs {\n  min-width: 80px;\n}\n\n.w-sm {\n  min-width: 95px;\n}\n\n.w-md {\n  min-width: 110px;\n}\n\n.w-lg {\n  min-width: 140px;\n}\n\n.w-xl {\n  min-width: 160px;\n}\n\n.bg-overlay {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  top: 0;\n  opacity: 0.7;\n  background-color: #000;\n}\n\n.flex-1 {\n  flex: 1;\n}\n\n.alert-dismissible .btn-close {\n  font-size: 10px;\n  padding: 1.05rem 1.25rem;\n  background: transparent url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e\") center/1em auto no-repeat;\n}\n\n#preloader {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: var(--bs-secondary-bg);\n  z-index: 9999;\n}\n\n#status {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translateY(-50%);\n  margin: -20px 0 0 -20px;\n}\n\n.spinner .spin-icon {\n  font-size: 56px;\n  color: #5664d2;\n  position: relative;\n  display: inline-block;\n  animation: spin 1.6s infinite linear;\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(359deg);\n  }\n}\n.form-check-right {\n  padding-left: 0;\n  display: inline-block;\n  padding-right: 1.5em;\n}\n.form-check-right .form-check-input {\n  float: right;\n  margin-left: 0;\n  margin-right: -1.5em;\n}\n.form-check-right .form-check-label {\n  display: block;\n}\n\n.form-check {\n  position: relative;\n  text-align: left;\n}\n\n.form-check-label {\n  cursor: pointer;\n  margin-bottom: 0;\n}\n\n.activity-wid {\n  margin-top: 8px;\n  margin-left: 16px;\n}\n.activity-wid .activity-list {\n  position: relative;\n  padding: 0 0 40px 30px;\n}\n.activity-wid .activity-list:before {\n  content: \"\";\n  border-left: 2px dashed rgba(86, 100, 210, 0.25);\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  top: 32px;\n}\n.activity-wid .activity-list .activity-icon {\n  position: absolute;\n  left: -15px;\n  top: 0;\n  z-index: 9;\n}\n.activity-wid .activity-list:last-child {\n  padding-bottom: 0px;\n}\n\n.button-items {\n  margin-left: -8px;\n  margin-bottom: -12px;\n}\n.button-items .btn {\n  margin-bottom: 12px;\n  margin-left: 8px;\n}\n\n.mfp-popup-form {\n  max-width: 1140px;\n}\n\n.bs-example-modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block;\n}\n\n.icon-demo-content {\n  color: #adb5bd;\n}\n.icon-demo-content i {\n  display: inline-block;\n  width: 40px;\n  height: 40px;\n  line-height: 36px;\n  font-size: 22px;\n  color: #74788d;\n  border: 2px solid var(--bs-border-color);\n  border-radius: 4px;\n  transition: all 0.4s;\n  text-align: center;\n  margin-right: 16px;\n  vertical-align: middle;\n}\n.icon-demo-content .col-lg-4 {\n  margin-top: 24px;\n}\n.icon-demo-content .col-lg-4:hover i {\n  color: #fff;\n  background-color: #5664d2;\n  border-color: #5664d2;\n}\n\n.grid-structure .grid-container {\n  background-color: #f8f9fa;\n  margin-top: 10px;\n  font-size: 0.8rem;\n  font-weight: 500;\n  padding: 10px 20px;\n}\n\n.card-radio {\n  background-color: var(--bs-secondary-bg);\n  border: 2px solid var(--bs-border-color);\n  border-radius: 0.375rem;\n  padding: 1rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: block;\n}\n.card-radio:hover {\n  cursor: pointer;\n}\n\n.card-radio-label {\n  display: block;\n}\n\n.card-radio-input {\n  display: none;\n}\n.card-radio-input:checked + .card-radio {\n  border-color: #5664d2 !important;\n}\n\n.navs-carousel .owl-nav {\n  margin-top: 16px;\n}\n.navs-carousel .owl-nav button {\n  width: 30px;\n  height: 30px;\n  line-height: 28px !important;\n  font-size: 20px !important;\n  border-radius: 50% !important;\n  background-color: rgba(86, 100, 210, 0.25) !important;\n  color: #5664d2 !important;\n  margin: 4px 8px !important;\n}\n\n@media print {\n  .vertical-menu,\n  .right-bar,\n  .page-title-box,\n  .navbar-header,\n  .footer {\n    display: none !important;\n  }\n  .card-body,\n  .main-content,\n  .right-bar,\n  .page-content,\n  body {\n    padding: 0;\n    margin: 0;\n  }\n  .card {\n    border: 0;\n  }\n}\n[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\n  max-height: 100%; /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: \" \";\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 6px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: \"\";\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}\n\n.fc-toolbar h2 {\n  font-size: 16px;\n  line-height: 30px;\n  text-transform: uppercase;\n}\n\n.fc th.fc-widget-header {\n  background: #f8f9fa;\n  font-size: 13px;\n  line-height: 20px;\n  padding: 10px 0;\n  text-transform: uppercase;\n  font-weight: 600;\n}\n\n.fc-unthemed .fc-content,\n.fc-unthemed .fc-divider,\n.fc-unthemed .fc-list-heading td,\n.fc-unthemed .fc-list-view,\n.fc-unthemed .fc-popover,\n.fc-unthemed .fc-row,\n.fc-unthemed tbody,\n.fc-unthemed td,\n.fc-unthemed th,\n.fc-unthemed thead {\n  border-color: var(--bs-border-color);\n}\n.fc-unthemed td.fc-today {\n  background: #fdfdfe;\n}\n\n.fc-button {\n  background: var(--bs-secondary-bg);\n  border-color: var(--bs-border-color);\n  color: #2d3448;\n  text-transform: capitalize;\n  box-shadow: none;\n  padding: 6px 12px !important;\n  height: auto !important;\n}\n\n.fc-state-down,\n.fc-state-active,\n.fc-state-disabled {\n  background-color: #5664d2;\n  color: #fff;\n  text-shadow: none;\n}\n\n.fc-event {\n  border-radius: 2px;\n  border: none;\n  cursor: move;\n  font-size: 0.8125rem;\n  margin: 5px 7px;\n  padding: 5px 5px;\n  text-align: center;\n}\n\n#external-events .external-event {\n  text-align: left !important;\n  padding: 8px 16px;\n}\n\n.fc-event, .fc-event-dot {\n  background-color: #5664d2;\n}\n\n.fc-event .fc-content {\n  color: #fff;\n}\n\n.fc .table-bordered td, .fc .table-bordered th {\n  border-color: var(--bs-border-color);\n}\n@media (max-width: 575.98px) {\n  .fc .fc-toolbar {\n    display: block;\n  }\n}\n.fc .fc-toolbar h2 {\n  font-size: 16px;\n  line-height: 30px;\n  text-transform: uppercase;\n}\n@media (max-width: 767.98px) {\n  .fc .fc-toolbar .fc-left,\n  .fc .fc-toolbar .fc-right,\n  .fc .fc-toolbar .fc-center {\n    float: none;\n    display: block;\n    text-align: center;\n    clear: both;\n    margin: 10px 0;\n  }\n  .fc .fc-toolbar > * > * {\n    float: none;\n  }\n  .fc .fc-toolbar .fc-today-button {\n    display: none;\n  }\n}\n.fc .fc-toolbar .btn {\n  text-transform: capitalize;\n}\n\n.fc-bootstrap .fc-today.alert-info {\n  background-color: var(--bs-tertiary-bg);\n}\n\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {\n  background-color: #000 !important;\n}\n\n[dir=rtl] .fc-header-toolbar {\n  direction: ltr !important;\n}\n\n[dir=rtl] .fc-toolbar > * > :not(:first-child) {\n  margin-left: 0.75em;\n}\n\n.sp-container {\n  background-color: var(--bs-secondary-bg);\n}\n.sp-container button {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.71094rem;\n  border-radius: 0.2rem;\n  font-weight: 400;\n  color: #212529;\n}\n.sp-container button.sp-palette-toggle {\n  background-color: #f8f9fa;\n}\n.sp-container button.sp-choose {\n  background-color: #1cbb8c;\n  margin-left: 5px;\n  margin-right: 0;\n}\n\n.sp-palette-container {\n  border-right: 1px solid var(--bs-border-color);\n}\n\n.sp-input {\n  background-color: var(--bs-secondary-bg);\n  border-color: var(--bs-border-color-translucent) !important;\n  color: var(--bs-body-color);\n}\n.sp-input:focus {\n  outline: none;\n}\n\n[dir=rtl] .sp-alpha {\n  direction: rtl;\n}\n[dir=rtl] .sp-original-input-container .sp-add-on {\n  border-top-right-radius: 0 !important;\n  border-bottom-right-radius: 0 !important;\n  border-top-left-radius: 4px !important;\n  border-bottom-left-radius: 4px !important;\n}\n[dir=rtl] input.spectrum.with-add-on {\n  border: 1px solid var(--bs-border-color-translucent);\n  border-left: 0;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n  border-top-right-radius: var(--bs-border-radius);\n  border-bottom-right-radius: var(--bs-border-radius);\n}\n\n#session-timeout-dialog .close {\n  display: none;\n}\n#session-timeout-dialog .countdown-holder {\n  color: #ff3d60;\n  font-weight: 500;\n}\n#session-timeout-dialog .btn-default {\n  background-color: #fff;\n  color: #ff3d60;\n  box-shadow: none;\n}\n\n.rs-control {\n  margin: 0px auto;\n}\n\n.rs-path-color {\n  background-color: #f1f5f7;\n}\n\n.rs-bg-color {\n  background-color: var(--bs-body-bg) !important;\n}\n\n.rs-border {\n  border-color: transparent;\n}\n\n.rs-handle {\n  background-color: #2d3448;\n}\n\n.rs-circle-border .rs-border {\n  border: 8px solid #f1f5f7;\n}\n\n.rs-disabled {\n  opacity: 1;\n}\n\n.outer-border .rs-border {\n  border-width: 0px;\n}\n.outer-border .rs-border.rs-outer {\n  border: 14px solid #f1f5f7;\n}\n.outer-border .rs-handle {\n  margin-left: 0 !important;\n}\n.outer-border .rs-path-color {\n  background-color: transparent;\n}\n\n.outer-border-dot .rs-border.rs-outer {\n  border: 16px dotted;\n}\n.outer-border-dot .rs-handle {\n  margin-left: 0 !important;\n}\n\n.rs-range-primary .rs-range-color {\n  background-color: #5664d2;\n}\n.rs-range-primary .rs-handle-dot {\n  background-color: #b7bdec;\n  border-color: #5664d2;\n}\n.rs-range-primary .rs-handle-dot:after {\n  background-color: #5664d2;\n}\n.rs-range-primary.rs-circle-border .rs-handle {\n  background-color: #5664d2;\n}\n.rs-range-primary.outer-border-dot .rs-border.rs-outer {\n  border-color: #b7bdec;\n}\n\n.rs-range-secondary .rs-range-color {\n  background-color: #74788d;\n}\n.rs-range-secondary .rs-handle-dot {\n  background-color: #b7b9c4;\n  border-color: #74788d;\n}\n.rs-range-secondary .rs-handle-dot:after {\n  background-color: #74788d;\n}\n.rs-range-secondary.rs-circle-border .rs-handle {\n  background-color: #74788d;\n}\n.rs-range-secondary.outer-border-dot .rs-border.rs-outer {\n  border-color: #b7b9c4;\n}\n\n.rs-range-success .rs-range-color {\n  background-color: #1cbb8c;\n}\n.rs-range-success .rs-handle-dot {\n  background-color: #69e9c3;\n  border-color: #1cbb8c;\n}\n.rs-range-success .rs-handle-dot:after {\n  background-color: #1cbb8c;\n}\n.rs-range-success.rs-circle-border .rs-handle {\n  background-color: #1cbb8c;\n}\n.rs-range-success.outer-border-dot .rs-border.rs-outer {\n  border-color: #69e9c3;\n}\n\n.rs-range-info .rs-range-color {\n  background-color: #4aa3ff;\n}\n.rs-range-info .rs-handle-dot {\n  background-color: #c4e1ff;\n  border-color: #4aa3ff;\n}\n.rs-range-info .rs-handle-dot:after {\n  background-color: #4aa3ff;\n}\n.rs-range-info.rs-circle-border .rs-handle {\n  background-color: #4aa3ff;\n}\n.rs-range-info.outer-border-dot .rs-border.rs-outer {\n  border-color: #c4e1ff;\n}\n\n.rs-range-warning .rs-range-color {\n  background-color: #fcb92c;\n}\n.rs-range-warning .rs-handle-dot {\n  background-color: #fee1a5;\n  border-color: #fcb92c;\n}\n.rs-range-warning .rs-handle-dot:after {\n  background-color: #fcb92c;\n}\n.rs-range-warning.rs-circle-border .rs-handle {\n  background-color: #fcb92c;\n}\n.rs-range-warning.outer-border-dot .rs-border.rs-outer {\n  border-color: #fee1a5;\n}\n\n.rs-range-danger .rs-range-color {\n  background-color: #ff3d60;\n}\n.rs-range-danger .rs-handle-dot {\n  background-color: #ffb7c4;\n  border-color: #ff3d60;\n}\n.rs-range-danger .rs-handle-dot:after {\n  background-color: #ff3d60;\n}\n.rs-range-danger.rs-circle-border .rs-handle {\n  background-color: #ff3d60;\n}\n.rs-range-danger.outer-border-dot .rs-border.rs-outer {\n  border-color: #ffb7c4;\n}\n\n.rs-range-light .rs-range-color {\n  background-color: #f8f9fa;\n}\n.rs-range-light .rs-handle-dot {\n  background-color: white;\n  border-color: #f8f9fa;\n}\n.rs-range-light .rs-handle-dot:after {\n  background-color: #f8f9fa;\n}\n.rs-range-light.rs-circle-border .rs-handle {\n  background-color: #f8f9fa;\n}\n.rs-range-light.outer-border-dot .rs-border.rs-outer {\n  border-color: white;\n}\n\n.rs-range-dark .rs-range-color {\n  background-color: #212529;\n}\n.rs-range-dark .rs-handle-dot {\n  background-color: #58626d;\n  border-color: #212529;\n}\n.rs-range-dark .rs-handle-dot:after {\n  background-color: #212529;\n}\n.rs-range-dark.rs-circle-border .rs-handle {\n  background-color: #212529;\n}\n.rs-range-dark.outer-border-dot .rs-border.rs-outer {\n  border-color: #58626d;\n}\n\n.rs-handle-arrow .rs-handle {\n  background-color: transparent;\n  border: 8px solid transparent;\n  border-right-color: #2d3448;\n  margin: -6px 0px 0px 14px !important;\n  border-width: 6px 104px 6px 4px;\n}\n.rs-handle-arrow .rs-handle:before {\n  display: block;\n  content: \" \";\n  position: absolute;\n  height: 22px;\n  width: 22px;\n  background: #2d3448;\n  right: -11px;\n  bottom: -11px;\n  border-radius: 100px;\n}\n\n.rs-handle-arrow-dash .rs-handle {\n  background-color: transparent;\n  border: 8px solid transparent;\n  border-right-color: #2d3448;\n  margin: -8px 0 0 14px !important;\n}\n.rs-handle-arrow-dash .rs-handle:before {\n  display: block;\n  content: \" \";\n  position: absolute;\n  height: 12px;\n  width: 12px;\n  background: #2d3448;\n  right: -6px;\n  bottom: -6px;\n  border-radius: 100%;\n}\n.rs-handle-arrow-dash .rs-handle:after {\n  display: block;\n  content: \" \";\n  width: 80px;\n  position: absolute;\n  top: -1px;\n  right: 0px;\n  border-top: 2px dotted #2d3448;\n}\n\n.irs {\n  font-family: var(--bs-font-sans-serif);\n}\n\n.irs--round .irs-bar,\n.irs--round .irs-to,\n.irs--round .irs-from,\n.irs--round .irs-single {\n  background: #5664d2 !important;\n  font-size: 11px;\n}\n.irs--round .irs-to:before,\n.irs--round .irs-from:before,\n.irs--round .irs-single:before {\n  display: none;\n}\n.irs--round .irs-line {\n  background: var(--bs-tertiary-bg);\n  border-color: var(--bs-border-color);\n}\n.irs--round .irs-grid-text {\n  font-size: 11px;\n  color: #adb5bd;\n}\n.irs--round .irs-min,\n.irs--round .irs-max {\n  color: #adb5bd;\n  background: var(--bs-tertiary-bg);\n  font-size: 11px;\n}\n.irs--round .irs-handle {\n  border: 2px solid #5664d2;\n  width: 16px;\n  height: 16px;\n  top: 29px;\n  background-color: var(--bs-secondary-bg) !important;\n}\n\n.swal2-container .swal2-title {\n  font-size: 24px;\n  font-weight: 500;\n}\n\n.swal2-content {\n  font-size: 16px;\n}\n\n.swal2-icon.swal2-question {\n  border-color: #4aa3ff;\n  color: #4aa3ff;\n}\n.swal2-icon.swal2-success [class^=swal2-success-line] {\n  background-color: #1cbb8c;\n}\n.swal2-icon.swal2-success .swal2-success-ring {\n  border-color: rgba(28, 187, 140, 0.3);\n}\n.swal2-icon.swal2-warning {\n  border-color: #fcb92c;\n  color: #fcb92c;\n}\n\n.swal2-styled:focus {\n  box-shadow: none;\n}\n\n.swal2-progress-steps .swal2-progress-step {\n  background: #5664d2;\n}\n.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {\n  background: #5664d2;\n}\n.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step, .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {\n  background: rgba(86, 100, 210, 0.3);\n}\n.swal2-progress-steps .swal2-progress-step-line {\n  background: #5664d2;\n}\n\n.swal2-loader {\n  border-color: #5664d2 transparent #5664d2 transparent;\n}\n\n.swal2-modal {\n  background-color: var(--bs-secondary-bg) !important;\n  color: var(--bs-body-color) !important;\n}\n\n.swal2-content {\n  background-color: var(--bs-secondary-bg) !important;\n  color: var(--bs-secondary-color) !important;\n}\n\n.swal2-title {\n  color: var(--bs-secondary-color) !important;\n}\n\n.symbol {\n  border-color: var(--bs-secondary-bg);\n}\n\n.rating-symbol-background, .rating-symbol-foreground {\n  font-size: 24px;\n}\n\n.rating-symbol-foreground {\n  top: 0px;\n}\n\n.rating-star > span {\n  display: inline-block;\n  vertical-align: middle;\n}\n.rating-star > span.badge {\n  margin-left: 4px;\n}\n\n/* =============\n   Notification\n============= */\n#toast-container > div {\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  opacity: 1;\n}\n#toast-container > div:hover {\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  opacity: 0.9;\n}\n#toast-container.toast-top-full-width > div, #toast-container.toast-bottom-full-width > div {\n  min-width: 96%;\n  margin: 4px auto;\n}\n\n.toast-primary {\n  border: 2px solid #5664d2 !important;\n  background-color: rgba(86, 100, 210, 0.8) !important;\n}\n\n.toast-secondary {\n  border: 2px solid #74788d !important;\n  background-color: rgba(116, 120, 141, 0.8) !important;\n}\n\n.toast-success {\n  border: 2px solid #1cbb8c !important;\n  background-color: rgba(28, 187, 140, 0.8) !important;\n}\n\n.toast-info {\n  border: 2px solid #4aa3ff !important;\n  background-color: rgba(74, 163, 255, 0.8) !important;\n}\n\n.toast-warning {\n  border: 2px solid #fcb92c !important;\n  background-color: rgba(252, 185, 44, 0.8) !important;\n}\n\n.toast-danger {\n  border: 2px solid #ff3d60 !important;\n  background-color: rgba(255, 61, 96, 0.8) !important;\n}\n\n.toast-light {\n  border: 2px solid #f8f9fa !important;\n  background-color: rgba(248, 249, 250, 0.8) !important;\n}\n\n.toast-dark {\n  border: 2px solid #212529 !important;\n  background-color: rgba(33, 37, 41, 0.8) !important;\n}\n\n.toast-error {\n  background-color: rgba(255, 61, 96, 0.8);\n  border: 2px solid #ff3d60;\n}\n\n.toastr-options {\n  padding: 24px;\n  background-color: var(--bs-tertiary-bg);\n  margin-bottom: 0;\n  border: 1px solid var(--bs-border-color);\n}\n\n.error {\n  color: #ff3d60;\n}\n\n.parsley-error {\n  border-color: #ff3d60;\n}\n\n.parsley-errors-list {\n  display: none;\n  margin: 0;\n  padding: 0;\n}\n.parsley-errors-list.filled {\n  display: block;\n}\n.parsley-errors-list > li {\n  font-size: 12px;\n  list-style: none;\n  color: #ff3d60;\n  margin-top: 5px;\n}\n\n.select2-container {\n  display: block;\n}\n.select2-container .select2-selection--single {\n  background-color: var(--bs-secondary-bg);\n  border: 1px solid var(--bs-border-color-translucent);\n  height: 38px;\n}\n.select2-container .select2-selection--single:focus {\n  outline: none;\n}\n.select2-container .select2-selection--single .select2-selection__rendered {\n  line-height: 36px;\n  padding-left: 12px;\n  color: var(--bs-body-color);\n}\n.select2-container .select2-selection--single .select2-selection__arrow {\n  height: 34px;\n  width: 34px;\n  right: 3px;\n}\n.select2-container .select2-selection--single .select2-selection__arrow b {\n  border-color: #adb5bd transparent transparent transparent;\n  border-width: 6px 6px 0 6px;\n}\n.select2-container .select2-selection--single .select2-selection__placeholder {\n  color: var(--bs-body-color);\n}\n\n.select2-container--open .select2-selection--single .select2-selection__arrow b {\n  border-color: transparent transparent #adb5bd transparent !important;\n  border-width: 0 6px 6px 6px !important;\n}\n\n.select2-container--default .select2-search--dropdown {\n  padding: 10px;\n  background-color: var(--bs-secondary-bg);\n}\n.select2-container--default .select2-search--dropdown .select2-search__field {\n  border: 1px solid var(--bs-border-color-translucent);\n  background-color: var(--bs-secondary-bg);\n  color: #74788d;\n  outline: none;\n}\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background-color: #5664d2;\n}\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: var(--bs-tertiary-bg);\n  color: var(--bs-secondary-color);\n}\n.select2-container--default .select2-results__option[aria-selected=true]:hover {\n  background-color: #5664d2;\n  color: #fff;\n}\n\n.select2-results__option {\n  padding: 6px 12px;\n}\n\n.select2-dropdown {\n  border: 1px solid var(--bs-border-color);\n  background-color: var(--bs-secondary-bg);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n\n.select2-search input {\n  border: 1px solid var(--bs-border-color);\n}\n\n.select2-container .select2-selection--multiple {\n  min-height: 38px;\n  background-color: var(--bs-secondary-bg);\n  border: 1px solid var(--bs-border-color-translucent) !important;\n}\n.select2-container .select2-selection--multiple .select2-selection__rendered {\n  padding: 2px 10px;\n}\n.select2-container .select2-selection--multiple .select2-search__field {\n  border: 0;\n  color: var(--bs-body-color);\n}\n.select2-container .select2-selection--multiple .select2-search__field::placeholder {\n  color: var(--bs-body-color);\n}\n.select2-container .select2-selection--multiple .select2-selection__choice {\n  background-color: var(--bs-tertiary-bg);\n  border: 1px solid var(--bs-border-color);\n  border-radius: 1px;\n  padding: 0 7px;\n}\n\n.select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: var(--bs-border-color);\n}\n.select2-container--default .select2-results__group {\n  font-weight: 600;\n}\n\n.select2-result-repository__avatar {\n  float: left;\n  width: 60px;\n  margin-right: 10px;\n}\n.select2-result-repository__avatar img {\n  width: 100%;\n  height: auto;\n  border-radius: 2px;\n}\n\n.select2-result-repository__statistics {\n  margin-top: 7px;\n}\n\n.select2-result-repository__forks,\n.select2-result-repository__stargazers,\n.select2-result-repository__watchers {\n  display: inline-block;\n  font-size: 11px;\n  margin-right: 1em;\n  color: #adb5bd;\n}\n.select2-result-repository__forks .fa,\n.select2-result-repository__stargazers .fa,\n.select2-result-repository__watchers .fa {\n  margin-right: 4px;\n}\n.select2-result-repository__forks .fa.fa-flash::before,\n.select2-result-repository__stargazers .fa.fa-flash::before,\n.select2-result-repository__watchers .fa.fa-flash::before {\n  content: \"\\f0e7\";\n  font-family: \"Font Awesome 5 Free\";\n}\n\n.select2-results__option--highlighted .select2-result-repository__forks,\n.select2-results__option--highlighted .select2-result-repository__stargazers,\n.select2-results__option--highlighted .select2-result-repository__watchers {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.select2-result-repository__meta {\n  overflow: hidden;\n}\n\n.img-flag {\n  margin-right: 7px;\n  height: 15px;\n  width: 18px;\n}\n\n/* CSS Switch */\ninput[switch] {\n  display: none;\n}\ninput[switch] + label {\n  font-size: 1em;\n  line-height: 1;\n  width: 56px;\n  height: 24px;\n  background-color: #ced4da;\n  background-image: none;\n  border-radius: 2rem;\n  padding: 0.16667rem;\n  cursor: pointer;\n  display: inline-block;\n  text-align: center;\n  position: relative;\n  font-weight: 500;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch] + label:before {\n  color: #212529;\n  content: attr(data-off-label);\n  display: block;\n  font-family: inherit;\n  font-weight: 500;\n  font-size: 12px;\n  line-height: 21px;\n  position: absolute;\n  right: 1px;\n  margin: 3px;\n  top: -2px;\n  text-align: center;\n  min-width: 1.66667rem;\n  overflow: hidden;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch] + label:after {\n  content: \"\";\n  position: absolute;\n  left: 3px;\n  background-color: #eff2f7;\n  box-shadow: none;\n  border-radius: 2rem;\n  height: 20px;\n  width: 20px;\n  top: 2px;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch]:checked + label {\n  background-color: #5664d2;\n}\n\ninput[switch]:checked + label {\n  background-color: #5664d2;\n}\ninput[switch]:checked + label:before {\n  color: #fff;\n  content: attr(data-on-label);\n  right: auto;\n  left: 3px;\n}\ninput[switch]:checked + label:after {\n  left: 33px;\n  background-color: #eff2f7;\n}\n\ninput[switch=bool] + label {\n  background-color: #ff3d60;\n}\n\ninput[switch=bool] + label:before, input[switch=bool]:checked + label:before,\ninput[switch=default]:checked + label:before {\n  color: #fff;\n}\n\ninput[switch=bool]:checked + label {\n  background-color: #1cbb8c;\n}\n\ninput[switch=default]:checked + label {\n  background-color: #a2a2a2;\n}\n\ninput[switch=primary]:checked + label {\n  background-color: #5664d2;\n}\n\ninput[switch=success]:checked + label {\n  background-color: #1cbb8c;\n}\n\ninput[switch=info]:checked + label {\n  background-color: #4aa3ff;\n}\n\ninput[switch=warning]:checked + label {\n  background-color: #fcb92c;\n}\n\ninput[switch=danger]:checked + label {\n  background-color: #ff3d60;\n}\n\ninput[switch=dark]:checked + label {\n  background-color: #212529;\n}\n\n.square-switch {\n  margin-right: 7px;\n}\n.square-switch input[switch] + label, .square-switch input[switch] + label:after {\n  border-radius: 4px;\n}\n\n.datepicker {\n  border: 1px solid #f8f9fa;\n  padding: 8px;\n  z-index: 999 !important;\n}\n.datepicker table tr th {\n  font-weight: 500;\n}\n.datepicker table tr td.active, .datepicker table tr td.active:hover, .datepicker table tr td .active.disabled, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.selected, .datepicker table tr td.selected:hover, .datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover {\n  background-color: #5664d2 !important;\n  background-image: none;\n  box-shadow: none;\n  color: #fff !important;\n}\n.datepicker table tr td.day.focused, .datepicker table tr td.day:hover,\n.datepicker table tr td span.focused,\n.datepicker table tr td span:hover {\n  background: #eff2f7;\n}\n.datepicker table tr td.new, .datepicker table tr td.old,\n.datepicker table tr td span.new,\n.datepicker table tr td span.old {\n  color: #adb5bd;\n  opacity: 0.6;\n}\n.datepicker table tr td.range, .datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:hover, .datepicker table tr td.range:hover {\n  background-color: #f1f5f7;\n}\n\n.table-condensed > thead > tr > th, .table-condensed > tbody > tr > td {\n  padding: 7px;\n}\n\n.bootstrap-touchspin.input-group > .input-group-prepend > .btn, .bootstrap-touchspin.input-group > .input-group-prepend > .input-group-text {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.bootstrap-touchspin.input-group > .input-group-append > .btn, .bootstrap-touchspin.input-group > .input-group-append > .input-group-text {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.dataTables_wrapper.container-fluid {\n  padding: 0;\n}\n\ndiv.dataTables_wrapper div.dataTables_filter {\n  text-align: right;\n}\n@media (max-width: 767px) {\n  div.dataTables_wrapper div.dataTables_filter {\n    text-align: center;\n  }\n}\ndiv.dataTables_wrapper div.dataTables_filter input {\n  margin-left: 0.5em;\n  margin-right: 0;\n}\n\n.datatable td:focus {\n  outline: none;\n}\n\ndiv.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:first-child {\n  padding-left: 0;\n}\ndiv.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:last-child {\n  padding-right: 0;\n}\n\ntable.dataTable {\n  border-collapse: collapse !important;\n  margin-bottom: 15px !important;\n}\ntable.dataTable thead .sorting:before,\ntable.dataTable thead .sorting_asc:before,\ntable.dataTable thead .sorting_desc:before,\ntable.dataTable thead .sorting_asc_disabled:before,\ntable.dataTable thead .sorting_desc_disabled:before {\n  left: auto;\n  right: 0.5rem;\n  content: \"\\f0360\";\n  font-family: \"Material Design Icons\";\n  font-size: 1rem;\n  top: 9px;\n}\ntable.dataTable thead .sorting:after,\ntable.dataTable thead .sorting_asc:after,\ntable.dataTable thead .sorting_desc:after,\ntable.dataTable thead .sorting_asc_disabled:after,\ntable.dataTable thead .sorting_desc_disabled:after {\n  left: auto;\n  right: 0.5em;\n  content: \"\\f035d\";\n  font-family: \"Material Design Icons\";\n  top: 15px;\n  font-size: 1rem;\n}\ntable.dataTable thead tr th.sorting_asc, table.dataTable thead tr th.sorting_desc, table.dataTable thead tr th.sorting,\ntable.dataTable thead tr td.sorting_asc,\ntable.dataTable thead tr td.sorting_desc,\ntable.dataTable thead tr td.sorting {\n  padding-left: 12px;\n  padding-right: 30px;\n}\ntable.dataTable tbody > tr.selected, table.dataTable tbody > tr > .selected {\n  background-color: rgba(86, 100, 210, 0.2);\n}\ntable.dataTable tbody > tr.selected td, table.dataTable tbody > tr > .selected td {\n  border-color: rgba(86, 100, 210, 0.2);\n  color: #5664d2;\n}\ntable.dataTable tbody td:focus {\n  outline: none !important;\n}\ntable.dataTable tbody th.focus, table.dataTable tbody td.focus {\n  outline: 2px solid #5664d2 !important;\n  outline-offset: -1px;\n  background-color: rgba(86, 100, 210, 0.15);\n}\n\n.dataTables_info {\n  font-weight: 600;\n}\n\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > td:first-child:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > th:first-child:before {\n  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.1);\n  background-color: #1cbb8c;\n  bottom: auto;\n}\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {\n  background-color: #ff3d60;\n}\n\ndiv.dt-button-info {\n  background-color: #5664d2;\n  border: none;\n  color: #fff;\n  box-shadow: none;\n  border-radius: 3px;\n  text-align: center;\n  z-index: 21;\n}\ndiv.dt-button-info h2 {\n  border-bottom: none;\n  background-color: rgba(255, 255, 255, 0.2);\n  color: #fff;\n}\n\n@media (max-width: 767.98px) {\n  li.paginate_button.previous,\n  li.paginate_button.next {\n    display: inline-block;\n    font-size: 1.5rem;\n  }\n  li.paginate_button {\n    display: none;\n  }\n  .dataTables_paginate ul {\n    text-align: center;\n    display: block;\n    margin: 1rem 0 0 !important;\n  }\n  div.dt-buttons {\n    display: inline-table;\n    margin-bottom: 1rem;\n  }\n}\n.activate-select .sorting_1 {\n  background-color: #f8f9fa;\n}\n\n.table-bordered {\n  border: var(--bs-border-width) solid var(--bs-border-color);\n}\n\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td {\n  position: relative;\n}\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control {\n  padding-left: 30px;\n}\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before {\n  top: 64%;\n  left: 5px;\n  height: 14px;\n  width: 14px;\n  margin-top: -14px;\n  display: block;\n  position: absolute;\n  color: #fff;\n  border: 2px solid #fff;\n  border-radius: 14px;\n  box-sizing: content-box;\n  text-align: center;\n  text-indent: 0 !important;\n  line-height: 12px;\n  content: \"+\";\n  background-color: #5664d2;\n}\n\n.tox-tinymce {\n  border: 2px solid var(--bs-border-color) !important;\n}\n\n.tox .tox-statusbar {\n  border-top: 1px solid var(--bs-border-color) !important;\n}\n.tox .tox-menubar,\n.tox .tox-edit-area__iframe,\n.tox .tox-statusbar {\n  background-color: var(--bs-secondary-bg) !important;\n  background: none !important;\n}\n.tox .tox-mbtn {\n  color: var(--bs-body-color) !important;\n}\n.tox .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {\n  background-color: var(--bs-tertiary-bg) !important;\n}\n.tox .tox-tbtn:hover {\n  background-color: var(--bs-tertiary-bg) !important;\n}\n.tox .tox-toolbar__primary {\n  border-color: var(--bs-border-color) !important;\n}\n.tox .tox-toolbar,\n.tox .tox-toolbar__overflow,\n.tox .tox-toolbar__primary {\n  background: var(--bs-tertiary-bg) !important;\n}\n.tox .tox-tbtn {\n  color: var(--bs-body-color) !important;\n}\n.tox .tox-tbtn svg {\n  fill: var(--bs-body-color) !important;\n}\n.tox .tox-statusbar a,\n.tox .tox-statusbar__path-item,\n.tox .tox-statusbar__wordcount {\n  color: var(--bs-body-color) !important;\n}\n.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\n  border-right: 1px solid var(--bs-border-color) !important;\n}\n\n.tox-tinymce-aux {\n  z-index: 1050 !important;\n}\n\n.tox .tox-toolbar-overlord {\n  background-color: var(--bs-tertiary-bg) !important;\n}\n\n/* Dropzone */\n.dropzone {\n  min-height: 230px;\n  border: 2px dashed var(--bs-border-color);\n  background: var(--bs-secondary-bg);\n  border-radius: 6px;\n}\n.dropzone .dz-message {\n  font-size: 24px;\n  width: 100%;\n}\n\n.sp-colorize-container {\n  border: none !important;\n}\n\n.twitter-bs-wizard .twitter-bs-wizard-nav {\n  position: relative;\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav:before {\n  content: \"\";\n  width: 100%;\n  height: 2px;\n  background-color: var(--bs-border-color);\n  position: absolute;\n  left: 0;\n  top: 26px;\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n  display: inline-block;\n  width: 38px;\n  height: 38px;\n  line-height: 34px;\n  border: 2px solid #5664d2;\n  color: #5664d2;\n  text-align: center;\n  border-radius: 50%;\n  position: relative;\n  background-color: var(--bs-secondary-bg);\n}\n@media (max-width: 991.98px) {\n  .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n    display: block;\n    margin: 0 auto 8px !important;\n  }\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {\n  display: block;\n  margin-top: 8px;\n  font-weight: 600;\n}\n@media (max-width: 575.98px) {\n  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {\n    display: none;\n  }\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active {\n  background-color: transparent;\n  color: var(--bs-body-color);\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active .step-number {\n  background-color: #5664d2;\n  color: #fff;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link {\n  padding-top: 24px;\n  padding-left: 0;\n  list-style: none;\n  margin-bottom: 0;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li {\n  display: inline-block;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li a {\n  display: inline-block;\n  padding: 0.47rem 0.75rem;\n  background-color: #5664d2;\n  color: #fff;\n  border-radius: 0.25rem;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li.disabled a {\n  cursor: not-allowed;\n  background-color: #7682db;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li.next {\n  float: right;\n}\n\n.twitter-bs-wizard-tab-content {\n  padding-top: 24px;\n  min-height: 262px;\n}\n\n.table-rep-plugin .btn-toolbar {\n  display: block;\n}\n.table-rep-plugin .table-responsive {\n  border: none !important;\n}\n.table-rep-plugin .btn-group .btn-default {\n  background-color: #f8f9fa;\n  color: #212529;\n  border: 1px solid #f2f4f6;\n}\n.table-rep-plugin .btn-group .btn-default.btn-primary {\n  background-color: #5664d2;\n  border-color: #5664d2;\n  color: #fff;\n}\n.table-rep-plugin .btn-group.pull-right {\n  float: right;\n}\n.table-rep-plugin .btn-group.pull-right .dropdown-menu {\n  right: 0;\n  transform: none !important;\n  top: 100% !important;\n}\n.table-rep-plugin tbody th {\n  font-size: 14px;\n  font-weight: normal;\n}\n.table-rep-plugin .checkbox-row {\n  padding-left: 40px;\n  color: var(--bs-body-color) !important;\n}\n.table-rep-plugin .checkbox-row:hover {\n  background-color: #f6f8fa !important;\n}\n.table-rep-plugin .checkbox-row label {\n  display: inline-block;\n  padding-left: 5px;\n  position: relative;\n}\n.table-rep-plugin .checkbox-row label::before {\n  -o-transition: 0.3s ease-in-out;\n  -webkit-transition: 0.3s ease-in-out;\n  background-color: #fff;\n  border-radius: 3px;\n  border: 1px solid #f1f5f7;\n  content: \"\";\n  display: inline-block;\n  height: 17px;\n  left: 0;\n  margin-left: -20px;\n  position: absolute;\n  transition: 0.3s ease-in-out;\n  width: 17px;\n  outline: none !important;\n}\n.table-rep-plugin .checkbox-row label::after {\n  color: #eff2f7;\n  display: inline-block;\n  font-size: 11px;\n  height: 16px;\n  left: 0;\n  margin-left: -20px;\n  padding-left: 3px;\n  padding-top: 1px;\n  position: absolute;\n  top: -1px;\n  width: 16px;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox] {\n  cursor: pointer;\n  opacity: 0;\n  z-index: 1;\n  outline: none !important;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:disabled + label {\n  opacity: 0.65;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:focus + label::before {\n  outline-offset: -2px;\n  outline: none;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::after {\n  content: \"\\f00c\";\n  font-family: \"Font Awesome 5 Free\";\n  font-weight: 900;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:disabled + label::before {\n  background-color: #f8f9fa;\n  cursor: not-allowed;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::before {\n  background-color: #5664d2;\n  border-color: #5664d2;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::after {\n  color: #fff;\n}\n.table-rep-plugin .fixed-solution .sticky-table-header {\n  top: 70px !important;\n  background-color: #5664d2;\n}\n.table-rep-plugin .fixed-solution .sticky-table-header table {\n  color: #fff;\n}\n\n@media (min-width: 992px) {\n  body[data-layout=horizontal] .fixed-solution .sticky-table-header {\n    top: 120px !important;\n  }\n}\n\n.table-edits input, .table-edits select {\n  height: calc(1.5em + 0.5rem + calc(var(--bs-border-width) * 2));\n  padding: 0.25rem 0.5rem;\n  border: 1px solid var(--bs-border-color-translucent);\n  background-color: var(--bs-secondary-bg);\n  color: var(--bs-body-color);\n  border-radius: var(--bs-border-radius);\n}\n.table-edits input:focus, .table-edits select:focus {\n  outline: none;\n  border-color: #abb2e9;\n}\n\n.apex-charts {\n  min-height: 10px !important;\n}\n.apex-charts text {\n  font-family: var(--bs-font-sans-serif) !important;\n  fill: #adb5bd;\n}\n.apex-charts .apexcharts-canvas {\n  margin: 0 auto;\n}\n\n.apexcharts-tooltip {\n  background-color: var(--bs-card-bg) !important;\n  border: 1px solid var(--bs-border-color) !important;\n}\n\n.apexcharts-tooltip-title {\n  background-color: var(--bs-body-bg) !important;\n  border-bottom: 1px solid var(--bs-border-color) !important;\n}\n\n.apexcharts-tooltip-title,\n.apexcharts-tooltip-text {\n  font-family: var(--bs-font-sans-serif) !important;\n}\n\n.apexcharts-legend-series {\n  font-weight: 500;\n}\n\n.apexcharts-gridline {\n  pointer-events: none;\n  stroke: #dee2e6;\n}\n\n.apexcharts-legend-text {\n  color: #74788d !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-size: 13px !important;\n}\n\n.apexcharts-pie-label {\n  fill: #fff !important;\n}\n\n.apexcharts-yaxis text,\n.apexcharts-xaxis text {\n  font-family: var(--bs-font-sans-serif) !important;\n  fill: #adb5bd;\n}\n\n.apexcharts-grid-row {\n  stroke: var(--bs-border-color) !important;\n}\n\n.apexcharts-gridline {\n  stroke: var(--bs-border-color) !important;\n}\n\n/* Flot chart */\n.flot-charts-height {\n  height: 320px;\n}\n\n.flotTip {\n  padding: 8px 12px;\n  background-color: rgba(33, 37, 41, 0.9);\n  z-index: 100;\n  color: #f8f9fa;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  border-radius: 4px;\n}\n\n.legendLabel {\n  color: #adb5bd;\n}\n\n.jqstooltip {\n  box-sizing: content-box;\n  width: auto !important;\n  height: auto !important;\n  background-color: #252b3b !important;\n  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.1);\n  padding: 5px 10px !important;\n  border-radius: 3px;\n  border-color: #212529 !important;\n}\n\n.jqsfield {\n  color: #eff2f7 !important;\n  font-size: 12px !important;\n  line-height: 18px !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-weight: 500 !important;\n}\n\n.gmaps, .gmaps-panaroma {\n  height: 300px;\n  background: #f8f9fa;\n  border-radius: 3px;\n}\n\n.gmaps-overlay {\n  display: block;\n  text-align: center;\n  color: #fff;\n  font-size: 16px;\n  line-height: 40px;\n  background: #5664d2;\n  border-radius: 4px;\n  padding: 10px 20px;\n}\n\n.gmaps-overlay_arrow {\n  left: 50%;\n  margin-left: -16px;\n  width: 0;\n  height: 0;\n  position: absolute;\n}\n.gmaps-overlay_arrow.above {\n  bottom: -15px;\n  border-left: 16px solid transparent;\n  border-right: 16px solid transparent;\n  border-top: 16px solid #5664d2;\n}\n.gmaps-overlay_arrow.below {\n  top: -15px;\n  border-left: 16px solid transparent;\n  border-right: 16px solid transparent;\n  border-bottom: 16px solid #5664d2;\n}\n\n.jvectormap-label {\n  border: none;\n  background: #252b3b;\n  color: #f8f9fa;\n  font-family: var(--bs-font-sans-serif);\n  font-size: 0.9rem;\n  padding: 5px 8px;\n}\n\n.editable-input .form-control {\n  display: inline-block;\n}\n\n.editable-buttons {\n  margin-left: 7px;\n}\n.editable-buttons .editable-cancel {\n  margin-left: 7px;\n}\n\n.auth-body-bg {\n  background-color: var(--bs-secondary-bg);\n}\n\n.authentication-bg {\n  background-image: url(\"../images/authentication-bg.jpg\");\n  height: 100vh;\n  background-size: cover;\n  background-position: center;\n}\n.authentication-bg .bg-overlay {\n  background-color: #292626;\n}\n@media (max-width: 991px) {\n  .authentication-bg {\n    display: none;\n  }\n}\n\n.authentication-page-content {\n  height: 100vh;\n  display: flex;\n}\n\n.auth-form-group-custom {\n  position: relative;\n}\n.auth-form-group-custom .form-control {\n  height: 60px;\n  padding-top: 28px;\n  padding-left: 60px;\n}\n.auth-form-group-custom label {\n  position: absolute;\n  top: 7px;\n  left: 60px;\n}\n.auth-form-group-custom .auti-custom-input-icon {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  left: 19px;\n  font-size: 24px;\n  color: #5664d2;\n}\n\n.auth-logo.logo-light {\n  display: none;\n}\n.auth-logo.logo-dark {\n  display: block;\n}\n\n[data-bs-theme=dark] .authentication-logo .logo-light {\n  display: block;\n}\n[data-bs-theme=dark] .authentication-logo .logo-dark {\n  display: none;\n}\n\n.search-box .form-control {\n  border-radius: 30px;\n  padding-left: 40px;\n}\n.search-box .search-icon {\n  font-size: 16px;\n  position: absolute;\n  left: 13px;\n  top: 0;\n  line-height: 38px;\n}\n\n.ecommerce .accordion-item {\n  border-bottom: 1px solid var(--bs-border-color);\n}\n.ecommerce .accordion-item .accordion-collapse {\n  border: 0;\n}\n.ecommerce button {\n  border: 0;\n  display: block;\n  color: var(--bs-body-color);\n  font-weight: 500;\n  padding: 8px 16px;\n}\n.ecommerce button[data-bs-toggle=collapse].collapsed::after {\n  content: \"\\f0415\";\n}\n.ecommerce button[data-bs-toggle=collapse]::after {\n  content: \"\\f0374\";\n  display: block;\n  font-family: \"Material Design Icons\";\n  font-size: 16px;\n  position: absolute;\n  right: 20px;\n  font-weight: 500;\n  top: 50%;\n  background-image: none;\n  transform: translateY(-50%);\n}\n.ecommerce .accordion-body {\n  padding: 8px 0;\n  border: 0;\n}\n.ecommerce .accordion-body li a {\n  display: block;\n  padding: 4px 16px;\n  color: var(--bs-body-color);\n}\n.ecommerce .accordion-body li.active a {\n  color: #5664d2;\n}\n\n.product-detai-imgs .nav .nav-link {\n  margin: 7px 0px;\n}\n.product-detai-imgs .nav .nav-link.active {\n  background-color: var(--bs-tertiary-bg);\n}\n\n.product-color a {\n  display: inline-block;\n  text-align: center;\n  color: var(--bs-body-color);\n}\n.product-color a .product-color-item {\n  margin: 7px;\n  border: 2px solid var(--bs-border-color);\n  border-radius: 4px;\n}\n.product-color a.active, .product-color a:hover {\n  color: #5664d2;\n}\n.product-color a.active .product-color-item, .product-color a:hover .product-color-item {\n  border-color: #5664d2 !important;\n}\n\n.ecommerce-sortby-list li {\n  color: var(--bs-body-color);\n}\n.ecommerce-sortby-list li a {\n  color: var(--bs-body-color);\n  padding: 4px;\n}\n.ecommerce-sortby-list li.active a {\n  color: #5664d2;\n}\n\n.product-box {\n  padding: 24px;\n  border: 1px solid var(--bs-border-color);\n  transition: all 0.4s;\n}\n.product-box:hover {\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n\n.product-img {\n  position: relative;\n}\n.product-img .product-ribbon {\n  position: absolute;\n  top: 0;\n  left: -24px;\n  padding: 6px 8px;\n  border-radius: 0px 30px 30px 0px;\n}\n.product-img .product-like {\n  position: absolute;\n  top: 0;\n  right: 0;\n}\n.product-img .product-like a {\n  display: inline-block;\n  width: 40px;\n  height: 40px;\n  border: 2px solid var(--bs-border-color);\n  line-height: 38px;\n  border-radius: 50%;\n  text-align: center;\n  color: #adb5bd;\n}\n\n.product-detail .nav-pills .nav-link {\n  margin-bottom: 7px;\n}\n.product-detail .nav-pills .nav-link.active {\n  background-color: var(--bs-tertiary-bg);\n}\n.product-detail .nav-pills .nav-link .tab-img {\n  width: 5rem;\n}\n.product-detail .product-img {\n  border: 1px solid var(--bs-border-color);\n  padding: 24px;\n}\n\n.product-desc-list li {\n  padding: 4px 0px;\n}\n\n.product-review-link .list-inline-item a {\n  color: #74788d;\n}\n.product-review-link .list-inline-item:not(:last-child) {\n  margin-right: 14px;\n}\n\n.product-cart-touchspin {\n  border: 1px solid var(--bs-border-color);\n  background-color: var(--bs-secondary-bg);\n  border-radius: 0.375rem;\n}\n.product-cart-touchspin .form-control {\n  border-color: transparent;\n  height: 32px;\n}\n.product-cart-touchspin .input-group-btn .btn {\n  background-color: transparent !important;\n  border-color: transparent !important;\n  color: #5664d2 !important;\n  font-size: 16px;\n  padding: 3px 12px;\n  box-shadow: none;\n}\n\n.shipping-address {\n  box-shadow: none;\n}\n.shipping-address.active {\n  border-color: #5664d2 !important;\n}\n\n/* ==============\n  Email\n===================*/\n.email-leftbar {\n  width: 236px;\n  float: left;\n  padding: 20px;\n  border-radius: 5px;\n}\n\n.email-rightbar {\n  margin-left: 260px;\n}\n\n.chat-user-box p.user-title {\n  color: var(--bs-body-color);\n  font-weight: 600;\n}\n.chat-user-box p {\n  font-size: 12px;\n}\n\n@media (max-width: 767px) {\n  .email-leftbar {\n    float: none;\n    width: 100%;\n  }\n  .email-rightbar {\n    margin: 0;\n  }\n}\n.mail-list a {\n  display: block;\n  color: #74788d;\n  line-height: 24px;\n  padding: 8px 5px;\n}\n.mail-list a.active {\n  color: #ff3d60;\n  font-weight: 500;\n}\n\n.message-list {\n  display: block;\n  padding-left: 0;\n}\n.message-list li {\n  position: relative;\n  display: block;\n  height: 50px;\n  line-height: 50px;\n  cursor: default;\n  transition-duration: 0.3s;\n}\n.message-list li a {\n  color: #74788d;\n}\n.message-list li:hover {\n  background: var(--bs-tertiary-bg);\n  transition-duration: 0.05s;\n}\n.message-list li .col-mail {\n  float: left;\n  position: relative;\n}\n.message-list li .col-mail-1 {\n  width: 320px;\n}\n.message-list li .col-mail-1 .star-toggle,\n.message-list li .col-mail-1 .checkbox-wrapper-mail,\n.message-list li .col-mail-1 .dot {\n  display: block;\n  float: left;\n}\n.message-list li .col-mail-1 .dot {\n  border: 4px solid transparent;\n  border-radius: 100px;\n  margin: 22px 26px 0;\n  height: 0;\n  width: 0;\n  line-height: 0;\n  font-size: 0;\n}\n.message-list li .col-mail-1 .checkbox-wrapper-mail {\n  margin: 15px 10px 0 20px;\n}\n.message-list li .col-mail-1 .star-toggle {\n  margin-top: 18px;\n  margin-left: 5px;\n}\n.message-list li .col-mail-1 .title {\n  position: absolute;\n  top: 0;\n  left: 110px;\n  right: 0;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  margin-bottom: 0;\n}\n.message-list li .col-mail-2 {\n  position: absolute;\n  top: 0;\n  left: 320px;\n  right: 0;\n  bottom: 0;\n}\n.message-list li .col-mail-2 .subject,\n.message-list li .col-mail-2 .date {\n  position: absolute;\n  top: 0;\n}\n.message-list li .col-mail-2 .subject {\n  left: 0;\n  right: 200px;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n.message-list li .col-mail-2 .date {\n  right: 0;\n  width: 170px;\n  padding-left: 80px;\n}\n.message-list li.active, .message-list li.active:hover {\n  box-shadow: inset 3px 0 0 #5664d2;\n}\n.message-list li.unread {\n  background-color: var(--bs-tertiary-bg);\n  font-weight: 500;\n  color: var(--bs-body-color);\n}\n.message-list li.unread a {\n  color: var(--bs-body-color);\n  font-weight: 500;\n}\n.message-list .checkbox-wrapper-mail {\n  cursor: pointer;\n  height: 20px;\n  width: 20px;\n  position: relative;\n  display: inline-block;\n  box-shadow: inset 0 0 0 1px #ced4da;\n  border-radius: 1px;\n}\n.message-list .checkbox-wrapper-mail input {\n  opacity: 0;\n  cursor: pointer;\n}\n.message-list .checkbox-wrapper-mail input:checked ~ label {\n  opacity: 1;\n}\n.message-list .checkbox-wrapper-mail label {\n  position: absolute;\n  height: 20px;\n  width: 20px;\n  left: 0;\n  cursor: pointer;\n  opacity: 0;\n  margin-bottom: 0;\n  transition-duration: 0.05s;\n  top: 0;\n}\n.message-list .checkbox-wrapper-mail label:before {\n  content: \"\\f012c\";\n  font-family: \"Material Design Icons\";\n  top: 0;\n  height: 20px;\n  color: #16181b;\n  width: 20px;\n  position: absolute;\n  margin-top: -16px;\n  left: 4px;\n  font-size: 13px;\n}\n\n@media (max-width: 575.98px) {\n  .message-list li .col-mail-1 {\n    width: 200px;\n  }\n}\n.chat-leftsidebar {\n  background-color: var(--bs-secondary-bg);\n  border-radius: 0.375rem 0 0 0.375rem;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n@media (min-width: 992px) {\n  .chat-leftsidebar {\n    min-width: 360px;\n  }\n}\n.chat-leftsidebar .chat-leftsidebar-nav .nav {\n  background-color: var(--bs-tertiary-bg);\n}\n.chat-leftsidebar .chat-leftsidebar-nav .nav .nav-link.active {\n  background-color: var(--bs-secondary-bg);\n  color: #5664d2;\n}\n\n.chat-noti-dropdown.active:before {\n  content: \"\";\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  background-color: #ff3d60;\n  border-radius: 50%;\n  right: 0;\n}\n.chat-noti-dropdown .btn {\n  padding: 0px;\n  box-shadow: none;\n  font-size: 16px;\n}\n\n.chat-search-box .form-control {\n  border: 0;\n}\n\n.chat-list {\n  margin: 0;\n}\n.chat-list li.active a {\n  background-color: var(--bs-tertiary-bg);\n}\n.chat-list li a {\n  display: block;\n  padding: 14px 16px;\n  color: #74788d;\n  transition: all 0.4s;\n  border-top: 1px solid var(--bs-border-color);\n  border-radius: 4px;\n}\n.chat-list li a:hover {\n  background-color: rgba(var(--bs-tertiary-bg), 0.7);\n}\n.chat-list li .user-img {\n  position: relative;\n}\n.chat-list li .user-img .user-status {\n  width: 10px;\n  height: 10px;\n  background-color: #adb5bd;\n  border-radius: 50%;\n  border: 2px solid var(--bs-border-color);\n  position: absolute;\n  right: 0;\n  bottom: 0;\n}\n.chat-list li .user-img.online .user-status {\n  background-color: #1cbb8c;\n}\n.chat-list li .user-img.away .user-status {\n  background-color: #fcb92c;\n}\n\n.user-chat {\n  background-color: var(--bs-secondary-bg);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n.user-chat .user-chat-border {\n  border-bottom: 1px solid var(--bs-border-color);\n}\n\n.user-chat-nav .dropdown .nav-btn {\n  height: 36px;\n  width: 36px;\n  line-height: 36px;\n  box-shadow: none;\n  padding: 0;\n  font-size: 20px;\n  border-radius: 50%;\n}\n.user-chat-nav .dropdown .dropdown-menu {\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  border: 1px solid var(--bs-border-color);\n}\n\n.chat-conversation li {\n  clear: both;\n}\n.chat-conversation .chat-avatar {\n  float: left;\n  margin-right: 8px;\n}\n.chat-conversation .chat-avatar img {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n}\n.chat-conversation .chat-day-title {\n  position: relative;\n  text-align: center;\n  margin-bottom: 24px;\n  margin-top: 12px;\n}\n.chat-conversation .chat-day-title .title {\n  background-color: var(--bs-tertiary-bg);\n  position: relative;\n  z-index: 1;\n  padding: 3px 16px;\n  border-radius: 30px;\n}\n.chat-conversation .chat-day-title:before {\n  content: \"\";\n  position: absolute;\n  width: 100%;\n  height: 1px;\n  left: 0;\n  right: 0;\n  background-color: var(--bs-border-color);\n  top: 10px;\n}\n.chat-conversation .chat-day-title .badge {\n  font-size: 12px;\n}\n.chat-conversation .conversation-list {\n  margin-bottom: 24px;\n  display: inline-block;\n  position: relative;\n}\n.chat-conversation .conversation-list .ctext-wrap {\n  overflow: hidden;\n}\n.chat-conversation .conversation-list .ctext-wrap .conversation-name {\n  font-weight: 600;\n  margin-bottom: 7px;\n}\n.chat-conversation .conversation-list .ctext-wrap-content {\n  padding: 12px 16px;\n  background-color: #5664d2;\n  border-radius: 0.375rem;\n  color: #fff;\n}\n.chat-conversation .conversation-list .chat-time {\n  margin-top: 7px;\n  font-size: 12px;\n  text-align: right;\n}\n.chat-conversation .right .conversation-list {\n  float: right;\n}\n.chat-conversation .right .conversation-list .conversation-name {\n  text-align: right;\n}\n.chat-conversation .right .conversation-list .ctext-wrap-content {\n  background-color: var(--bs-tertiary-bg);\n  text-align: right;\n  color: #74788d;\n}\n.chat-conversation .right .conversation-list .chat-time {\n  text-align: left;\n}\n\n.chat-input-section {\n  background-color: var(--bs-secondary-bg);\n  border-radius: 0.375rem;\n}\n\n.chat-input {\n  background-color: var(--bs-tertiary-bg) !important;\n  border-color: var(--bs-border-color) !important;\n}\n\n.chat-input-links {\n  position: absolute;\n  left: 16px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.chat-input-links li a {\n  font-size: 16px;\n  line-height: 36px;\n  padding: 0px 4px;\n  display: inline-block;\n}\n\n@media (max-width: 575.98px) {\n  .chat-send {\n    min-width: auto;\n  }\n}\n\n.task-box {\n  border: 1px solid var(--bs-border-color);\n  box-shadow: none;\n}\n.task-box .team .team-member {\n  margin-right: 6px;\n}\n\n.gu-transit {\n  border: 1px dashed var(--bs-border-color) !important;\n  background-color: #eff2f7 !important;\n}\n\n.counter-number {\n  font-size: 24px;\n  font-weight: 600;\n  font-family: \"Inter\", sans-serif;\n  text-align: center;\n  display: flex;\n}\n.counter-number span {\n  font-size: 14px;\n  font-weight: 400;\n  display: block;\n  padding-top: 5px;\n}\n\n.coming-box {\n  width: 25%;\n}\n\n.comingsoon-bg {\n  background-image: url(\"../images/comingsoon-bg.jpg\");\n}\n\n/************** vertical timeline **************/\n.timeline {\n  position: relative;\n  margin-bottom: 30px;\n}\n.timeline:before {\n  content: \"\";\n  position: absolute;\n  width: 3px;\n  top: 30px;\n  left: 0;\n  bottom: 0;\n  background-color: var(--bs-tertiary-bg);\n}\n.timeline .timeline-item {\n  display: flex;\n}\n.timeline .timeline-block {\n  width: 100%;\n}\n.timeline .time-show-btn {\n  margin-bottom: 30px;\n}\n.timeline .timeline-box {\n  margin: 20px 0;\n  position: relative;\n  margin-left: 45px;\n}\n.timeline .timeline-date {\n  display: inline-block;\n  padding: 4px 16px 4px 8px;\n  border-radius: 0px 30px 30px 0px;\n  background-color: #5664d2;\n  color: #fff;\n  position: relative;\n  left: -30px;\n}\n.timeline .timeline-date .circle-dot {\n  margin-right: 8px;\n}\n.timeline .timeline-icon {\n  position: absolute;\n  width: 10px;\n  height: 10px;\n  background: var(--bs-secondary-bg);\n  border-radius: 50%;\n  display: block;\n  border: 2px solid #5664d2;\n  left: -48px;\n  text-align: center;\n  top: 27px;\n  z-index: 9;\n}\n.timeline .timeline-icon:before {\n  content: \"\";\n  position: absolute;\n  height: 3px;\n  width: 20px;\n  background-color: var(--bs-tertiary-bg);\n  left: 12px;\n  top: 3px;\n}\n.timeline .timeline-album {\n  margin-top: 16px;\n}\n.timeline .timeline-album a {\n  display: inline-block;\n  margin-right: 5px;\n}\n.timeline .timeline-album img {\n  height: 40px;\n  width: auto;\n  border-radius: 4px;\n}\n\n@media (min-width: 768px) {\n  .timeline .timeline-block {\n    width: 50%;\n  }\n  .timeline:before {\n    left: 50%;\n  }\n  .timeline .time-show-btn {\n    position: relative;\n    left: 67px;\n    text-align: right;\n  }\n  .timeline-item::before {\n    content: \"\";\n    display: block;\n    width: 50%;\n  }\n  .timeline-item.timeline-left {\n    text-align: right;\n  }\n  .timeline-item.timeline-left::after {\n    content: \"\";\n    display: block;\n    width: 50%;\n  }\n  .timeline-item.timeline-left::before {\n    display: none;\n  }\n  .timeline-item.timeline-left .timeline-box {\n    margin-left: 0;\n    margin-right: 45px;\n  }\n  .timeline-item.timeline-left .timeline-date {\n    padding: 4px 8px 4px 16px;\n    border-radius: 30px 0px 0px 30px;\n    left: auto;\n    right: -30px;\n  }\n  .timeline-item.timeline-left .timeline-date .circle-dot {\n    float: right;\n    margin-right: 0px;\n    margin-left: 8px;\n  }\n  .timeline-item.timeline-left .timeline-icon {\n    left: auto;\n    right: -52px;\n  }\n  .timeline-item.timeline-left .timeline-icon::before {\n    left: auto;\n    right: 12px;\n  }\n}\n.left-timeline::before {\n  left: 3px;\n}\n.left-timeline .timeline-item::before {\n  display: none;\n}\n.left-timeline .timeline-block {\n  width: 100%;\n}\n.left-timeline .timeline-icon {\n  left: -45px;\n}\n\n.pricing-nav-tabs {\n  display: inline-block;\n  background-color: var(--bs-secondary-bg);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  padding: 4px;\n  border-radius: 7px;\n}\n.pricing-nav-tabs li {\n  display: inline-block;\n}\n\n.pricing-box .plan-features li {\n  padding: 7px 0px;\n}\n\n/*********************\n    Faqs\n**********************/\n.faq-nav-tabs .nav-item {\n  margin: 0px 8px;\n}\n.faq-nav-tabs .nav-link {\n  text-align: center;\n  margin-bottom: 8px;\n  border: 2px solid var(--bs-border-color);\n  color: var(--bs-body-color);\n}\n.faq-nav-tabs .nav-link .nav-icon {\n  font-size: 40px;\n  margin-bottom: 8px;\n  display: block;\n}\n.faq-nav-tabs .nav-link.active {\n  border-color: #5664d2;\n  background-color: transparent;\n  color: var(--bs-body-color);\n}\n.faq-nav-tabs .nav-link.active .nav-icon {\n  color: #5664d2;\n}\n\n.text-error {\n  font-size: 120px;\n}\n@media (max-width: 575.98px) {\n  .text-error {\n    font-size: 86px;\n  }\n}\n\n.error-text {\n  color: #ff3d60;\n  position: relative;\n}\n.error-text .error-img {\n  position: absolute;\n  width: 120px;\n  left: -15px;\n  right: 0;\n  bottom: 47px;\n}\n@media (max-width: 575.98px) {\n  .error-text .error-img {\n    width: 86px;\n    left: -12px;\n    bottom: 38px;\n  }\n}", "// \r\n// _header.scss\r\n// \r\n\r\n#page-topbar {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    left: 0;\r\n    z-index: 1002;\r\n    background-color: var(--#{$prefix}header-bg);\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.navbar-header {\r\n    display: flex;\r\n    -ms-flex-pack: justify;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    height: $header-height;\r\n    padding: 0 calc(#{$grid-gutter-width} / 2) 0 0;\r\n\r\n    .dropdown {\r\n        .show.header-item {\r\n            background-color: var(--#{$prefix}secondary-bg);\r\n        }\r\n    }\r\n}\r\n\r\n.navbar-brand-box {\r\n    padding: 0 1.5rem;\r\n    width: $navbar-brand-box-width;\r\n}\r\n\r\n.logo {\r\n    line-height: 70px;\r\n\r\n    .logo-sm {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.logo-light {\r\n    display: none;\r\n}\r\n\r\n/* Search */\r\n\r\n.app-search {\r\n    padding: calc(#{$header-height - 38px} / 2) 0;\r\n\r\n    .form-control {\r\n        border: none;\r\n        height: 38px;\r\n        padding-left: 40px;\r\n        padding-right: 20px;\r\n        background-color: $topbar-search-bg;\r\n        box-shadow: none;\r\n        border-radius: 30px;\r\n    }\r\n    span {\r\n        position: absolute;\r\n        z-index: 10;\r\n        font-size: 16px;\r\n        line-height: 38px;\r\n        left: 13px;\r\n        top: 0;\r\n        color: $gray-600;\r\n    }\r\n}\r\n\r\n// Mega menu\r\n\r\n.megamenu-list {\r\n    li{\r\n        position: relative;\r\n        padding: 5px 0px;\r\n        a{\r\n            color: $dropdown-color;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n\r\n    .logo {\r\n\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.page-content {\r\n    padding: calc(#{$header-height} + #{$grid-gutter-width}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n}\r\n\r\n.header-item {\r\n    height: $header-height;\r\n    box-shadow: none !important;\r\n    color: var(--#{$prefix}header-item-color);\r\n    border: 0;\r\n    border-radius: 0px;\r\n\r\n    &:hover {\r\n        color: var(--#{$prefix}header-item-color);\r\n    }\r\n\r\n\r\n}\r\n\r\n.header-profile-user {\r\n    height: 36px;\r\n    width: 36px;\r\n    background-color: var(--#{$prefix}secondary-bg);\r\n    padding: 3px;\r\n}\r\n\r\n.user-dropdown{\r\n    .dropdown-item{\r\n        i{\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.noti-icon {\r\n    i {\r\n        font-size: 22px;\r\n        color: var(--#{$prefix}header-item-color);\r\n    }\r\n\r\n    .noti-dot{\r\n        position: absolute;\r\n        display: inline-block;\r\n        height: 6px;\r\n        width: 6px;    \r\n        background-color: $danger;\r\n        border-radius: 50%;\r\n        top: 20px;\r\n        right: 14px;   \r\n    }\r\n}\r\n\r\n.notification-item {\r\n    .d-flex {\r\n        padding: 0.75rem 1rem;\r\n\r\n        &:hover {\r\n            background-color: var(--#{$prefix}tertiary-bg);\r\n        }\r\n    }\r\n}\r\n\r\n// Dropdown with Icons\r\n.dropdown-icon-item {\r\n    display: block;\r\n    border-radius: 3px;\r\n    line-height: 34px;\r\n    text-align: center;\r\n    padding: 15px 0 9px;\r\n    display: block;\r\n    border: 1px solid transparent;\r\n    color: $gray-600;\r\n\r\n    img {\r\n        height: 24px;\r\n    }\r\n\r\n    span {\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n    }\r\n\r\n    &:hover {\r\n        border-color: var(--#{$prefix}border-color);\r\n    }\r\n}\r\n\r\n// Full Screen\r\n.fullscreen-enable {\r\n    [data-toggle=\"fullscreen\"] {\r\n        .ri-fullscreen-line:before {\r\n            content: \"\\ed73\";\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-topbar=\"dark\"] {\r\n    #page-topbar { \r\n        background-color: var(--#{$prefix}header-dark-bg);\r\n    }\r\n    .navbar-header {\r\n        .dropdown {\r\n            .show.header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color: var(--#{$prefix}header-dark-item-color);\r\n    \r\n        &:hover {\r\n            color: var(--#{$prefix}header-dark-item-color);\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color: var(--#{$prefix}header-dark-item-color);\r\n        }\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: var(--#{$prefix}topbar-search-bg);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}\r\n\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n    .navbar-brand-box {\r\n        background: var(--#{$prefix}sidebar-dark-bg);\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@media (max-width: 600px) {\r\n    .navbar-header {\r\n        .dropdown {\r\n            position: static;\r\n\r\n            .dropdown-menu {\r\n                left: 10px !important;\r\n                right: 10px !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 380px) {\r\n    .navbar-brand-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n    .page-content {\r\n        margin-top: $header-height;\r\n        padding: calc(55px + #{$grid-gutter-width}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n    }    \r\n}\r\n\r\n@media (max-width: 992px) { \r\n    body[data-layout=\"horizontal\"] {\r\n        .page-content {\r\n            margin-top: 15px;\r\n        }    \r\n    }\r\n}\r\n\r\n\r\n\r\nbody[data-topbar=\"colored\"] {\r\n    #page-topbar { \r\n        background-color:var(--#{$prefix}header-colored-bg);\r\n    }\r\n    .navbar-header {\r\n        .dropdown {\r\n            .show.header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color: var(--#{$prefix}header-dark-item-color);\r\n    \r\n        &:hover {\r\n            color: var(--#{$prefix}header-dark-item-color);\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color: var(--#{$prefix}header-dark-item-color);\r\n        }\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: var(--#{$prefix}topbar-search-bg);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}", "// Variables\n\n//\n// custom-variables\n//\n\n// Vertical Sidebar - Default Light\n$sidebar-bg: var(--#{$prefix}sidebar-bg);\n$sidebar-menu-item-color: var(--#{$prefix}sidebar-menu-item-color);\n$sidebar-menu-sub-item-color: var(--#{$prefix}sidebar-menu-sub-item-color);\n$sidebar-menu-item-icon-color: var(--#{$prefix}sidebar-menu-item-icon-color);\n$sidebar-menu-item-hover-color: var(--#{$prefix}sidebar-menu-item-hover-color);\n$sidebar-menu-item-active-color: var(--#{$prefix}sidebar-menu-item-active-color);\n$sidebar-width: 240px;\n$sidebar-collapsed-width: 70px;\n$sidebar-width-sm: 160px;\n\n// Vertical Sidebar - Dark\n$sidebar-dark-bg: var(--#{$prefix}sidebar-dark-bg); //2c313a\n$sidebar-dark-menu-item-color: var(--#{$prefix}sidebar-dark-menu-item-color);\n$sidebar-dark-menu-sub-item-color: var(--#{$prefix}sidebar-dark-menu-sub-item-color);\n$sidebar-dark-menu-item-icon-color: var(--#{$prefix}sidebar-dark-menu-item-icon-color);\n$sidebar-dark-menu-item-hover-color: var(--#{$prefix}sidebar-dark-menu-item-hover-color);\n$sidebar-dark-menu-item-active-color: var(--#{$prefix}sidebar-dark-menu-item-active-color);\n\n// Topbar - Deafult Light\n$header-height: 70px;\n$header-bg: var(--#{$prefix}header-bg);\n$header-item-color: var(--#{$prefix}header-item-color);\n\n// Topbar - Dark\n$header-dark-bg: var(--#{$prefix}header-dark-bg);\n$header-dark-item-color: #e9ecef;\n\n// Topbar - Colored\n$header-colored-bg: var(--#{$prefix}header-colored-bg);\n\n// Topbar Search\n$topbar-search-bg: var(--#{$prefix}topbar-search-bg);\n\n// Footer\n$footer-height: 60px;\n$footer-bg: var(--#{$prefix}footer-bg);\n$footer-color: #74788d;\n\n// Horizontal nav\n$topnav-bg: var(--#{$prefix}topnav-bg);\n\n$menu-item-color: var(--#{$prefix}menu-item-color);\n$menu-item-active-color: var(--#{$prefix}menu-item-active-color);\n\n// Right Sidebar\n$rightbar-width: 280px;\n\n// Display\n$display-none: none;\n$display-block: block;\n\n// Brand \n$navbar-brand-box-width: 240px;\n\n// Boxed layout width\n$boxed-layout-width: 1300px;\n$boxed-body-bg: var(--#{$prefix}boxed-body-bg);\n\n// Font Weight\n$font-weight-medium: 500;\n$font-weight-semibold: 600;\n\n// apex charts\n$apex-grid-color: $border-color;\n\n// table\n$table-head-bg: var(--#{$prefix}tertiary-bg);\n$table-dark-border-color: tint-color($gray-800, 7.5%);\n\n// Custom Font\n$font-family-secondary: 'Inter', sans-serif;\n\n\n\n// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white: #fff;\n$gray-100: #f8f9fa;\n$gray-200: #eff2f7;\n$gray-300: #f1f5f7;\n$gray-400: #ced4da;\n$gray-500: #adb5bd;\n$gray-600: #74788d;\n$gray-700: #2d3448;\n$gray-800: #252b3b;\n$gray-900: #212529;\n$black: #000;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900);\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue:    #5664d2;\n$indigo:  #564ab1;\n$purple:  #5664d2;\n$pink:    #e83e8c;\n$red:     #ff3d60;\n$orange:  #f1734f;\n$yellow:  #fcb92c;\n$green:   #1cbb8c;\n$teal:    #050505;\n$cyan:    #4aa3ff;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\": $blue,\n  \"indigo\": $indigo,\n  \"purple\": $purple,\n  \"pink\": $pink,\n  \"red\": $red,\n  \"orange\": $orange,\n  \"yellow\": $yellow,\n  \"green\": $green,\n  \"teal\": $teal,\n  \"cyan\": $cyan,\n  \"black\": $black,\n  \"white\": $white,\n  \"gray\": $gray-600,\n  \"gray-dark\": $gray-800\n);\n// scss-docs-end colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio: 1.7;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark: $black;\n$color-contrast-light: $white;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%);\n$blue-200: tint-color($blue, 60%);\n$blue-300: tint-color($blue, 40%);\n$blue-400: tint-color($blue, 20%);\n$blue-500: $blue;\n$blue-600: shade-color($blue, 20%);\n$blue-700: shade-color($blue, 40%);\n$blue-800: shade-color($blue, 60%);\n$blue-900: shade-color($blue, 80%);\n\n$indigo-100: tint-color($indigo, 80%);\n$indigo-200: tint-color($indigo, 60%);\n$indigo-300: tint-color($indigo, 40%);\n$indigo-400: tint-color($indigo, 20%);\n$indigo-500: $indigo;\n$indigo-600: shade-color($indigo, 20%);\n$indigo-700: shade-color($indigo, 40%);\n$indigo-800: shade-color($indigo, 60%);\n$indigo-900: shade-color($indigo, 80%);\n\n$purple-100: tint-color($purple, 80%);\n$purple-200: tint-color($purple, 60%);\n$purple-300: tint-color($purple, 40%);\n$purple-400: tint-color($purple, 20%);\n$purple-500: $purple;\n$purple-600: shade-color($purple, 20%);\n$purple-700: shade-color($purple, 40%);\n$purple-800: shade-color($purple, 60%);\n$purple-900: shade-color($purple, 80%);\n\n$pink-100: tint-color($pink, 80%);\n$pink-200: tint-color($pink, 60%);\n$pink-300: tint-color($pink, 40%);\n$pink-400: tint-color($pink, 20%);\n$pink-500: $pink;\n$pink-600: shade-color($pink, 20%);\n$pink-700: shade-color($pink, 40%);\n$pink-800: shade-color($pink, 60%);\n$pink-900: shade-color($pink, 80%);\n\n$red-100: tint-color($red, 80%);\n$red-200: tint-color($red, 60%);\n$red-300: tint-color($red, 40%);\n$red-400: tint-color($red, 20%);\n$red-500: $red;\n$red-600: shade-color($red, 20%);\n$red-700: shade-color($red, 40%);\n$red-800: shade-color($red, 60%);\n$red-900: shade-color($red, 80%);\n\n$orange-100: tint-color($orange, 80%);\n$orange-200: tint-color($orange, 60%);\n$orange-300: tint-color($orange, 40%);\n$orange-400: tint-color($orange, 20%);\n$orange-500: $orange;\n$orange-600: shade-color($orange, 20%);\n$orange-700: shade-color($orange, 40%);\n$orange-800: shade-color($orange, 60%);\n$orange-900: shade-color($orange, 80%);\n\n$yellow-100: tint-color($yellow, 80%);\n$yellow-200: tint-color($yellow, 60%);\n$yellow-300: tint-color($yellow, 40%);\n$yellow-400: tint-color($yellow, 20%);\n$yellow-500: $yellow;\n$yellow-600: shade-color($yellow, 20%);\n$yellow-700: shade-color($yellow, 40%);\n$yellow-800: shade-color($yellow, 60%);\n$yellow-900: shade-color($yellow, 80%);\n\n$green-100: tint-color($green, 80%);\n$green-200: tint-color($green, 60%);\n$green-300: tint-color($green, 40%);\n$green-400: tint-color($green, 20%);\n$green-500: $green;\n$green-600: shade-color($green, 20%);\n$green-700: shade-color($green, 40%);\n$green-800: shade-color($green, 60%);\n$green-900: shade-color($green, 80%);\n\n$teal-100: tint-color($teal, 80%);\n$teal-200: tint-color($teal, 60%);\n$teal-300: tint-color($teal, 40%);\n$teal-400: tint-color($teal, 20%);\n$teal-500: $teal;\n$teal-600: shade-color($teal, 20%);\n$teal-700: shade-color($teal, 40%);\n$teal-800: shade-color($teal, 60%);\n$teal-900: shade-color($teal, 80%);\n\n$cyan-100: tint-color($cyan, 80%);\n$cyan-200: tint-color($cyan, 60%);\n$cyan-300: tint-color($cyan, 40%);\n$cyan-400: tint-color($cyan, 20%);\n$cyan-500: $cyan;\n$cyan-600: shade-color($cyan, 20%);\n$cyan-700: shade-color($cyan, 40%);\n$cyan-800: shade-color($cyan, 60%);\n$cyan-900: shade-color($cyan, 80%);\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n);\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n);\n\n$purples: (\n  \"purple-100\": $purple-100,\n  \"purple-200\": $purple-200,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n);\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n);\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n);\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n);\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n);\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n);\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n);\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n);\n// fusv-enable\n\n// scss-docs-start theme-color-variables\n$primary: $blue;\n$secondary: $gray-600;\n$success: $green;\n$info: $cyan;\n$warning: $yellow;\n$danger: $red;\n$light: $gray-100;\n$dark: $gray-900;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\": $primary,\n  \"secondary\": $secondary,\n  \"success\": $success,\n  \"info\": $info,\n  \"warning\": $warning,\n  \"danger\": $danger,\n  \"light\": $light,\n  \"dark\": $dark\n);\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-text-variables\n$primary-text-emphasis: shade-color($primary, 60%);\n$secondary-text-emphasis: shade-color($secondary, 60%);\n$success-text-emphasis: shade-color($success, 60%);\n$info-text-emphasis: shade-color($info, 60%);\n$warning-text-emphasis: shade-color($warning, 60%);\n$danger-text-emphasis: shade-color($danger, 60%);\n$light-text-emphasis: $gray-700;\n$dark-text-emphasis: $gray-700;\n// scss-docs-end theme-text-variables\n\n// scss-docs-start theme-bg-subtle-variables\n$primary-bg-subtle: tint-color($primary, 80%);\n$secondary-bg-subtle: tint-color($secondary, 80%);\n$success-bg-subtle: tint-color($success, 80%);\n$info-bg-subtle: tint-color($info, 80%);\n$warning-bg-subtle: tint-color($warning, 80%);\n$danger-bg-subtle: tint-color($danger, 80%);\n$light-bg-subtle: mix($gray-100, $white);\n$dark-bg-subtle: $gray-400;\n// scss-docs-end theme-bg-subtle-variables\n\n// scss-docs-start theme-border-subtle-variables\n$primary-border-subtle: tint-color($primary, 60%);\n$secondary-border-subtle: tint-color($secondary, 60%);\n$success-border-subtle: tint-color($success, 60%);\n$info-border-subtle: tint-color($info, 60%);\n$warning-border-subtle: tint-color($warning, 60%);\n$danger-border-subtle: tint-color($danger, 60%);\n$light-border-subtle: $gray-200;\n$dark-border-subtle: $gray-500;\n// scss-docs-end theme-border-subtle-variables\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n  );\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret: false;\n$enable-rounded: true;\n$enable-shadows: false;\n$enable-gradients: false;\n$enable-transitions: true;\n$enable-reduced-motion: true;\n$enable-smooth-scroll: true;\n$enable-grid-classes: true;\n$enable-container-classes: true;\n$enable-cssgrid: false;\n$enable-button-pointers: true;\n$enable-rfs: true;\n$enable-validation-icons: true;\n$enable-negative-margins: true;\n$enable-deprecation-messages: true;\n$enable-important-utilities: true;\n\n$enable-dark-mode: true;\n$color-mode-type: data; // `data` or `media-query`\n\n// Prefix for :root CSS variables\n\n$variable-prefix: bs-; // Deprecated in v5.2.0 for the shorter `$prefix`\n$prefix: $variable-prefix;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0));\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem;\n$spacers: (\n  0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n  );\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n);\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-text-align:            null;\n$body-color:                $gray-900;\n$body-bg:                   #f1f5f7;\n\n$body-secondary-color:      rgba($body-color, .75);\n$body-secondary-bg:         $white;\n\n$body-tertiary-color:       rgba($body-color, .5);\n$body-tertiary-bg:          $gray-100;\n\n$body-emphasis-color:       $black;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                        $primary;\n$link-decoration:                   none;\n$link-shade-percentage:             20%;\n$link-hover-color:                  shift-color($link-color, $link-shade-percentage);\n$link-hover-decoration:             underline;\n\n$stretched-link-pseudo-element:     after;\n$stretched-link-z-index:            1;\n\n// Icon links\n// scss-docs-start icon-link-variables\n$icon-link-gap:                     .375rem;\n$icon-link-underline-offset:        .25em;\n$icon-link-icon-size:               1em;\n$icon-link-icon-transition:         .2s ease-in-out transform;\n$icon-link-icon-transform:          translate3d(.25em, 0, 0);\n// scss-docs-end icon-link-variables\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom: 1rem;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n);\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\"\n);\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n);\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12;\n$grid-gutter-width:           1.5rem;\n$grid-row-columns:            6;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width * 0.5;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:        1px;\n$border-widths: (\n  0: 0,\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n);\n$border-style: solid;\n$border-color: $gray-300;\n$border-color-translucent: rgba($black, .175);\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius: .375rem;\n$border-radius-sm: .25rem;\n$border-radius-lg: .5rem;\n$border-radius-xl: 1rem;\n$border-radius-xxl: 2rem;\n$border-radius-pill: 50rem;\n// scss-docs-end border-radius-variables\n// fusv-disable\n$border-radius-2xl: $border-radius-xxl; // Deprecated in v5.3.0\n// fusv-enable\n\n// scss-docs-start box-shadow-variables\n$box-shadow-sm:               0 1px 1px rgba($black,.05);\n$box-shadow:                  0 2px 4px rgba($black,.08);\n$box-shadow-lg:               0 5px 12px rgba($black, 0.1);\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075);\n// scss-docs-end box-shadow-variables\n\n$component-active-color: $white;\n$component-active-bg: $primary;\n\n// scss-docs-start focus-ring-variables\n$focus-ring-width: .25rem;\n$focus-ring-opacity: .25;\n$focus-ring-color: rgba($primary, $focus-ring-opacity);\n$focus-ring-blur: 0;\n$focus-ring-box-shadow: 0 0 $focus-ring-blur $focus-ring-width $focus-ring-color;\n// scss-docs-end focus-ring-variables\n\n// scss-docs-start caret-variables\n$caret-width:                   .3em;\n$caret-vertical-align:          $caret-width * .85;\n$caret-spacing:                 $caret-width * .85;\n// scss-docs-end caret-variables\n\n$transition-base:               all .2s ease-in-out;\n$transition-fade:               opacity .15s linear;\n// scss-docs-start collapse-transition\n$transition-collapse:           height .35s ease;\n$transition-collapse-width:     width .35s ease;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%));\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      'Nunito', sans-serif;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n// stylelint-enable value-keyword-case\n$font-family-base: var(--#{$prefix}font-sans-serif);\n$font-family-code: var(--#{$prefix}font-monospace);\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\n// $font-size-base affects the font size of the body text\n$font-size-root:                null;\n$font-size-base:                0.9rem; // Assumes the browser default, typically `16px`\n$font-size-sm:                  $font-size-base * .875;\n$font-size-lg:                  $font-size-base * 1.25;\n\n$font-weight-lighter: lighter;\n$font-weight-light: 300;\n$font-weight-normal: 400;\n$font-weight-medium: 500;\n$font-weight-semibold: 600;\n$font-weight-bold: 600;\n$font-weight-bolder: bolder;\n\n$font-weight-base: $font-weight-normal;\n\n$line-height-base: 1.5;\n$line-height-sm: 1.25;\n$line-height-lg: 2;\n\n$h1-font-size: $font-size-base * 2.5;\n$h2-font-size: $font-size-base * 2;\n$h3-font-size: $font-size-base * 1.75;\n$h4-font-size: $font-size-base * 1.5;\n$h5-font-size: $font-size-base * 1.25;\n$h6-font-size: $font-size-base;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n);\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer * 0.5;\n$headings-font-family:        null;\n$headings-font-style:         null;\n$headings-font-weight:        500;\n$headings-line-height:        1.2;\n$headings-color:              $gray-800;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n);\n\n$display-font-family: null;\n$display-font-style: null;\n$display-font-weight: 300;\n$display-line-height: $headings-line-height;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size: $font-size-base * 1.25;\n$lead-font-weight: 300;\n\n$small-font-size:  80%;\n\n$sub-sup-font-size: .75em;\n\n// fusv-disable\n$text-muted: var(--#{$prefix}secondary-color); // Deprecated in 5.3.0\n// fusv-enable\n\n$initialism-font-size: $small-font-size;\n\n$blockquote-margin-y: $spacer;\n$blockquote-font-size: $font-size-base * 1.25;\n$blockquote-footer-color: $gray-600;\n$blockquote-footer-font-size: $small-font-size;\n\n$hr-margin-y: $spacer;\n$hr-color: rgba($black, .1);\n\n// fusv-disable\n$hr-bg-color: null; // Deprecated in v5.2.0\n$hr-height: null; // Deprecated in v5.2.0\n// fusv-enable\n\n$hr-border-color: null; // Allows for inherited colors\n$hr-border-width: var(--#{$prefix}border-width);\n$hr-opacity: .7;\n\n// scss-docs-start vr-variables\n$vr-border-width:             var(--#{$prefix}border-width);\n// scss-docs-end vr-variables\n\n$legend-margin-bottom:        .5rem !default;\n$legend-font-size:            1.5rem !default;\n$legend-font-weight:          null !default;\n\n$dt-font-weight:            $font-weight-bold;\n\n$list-inline-padding:       .5rem;\n\n$mark-padding:              .2em;\n$mark-bg:                   #fcf8e3;\n$mark-color:                $body-color;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .75rem;\n$table-cell-padding-x:        .75rem;\n$table-cell-padding-y-sm:     .3rem;\n$table-cell-padding-x-sm:     .3rem;\n\n$table-cell-vertical-align: top;\n\n$table-color:                 var(--#{$prefix}body-color);\n$table-bg:                    var(--#{$prefix}table-bg);\n$table-accent-bg:             transparent;\n\n$table-th-font-weight:        null;\n\n$table-striped-color:         $table-color;\n$table-striped-bg-factor:     .05;\n$table-striped-bg:            rgba($black, $table-striped-bg-factor);\n\n$table-active-color:          $table-color;\n$table-active-bg-factor:      .1;\n$table-active-bg:             rgba($black, $table-active-bg-factor);\n\n$table-hover-color:           $table-color;\n$table-hover-bg-factor:       .075;\n$table-hover-bg:              rgba($black, $table-hover-bg-factor);\n\n$table-border-factor:         .1;\n$table-border-width:          var(--#{$prefix}border-width);\n$table-border-color:          var(--#{$prefix}border-color);\n\n$table-striped-order:         odd;\n$table-striped-columns-order: even;\n\n$table-group-separator-color: $border-color;\n\n$table-caption-color:         var(--#{$prefix}secondary-color);\n\n$table-bg-scale:              -80%;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\": shift-color($primary, $table-bg-scale),\n  \"secondary\": shift-color($secondary, $table-bg-scale),\n  \"success\": shift-color($success, $table-bg-scale),\n  \"info\": shift-color($info, $table-bg-scale),\n  \"warning\": shift-color($warning, $table-bg-scale),\n  \"danger\": shift-color($danger, $table-bg-scale),\n  \"light\": $gray-100,\n  \"dark\": $dark,\n  );\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y: .47rem;\n$input-btn-padding-x: .75rem;\n$input-btn-font-family: null;\n$input-btn-font-size: $font-size-base;\n$input-btn-line-height: $line-height-base;\n\n$input-btn-focus-width: $focus-ring-width;\n$input-btn-focus-color-opacity: $focus-ring-opacity;\n$input-btn-focus-color: $focus-ring-color;\n$input-btn-focus-blur: $focus-ring-blur;\n$input-btn-focus-box-shadow: $focus-ring-box-shadow;\n\n$input-btn-padding-y-sm: .25rem;\n$input-btn-padding-x-sm: .5rem;\n$input-btn-font-size-sm: $font-size-sm;\n\n$input-btn-padding-y-lg: .5rem;\n$input-btn-padding-x-lg: 1rem;\n$input-btn-font-size-lg: $font-size-lg;\n\n$input-btn-border-width: var(--#{$prefix}border-width);\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-color:  $white;\n$btn-padding-y: $input-btn-padding-y;\n$btn-padding-x: $input-btn-padding-x;\n$btn-font-family: $input-btn-font-family;\n$btn-font-size: $input-btn-font-size;\n$btn-line-height: $input-btn-line-height;\n$btn-white-space: null; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm: $input-btn-padding-y-sm;\n$btn-padding-x-sm: $input-btn-padding-x-sm;\n$btn-font-size-sm: $input-btn-font-size-sm;\n\n$btn-padding-y-lg: $input-btn-padding-y-lg;\n$btn-padding-x-lg: $input-btn-padding-x-lg;\n$btn-font-size-lg: $input-btn-font-size-lg;\n\n$btn-border-width: $input-btn-border-width;\n\n$btn-font-weight:                 $font-weight-normal;\n$btn-box-shadow:                  inset 0 1px 0 rgba($white, .15),0 1px 1px rgba($black, .075);\n$btn-focus-width:                 $input-btn-focus-width;\n$btn-focus-box-shadow:            $input-btn-focus-box-shadow;\n$btn-disabled-opacity:            .65;\n$btn-active-box-shadow:           inset 0 3px 5px rgba($black, .125);\n\n$btn-link-color:                  var(--#{$prefix}link-color);\n$btn-link-hover-color:            var(--#{$prefix}link-hover-color);\n$btn-link-disabled-color:         $gray-600;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:               var(--#{$prefix}border-radius);\n$btn-border-radius-sm:            var(--#{$prefix}border-radius-sm);\n$btn-border-radius-lg:            var(--#{$prefix}border-radius-lg);\n\n$btn-transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out,box-shadow .15s ease-in-out;\n\n$btn-hover-bg-shade-amount:       15%;\n$btn-hover-bg-tint-amount:        15%;\n$btn-hover-border-shade-amount:   20%;\n$btn-hover-border-tint-amount:    10%;\n$btn-active-bg-shade-amount:      20%;\n$btn-active-bg-tint-amount:       20%;\n$btn-active-border-shade-amount:  25%;\n$btn-active-border-tint-amount:   10%;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:            .25rem;\n$form-text-font-size:             $small-font-size;\n$form-text-font-style:            null;\n$form-text-font-weight:           null;\n$form-text-color:                 var(--#{$prefix}secondary-color);\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:          .5rem;\n$form-label-font-size:              null;\n$form-label-font-style:             null;\n$form-label-font-weight:            null;\n$form-label-color:                  null;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                   $input-btn-padding-y;\n$input-padding-x:                   $input-btn-padding-x;\n$input-font-family:                 $input-btn-font-family;\n$input-font-size:                   $input-btn-font-size;\n$input-font-weight:                 $font-weight-base;\n$input-line-height:                 $input-btn-line-height;\n\n$input-padding-y-sm:                $input-btn-padding-y-sm;\n$input-padding-x-sm:                $input-btn-padding-x-sm;\n$input-font-size-sm:                $input-btn-font-size-sm;\n\n$input-padding-y-lg:                $input-btn-padding-y-lg;\n$input-padding-x-lg:                $input-btn-padding-x-lg;\n$input-font-size-lg:                $input-btn-font-size-lg;\n\n$input-bg:                          var(--#{$prefix}secondary-bg);\n$input-disabled-color:              null;\n$input-disabled-bg:                 var(--#{$prefix}secondary-bg);\n$input-disabled-border-color:       null;\n\n$input-color:                       var(--#{$prefix}body-color);\n$input-border-color:                var(--#{$prefix}border-color-translucent);\n$input-border-width:                $input-btn-border-width;\n$input-box-shadow:                  $box-shadow-inset;\n\n$input-border-radius:               var(--#{$prefix}border-radius);\n$input-border-radius-sm:            var(--#{$prefix}border-radius-sm);\n$input-border-radius-lg:            var(--#{$prefix}border-radius-lg);\n\n$input-focus-bg:                    $input-bg;\n$input-focus-border-color:          tint-color($component-active-bg, 50%);\n$input-focus-color:                 $input-color;\n$input-focus-width:                 $input-btn-focus-width;\n$input-focus-box-shadow:            $input-btn-focus-box-shadow;\n\n$input-placeholder-color:           var(--#{$prefix}secondary-color);\n$input-plaintext-color:             var(--#{$prefix}body-color);\n\n$input-height-border:               calc(#{$input-border-width} * 2); // stylelint-disable-line function-disallowed-list\n\n$input-height-inner:                add($input-line-height * 1em, $input-padding-y * 2);\n$input-height-inner-half:           add($input-line-height * .5em, $input-padding-y);\n$input-height-inner-quarter:        add($input-line-height * .25em, $input-padding-y * .5);\n\n$input-height:                      add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false));\n$input-height-sm:                   add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false));\n$input-height-lg:                   add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false));\n\n$input-transition:                  border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$form-color-width:                  3rem;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:            1em;\n$form-check-min-height:             $font-size-base * $line-height-base;\n$form-check-padding-start:          $form-check-input-width + .5em;\n$form-check-margin-bottom:          .125rem;\n$form-check-label-color:            null;\n$form-check-label-cursor:           null;\n$form-check-transition:             background-color .15s ease-in-out,background-position .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;\n\n$form-check-input-active-filter:    brightness(90%);\n\n$form-check-input-bg:               $input-bg;\n$form-check-input-border:           var(--#{$prefix}border-width) solid var(--#{$prefix}border-color-translucent);\n$form-check-input-border-radius:    .25em;\n$form-check-radio-border-radius:    50%;\n$form-check-input-focus-border:     $input-focus-border-color;\n$form-check-input-focus-box-shadow: $focus-ring-box-shadow;\n\n$form-check-input-checked-color:                              $component-active-color;\n$form-check-input-checked-bg-color:                           $component-active-bg;\n$form-check-input-checked-border-color:                       $form-check-input-checked-bg-color;\n$form-check-input-checked-bg-image:                           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>\");\n$form-check-radio-checked-bg-image:                           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\");\n\n$form-check-input-indeterminate-color:                        $component-active-color;\n$form-check-input-indeterminate-bg-color:                     $component-active-bg;\n$form-check-input-indeterminate-border-color:                 $form-check-input-indeterminate-bg-color;\n$form-check-input-indeterminate-bg-image:                     url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\");\n\n$form-check-input-disabled-opacity:                           .5;\n$form-check-label-disabled-opacity:                           $form-check-input-disabled-opacity;\n$form-check-btn-check-disabled-opacity:                       $btn-disabled-opacity;\n\n$form-check-inline-margin-end:                                1rem;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color:                                           rgba($black, .25);\n$form-switch-width:                                           2em;\n$form-switch-padding-start:                                   $form-switch-width + .5em;\n$form-switch-bg-image:                                        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\");\n$form-switch-border-radius:                                   $form-switch-width;\n$form-switch-transition:                                      background-position .15s ease-in-out;\n\n$form-switch-focus-color:                                     $input-focus-border-color;\n$form-switch-focus-bg-image:                                  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\");\n\n$form-switch-checked-color:                                   $component-active-color;\n$form-switch-checked-bg-image:                                url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\");\n$form-switch-checked-bg-position:                             right center;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:                                 $input-padding-y;\n$input-group-addon-padding-x:                                 $input-padding-x;\n$input-group-addon-font-weight:                               $input-font-weight;\n$input-group-addon-color:                                     $input-color;\n$input-group-addon-bg:                                        var(--#{$prefix}tertiary-bg);\n$input-group-addon-border-color:                              $input-border-color;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:                                      $input-padding-y;\n$form-select-padding-x:                                      $input-padding-x;\n$form-select-font-family:                                    $input-font-family;\n$form-select-font-size:                                      $input-font-size;\n$form-select-indicator-padding:                              $form-select-padding-x * 3; // Extra padding for background-image\n$form-select-font-weight:                                    $input-font-weight;\n$form-select-line-height:                                    $input-line-height;\n$form-select-color:                                          $input-color;\n$form-select-bg:                                             $input-bg;\n$form-select-disabled-color:                                 null;\n$form-select-disabled-bg:                                    $input-disabled-bg;\n$form-select-disabled-border-color:                          $input-disabled-border-color;\n$form-select-bg-position:                                    right $form-select-padding-x center;\n$form-select-bg-size:                                        16px 12px; // In pixels because image dimensions\n$form-select-indicator-color:                                $gray-800;\n$form-select-indicator:                                      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>\");\n\n$form-select-feedback-icon-padding-end:                       $form-select-padding-x * 2.5 + $form-select-indicator-padding;\n$form-select-feedback-icon-position:                          center right $form-select-indicator-padding;\n$form-select-feedback-icon-size:                              $input-height-inner-half $input-height-inner-half;\n\n$form-select-border-width:                                    $input-border-width;\n$form-select-border-color:                                    $input-border-color;\n$form-select-border-radius:                                   $input-border-radius;\n$form-select-box-shadow:                                      $box-shadow-inset;\n\n$form-select-focus-border-color:                              $input-focus-border-color;\n$form-select-focus-width:                                     $input-focus-width;\n$form-select-focus-box-shadow:                                0 0 0 $form-select-focus-width $input-btn-focus-color;\n\n$form-select-padding-y-sm:                                    $input-padding-y-sm;\n$form-select-padding-x-sm:                                    $input-padding-x-sm;\n$form-select-font-size-sm:                                    $input-font-size-sm;\n$form-select-border-radius-sm:                                $input-border-radius-sm;\n\n$form-select-padding-y-lg:                                    $input-padding-y-lg;\n$form-select-padding-x-lg:                                    $input-padding-x-lg;\n$form-select-font-size-lg:                                    $input-font-size-lg;\n$form-select-border-radius-lg:                                $input-border-radius-lg;\n$form-select-transition:                                      $input-transition;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:                                      100%;\n$form-range-track-height:                                     .5rem;\n$form-range-track-cursor:                                     pointer;\n$form-range-track-bg:                                         var(--#{$prefix}tertiary-bg);\n$form-range-track-border-radius:                              1rem;\n$form-range-track-box-shadow:                                 $box-shadow-inset;\n\n$form-range-thumb-width:                                      1rem;\n$form-range-thumb-height:                                     $form-range-thumb-width;\n$form-range-thumb-bg:                                         $component-active-bg;\n$form-range-thumb-border:                                     0;\n$form-range-thumb-border-radius:                              1rem;\n$form-range-thumb-box-shadow:                                 0 .1rem .25rem rgba($black, .1);\n$form-range-thumb-focus-box-shadow:                           0 0 0 1px $body-bg,$input-focus-box-shadow;\n$form-range-thumb-focus-box-shadow-width:                     $input-focus-width; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:                                  tint-color($component-active-bg, 70%);\n$form-range-thumb-disabled-bg:                                var(--#{$prefix}secondary-color);\n$form-range-thumb-transition:                                 background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:                                      $input-color;\n$form-file-button-bg:                                         var(--#{$prefix}tertiary-bg);\n$form-file-button-hover-bg:                                   var(--#{$prefix}secondary-bg);\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:                                        add(3.5rem, $input-height-border);\n$form-floating-line-height:                                   1.25;\n$form-floating-padding-x:                                     $input-padding-x;\n$form-floating-padding-y:                                     1rem;\n$form-floating-input-padding-t:                               1.625rem;\n$form-floating-input-padding-b:                               .625rem;\n$form-floating-label-height:                                  1.5em;\n$form-floating-label-opacity:                                 .65;\n$form-floating-label-transform:                               scale(.85) translateY(-.5rem) translateX(.15rem);\n$form-floating-label-disabled-color:                          $gray-600;\n$form-floating-transition:                                    opacity .1s ease-in-out, transform .1s ease-in-out;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:                                    $form-text-margin-top;\n$form-feedback-font-size:                                     $form-text-font-size;\n$form-feedback-font-style:                                    $form-text-font-style;\n$form-feedback-valid-color:                                   $success;\n$form-feedback-invalid-color:                                 $danger;\n\n$form-feedback-icon-valid-color:                              $form-feedback-valid-color;\n$form-feedback-icon-valid:                                    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\");\n$form-feedback-icon-invalid-color:                            $form-feedback-invalid-color;\n$form-feedback-icon-invalid:                                  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\");\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-colors\n$form-valid-color:                                            $form-feedback-valid-color;\n$form-valid-border-color:                                     $form-feedback-valid-color;\n$form-invalid-color:                                          $form-feedback-invalid-color;\n$form-invalid-border-color:                                   $form-feedback-invalid-color;\n// scss-docs-end form-validation-colors\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\"color\": var(--#{$prefix}form-valid-color),\n    \"icon\": $form-feedback-icon-valid,\n    \"tooltip-color\": #fff,\n    \"tooltip-bg-color\": var(--#{$prefix}success),\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}success-rgb), $input-btn-focus-color-opacity),\n    \"border-color\": var(--#{$prefix}form-valid-border-color),\n  ),\n  \"invalid\": (\"color\": var(--#{$prefix}form-invalid-color),\n    \"icon\": $form-feedback-icon-invalid,\n    \"tooltip-color\": #fff,\n    \"tooltip-bg-color\": var(--#{$prefix}danger),\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}danger-rgb), $input-btn-focus-color-opacity),\n    \"border-color\": var(--#{$prefix}form-invalid-border-color),\n  ));\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                         1000;\n$zindex-sticky:                           1020;\n$zindex-fixed:                            1030;\n$zindex-offcanvas-backdrop:               1040;\n$zindex-offcanvas:                        1050;\n$zindex-modal-backdrop:                   1050;\n$zindex-modal:                            1060;\n$zindex-popover:                          1070;\n$zindex-tooltip:                          1080;\n$zindex-toast:                            1090;\n// scss-docs-end zindex-stack\n\n// scss-docs-start zindex-levels-map\n$zindex-levels: (\n  n1: -1,\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3);\n// scss-docs-end zindex-levels-map\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                        .5rem;\n$nav-link-padding-x:                        1rem;\n$nav-link-font-size:                        null;\n$nav-link-font-weight:                      null;\n$nav-link-color:                            var(--#{$prefix}link-color);\n$nav-link-hover-color:                      var(--#{$prefix}link-hover-color);\n$nav-link-transition:                       color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out;\n$nav-link-disabled-color:                   var(--#{$prefix}secondary-color);\n$nav-link-focus-box-shadow:                 $focus-ring-box-shadow;\n\n$nav-tabs-border-color:                     var(--#{$prefix}border-color);\n$nav-tabs-border-width:                     var(--#{$prefix}border-width);\n$nav-tabs-border-radius:                    var(--#{$prefix}border-radius);\n$nav-tabs-link-hover-border-color:          var(--#{$prefix}secondary-bg) var(--#{$prefix}secondary-bg) $nav-tabs-border-color;\n$nav-tabs-link-active-color:                var(--#{$prefix}emphasis-color);\n$nav-tabs-link-active-bg:                   var(--#{$prefix}secondary-bg);\n$nav-tabs-link-active-border-color:         var(--#{$prefix}border-color) var(--#{$prefix}border-color) $nav-tabs-link-active-bg;\n\n$nav-pills-border-radius:                   var(--#{$prefix}border-radius);\n$nav-pills-link-active-color:               $component-active-color;\n$nav-pills-link-active-bg:                  $component-active-bg;\n\n$nav-underline-gap:                         1rem;\n$nav-underline-border-width:                .125rem;\n$nav-underline-link-active-color:           var(--#{$prefix}emphasis-color);\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                          $spacer * .5;\n$navbar-padding-x:                          null;\n\n$navbar-nav-link-padding-x:                 .5rem;\n\n$navbar-brand-font-size:                    $font-size-lg;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                           $font-size-base * $line-height-base + $nav-link-padding-y * 2;\n$navbar-brand-height:                       $navbar-brand-font-size * $line-height-base;\n$navbar-brand-padding-y:                    ($nav-link-height - $navbar-brand-height) * .5;\n$navbar-brand-margin-end:                   1rem;\n\n$navbar-toggler-padding-y:                  .25rem;\n$navbar-toggler-padding-x:                  .75rem;\n$navbar-toggler-font-size:                  $font-size-lg;\n$navbar-toggler-border-radius:              $btn-border-radius;\n$navbar-toggler-focus-width:                $btn-focus-width;\n$navbar-toggler-transition:                 box-shadow .15s ease-in-out;\n\n$navbar-light-color:                        rgba(var(--#{$prefix}emphasis-color-rgb), .65);\n$navbar-light-hover-color:                  rgba(var(--#{$prefix}emphasis-color-rgb), .8);\n$navbar-light-active-color:                 rgba(var(--#{$prefix}emphasis-color-rgb), 1);\n$navbar-light-disabled-color:               rgba(var(--#{$prefix}emphasis-color-rgb), .3);\n$navbar-light-icon-color:                   rgba($body-color, .75);\n$navbar-light-toggler-icon-bg:              url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-icon-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\");\n$navbar-light-toggler-border-color:         rgba(var(--#{$prefix}emphasis-color-rgb), .15);\n$navbar-light-brand-color:                  $navbar-light-active-color;\n$navbar-light-brand-hover-color:            $navbar-light-active-color;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-dark-variables\n$navbar-dark-color:                         rgba($white, .55);\n$navbar-dark-hover-color:                   rgba($white, .75);\n$navbar-dark-active-color:                  $white;\n$navbar-dark-disabled-color:                rgba($white, .25);\n$navbar-dark-toggler-icon-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\");\n$navbar-dark-toggler-border-color:          rgba($white, .1);\n$navbar-dark-brand-color:                   $navbar-dark-active-color;\n$navbar-dark-brand-hover-color:             $navbar-dark-active-color;\n// scss-docs-end navbar-dark-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                        10rem;\n$dropdown-padding-x:                        0;\n$dropdown-padding-y:                        .5rem;\n$dropdown-spacer:                           .125rem;\n$dropdown-font-size:                        $font-size-base;\n$dropdown-color:                            var(--#{$prefix}body-color);\n$dropdown-bg:                               var(--#{$prefix}secondary-bg);\n$dropdown-border-color:                     var(--#{$prefix}border-color);\n$dropdown-border-radius:                    var(--#{$prefix}border-radius);\n$dropdown-border-width:                     var(--#{$prefix}border-width);\n$dropdown-inner-border-radius:              calc(#{$dropdown-border-radius} - #{$dropdown-border-width}); // stylelint-disable-line function-disallowed-list\n$dropdown-divider-bg:                       $dropdown-border-color;\n$dropdown-divider-margin-y:                 $spacer * .5;\n$dropdown-box-shadow:                       0 .5rem 1rem rgba($black, .175);\n\n$dropdown-link-color:                       var(--#{$prefix}body-color);\n$dropdown-link-hover-color:                 $dropdown-link-color;\n$dropdown-link-hover-bg:                    var(--#{$prefix}tertiary-bg);\n\n$dropdown-link-active-color:                var(--#{$prefix}secondary-color);\n$dropdown-link-active-bg:                   var(--#{$prefix}tertiary-bg);\n\n$dropdown-link-disabled-color:              var(--#{$prefix}tertiary-color);\n\n$dropdown-item-padding-y:                   .35rem;\n$dropdown-item-padding-x:                   1.5rem;\n\n$dropdown-header-color:                     $gray-600;\n$dropdown-header-padding-x:                 $dropdown-item-padding-x;\n$dropdown-header-padding-y:                 $dropdown-padding-y;\n// fusv-disable\n$dropdown-header-padding:                   $dropdown-header-padding-y $dropdown-header-padding-x; // Deprecated in v5.2.0\n// fusv-enable\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:                         $gray-300;\n$dropdown-dark-bg:                            $gray-800;\n$dropdown-dark-border-color:                  $dropdown-border-color;\n$dropdown-dark-divider-bg:                    $dropdown-divider-bg;\n$dropdown-dark-box-shadow:                    null;\n$dropdown-dark-link-color:                    $dropdown-dark-color;\n$dropdown-dark-link-hover-color:              $white;\n$dropdown-dark-link-hover-bg:                 rgba($white, .15);\n$dropdown-dark-link-active-color:             $dropdown-link-active-color;\n$dropdown-dark-link-active-bg:                $dropdown-link-active-bg;\n$dropdown-dark-link-disabled-color:           $gray-500;\n$dropdown-dark-header-color:                  $gray-500;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:                        .5rem;\n$pagination-padding-x:                        .75rem;\n$pagination-padding-y-sm:                     .25rem;\n$pagination-padding-x-sm:                     .5rem;\n$pagination-padding-y-lg:                     .75rem;\n$pagination-padding-x-lg:                     1.5rem;\n\n$pagination-font-size:                        $font-size-base;\n\n$pagination-color:                            var(--#{$prefix}link-color);\n$pagination-bg:                               var(--#{$prefix}secondary-bg);\n$pagination-border-radius:                    var(--#{$prefix}border-radius);\n$pagination-border-width:                     var(--#{$prefix}border-width);\n$pagination-margin-start:                     calc(#{$pagination-border-width} * -1); // stylelint-disable-line function-disallowed-list\n$pagination-border-color:                     var(--#{$prefix}border-color);\n\n$pagination-focus-color:                      var(--#{$prefix}link-hover-color);\n$pagination-focus-bg:                         var(--#{$prefix}secondary-bg);\n$pagination-focus-box-shadow:                 $focus-ring-box-shadow;\n$pagination-focus-outline:                    0;\n\n$pagination-hover-color: var(--#{$prefix}link-hover-color);\n$pagination-hover-bg: var(--#{$prefix}tertiary-bg);\n$pagination-hover-border-color: var(--#{$prefix}border-color); // Todo in v6: remove this?\n\n$pagination-active-color: $component-active-color;\n$pagination-active-bg: $component-active-bg;\n$pagination-active-border-color: $component-active-bg;\n\n$pagination-disabled-color: var(--#{$prefix}secondary-color);\n$pagination-disabled-bg: var(--#{$prefix}secondary-bg);\n$pagination-disabled-border-color: var(--#{$prefix}border-color);\n\n$pagination-transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$pagination-border-radius-sm: var(--#{$prefix}border-radius-sm);\n$pagination-border-radius-lg: var(--#{$prefix}border-radius-lg);\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max: .5;\n$placeholder-opacity-min: .2;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y: 1.25rem;\n$card-spacer-x: 1.25rem;\n$card-title-spacer-y: $spacer * .5;\n$card-title-color: null;\n$card-subtitle-color: null;\n$card-border-width: var(--#{$prefix}border-width);\n$card-border-color: var(--#{$prefix}border-color);\n$card-border-radius: var(--#{$prefix}border-radius);\n$card-box-shadow: 0 2px 4px rgba(0, 0, 0, .08);\n$card-inner-border-radius: subtract($card-border-radius, $card-border-width);\n$card-cap-padding-y: $card-spacer-y * .5;\n$card-cap-padding-x: $card-spacer-x;\n$card-cap-bg: rgba(var(--#{$prefix}body-color-rgb), .03);\n$card-cap-color: null;\n$card-height: null;\n$card-color: null;\n$card-bg: var(--#{$prefix}secondary-bg);\n$card-img-overlay-padding: $spacer;\n$card-group-margin: $grid-gutter-width * .5;\n$card-title-desc: $gray-600;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y: 1rem;\n$accordion-padding-x: 1.25rem;\n$accordion-color: var(--#{$prefix}body-color);\n$accordion-bg: var(--#{$prefix}secondary-bg);\n$accordion-border-width: var(--#{$prefix}border-width);\n$accordion-border-color: var(--#{$prefix}border-color-translucent);\n$accordion-border-radius: var(--#{$prefix}border-radius);\n$accordion-inner-border-radius: subtract($accordion-border-radius, $accordion-border-width);\n\n$accordion-body-padding-y: $accordion-padding-y;\n$accordion-body-padding-x: $accordion-padding-x;\n\n$accordion-button-padding-y: $accordion-padding-y;\n$accordion-button-padding-x: $accordion-padding-x;\n$accordion-button-color: var(--#{$prefix}body-color);\n$accordion-button-bg: var(--#{$prefix}accordion-bg);\n$accordion-transition: $btn-transition,border-radius .15s ease;\n$accordion-button-active-bg: var(--#{$prefix}primary-bg-subtle);\n$accordion-button-active-color: var(--#{$prefix}primary-text-emphasis);\n\n$accordion-button-focus-border-color: $input-focus-border-color;\n$accordion-button-focus-box-shadow: $btn-focus-box-shadow;\n\n$accordion-icon-width: 1.25rem;\n$accordion-icon-color: $body-color;\n$accordion-icon-active-color: $primary-text-emphasis;\n$accordion-icon-transition: transform .2s ease-in-out;\n$accordion-icon-transform: rotate(-180deg);\n\n$accordion-button-icon: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\");\n$accordion-button-active-icon: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\");\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size: $font-size-sm;\n$tooltip-max-width: 200px;\n$tooltip-color: var(--#{$prefix}body-bg);\n$tooltip-bg: var(--#{$prefix}emphasis-color);\n$tooltip-border-radius: var(--#{$prefix}border-radius);\n$tooltip-opacity: .9;\n$tooltip-padding-y: $spacer * .25;\n$tooltip-padding-x: $spacer * .5;\n$tooltip-margin: null; // TODO: remove this in v6\n\n$tooltip-arrow-width: .8rem;\n$tooltip-arrow-height: .4rem;\n// fusv-disable\n$tooltip-arrow-color: $tooltip-bg; // Deprecated in Bootstrap 5.2.0 for CSS variables\n// fusv-enable\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y: $tooltip-padding-y;\n$form-feedback-tooltip-padding-x: $tooltip-padding-x;\n$form-feedback-tooltip-font-size: $tooltip-font-size;\n$form-feedback-tooltip-line-height: null;\n$form-feedback-tooltip-opacity: $tooltip-opacity;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius;\n// scss-docs-end tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size: $font-size-sm;\n$popover-bg: var(--#{$prefix}secondary-bg);\n$popover-max-width: 276px;\n$popover-border-width: var(--#{$prefix}border-width);\n$popover-border-color: var(--#{$prefix}border-color-translucent);\n$popover-border-radius: var(--#{$prefix}border-radius-lg);\n$popover-inner-border-radius: calc(#{$popover-border-radius} - #{$popover-border-width}); // stylelint-disable-line function-disallowed-list\n$popover-box-shadow: $box-shadow;\n\n$popover-header-font-size: $font-size-base;\n$popover-header-bg: var(--#{$prefix}secondary-bg);\n$popover-header-color: var(--#{$prefix}headings-color);\n$popover-header-padding-y: .5rem;\n$popover-header-padding-x: .75rem;\n\n$popover-body-color: var(--#{$prefix}body-color);\n$popover-body-padding-y: $popover-header-padding-y;\n$popover-body-padding-x: $popover-header-padding-x;\n\n$popover-arrow-width: 1rem;\n$popover-arrow-height: .5rem;\n// scss-docs-end popover-variables\n\n// fusv-disable\n// Deprecated in Bootstrap 5.2.0 for CSS variables\n$popover-arrow-color: $popover-bg;\n$popover-arrow-outer-color: var(--#{$prefix}border-color-translucent);\n// fusv-enable\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width: 350px;\n$toast-padding-x: .75rem;\n$toast-padding-y: .5rem;\n$toast-font-size: .875rem;\n$toast-color: null;\n$toast-background-color: rgba(var(--#{$prefix}body-bg-rgb), .85);\n$toast-border-width: var(--#{$prefix}border-width);\n$toast-border-color: var(--#{$prefix}border-color-translucent);\n$toast-border-radius: var(--#{$prefix}border-radius);\n$toast-box-shadow: var(--#{$prefix}box-shadow);\n$toast-spacing: $container-padding-x;\n\n$toast-header-color: var(--#{$prefix}secondary-color);\n$toast-header-background-color: rgba(var(--#{$prefix}body-bg-rgb), .85);\n$toast-header-border-color: $toast-border-color;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size: .75em;\n$badge-font-weight: $font-weight-bold;\n$badge-color: $white;\n$badge-padding-y: .35em;\n$badge-padding-x: .65em;\n$badge-border-radius: var(--#{$prefix}border-radius);\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding: $spacer;\n\n$modal-footer-margin-between: .5rem;\n\n$modal-dialog-margin: .5rem;\n$modal-dialog-margin-y-sm-up: 1.75rem;\n\n$modal-title-line-height: $line-height-base;\n\n$modal-content-color: null;\n$modal-content-bg: var(--#{$prefix}secondary-bg);\n$modal-content-border-color: var(--#{$prefix}border-color);\n$modal-content-border-width: var(--#{$prefix}border-width);\n$modal-content-border-radius: var(--#{$prefix}border-radius-lg);\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width);\n$modal-content-box-shadow-xs: $box-shadow-sm;\n$modal-content-box-shadow-sm-up: $box-shadow;\n\n$modal-backdrop-bg: $black;\n$modal-backdrop-opacity: .5;\n\n$modal-header-border-color: var(--#{$prefix}border-color);\n$modal-header-border-width: $modal-content-border-width;\n$modal-header-padding-y: $modal-inner-padding;\n$modal-header-padding-x: $modal-inner-padding;\n$modal-header-padding: $modal-header-padding-y $modal-header-padding-x; // Keep this for backwards compatibility\n\n$modal-footer-bg: null;\n$modal-footer-border-color: $modal-header-border-color;\n$modal-footer-border-width: $modal-header-border-width;\n\n$modal-sm: 300px;\n$modal-md: 500px;\n$modal-lg: 800px;\n$modal-xl: 1140px;\n\n$modal-fade-transform: translate(0, -50px);\n$modal-show-transform: none;\n$modal-transition: transform .3s ease-out;\n$modal-scale-transform: scale(1.02);\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y: .75rem;\n$alert-padding-x: 1.25rem;\n$alert-margin-bottom: 1rem;\n$alert-border-radius: var(--#{$prefix}border-radius);\n$alert-link-font-weight: $font-weight-bold;\n$alert-border-width: var(--#{$prefix}border-width);\n$alert-bg-scale: -80%;\n$alert-border-scale: -70%;\n$alert-color-scale: 40%;\n$alert-dismissible-padding-r: $alert-padding-x * 3; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height: 1rem;\n$progress-font-size: $font-size-base * .75;\n$progress-bg: var(--#{$prefix}tertiary-bg);\n$progress-border-radius: var(--#{$prefix}border-radius);\n$progress-box-shadow: var(--#{$prefix}box-shadow-inset);\n$progress-bar-color: $white;\n$progress-bar-bg: $primary;\n$progress-bar-animation-timing: 1s linear infinite;\n$progress-bar-transition: width .6s ease;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color: var(--#{$prefix}body-color);\n$list-group-bg: var(--#{$prefix}secondary-bg);\n$list-group-border-color: var(--#{$prefix}border-color);\n$list-group-border-width: var(--#{$prefix}border-width);\n$list-group-border-radius: var(--#{$prefix}border-radius);\n\n$list-group-item-padding-y: .75rem;\n$list-group-item-padding-x: 1.25rem;\n// fusv-disable\n$list-group-item-bg-scale: -80%; // Deprecated in v5.3.0\n$list-group-item-color-scale: 40%; // Deprecated in v5.3.0\n// fusv-enable\n\n$list-group-hover-bg: var(--#{$prefix}tertiary-bg);\n$list-group-active-color: $component-active-color;\n$list-group-active-bg: $component-active-bg;\n$list-group-active-border-color: $list-group-active-bg;\n\n$list-group-disabled-color: var(--#{$prefix}secondary-color);\n$list-group-disabled-bg: $list-group-bg;\n\n$list-group-action-color: var(--#{$prefix}secondary-color);\n$list-group-action-hover-color: var(--#{$prefix}emphasis-color);\n\n$list-group-action-active-color: var(--#{$prefix}body-color);\n$list-group-action-active-bg: var(--#{$prefix}secondary-bg);\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding: .25rem;\n$thumbnail-bg: var(--#{$prefix}body-bg);\n$thumbnail-border-width: var(--#{$prefix}border-width);\n$thumbnail-border-color: var(--#{$prefix}border-color);\n$thumbnail-border-radius: var(--#{$prefix}border-radius);\n$thumbnail-box-shadow: var(--#{$prefix}box-shadow-sm);\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size: $small-font-size;\n$figure-caption-color: $gray-600;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size: null;\n$breadcrumb-padding-y: .75rem;\n$breadcrumb-padding-x: 1rem;\n$breadcrumb-item-padding-x: .5rem;\n$breadcrumb-margin-bottom: 1rem;\n$breadcrumb-bg: null;\n$breadcrumb-divider-color: $gray-600;\n$breadcrumb-active-color: $gray-600;\n$breadcrumb-divider: quote(\"\\F0142\");\n$breadcrumb-divider-flipped: $breadcrumb-divider;\n$breadcrumb-border-radius: null;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:      $white;\n$carousel-control-width:      15%;\n$carousel-control-opacity:    .5;\n$carousel-control-hover-opacity: .9;\n$carousel-control-transition: opacity .15s ease;\n\n$carousel-indicator-width: 30px;\n$carousel-indicator-height: 3px;\n$carousel-indicator-hit-area-height: 10px;\n$carousel-indicator-spacer: 3px;\n$carousel-indicator-opacity: .5;\n$carousel-indicator-active-bg: $white;\n$carousel-indicator-active-opacity: 1;\n$carousel-indicator-transition: opacity .6s ease;\n\n$carousel-caption-width: 70%;\n$carousel-caption-color: $white;\n$carousel-caption-padding-y: 1.25rem;\n$carousel-caption-spacer: 1.25rem;\n\n$carousel-control-icon-width: 2rem;\n\n$carousel-control-prev-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\");\n$carousel-control-next-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\");\n\n$carousel-transition-duration: .6s;\n$carousel-transition: transform $carousel-transition-duration ease-in-out; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n// scss-docs-end carousel-variables\n\n// scss-docs-start carousel-dark-variables\n$carousel-dark-indicator-active-bg: $black;\n$carousel-dark-caption-color: $black;\n$carousel-dark-control-icon-filter: invert(1) grayscale(100);\n// scss-docs-end carousel-dark-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width: 2rem;\n$spinner-height: $spinner-width;\n$spinner-vertical-align: -.125em;\n$spinner-border-width: .25em;\n$spinner-animation-speed: .75s;\n\n$spinner-width-sm: 1rem;\n$spinner-height-sm: $spinner-width-sm;\n$spinner-border-width-sm: .2em;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width: 1em;\n$btn-close-height: $btn-close-width;\n$btn-close-padding-x: .25em;\n$btn-close-padding-y: $btn-close-padding-x;\n$btn-close-color: $black;\n$btn-close-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>\");\n$btn-close-bg-dark: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$black}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\");\n$btn-close-focus-shadow: none;\n$btn-close-opacity: .5;\n$btn-close-hover-opacity: .75;\n$btn-close-focus-opacity: 1;\n$btn-close-disabled-opacity: .25;\n$btn-close-white-filter: invert(1) grayscale(100%) brightness(200%);\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y: $modal-inner-padding;\n$offcanvas-padding-x: $modal-inner-padding;\n$offcanvas-horizontal-width: 400px;\n$offcanvas-vertical-height: 30vh;\n$offcanvas-transition-duration: .3s;\n$offcanvas-border-color: $modal-content-border-color;\n$offcanvas-border-width: $modal-content-border-width;\n$offcanvas-title-line-height: $modal-title-line-height;\n$offcanvas-bg-color: var(--#{$prefix}secondary-bg);\n$offcanvas-color: var(--#{$prefix}body-color);\n$offcanvas-box-shadow: $modal-content-box-shadow-xs;\n$offcanvas-backdrop-bg: $modal-backdrop-bg;\n$offcanvas-backdrop-opacity: $modal-backdrop-opacity;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size: 87.5%;\n$code-color: $pink;\n\n$kbd-padding-y: .2rem;\n$kbd-padding-x: .4rem;\n$kbd-font-size: $code-font-size;\n$kbd-color: var(--#{$prefix}body-bg);\n$kbd-bg: var(--#{$prefix}body-color);\n$nested-kbd-font-weight: null; // Deprecated in v5.2.0, removing in v6\n\n$pre-color: $gray-900;", "// \r\n// Page-title\r\n// \r\n\r\n.page-title-box {\r\n    padding-bottom: $grid-gutter-width;\r\n\r\n    .breadcrumb {\r\n        background-color: transparent;\r\n        padding: 0;\r\n    }\r\n\r\n    h4 {\r\n        font-size: 15px;\r\n        text-transform: uppercase;\r\n        font-weight: 600;\r\n    }\r\n}\r\n", "// \r\n// _footer.scss\r\n// \r\n\r\n.footer {\r\n    bottom: 0;\r\n    padding: 20px calc(#{$grid-gutter-width} / 2);\r\n    position: absolute;\r\n    right: 0;\r\n    color: $footer-color;\r\n    left: $sidebar-width;\r\n    height: $footer-height;\r\n    box-shadow: $box-shadow;\r\n    background-color: $footer-bg;\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .footer {\r\n        left: 0;\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n    .footer {\r\n        left: $sidebar-collapsed-width;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .footer {\r\n        left: 0 !important;\r\n    }  \r\n}", "//\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    background-color: $card-bg;\r\n    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\r\n    display: block;\r\n    position: fixed;\r\n    transition: all 200ms ease-out;\r\n    width: $rightbar-width;\r\n    z-index: 9999;\r\n    float: right !important;\r\n    right: -($rightbar-width + 10px);\r\n    top: 0;\r\n    bottom: 0;\r\n\r\n    .right-bar-toggle {\r\n        background-color: lighten($dark, 7%);\r\n        height: 24px;\r\n        width: 24px;\r\n        line-height: 24px;\r\n        color: $gray-200;\r\n        text-align: center;\r\n        border-radius: 50%;\r\n\r\n        &:hover {\r\n            background-color: lighten($dark, 10%);\r\n        }\r\n    }\r\n}\r\n\r\n// Rightbar overlay\r\n.rightbar-overlay {\r\n    background-color: rgba($dark, 0.55);\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    bottom: 0;\r\n    display: none;\r\n    z-index: 9998;\r\n    transition: all .2s ease-out;\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        right: 0;\r\n    }\r\n    .rightbar-overlay {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(md) {\r\n    .right-bar {\r\n        overflow: auto;\r\n        .slimscroll-menu {\r\n            height: auto !important;\r\n        }\r\n    }\r\n}", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\r\n// _menu.scss\r\n// \r\n\r\n.metismenu {\r\n    margin: 0;\r\n\r\n    li {\r\n        display: block;\r\n        width: 100%;\r\n    }\r\n\r\n    .mm-collapse {\r\n        display: none;\r\n\r\n        &:not(.mm-show) {\r\n            display: none;\r\n        }\r\n\r\n        &.mm-show {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .mm-collapsing {\r\n        position: relative;\r\n        height: 0;\r\n        overflow: hidden;\r\n        transition-timing-function: ease;\r\n        transition-duration: .35s;\r\n        transition-property: height, visibility;\r\n    }\r\n}\r\n\r\n\r\n.vertical-menu {\r\n    width: $sidebar-width;\r\n    z-index: 1001;\r\n    background: var(--#{$prefix}sidebar-bg);\r\n    bottom: 0;\r\n    margin-top: 0;\r\n    position: fixed;\r\n    top: $header-height;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.main-content {\r\n    margin-left: $sidebar-width;\r\n    overflow: hidden;\r\n\r\n    .content {\r\n        padding: 0 15px 10px 15px;\r\n        margin-top: $header-height;\r\n    }\r\n}\r\n\r\n\r\n#sidebar-menu {\r\n    padding: 10px 0 30px 0;\r\n\r\n    .mm-active {\r\n        >.has-arrow {\r\n            &:after {\r\n                transform: rotate(-180deg);\r\n            }\r\n        }\r\n    }\r\n\r\n    .has-arrow {\r\n        &:after {\r\n            content: \"\\F0140\";\r\n            font-family: 'Material Design Icons';\r\n            display: block;\r\n            float: right;\r\n            transition: transform .2s;\r\n            font-size: 1rem;\r\n        }\r\n    }\r\n\r\n    ul {\r\n        li {\r\n            a {\r\n                display: block;\r\n                padding: .625rem 1.5rem;\r\n                color: var(--#{$prefix}sidebar-menu-item-color);\r\n                position: relative;\r\n                font-size: 13.3px;\r\n                transition: all .4s;\r\n                font-family: $font-family-secondary;\r\n                font-weight: 500;\r\n\r\n                i {\r\n                    display: inline-block;\r\n                    min-width: 1.5rem;\r\n                    padding-bottom: .125em;\r\n                    font-size: 1.1rem;\r\n                    line-height: 1.40625rem;\r\n                    vertical-align: middle;\r\n                    color: var(--#{$prefix}sidebar-menu-item-icon-color);\r\n                    transition: all .4s;\r\n                    opacity: .75;\r\n                }\r\n\r\n                &:hover {\r\n                    color:  var(--#{$prefix}sidebar-menu-item-hover-color);\r\n\r\n                    i {\r\n                        color:  var(--#{$prefix}sidebar-menu-item-hover-color);\r\n                    }\r\n                }\r\n            }\r\n\r\n            .badge {\r\n                margin-top: 4px;\r\n            }\r\n\r\n            ul.sub-menu {\r\n                padding: 0;\r\n\r\n                li {\r\n\r\n                    a {\r\n                        padding: .4rem 1.5rem .4rem 3.2rem;\r\n                        font-size: 13px;\r\n                        color:  var(--#{$prefix}sidebar-menu-sub-item-color);\r\n                    }\r\n\r\n                    ul.sub-menu {\r\n                        padding: 0;\r\n\r\n                        li {\r\n                            a {\r\n                                padding: .4rem 1.5rem .4rem 4.2rem;\r\n                                font-size: 13.5px;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n.menu-title {\r\n    padding: 12px 20px !important;\r\n    letter-spacing: .05em;\r\n    pointer-events: none;\r\n    cursor: default;\r\n    font-size: 11px;\r\n    text-transform: uppercase;\r\n    color: var(--#{$prefix}sidebar-menu-item-icon-color);\r\n    font-weight: $font-weight-semibold;\r\n    font-family: $font-family-secondary;\r\n    opacity: .5;\r\n}\r\n\r\n\r\n.mm-active {\r\n    color: var(--#{$prefix}sidebar-menu-item-active-color)  !important;\r\n    > a{\r\n        color: var(--#{$prefix}sidebar-menu-item-active-color)  !important;\r\n        i {\r\n            color: var(--#{$prefix}sidebar-menu-item-active-color)  !important;\r\n        }\r\n    }\r\n    > i {\r\n        color: var(--#{$prefix}sidebar-menu-item-active-color)  !important;\r\n    }\r\n    .active {\r\n        color: var(--#{$prefix}sidebar-menu-item-active-color)  !important;\r\n\r\n        i {\r\n            color: var(--#{$prefix}sidebar-menu-item-active-color)  !important;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .vertical-menu {\r\n        display: none;\r\n    }\r\n\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    body.sidebar-enable {\r\n        .vertical-menu {\r\n            display: block;\r\n        }\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n\r\n    .main-content {\r\n        margin-left: $sidebar-collapsed-width;\r\n    }\r\n\r\n    .navbar-brand-box {\r\n        width: $sidebar-collapsed-width !important;\r\n    }\r\n\r\n    .logo {\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    // Side menu\r\n    .vertical-menu {\r\n        position: absolute;\r\n        width: $sidebar-collapsed-width !important;\r\n        z-index: 5;\r\n\r\n        .simplebar-mask,\r\n        .simplebar-content-wrapper {\r\n            overflow: visible !important;\r\n        }\r\n\r\n        .simplebar-scrollbar {\r\n            display: none !important;\r\n        }\r\n\r\n        .simplebar-offset {\r\n            bottom: 0 !important;\r\n        }\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n\r\n            .menu-title,\r\n            .badge,\r\n            .collapse.in {\r\n                display: none !important;\r\n            }\r\n\r\n            .nav.collapse {\r\n                height: inherit !important;\r\n            }\r\n\r\n            .has-arrow {\r\n                &:after {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            > ul {\r\n                > li {\r\n                    position: relative;\r\n                    white-space: nowrap;\r\n\r\n                    > a {\r\n                        padding: 15px 20px;\r\n                        min-height: 55px;\r\n                        transition: none;\r\n                        \r\n                        &:hover,\r\n                        &:active,\r\n                        &:focus {\r\n                            color:  var(--#{$prefix}sidebar-menu-item-hover-color);\r\n                        }\r\n\r\n                        i {\r\n                            font-size: 20px;\r\n                            margin-left: 4px;\r\n                        }\r\n\r\n                        span {\r\n                            display: none;\r\n                            padding-left: 25px;\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n                        > a {\r\n                            position: relative;\r\n                            width: calc(190px + #{$sidebar-collapsed-width});\r\n                            color: $primary;\r\n                            // background-color: darken($sidebar-bg, 4%);\r\n                            transition: none;\r\n\r\n                            i{\r\n                                color: $primary;\r\n                            }\r\n\r\n                            span {\r\n                                display: inline;\r\n                            }\r\n                        }\r\n\r\n                        >ul {\r\n                            display: block;\r\n                            left: $sidebar-collapsed-width;\r\n                            position: absolute;\r\n                            width: 190px;\r\n                            height: auto !important;\r\n                            box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\r\n\r\n                            ul {\r\n                                box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\r\n                            }\r\n\r\n                            a {\r\n                                box-shadow: none;\r\n                                padding: 8px 20px;\r\n                                position: relative;\r\n                                width: 190px;\r\n                                z-index: 6;\r\n                                color:  var(--#{$prefix}sidebar-menu-sub-item-color);\r\n\r\n                                &:hover {\r\n                                    color: var(--#{$prefix}sidebar-menu-item-hover-color);\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n                    padding: 5px 0;\r\n                    z-index: 9999;\r\n                    display: none;\r\n                    background-color: var(--#{$prefix}sidebar-bg);\r\n\r\n                    li {\r\n                        &:hover {\r\n                            >ul {\r\n                                display: block;\r\n                                left: 190px;\r\n                                height: auto !important;\r\n                                margin-top: -36px;\r\n                                position: absolute;\r\n                                width: 190px;\r\n                            }\r\n                        }\r\n\r\n                        >a {\r\n                            span.pull-right {\r\n                                position: absolute;\r\n                                right: 20px;\r\n                                top: 12px;\r\n                                transform: rotate(270deg);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li.active {\r\n                        a {\r\n                            color: $gray-100;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n    .vertical-menu {\r\n        background: var(--#{$prefix}sidebar-dark-bg);\r\n    }\r\n\r\n    #sidebar-menu {\r\n    \r\n        ul {\r\n            li {\r\n                a {\r\n                    color: var(--#{$prefix}sidebar-dark-menu-item-color);\r\n\r\n                    i {\r\n                        color: var(--#{$prefix}sidebar-dark-menu-item-color);\r\n                    }\r\n    \r\n                    &:hover {\r\n                        color: var(--#{$prefix}sidebar-dark-menu-item-hover-color);\r\n\r\n                        i {\r\n                            color: var(--#{$prefix}sidebar-dark-menu-item-hover-color);;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul.sub-menu {\r\n                    li {\r\n\r\n                        a {\r\n                            color: var(--#{$prefix}sidebar-dark-menu-sub-item-color);\r\n\r\n                            &:hover {\r\n                                color: var(--#{$prefix}sidebar-dark-menu-item-hover-color);;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    // Enlarge menu\r\n    &.vertical-collpsed {\r\n        min-height: 1400px;\r\n\r\n        // Side menu\r\n        .vertical-menu {\r\n\r\n            // Sidebar Menu\r\n            #sidebar-menu {\r\n\r\n                > ul {\r\n                    > li {\r\n                        \r\n                        &:hover {\r\n                            > a {\r\n                                background-color: var(--#{$prefix}sidebar-dark-bg);\r\n                                color: var(--#{$prefix}sidebar-dark-menu-item-hover-color);;\r\n                                i{\r\n                                    color: var(--#{$prefix}sidebar-dark-menu-item-hover-color);;\r\n                                }\r\n                            }\r\n\r\n                            >ul {\r\n                                a{\r\n                                    color: var(--#{$prefix}sidebar-dark-menu-sub-item-color);\r\n                                    &:hover{\r\n                                        color:  var(--#{$prefix}sidebar-menu-item-hover-color);\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    ul{\r\n                        background-color: var(--#{$prefix}secondary-bg);\r\n                    }\r\n                    \r\n                }\r\n\r\n                ul{\r\n\r\n                    >li{\r\n                        >a{\r\n                            &.mm-active{\r\n                                color:var(--#{$prefix}sidebar-dark-menu-item-active-color) !important;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li{\r\n                        li{\r\n                            &.mm-active, &.active {\r\n                               > a{\r\n                                    color:var(--#{$prefix}sidebar-menu-item-active-color) !important;\r\n                                }\r\n                            }\r\n\r\n                            a{\r\n                                &.mm-active, &.active {\r\n                                    color: var(--#{$prefix}sidebar-menu-item-active-color)  !important;\r\n                                }\r\n\r\n                                \r\n                            }\r\n                        }\r\n                    }\r\n                    \r\n                    \r\n                }\r\n            }\r\n\r\n\r\n        }\r\n    }\r\n    \r\n    .mm-active {\r\n        color: $sidebar-dark-menu-item-active-color !important;\r\n        > a{\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n        > i {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n        }\r\n        .active {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        color: var(--#{$prefix}sidebar-dark-menu-item-color);\r\n    }\r\n}\r\n\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n}\r\n\r\n// Compact Sidebar\r\n\r\nbody[data-sidebar-size=\"small\"] {\r\n    .navbar-brand-box{\r\n        width: $sidebar-width-sm;\r\n\r\n        @media (max-width: 992px) {\r\n            width: auto;\r\n        }\r\n    }\r\n    .vertical-menu{\r\n        width: $sidebar-width-sm;\r\n        text-align: center;\r\n\r\n        .has-arrow:after,\r\n        .badge {\r\n            display: none !important;\r\n        }\r\n    }\r\n    .main-content {\r\n        margin-left: $sidebar-width-sm;\r\n    }\r\n    .footer {\r\n        left: $sidebar-width-sm;\r\n        @media (max-width: 991px){\r\n            left: 0;\r\n        }\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul li {\r\n            // &.menu-title{\r\n            //     background-color: lighten($sidebar-dark-bg, 2%);\r\n            // }\r\n            a{\r\n                i{\r\n                    display: block;\r\n                }\r\n            }\r\n            ul.sub-menu {\r\n                li {\r\n                    a{\r\n                        padding-left: 1.5rem;\r\n                    }\r\n\r\n                    ul.sub-menu {\r\n                        li {\r\n                            a{\r\n                                padding-left: 1.5rem;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.vertical-collpsed {\r\n        .main-content {\r\n            margin-left: $sidebar-collapsed-width;\r\n        }\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                text-align: left;\r\n                >ul{\r\n                    >li{\r\n                        >a {\r\n                            i{\r\n                                display: inline-block;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .footer {\r\n            left: $sidebar-collapsed-width;\r\n        }\r\n    }\r\n}\r\n\r\n// Colored Sidebar\r\n\r\nbody[data-sidebar=\"colored\"] {\r\n    .vertical-menu {\r\n        background: $primary;\r\n    }\r\n    .navbar-brand-box{\r\n        background-color: $primary;\r\n        .logo-dark{\r\n            display: none;\r\n        }\r\n        .logo-light{\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul {\r\n            li {\r\n                &.menu-title{\r\n                    color: rgba($white, 0.6);\r\n                }\r\n  \r\n                a{\r\n                    color: rgba($white, 0.6);\r\n                    i{\r\n                        color: rgba($white, 0.6);\r\n                    }\r\n                    &.waves-effect {\r\n                        .waves-ripple {\r\n                          background: rgba($white, 0.1);\r\n                        }\r\n                    }\r\n                }\r\n  \r\n                ul.sub-menu {\r\n                    li {\r\n                        a{\r\n                            color: rgba($white,.5);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Enlarge menu\r\n    &.vertical-collpsed {\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                >ul{\r\n                    >li{\r\n                        &:hover>a{\r\n                            background-color: lighten($primary, 2%);\r\n                            color: $white;\r\n                            i{\r\n                                color: $white;\r\n                            }\r\n                        }\r\n                       \r\n                    }\r\n                }\r\n\r\n                ul{\r\n\r\n                    >li{\r\n                        >a{\r\n                            &.mm-active{\r\n                                color: $sidebar-dark-menu-item-active-color !important;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li{\r\n                        li{\r\n                            &.mm-active, &.active {\r\n                               > a{\r\n                                    color: var(--#{$prefix}sidebar-menu-item-active-color)  !important;\r\n                                }\r\n                            }\r\n\r\n                            a{\r\n                                &.mm-active, &.active {\r\n                                    color: var(--#{$prefix}sidebar-menu-item-active-color)  !important;\r\n                                }\r\n\r\n                                \r\n                            }\r\n                        }\r\n                    }\r\n                    \r\n                    \r\n                }\r\n            }\r\n        }\r\n    }\r\n    \r\n    .mm-active {\r\n        color: $white !important;\r\n        > a{\r\n            color: $white !important;\r\n            i {\r\n                color: $white !important;\r\n            }\r\n        }\r\n        > i {\r\n            color: $white !important;\r\n        }\r\n        .active {\r\n            color: $white !important;\r\n\r\n            i {\r\n                color: $white !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        color: $white !important;\r\n    }\r\n}", "// \r\n// _horizontal.scss\r\n// \r\n\r\n.topnav {\r\n    background: var(--#{$prefix}topnav-bg);\r\n    padding: 0 calc(#{$grid-gutter-width} / 2);\r\n    box-shadow: $box-shadow;\r\n    margin-top: $header-height;\r\n    position: fixed;\r\n    left: 0;\r\n    right: 0;\r\n    z-index: 100;\r\n\r\n    .topnav-menu {\r\n        margin: 0;\r\n        padding: 0;\r\n    }\r\n\r\n    .navbar-nav {\r\n\r\n        .nav-link {\r\n            font-size: 15px;\r\n            position: relative;\r\n            padding: 1rem 1.3rem;\r\n            color: $menu-item-color;\r\n            font-family: $font-family-secondary;\r\n\r\n            i {\r\n                font-size: 15px;\r\n                vertical-align: middle;\r\n                display: inline-block;\r\n            }\r\n\r\n            &:focus,\r\n            &:hover {\r\n                color: $menu-item-active-color;\r\n                background-color: transparent;\r\n            }\r\n        }\r\n\r\n        .dropdown-item {\r\n            color: $menu-item-color;\r\n\r\n            &.active,\r\n            &:hover {\r\n                color: $menu-item-active-color;\r\n            }\r\n        }\r\n\r\n        .nav-item {\r\n            .nav-link.active {\r\n                color: $menu-item-active-color;\r\n            }\r\n        }\r\n\r\n        .dropdown {\r\n            &.active {\r\n                >a {\r\n                    color: $menu-item-active-color;\r\n                    background-color: transparent;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(xl) {\r\n\r\n    body[data-layout=\"horizontal\"] {\r\n\r\n        .container-fluid,\r\n        .navbar-header {\r\n            max-width: 85%;\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n    .topnav {\r\n        .navbar-nav {\r\n            .nav-item {\r\n                &:first-of-type {\r\n                    .nav-link {\r\n                        padding-left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown-item {\r\n            padding: .5rem 1.5rem;\r\n            min-width: 180px;\r\n        }\r\n\r\n        .dropdown {\r\n            &.mega-dropdown {\r\n\r\n                // position: static;\r\n                .mega-dropdown-menu {\r\n                    left: 0px;\r\n                    right: auto;\r\n                }\r\n            }\r\n\r\n            .dropdown-menu {\r\n                margin-top: 0;\r\n                border-radius: 0 0 $dropdown-border-radius $dropdown-border-radius;\r\n\r\n                .arrow-down {\r\n                    &::after {\r\n                        right: 15px;\r\n                        transform: rotate(-135deg) translateY(-50%);\r\n                        position: absolute;\r\n                    }\r\n                }\r\n\r\n                .dropdown {\r\n                    .dropdown-menu {\r\n                        position: absolute;\r\n                        top: 0 !important;\r\n                        left: 100%;\r\n                        display: none\r\n                    }\r\n                }\r\n            }\r\n\r\n            &:hover {\r\n                >.dropdown-menu {\r\n                    display: block;\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown:hover>.dropdown-menu>.dropdown:hover>.dropdown-menu {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .navbar-toggle {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.arrow-down {\r\n    display: inline-block;\r\n\r\n    &:after {\r\n        border-color: initial;\r\n        border-style: solid;\r\n        border-width: 0 0 1px 1px;\r\n        content: \"\";\r\n        height: .4em;\r\n        display: inline-block;\r\n        right: 5px;\r\n        top: 50%;\r\n        margin-left: 10px;\r\n        transform: rotate(-45deg) translateY(-50%);\r\n        transform-origin: top;\r\n        transition: all .3s ease-out;\r\n        width: .4em;\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(xl) {\r\n    .topnav-menu {\r\n        .navbar-nav {\r\n            li {\r\n                &:last-of-type {\r\n                    .dropdown {\r\n                        .dropdown-menu {\r\n                            right: 100%;\r\n                            left: auto;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(lg) {\r\n\r\n    .navbar-brand-box {\r\n        .logo-dark {\r\n            display: $display-block;\r\n\r\n            span.logo-sm {\r\n                display: $display-block;\r\n            }\r\n        }\r\n\r\n        .logo-light {\r\n            display: $display-none;\r\n        }\r\n    }\r\n\r\n    .topnav {\r\n        max-height: 360px;\r\n        overflow-y: auto;\r\n        padding: 0;\r\n\r\n        .navbar-nav {\r\n            .nav-link {\r\n                padding: 0.75rem 1.1rem;\r\n            }\r\n        }\r\n\r\n        .dropdown {\r\n            .dropdown-menu {\r\n                background-color: transparent;\r\n                border: none;\r\n                box-shadow: none;\r\n                padding-left: 15px;\r\n\r\n                &.dropdown-mega-menu-xl {\r\n                    width: auto;\r\n\r\n                    .row {\r\n                        margin: 0px;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .dropdown-item {\r\n                position: relative;\r\n                background-color: transparent;\r\n\r\n                &.active,\r\n                &:active {\r\n                    color: $primary;\r\n                }\r\n            }\r\n        }\r\n\r\n        .arrow-down {\r\n            &::after {\r\n                right: 15px;\r\n                position: absolute;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-up(lg) {\r\n\r\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] {\r\n        .navbar-brand-box {\r\n            .logo-dark {\r\n                display: $display-block;\r\n            }\r\n\r\n            .logo-light {\r\n                display: $display-none;\r\n            }\r\n        }\r\n\r\n        .topnav {\r\n            background-color: var(--#{$prefix}header-dark-bg);\r\n\r\n            .navbar-nav {\r\n\r\n                .nav-link {\r\n                    color: rgba($white, 0.6);\r\n\r\n                    &:focus,\r\n                    &:hover {\r\n                        color: rgba($white, 0.9);\r\n                    }\r\n                }\r\n\r\n                >.dropdown {\r\n                    &.active {\r\n                        >a {\r\n                            color: rgba($white, 0.9) !important;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n[data-bs-theme=\"dark\"][data-layout=\"horizontal\"][data-topbar=\"light\"] {\r\n    .navbar-brand-box {\r\n        .logo-dark {\r\n            display: $display-none;\r\n        }\r\n\r\n        .logo-light {\r\n            display: $display-block;\r\n        }\r\n    }\r\n}", "// \r\n// _layouts.scss\r\n// \r\n\r\nbody[data-layout-size=\"boxed\"] {\r\n    background-color:var(--#{$prefix}boxed-body-bg);\r\n    #layout-wrapper {\r\n        background-color: var(--#{$prefix}body-bg);\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n        box-shadow: $box-shadow;\r\n    }\r\n\r\n    #page-topbar {\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n    }\r\n\r\n    .footer {\r\n        margin: 0 auto;\r\n        max-width: calc(#{$boxed-layout-width} - #{$sidebar-width});\r\n    }\r\n\r\n    &.vertical-collpsed {\r\n        .footer {\r\n            max-width: calc(#{$boxed-layout-width} - #{$sidebar-collapsed-width});\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Horizontal Boxed Layout\r\n\r\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"]{\r\n    .topnav{\r\n        max-width: $boxed-layout-width;\r\n        margin-left: auto;\r\n        margin-right: auto;\r\n    }\r\n}", "// \r\n// root.scss\r\n//\r\n\r\n@mixin color-mode($mode: light, $root: false) {\r\n    @if $color-mode-type ==\"media-query\" {\r\n        @if $root ==true {\r\n            @media (prefers-color-scheme: $mode) {\r\n                :root {\r\n                    @content;\r\n                }\r\n            }\r\n        }\r\n\r\n        @else {\r\n            @media (prefers-color-scheme: $mode) {\r\n                @content;\r\n            }\r\n        }\r\n    }\r\n\r\n    @else {\r\n        [data-bs-theme=\"#{$mode}\"] {\r\n            @content;\r\n        }\r\n    }\r\n}\r\n\r\n//theme-light\r\n:root,\r\n[data-bs-theme=\"light\"] {\r\n    --#{$prefix}header-bg: #ffffff;\r\n    --#{$prefix}header-item-color: #{$header-item-color};\r\n    --#{$prefix}header-item-sub-color: #{$text-muted};\r\n    --#{$prefix}header-height: 70px;\r\n\r\n    --#{$prefix}rightbar-width: 280px;\r\n\r\n    --#{$prefix}boxed-layout-width: 1300px;\r\n\r\n    --#{$prefix}footer-height: 60px;\r\n    --#{$prefix}footer-bg: #ffffff;\r\n\r\n    [data-topbar=\"light\"]{\r\n        --#{$prefix}topbar-search-bg: #f1f5f7;\r\n    }\r\n\r\n    --#{$prefix}header-dark-bg:    #252b3b;\r\n\r\n    --#{$prefix}menu-item-color: #74788d;\r\n    --#{$prefix}menu-item-active-color: #5664d2;\r\n\r\n    // Topbar User\r\n    --#{$prefix}topbar-search-bg:           #f1f5f712;\r\n\r\n    // Horizontal nav\r\n    --#{$prefix}topnav-bg:                  #{$white};\r\n    --#{$prefix}topnav-item-color:          #{darken($gray-600, 10%)};\r\n    --#{$prefix}topnav-item-color-active: var(--#{$prefix}primary);\r\n\r\n    // twocolumn menu\r\n    --#{$prefix}twocolumn-menu-iconview-bg: #{$white};\r\n    --#{$prefix}twocolumn-menu-bg: #{$white};\r\n\r\n    --#{$prefix}header-item-color: #636e75;\r\n\r\n    --#{$prefix}boxed-body-bg: #dee7ec;\r\n\r\n    [data-topbar=\"dark\"] {\r\n        --#{$prefix}header-bg: #{$gray-800};\r\n        --#{$prefix}header-item-color: #{$gray-100};\r\n        --#{$prefix}header-dark-item-color: #e9ecef;\r\n    }\r\n\r\n    [data-topbar=\"colored\"] {\r\n         --#{$prefix}header-colored-bg: #556ee6;\r\n         --#{$prefix}topbar-search-bg:  rgba(241, 245, 247, 0.071);\r\n         --#{$prefix}header-dark-item-color: #e9ecef;\r\n    }\r\n\r\n    [data-sidebar=\"colored\"]{\r\n        --#{$prefix}topbar-search-bg: #f1f5f7;\r\n    }\r\n\r\n    //sidebar vertical light\r\n    --#{$prefix}sidebar-bg: #ffffff;\r\n    --#{$prefix}sidebar-menu-item-color: #74788d;\r\n    --#{$prefix}sidebar-menu-sub-item-color: #7c8a96;\r\n    --#{$prefix}sidebar-menu-item-icon-color: #505d69;\r\n    --#{$prefix}sidebar-menu-item-hover-color: #383c40;\r\n    --#{$prefix}sidebar-menu-item-active-color: #556ee6;\r\n    --#{$prefix}sidebar-width: 240px;\r\n    --#{$prefix}sidebar-collapsed-width: 70px;\r\n    --#{$prefix}sidebar-width-sm: 160px;\r\n\r\n    [data-sidebar=\"light\"]{\r\n        --#{$prefix}sidebar-bg: #ffffff;\r\n        --#{$prefix}sidebar-menu-item-color: #74788d;\r\n        --#{$prefix}sidebar-menu-sub-item-color: #7c8a96;\r\n        --#{$prefix}sidebar-menu-item-icon-color: #505d69;\r\n        --#{$prefix}sidebar-menu-item-hover-color: #383c40;\r\n        --#{$prefix}sidebar-menu-item-active-color: #556ee6;\r\n    }\r\n\r\n    [data-sidebar=\"dark\"] {\r\n        --#{$prefix}sidebar-dark-bg: #252b3b; //2c313a\r\n        --#{$prefix}sidebar-dark-menu-item-color: #8590a5;\r\n        --#{$prefix}sidebar-dark-menu-sub-item-color: #8590a5;\r\n        --#{$prefix}sidebar-dark-menu-item-icon-color: #8590a5;\r\n        --#{$prefix}sidebar-dark-menu-item-hover-color: #d7e4ec;\r\n        --#{$prefix}sidebar-dark-menu-item-active-color: #d7e4ec;\r\n        --#{$prefix}topbar-search-bg: #f1f5f7;\r\n    }\r\n\r\n}\r\n\r\n//theme dark\r\n@if $enable-dark-mode {\r\n    @include color-mode(dark, true) {\r\n        --#{$prefix}light: #{$light-dark};\r\n        --#{$prefix}light-rgb: #{to-rgb($light-dark)};\r\n        --#{$prefix}dark: #{$light-dark};\r\n        --#{$prefix}dark-rgb: #{to-rgb($light-dark)};\r\n\r\n        // header\r\n        --#{$prefix}header-bg:                      #272d3e;\r\n        --#{$prefix}header-dark-bg:                 #556ee6;\r\n        --#{$prefix}header-item-color:              #919bae;\r\n        --#{$prefix}topbar-search-bg:               #2b324412;\r\n\r\n        .table-light {\r\n             --#{$prefix}table-color: #{lighten($light-dark, 80%)};\r\n             --#{$prefix}table-bg: var(--#{$prefix}tertiary-bg);\r\n             --#{$prefix}table-border-color: var(--#{$prefix}border-color);\r\n             --#{$prefix}table-striped-bg: var(--#{$prefix}tertiary-bg);\r\n             --#{$prefix}table-striped-color: #{lighten($light-dark, 100%)};\r\n             --#{$prefix}table-active-bg: var(--#{$prefix}tertiary-bg);\r\n             --#{$prefix}table-active-color: #{lighten($light-dark, 100%)};\r\n             --#{$prefix}table-hover-bg: var(--#{$prefix}tertiary-bg);\r\n             --#{$prefix}table-hover-color: #{lighten($light-dark, 100%)};\r\n        }\r\n\r\n        &[data-topbar=\"colored\"]{\r\n            --#{$prefix}header-colored-bg: #556ee6;\r\n            --#{$prefix}topbar-search-bg:   #2b324412;\r\n        }\r\n\r\n        &[data-topbar=\"light\"] {\r\n            --#{$prefix}topbar-search-bg: #2b3244;\r\n        }\r\n\r\n        &[data-sidebar=\"colored\"] {\r\n            --#{$prefix}topbar-search-bg: #2b3244;\r\n        }\r\n\r\n        // horizontal nav\r\n        --#{$prefix}topnav-bg:              #282e3f;\r\n\r\n        --#{$prefix}header-item-sub-color: #{$text-muted};\r\n        // footer\r\n        --#{$prefix}footer-bg: #{$gray-800};\r\n\r\n        --#{$prefix}boxed-body-bg: #2d3447;\r\n\r\n        --#{$prefix}sidebar-bg: #ffffff;\r\n        --#{$prefix}sidebar-menu-item-color: #74788d;\r\n        --#{$prefix}sidebar-menu-sub-item-color: #7c8a96;\r\n        --#{$prefix}sidebar-menu-item-icon-color: #505d69;\r\n        --#{$prefix}sidebar-menu-item-hover-color: #d7e4ec;\r\n        --#{$prefix}sidebar-menu-item-active-color: #556ee6;\r\n\r\n        &[data-sidebar=\"dark\"] {\r\n        ---#{$prefix}sidebar-dark-bg: #252b3b; //2c313a\r\n        --#{$prefix}sidebar-dark-menu-item-color: #8590a5;\r\n        --#{$prefix}sidebar-dark-menu-sub-item-color: #8590a5;\r\n        --#{$prefix}sidebar-dark-menu-item-icon-color: #8590a5;\r\n        --#{$prefix}sidebar-dark-menu-item-hover-color: #d7e4ec;\r\n        --#{$prefix}sidebar-dark-menu-item-active-color: #d7e4ec;\r\n        --#{$prefix}topbar-search-bg: #2b3244;\r\n        }\r\n\r\n    }\r\n}", "\r\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n    position: relative;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n    -webkit-tap-highlight-color: transparent;\r\n  }\r\n  .waves-effect .waves-ripple {\r\n    position: absolute;\r\n    border-radius: 50%;\r\n    width: 100px;\r\n    height: 100px;\r\n    margin-top: -50px;\r\n    margin-left: -50px;\r\n    opacity: 0;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    -webkit-transition: all 0.5s ease-out;\r\n    -moz-transition: all 0.5s ease-out;\r\n    -o-transition: all 0.5s ease-out;\r\n    transition: all 0.5s ease-out;\r\n    -webkit-transition-property: -webkit-transform, opacity;\r\n    -moz-transition-property: -moz-transform, opacity;\r\n    -o-transition-property: -o-transform, opacity;\r\n    transition-property: transform, opacity;\r\n    -webkit-transform: scale(0) translate(0, 0);\r\n    -moz-transform: scale(0) translate(0, 0);\r\n    -ms-transform: scale(0) translate(0, 0);\r\n    -o-transform: scale(0) translate(0, 0);\r\n    transform: scale(0) translate(0, 0);\r\n    pointer-events: none;\r\n  }\r\n  .waves-effect.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n    background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  }\r\n  .waves-effect.waves-classic .waves-ripple {\r\n    background: rgba(0, 0, 0, 0.2);\r\n  }\r\n  .waves-effect.waves-classic.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n  }\r\n  .waves-notransition {\r\n    -webkit-transition: none !important;\r\n    -moz-transition: none !important;\r\n    -o-transition: none !important;\r\n    transition: none !important;\r\n  }\r\n  .waves-button,\r\n  .waves-circle {\r\n    -webkit-transform: translateZ(0);\r\n    -moz-transform: translateZ(0);\r\n    -ms-transform: translateZ(0);\r\n    -o-transform: translateZ(0);\r\n    transform: translateZ(0);\r\n    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n  }\r\n  .waves-button,\r\n  .waves-button:hover,\r\n  .waves-button:visited,\r\n  .waves-button-input {\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    cursor: pointer;\r\n    border: none;\r\n    outline: none;\r\n    color: inherit;\r\n    background-color: rgba(0, 0, 0, 0);\r\n    font-size: 1em;\r\n    line-height: 1em;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    z-index: 1;\r\n  }\r\n  .waves-button {\r\n    padding: 0.85em 1.1em;\r\n    border-radius: 0.2em;\r\n  }\r\n  .waves-button-input {\r\n    margin: 0;\r\n    padding: 0.85em 1.1em;\r\n  }\r\n  .waves-input-wrapper {\r\n    border-radius: 0.2em;\r\n    vertical-align: bottom;\r\n  }\r\n  .waves-input-wrapper.waves-button {\r\n    padding: 0;\r\n  }\r\n  .waves-input-wrapper .waves-button-input {\r\n    position: relative;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: 1;\r\n  }\r\n  .waves-circle {\r\n    text-align: center;\r\n    width: 2.5em;\r\n    height: 2.5em;\r\n    line-height: 2.5em;\r\n    border-radius: 50%;\r\n  }\r\n  .waves-float {\r\n    -webkit-mask-image: none;\r\n    -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    -webkit-transition: all 300ms;\r\n    -moz-transition: all 300ms;\r\n    -o-transition: all 300ms;\r\n    transition: all 300ms;\r\n  }\r\n  .waves-float:active {\r\n    -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n    box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  }\r\n  .waves-block {\r\n    display: block;\r\n  }\r\n\r\n.waves-effect.waves-light {\r\n    .waves-ripple {\r\n        background-color: rgba($white, 0.4);\r\n    }\r\n}\r\n\r\n.waves-effect.waves-primary {\r\n    .waves-ripple {\r\n        background-color: rgba($primary, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-success {\r\n    .waves-ripple {\r\n        background-color: rgba($success, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-info {\r\n    .waves-ripple {\r\n        background-color: rgba($info, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-warning {\r\n    .waves-ripple {\r\n        background-color: rgba($warning, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-danger {\r\n    .waves-ripple {\r\n        background-color: rgba($danger, 0.4);\r\n    }\r\n}", "//\n// avatar.scss\n//\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem;\n}\n\n.avatar-sm {\n  height: 3rem;\n  width: 3rem;\n}\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: $primary;\n  color: $white;\n  display: flex;\n  font-weight: $font-weight-medium;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n\n// avatar group\n.avatar-group {\n  padding-left: 12px;\n  display: flex;\n  flex-wrap: wrap;\n  .avatar-group-item {\n    margin-left: -12px;\n    border: 2px solid $card-bg;\n    border-radius: 50%;\n    transition: all 0.2s;\n    &:hover{\n      position: relative;\n      transform: translateY(-2px);\n    }\n  }\n}", "\r\n//\r\n// accordion.scss\r\n//\r\n\r\n.custom-accordion {\r\n    .card {\r\n        + .card {\r\n            margin-top: 0.5rem;\r\n        }\r\n    }\r\n\r\n    a {\r\n        &.collapsed {\r\n            i.accor-plus-icon {\r\n                &:before {\r\n                    content: \"\\F0415\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .card-header{\r\n        border-radius: 7px;\r\n    }\r\n}\r\n\r\n.custom-accordion-arrow{\r\n    .card{\r\n        border: 1px solid var(--#{$prefix}border-color);\r\n        box-shadow: none;\r\n    }\r\n    .card-header{\r\n        padding-left: 45px;\r\n        position: relative;\r\n\r\n        .accor-arrow-icon{\r\n            position: absolute;\r\n            display: inline-block;\r\n            width: 24px;\r\n            height: 24px;\r\n            line-height: 24px;\r\n            font-size: 16px;\r\n            background-color: $primary;\r\n            color: $white;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            left: 10px;\r\n            top: 50%;\r\n            transform: translateY(-50%);\r\n        }\r\n    }\r\n\r\n    a {\r\n        &.collapsed {\r\n            i.accor-arrow-icon {\r\n                &:before {\r\n                    content: \"\\F0142\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "//\n// _helper.scss\n//\n\n// Font Family\n.font-family-secondary {\n    font-family: $font-family-secondary;\n}\n\n.font-size-10 {\n    font-size: 10px !important;\n}\n\n.font-size-11 {\n    font-size: 11px !important;\n}\n\n.font-size-12 {\n    font-size: 12px !important;\n}\n\n.font-size-13 {\n    font-size: 13px !important;\n}\n\n.font-size-14 {\n    font-size: 14px !important;\n}\n\n.font-size-15 {\n    font-size: 15px !important;\n}\n\n.font-size-16 {\n    font-size: 16px !important;\n}\n\n.font-size-17 {\n    font-size: 17px !important;\n}\n\n.font-size-18 {\n    font-size: 18px !important;\n}\n\n.font-size-20 {\n    font-size: 20px !important;\n}\n\n.font-size-22 {\n    font-size: 22px !important;\n}\n\n.font-size-24 {\n    font-size: 24px !important;\n}\n\n\n\n// Social\n\n.social-list-item {\n    height: 2rem;\n    width: 2rem;\n    line-height: calc(2rem - 2px);\n    display: block;\n    border: 1px solid $gray-500;\n    border-radius: 50%;\n    color: $gray-500;\n    text-align: center;\n    transition: all 0.4s;\n\n    &:hover {\n        color: $gray-600;\n        background-color: $gray-200;\n    }\n}\n\n\n.w-xs {\n    min-width: 80px;\n}\n\n.w-sm {\n    min-width: 95px;\n}\n\n.w-md {\n    min-width: 110px;\n}\n\n.w-lg {\n    min-width: 140px;\n}\n\n.w-xl {\n    min-width: 160px;\n}\n\n// overlay\n\n.bg-overlay {\n    position: absolute;\n    height: 100%;\n    width: 100%;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    top: 0;\n    opacity: 0.7;\n    background-color: $black;\n}\n\n// flex-1\n\n.flex-1{\n    flex: 1;\n}\n\n\n\n// alert\n\n.alert-dismissible {\n    .btn-close {\n        font-size: 10px;\n        padding: $alert-padding-y * 1.4 $alert-padding-x;\n        background: transparent escape-svg($btn-close-bg-dark) center / $btn-close-width auto no-repeat;\n    }\n}", "// \r\n// preloader.scss\r\n//\r\n\r\n#preloader {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: $card-bg;\r\n    z-index: 9999;\r\n}\r\n\r\n#status {\r\n    position: absolute;\r\n    left: 50%;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    margin: -20px 0 0 -20px;\r\n}\r\n\r\n.spinner{\r\n    .spin-icon {\r\n        font-size: 56px;\r\n        color: $primary;\r\n        position: relative;\r\n        display: inline-block;\r\n        animation: spin 1.6s infinite linear;\r\n    }\r\n}\r\n\r\n@keyframes spin {\r\n    0% {\r\n      transform: rotate(0deg);\r\n    }\r\n    100% {\r\n      transform: rotate(359deg);\r\n    }\r\n}\r\n  ", "//\r\n// Forms.scss\r\n//\r\n\r\n\r\n// checkbox input right\r\n\r\n.form-check-right{\r\n  padding-left: 0;\r\n  display: inline-block;\r\n  padding-right: $form-check-padding-start;;\r\n  .form-check-input{\r\n    float: right;\r\n    margin-left: 0;\r\n    margin-right: $form-check-padding-start * -1;\r\n  }\r\n  .form-check-label{\r\n    display: block;\r\n  }\r\n}\r\n\r\n.form-check{\r\n  position: relative;\r\n  text-align: left /*rtl: right*/;\r\n}\r\n\r\n\r\n.form-check-label{\r\n  cursor: pointer;\r\n  margin-bottom: 0;\r\n}", "// \r\n// Widgets.scss\r\n// \r\n\r\n\r\n\r\n// activity widget\r\n\r\n.activity-wid{\r\n    margin-top: 8px;\r\n    margin-left: 16px;\r\n\r\n    .activity-list{\r\n        position: relative;\r\n        padding: 0 0 40px 30px;\r\n\r\n        &:before {\r\n            content: \"\";\r\n            border-left: 2px dashed rgba($primary,0.25);\r\n            position: absolute;\r\n            left: 0;\r\n            bottom: 0;\r\n            top: 32px\r\n        }\r\n        .activity-icon{\r\n            position: absolute;\r\n            left: -15px;\r\n            top: 0;\r\n            z-index: 9;\r\n        }\r\n\r\n        &:last-child{\r\n            padding-bottom: 0px;\r\n        }\r\n    }\r\n}", "// \r\n// _demos.scss\r\n// \r\n\r\n// Demo Only\r\n.button-items {\r\n    margin-left: -8px;\r\n    margin-bottom: -12px;\r\n    \r\n    .btn {\r\n        margin-bottom: 12px;\r\n        margin-left: 8px;\r\n    }\r\n}\r\n\r\n// Lightbox \r\n\r\n.mfp-popup-form {\r\n    max-width: 1140px;\r\n}\r\n\r\n// Modals\r\n\r\n.bs-example-modal {\r\n    position: relative;\r\n    top: auto;\r\n    right: auto;\r\n    bottom: auto;\r\n    left: auto;\r\n    z-index: 1;\r\n    display: block;\r\n  }\r\n\r\n\r\n// Icon demo ( Demo only )\r\n.icon-demo-content {\r\n  color: $gray-500;\r\n\r\n  i{\r\n    display: inline-block;\r\n    width: 40px;\r\n    height: 40px;\r\n    line-height: 36px;\r\n    font-size: 22px;\r\n    color: $gray-600;\r\n    border: 2px solid var(--#{$prefix}border-color);\r\n    border-radius: 4px;\r\n    transition: all 0.4s;\r\n    text-align: center;\r\n    margin-right: 16px;\r\n    vertical-align: middle;\r\n  }\r\n\r\n  .col-lg-4 {\r\n    margin-top: 24px;\r\n\r\n    &:hover {\r\n      i {\r\n        color: $white;\r\n        background-color: $primary;\r\n        border-color: $primary;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Grid\r\n\r\n.grid-structure {\r\n    .grid-container {\r\n        background-color: $gray-100;\r\n        margin-top: 10px;\r\n        font-size: .8rem;\r\n        font-weight: $font-weight-medium;\r\n        padding: 10px 20px;\r\n    }\r\n}\r\n\r\n\r\n// card radio\r\n\r\n.card-radio{\r\n  background-color: $card-bg;\r\n  border: 2px solid $card-border-color;\r\n  border-radius: $border-radius;\r\n  padding: 1rem;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  display: block;\r\n\r\n  &:hover{\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.card-radio-label{\r\n  display: block;\r\n}\r\n\r\n\r\n.card-radio-input{\r\n  display: none;\r\n  &:checked + .card-radio {\r\n    border-color: $primary !important;\r\n  }\r\n}\r\n\r\n.navs-carousel{\r\n  .owl-nav{\r\n      margin-top: 16px;\r\n      button{\r\n          width: 30px;\r\n          height: 30px;\r\n          line-height: 28px !important;\r\n          font-size: 20px !important;\r\n          border-radius: 50% !important;\r\n          background-color: rgba($primary, 0.25) !important;\r\n          color: $primary !important;\r\n          margin: 4px 8px !important;\r\n      }\r\n  }\r\n}", "// \r\n// print.scss\r\n//\r\n\r\n// Used invoice page\r\n@media print {\r\n    .vertical-menu,\r\n    .right-bar,\r\n    .page-title-box,\r\n    .navbar-header,\r\n    .footer {\r\n        display: none !important;\r\n    }\r\n    .card-body,\r\n    .main-content,\r\n    .right-bar,\r\n    .page-content,\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    .card{\r\n        border: 0;\r\n    }\r\n}", "[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\n  max-height: 100%; /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 6px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}", "// \r\n// calendar.scss\r\n//\r\n\r\n.fc-toolbar {\r\n  h2 {\r\n      font-size: 16px;\r\n      line-height: 30px;\r\n      text-transform: uppercase;\r\n  }\r\n}\r\n\r\n.fc {\r\n  th.fc-widget-header {\r\n      background: $light;\r\n      font-size: 13px;\r\n      line-height: 20px;\r\n      padding: 10px 0;\r\n      text-transform: uppercase;\r\n      font-weight: $font-weight-semibold;\r\n  }\r\n}\r\n\r\n.fc-unthemed{\r\n  .fc-content, \r\n  .fc-divider, \r\n  .fc-list-heading td, \r\n  .fc-list-view, \r\n  .fc-popover, \r\n  .fc-row, \r\n  tbody, \r\n  td, \r\n  th, \r\n  thead{\r\n      border-color: var(--#{$prefix}border-color);\r\n  }\r\n  td.fc-today {\r\n      background: lighten($gray-200, 4%);\r\n  }\r\n}\r\n\r\n.fc-button {\r\n  background: $card-bg;\r\n  border-color: var(--#{$prefix}border-color);\r\n  color: $gray-700;\r\n  text-transform: capitalize;\r\n  box-shadow: none;\r\n  padding: 6px 12px !important;\r\n  height: auto !important;\r\n}\r\n\r\n.fc-state-down,\r\n.fc-state-active,\r\n.fc-state-disabled {\r\n  background-color: $primary;\r\n  color: $white;\r\n  text-shadow: none;\r\n}\r\n\r\n.fc-event {\r\n  border-radius: 2px;\r\n  border: none;\r\n  cursor: move;\r\n  font-size: 0.8125rem;\r\n  margin: 5px 7px;\r\n  padding: 5px 5px;\r\n  text-align: center;\r\n}\r\n\r\n#external-events .external-event {\r\n  text-align: left!important;\r\n  padding: 8px 16px;\r\n}\r\n\r\n.fc-event, .fc-event-dot{\r\n  background-color: $primary;\r\n}\r\n\r\n.fc-event .fc-content{\r\n  color: $white;\r\n}\r\n\r\n.fc {\r\n  .table-bordered {\r\n    td, th {\r\n      border-color:var(--#{$prefix}border-color);\r\n    }\r\n  }\r\n  \r\n  .fc-toolbar {\r\n    @media (max-width: 575.98px) {\r\n      display: block;\r\n    }\r\n    \r\n      h2 {\r\n          font-size: 16px;\r\n          line-height: 30px;\r\n          text-transform: uppercase;\r\n      }\r\n\r\n      @media (max-width: 767.98px) {\r\n\r\n          .fc-left,\r\n          .fc-right,\r\n          .fc-center {\r\n              float: none;\r\n              display: block;\r\n              text-align: center;\r\n              clear: both;\r\n              margin: 10px 0;\r\n          }\r\n\r\n          >*>* {\r\n              float: none;\r\n          }\r\n\r\n          .fc-today-button {\r\n              display: none;\r\n          }\r\n      }\r\n      \r\n      .btn {\r\n          text-transform: capitalize;\r\n      }\r\n\r\n  }\r\n}\r\n.fc-bootstrap .fc-today.alert-info{\r\n  background-color: var(--#{$prefix}tertiary-bg);\r\n}\r\n\r\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {\r\n  background-color: $black !important;\r\n}\r\n\r\n// RTL\r\n[dir=\"rtl\"] .fc-header-toolbar {\r\n  direction: ltr !important;\r\n}\r\n\r\n[dir=\"rtl\"] .fc-toolbar>*>:not(:first-child) {\r\n  margin-left: .75em;\r\n}\r\n\r\n\r\n\r\n", "\r\n//\r\n// colorpicker.scss\r\n//\r\n\r\n.sp-container{\r\n  background-color: $dropdown-bg;\r\n  button{\r\n    padding: .25rem .5rem;\r\n      font-size: .71094rem;\r\n      border-radius: .2rem;\r\n      font-weight: 400;\r\n      color: $dark;\r\n  \r\n      &.sp-palette-toggle{\r\n        background-color: $light;\r\n      }\r\n      \r\n      &.sp-choose{\r\n        background-color: $success;\r\n        margin-left: 5px;\r\n        margin-right: 0;\r\n      }\r\n  }\r\n}\r\n\r\n.sp-palette-container{\r\n  border-right: 1px solid var(--#{$prefix}border-color);\r\n}\r\n\r\n.sp-input{\r\n  background-color: $input-bg;\r\n  border-color: $input-border-color !important;\r\n  color: $input-color;\r\n  &:focus{\r\n    outline: none;\r\n  }\r\n}\r\n\r\n\r\n[dir=\"rtl\"]{\r\n\r\n  .sp-alpha{\r\n    direction: rtl;\r\n  }\r\n\r\n  .sp-original-input-container {\r\n    .sp-add-on{\r\n      border-top-right-radius: 0!important;\r\n      border-bottom-right-radius: 0!important;\r\n      border-top-left-radius: 4px!important;\r\n      border-bottom-left-radius: 4px!important\r\n    }\r\n  } \r\n\r\n  input.spectrum.with-add-on{\r\n    border: 1px solid $input-border-color;\r\n    border-left: 0;\r\n    border-top-left-radius: 0;\r\n    border-bottom-left-radius: 0;\r\n    border-top-right-radius: $input-border-radius;\r\n    border-bottom-right-radius: $input-border-radius;\r\n\r\n  }\r\n}", "//\r\n// session-timeout.scss\r\n//\r\n\r\n#session-timeout-dialog {\r\n    .close {\r\n        display: none;\r\n    }\r\n\r\n    .countdown-holder {\r\n        color: $danger;\r\n        font-weight: $font-weight-medium;\r\n    }\r\n\r\n    .btn-default {\r\n        background-color: $white;\r\n        color: $danger;\r\n        box-shadow: none;\r\n    }\r\n}", "\r\n//\r\n// Round slider\r\n//\r\n\r\n.rs-control{\r\n  margin: 0px auto;\r\n}\r\n\r\n.rs-path-color{\r\n  background-color: $gray-300;\r\n}\r\n\r\n.rs-bg-color{\r\n  background-color: var(--#{$prefix}body-bg) !important;\r\n}\r\n\r\n.rs-border{\r\n  border-color: transparent;\r\n}\r\n\r\n.rs-handle{\r\n  background-color: $gray-700;\r\n}\r\n\r\n.rs-circle-border{\r\n  .rs-border{\r\n    border: 8px solid $gray-300;\r\n  }\r\n}\r\n\r\n.rs-disabled{\r\n  opacity: 1;\r\n}\r\n\r\n// Outer border\r\n\r\n.outer-border {\r\n  .rs-border{\r\n    border-width: 0px;\r\n    &.rs-outer  {\r\n      border: 14px solid $gray-300;\r\n  }\r\n  }\r\n  .rs-handle{\r\n    margin-left: 0 !important;\r\n  }\r\n  .rs-path-color{\r\n    background-color: transparent;\r\n  }\r\n}\r\n\r\n// Outer border dot\r\n\r\n.outer-border-dot {\r\n  .rs-border.rs-outer  {\r\n    border: 16px dotted;\r\n  }\r\n  .rs-handle{\r\n    margin-left: 0 !important;\r\n  }\r\n}\r\n\r\n@each $color,\r\n$value in $theme-colors {\r\n    .rs-range-#{$color} {\r\n        .rs-range-color{\r\n          background-color: $value;\r\n        }\r\n\r\n        .rs-handle-dot{\r\n          background-color: lighten(($value), 24%);\r\n          border-color: $value;\r\n          &:after{\r\n            background-color: $value;\r\n          }\r\n        }\r\n\r\n        &.rs-circle-border{\r\n          .rs-handle{\r\n            background-color: $value;\r\n          }\r\n        }\r\n\r\n        &.outer-border-dot {\r\n          .rs-border.rs-outer  {\r\n            border-color: lighten(($value), 24%);\r\n          }\r\n        }\r\n    }\r\n}\r\n\r\n// rs-handle-arrow\r\n\r\n.rs-handle-arrow{\r\n    .rs-handle  {\r\n      background-color: transparent;\r\n      border: 8px solid transparent;\r\n      border-right-color:$gray-700;\r\n      margin: -6px 0px 0px 14px !important;\r\n      border-width: 6px 104px 6px 4px;\r\n      &:before  {\r\n        display: block;\r\n        content: \" \";\r\n        position: absolute;\r\n        height: 22px;\r\n        width: 22px;\r\n        background:$gray-700;\r\n        right: -11px;\r\n        bottom: -11px;\r\n        border-radius: 100px;\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.rs-handle-arrow-dash{\r\n  .rs-handle  {\r\n    background-color: transparent;\r\n    border: 8px solid transparent;\r\n    border-right-color: $gray-700;\r\n    margin: -8px 0 0 14px !important;\r\n    &:before  {\r\n      display: block;\r\n      content: \" \";\r\n      position: absolute;\r\n      height: 12px;\r\n      width: 12px;\r\n      background: $gray-700;\r\n      right: -6px;\r\n      bottom: -6px;\r\n      border-radius: 100%;\r\n  }\r\n  &:after{\r\n    display: block;\r\n    content: \" \";\r\n    width: 80px;\r\n    position: absolute;\r\n    top: -1px;\r\n    right: 0px;\r\n    border-top: 2px dotted $gray-700\r\n  }\r\n}\r\n\r\n}", "//\r\n// Range slider\r\n//\r\n\r\n.irs {\r\n    font-family: $font-family-base;\r\n}\r\n\r\n.irs--round {\r\n\r\n    .irs-bar,\r\n    .irs-to,\r\n    .irs-from,\r\n    .irs-single {\r\n        background: $primary !important;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .irs-to,\r\n    .irs-from,\r\n    .irs-single {\r\n        &:before {\r\n            display: none;\r\n        }\r\n    }\r\n\r\n    .irs-line {\r\n        background: var(--#{$prefix}tertiary-bg);\r\n        border-color: var(--#{$prefix}border-color);\r\n    }\r\n\r\n    .irs-grid-text {\r\n        font-size: 11px;\r\n        color: $gray-500;\r\n    }\r\n\r\n    .irs-min,\r\n    .irs-max {\r\n        color: $gray-500;\r\n        background: var(--#{$prefix}tertiary-bg);\r\n        font-size: 11px;\r\n    }\r\n\r\n    .irs-handle {\r\n        border: 2px solid $primary;\r\n        width: 16px;\r\n        height: 16px;\r\n        top: 29px;\r\n        background-color: $card-bg !important;\r\n    }\r\n}", "\r\n//\r\n//  Sweetalert2\r\n//\r\n\r\n.swal2-container {\r\n  .swal2-title{\r\n    font-size: 24px;\r\n    font-weight: $font-weight-medium;\r\n  }  \r\n}\r\n\r\n.swal2-content{\r\n  font-size: 16px;\r\n}\r\n\r\n.swal2-icon{\r\n  &.swal2-question{\r\n    border-color: $info;\r\n    color: $info;\r\n  }\r\n  &.swal2-success {\r\n    [class^=swal2-success-line]{\r\n      background-color: $success;\r\n    }\r\n\r\n    .swal2-success-ring{\r\n      border-color: rgba($success, 0.3);\r\n    }\r\n  }\r\n  &.swal2-warning{\r\n    border-color: $warning;\r\n    color: $warning;\r\n  }\r\n}\r\n\r\n.swal2-styled{\r\n  &:focus{\r\n    box-shadow: none;\r\n  }\r\n}\r\n\r\n.swal2-progress-steps {\r\n  .swal2-progress-step{\r\n    background: $primary;\r\n    &.swal2-active-progress-step{\r\n      background: $primary;\r\n      &~.swal2-progress-step, &~.swal2-progress-step-line{\r\n        background: rgba($primary, 0.3);\r\n      }\r\n    }\r\n  }\r\n\r\n  .swal2-progress-step-line{\r\n    background: $primary;\r\n  }\r\n}\r\n\r\n.swal2-loader{\r\n  border-color: $primary transparent $primary transparent;\r\n}\r\n\r\n.swal2-modal{\r\n  background-color: var(--#{$prefix}secondary-bg) !important;\r\n  color: var(--#{$prefix}body-color) !important;\r\n}\r\n.swal2-content{\r\n  background-color: var(--#{$prefix}secondary-bg) !important;\r\n    color: var(--#{$prefix}secondary-color) !important;\r\n}\r\n\r\n\r\n.swal2-title{\r\n  color: var(--#{$prefix}secondary-color) !important;\r\n}", "\r\n//\r\n// Rating\r\n//\r\n\r\n.symbol{\r\n  border-color: $card-bg;\r\n}\r\n\r\n.rating-symbol-background, .rating-symbol-foreground {\r\n  font-size: 24px;\r\n}\r\n\r\n.rating-symbol-foreground {\r\n  top: 0px;\r\n}\r\n\r\n.rating-star{\r\n  > span{\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n\r\n    &.badge{\r\n      margin-left: 4px;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// toastr.scss\r\n//\r\n\r\n\r\n/* =============\r\n   Notification\r\n============= */\r\n#toast-container {\r\n    > div {\r\n        box-shadow: $box-shadow;\r\n        opacity: 1;\r\n        &:hover {\r\n            box-shadow: $box-shadow;\r\n            opacity: 0.9;\r\n        }\r\n    }\r\n\r\n    &.toast-top-full-width, &.toast-bottom-full-width{\r\n        > div{\r\n          min-width: 96%;\r\n          margin: 4px auto;\r\n        }\r\n      }\r\n\r\n}\r\n\r\n\r\n@each $color, $value in $theme-colors {\r\n    .toast-#{$color} {\r\n        border: 2px solid $value !important;\r\n        background-color: rgba(($value), 0.8) !important;\r\n    }\r\n}\r\n\r\n\r\n// for error\r\n\r\n.toast-error {\r\n    background-color: rgba($danger,0.8);\r\n    border: 2px solid $danger;\r\n}\r\n\r\n.toastr-options{\r\n    padding: 24px;\r\n    background-color: var(--#{$prefix}tertiary-bg);\r\n    margin-bottom: 0;\r\n    border: 1px solid var(--#{$prefix}border-color);\r\n}", "\r\n//\r\n// Parsley\r\n//\r\n\r\n.error {\r\n  color: $danger;\r\n}\r\n\r\n.parsley-error {\r\n  border-color: $danger;\r\n}\r\n\r\n.parsley-errors-list {\r\n  display: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  &.filled {\r\n    display: block;\r\n  }\r\n  > li {\r\n    font-size: 12px;\r\n    list-style: none;\r\n    color: $danger;\r\n    margin-top: 5px;\r\n  }\r\n}", "\r\n//\r\n// Select 2\r\n//\r\n\r\n.select2-container {\r\n  display: block;\r\n  .select2-selection--single {\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color;\r\n    height: 38px;\r\n    &:focus{\r\n      outline: none;\r\n    }\r\n\r\n    .select2-selection__rendered {\r\n      line-height: 36px;\r\n      padding-left: 12px;\r\n      color: $input-color;\r\n    }\r\n\r\n    .select2-selection__arrow {\r\n      height: 34px;\r\n      width: 34px;\r\n      right: 3px;\r\n\r\n      b{\r\n        border-color: $gray-500 transparent transparent transparent;\r\n        border-width: 6px 6px 0 6px;\r\n      }\r\n    }\r\n\r\n    .select2-selection__placeholder{\r\n      color: var(--#{$prefix}body-color);\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--open {\r\n  .select2-selection--single {\r\n\r\n    .select2-selection__arrow {\r\n\r\n      b{\r\n        border-color: transparent transparent $gray-500 transparent !important;\r\n        border-width: 0 6px 6px 6px !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default {\r\n  .select2-search--dropdown {\r\n      padding: 10px;\r\n      background-color: $dropdown-bg;\r\n      .select2-search__field {\r\n          border: 1px solid  $input-border-color;\r\n          background-color: $input-bg;\r\n          color: $gray-600;\r\n          outline: none;\r\n      }\r\n  }\r\n  .select2-results__option--highlighted[aria-selected] {\r\n      background-color: $primary;\r\n  }\r\n  .select2-results__option[aria-selected=true] {\r\n      background-color: $dropdown-link-active-bg;\r\n      color: $dropdown-link-active-color;\r\n      &:hover {\r\n          background-color: $primary;\r\n          color: $white;\r\n      }\r\n  }\r\n}\r\n\r\n.select2-results__option {\r\n  padding: 6px 12px;\r\n}\r\n\r\n.select2-dropdown {\r\n  border: 1px solid $dropdown-border-color;\r\n  background-color: $dropdown-bg;\r\n  box-shadow: $box-shadow;\r\n}\r\n\r\n.select2-search {\r\n  input{\r\n    border: 1px solid var(--#{$prefix}border-color);\r\n  }\r\n}\r\n\r\n.select2-container {\r\n  .select2-selection--multiple {\r\n    min-height: 38px;\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color !important;\r\n  \r\n    .select2-selection__rendered {\r\n      padding: 2px 10px;\r\n    }\r\n    .select2-search__field {\r\n      border: 0;\r\n      color: $input-color;\r\n      &::placeholder{\r\n          color: $input-color;\r\n      }\r\n  }\r\n    .select2-selection__choice {\r\n      background-color: var(--#{$prefix}tertiary-bg);\r\n      border: 1px solid var(--#{$prefix}border-color);\r\n      border-radius: 1px;\r\n      padding: 0 7px;\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default{\r\n  &.select2-container--focus {\r\n    .select2-selection--multiple{\r\n      border-color: var(--#{$prefix}border-color);\r\n    }\r\n  }\r\n\r\n  .select2-results__group{\r\n    font-weight: $font-weight-semibold;\r\n  }\r\n}\r\n\r\n// ajax select\r\n\r\n.select2-result-repository__avatar{\r\n    float: left;\r\n    width: 60px;\r\n    margin-right: 10px;\r\n  img{\r\n    width: 100%;\r\n    height: auto;\r\n    border-radius: 2px;\r\n  }\r\n}\r\n\r\n.select2-result-repository__statistics{\r\n  margin-top: 7px;\r\n}\r\n\r\n.select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  display: inline-block;\r\n  font-size: 11px;\r\n  margin-right: 1em;\r\n  color: $gray-500;\r\n\r\n  .fa{\r\n    margin-right: 4px;\r\n\r\n    &.fa-flash{\r\n      &::before{\r\n        content: \"\\f0e7\";\r\n        font-family: 'Font Awesome 5 Free';\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-results__option--highlighted{\r\n  .select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  color: rgba($white, 0.8);\r\n}\r\n}\r\n\r\n.select2-result-repository__meta{\r\n  overflow: hidden;\r\n}\r\n\r\n\r\n// templating-select\r\n\r\n.img-flag{\r\n  margin-right: 7px;\r\n  height: 15px;\r\n  width: 18px;\r\n}\r\n\r\n\r\n", "//\r\n//  Sweetalert2\r\n//\r\n\r\n/* CSS Switch */\r\ninput[switch] {\r\n  display: none;\r\n  + label {\r\n    font-size: 1em;\r\n    line-height: 1;\r\n    width: 56px;\r\n    height: 24px;\r\n    background-color: $gray-400;\r\n    background-image: none;\r\n    border-radius: 2rem;\r\n    padding: 0.16667rem;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    text-align: center;\r\n    position: relative;\r\n    font-weight: $font-weight-medium;\r\n    transition: all 0.1s ease-in-out;\r\n    &:before {\r\n      color: $dark;\r\n      content: attr(data-off-label);\r\n      display: block;\r\n      font-family: inherit;\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n      line-height: 21px;\r\n      position: absolute;\r\n      right: 1px;\r\n      margin: 3px;\r\n      top: -2px;\r\n      text-align: center;\r\n      min-width: 1.66667rem;\r\n      overflow: hidden;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n\r\n    &:after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 3px;\r\n      background-color: $gray-200;\r\n      box-shadow: none;\r\n      border-radius: 2rem;\r\n      height: 20px;\r\n      width: 20px;\r\n      top: 2px;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n  }\r\n\r\n  &:checked + label {\r\n    background-color: $primary;\r\n  }\r\n}\r\n\r\ninput[switch]:checked + label {\r\n  background-color: $primary;\r\n  &:before {\r\n    color: $white;\r\n    content: attr(data-on-label);\r\n    right: auto;\r\n    left: 3px;\r\n  }\r\n\r\n  &:after {\r\n    left: 33px;\r\n    background-color: $gray-200;\r\n  }\r\n}\r\n\r\ninput[switch=\"bool\"] + label {\r\n  background-color: $danger;\r\n}\r\ninput[switch=\"bool\"] + label:before,input[switch=\"bool\"]:checked + label:before,\r\ninput[switch=\"default\"]:checked + label:before{\r\n  color: $white;\r\n}\r\n\r\ninput[switch=\"bool\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"default\"]:checked + label {\r\n  background-color: #a2a2a2;\r\n}\r\n\r\ninput[switch=\"primary\"]:checked + label {\r\n  background-color: $primary;\r\n}\r\n\r\ninput[switch=\"success\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"info\"]:checked + label {\r\n  background-color: $info;\r\n}\r\n\r\ninput[switch=\"warning\"]:checked + label {\r\n  background-color: $warning;\r\n}\r\n\r\ninput[switch=\"danger\"]:checked + label {\r\n  background-color: $danger;\r\n}\r\n\r\ninput[switch=\"dark\"]:checked + label {\r\n  background-color: $dark;\r\n}\r\n\r\n.square-switch{\r\n  margin-right: 7px;\r\n  input[switch]+label, input[switch]+label:after{\r\n    border-radius: 4px;\r\n  }\r\n}", "\r\n//\r\n// Datepicker\r\n//\r\n\r\n.datepicker {\r\n  border: 1px solid $gray-100;\r\n  padding: 8px;\r\n  z-index: 999 !important;\r\n  table{\r\n    tr{\r\n      th{\r\n        font-weight: 500;\r\n      }\r\n      td{\r\n        &.active, &.active:hover, .active.disabled, &.active.disabled:hover,\r\n        &.today,  &.today:hover, &.today.disabled, &.today.disabled:hover, \r\n        &.selected, &.selected:hover, &.selected.disabled, &.selected.disabled:hover{\r\n          background-color: $primary !important;\r\n          background-image: none;\r\n          box-shadow: none;\r\n          color: $white !important;\r\n        }\r\n\r\n        &.day.focused,\r\n        &.day:hover,\r\n        span.focused,\r\n        span:hover {\r\n            background: $gray-200;\r\n        }\r\n\r\n        &.new,\r\n        &.old,\r\n        span.new,\r\n        span.old {\r\n            color: $gray-500;\r\n            opacity: 0.6;\r\n        }\r\n\r\n        &.range, &.range.disabled, &.range.disabled:hover, &.range:hover{\r\n            background-color: $gray-300;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-condensed{\r\n  >thead>tr>th, >tbody>tr>td {\r\n    padding: 7px;\r\n  }\r\n}", "\r\n//\r\n// Bootstrap touchspin\r\n//\r\n\r\n\r\n.bootstrap-touchspin{\r\n    &.input-group{\r\n      &>.input-group-prepend{\r\n        &>.btn, &>.input-group-text{\r\n        border-top-right-radius: 0;\r\n        border-bottom-right-radius: 0;\r\n        }\r\n      }\r\n    }\r\n  \r\n    &.input-group{\r\n      &>.input-group-append{\r\n        &>.btn, &>.input-group-text{\r\n          border-top-left-radius: 0;\r\n          border-bottom-left-radius: 0;\r\n        }\r\n      }\r\n    }\r\n  }", "//\r\n// datatable.scss\r\n\r\n\r\n.dataTables_wrapper {\r\n  &.container-fluid {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\ndiv.dataTables_wrapper {\r\n  div.dataTables_filter {\r\n    text-align: right;\r\n\r\n    @media (max-width: 767px) {\r\n      text-align: center;\r\n    }\r\n\r\n\r\n    input {\r\n      margin-left: 0.5em;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.datatable {\r\n  td {\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n  }\r\n}\r\n\r\ndiv.table-responsive>div.dataTables_wrapper>div.row>div[class^=\"col-\"] {\r\n  &:first-child {\r\n    padding-left: 0;\r\n  }\r\n\r\n  &:last-child {\r\n    padding-right: 0;\r\n  }\r\n}\r\n\r\n\r\ntable.dataTable {\r\n  border-collapse: collapse !important;\r\n  margin-bottom: 15px !important;\r\n\r\n  // Change icons view\r\n  thead {\r\n\r\n    .sorting,\r\n    .sorting_asc,\r\n    .sorting_desc,\r\n    .sorting_asc_disabled,\r\n    .sorting_desc_disabled {\r\n      &:before {\r\n        left: auto;\r\n        right: 0.5rem;\r\n        content: \"\\F0360\";\r\n        font-family: \"Material Design Icons\";\r\n        font-size: 1rem;\r\n        top: 9px;\r\n\r\n\r\n      }\r\n\r\n      &:after {\r\n        left: auto;\r\n        right: 0.5em;\r\n        content: \"\\F035D\";\r\n        font-family: \"Material Design Icons\";\r\n        top: 15px;\r\n        font-size: 1rem;\r\n      }\r\n    }\r\n\r\n    tr {\r\n\r\n      th,\r\n      td {\r\n\r\n        &.sorting_asc,\r\n        &.sorting_desc,\r\n        &.sorting {\r\n          padding-left: 12px;\r\n          padding-right: 30px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  tbody {\r\n    // Multi select table\r\n\r\n    > tr.selected, >tr>.selected {\r\n        background-color: rgba($primary,.2);\r\n        \r\n        td {\r\n            border-color: rgba($primary,.2);\r\n            color: $primary;\r\n        }\r\n    }\r\n    td {\r\n        &:focus {\r\n            outline: none !important;\r\n        }\r\n    }\r\n    // Key Tables\r\n    th.focus,td.focus{\r\n        outline: 2px solid $primary !important;\r\n        outline-offset: -1px;\r\n        background-color: rgba($primary, 0.15);\r\n    }\r\n}\r\n}\r\n\r\n.dataTables_info {\r\n  font-weight: $font-weight-semibold;\r\n}\r\n\r\n\r\n// Responsive data table\r\ntable.dataTable.dtr-inline.collapsed {\r\n  >tbody {\r\n    >tr[role=row] {\r\n\r\n      >td,\r\n      >th {\r\n        &:first-child {\r\n          &:before {\r\n            box-shadow: $box-shadow-lg;\r\n            background-color: $success;\r\n            bottom: auto;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    >tr.parent {\r\n\r\n      >td,\r\n      >th {\r\n        &:first-child {\r\n          &:before {\r\n            background-color: $danger;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Data Table copy button\r\ndiv.dt-button-info {\r\n  background-color: $primary;\r\n  border: none;\r\n  color: $white;\r\n  box-shadow: none;\r\n  border-radius: 3px;\r\n  text-align: center;\r\n  z-index: 21;\r\n\r\n  h2 {\r\n    border-bottom: none;\r\n    background-color: rgba($white, 0.2);\r\n    color: $white;\r\n  }\r\n}\r\n\r\n@include media-breakpoint-down(md) {\r\n\r\n  li.paginate_button.previous,\r\n  li.paginate_button.next {\r\n    display: inline-block;\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  li.paginate_button {\r\n    display: none;\r\n  }\r\n\r\n  .dataTables_paginate {\r\n    ul {\r\n      text-align: center;\r\n      display: block;\r\n      margin: $spacer 0 0 !important;\r\n    }\r\n  }\r\n\r\n  div.dt-buttons {\r\n    display: inline-table;\r\n    margin-bottom: $spacer;\r\n  }\r\n}\r\n\r\n// Active status\r\n.activate-select {\r\n  .sorting_1 {\r\n    background-color: $gray-100;\r\n  }\r\n}\r\n\r\n\r\n\r\n// datatable\r\n\r\n\r\n.table-bordered {\r\n  border: $table-border-width solid $table-border-color;\r\n}\r\n\r\n\r\n\r\n.table,\r\ntable {\r\n  &.dataTable {\r\n    &.dtr-inline.collapsed>tbody>tr>td {\r\n      position: relative;\r\n\r\n      &.dtr-control {\r\n        padding-left: 30px;\r\n\r\n        &:before {\r\n          top: 64%;\r\n          left: 5px;\r\n          height: 14px;\r\n          width: 14px;\r\n          margin-top: -14px;\r\n          display: block;\r\n          position: absolute;\r\n          color: $white;\r\n          border: 2px solid $white;\r\n          border-radius: 14px;\r\n          box-sizing: content-box;\r\n          text-align: center;\r\n          text-indent: 0 !important;\r\n          line-height: 12px;\r\n          content: '+';\r\n          background-color: $primary;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", "//\r\n// Form editors.scss\r\n//\r\n\r\n// Tinymce\r\n\r\n.tox-tinymce {\r\n    border: 2px solid var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.tox {\r\n    .tox-statusbar {\r\n        border-top: 1px solid var(--#{$prefix}border-color) !important;\r\n    }\r\n\r\n    .tox-menubar,\r\n    .tox-edit-area__iframe,\r\n    .tox-statusbar {\r\n        background-color: var(--#{$prefix}secondary-bg) !important;\r\n        background: none !important;\r\n    }\r\n\r\n    .tox-mbtn {\r\n        color: var(--#{$prefix}body-color) !important;\r\n\r\n        &:hover:not(:disabled):not(.tox-mbtn--active) {\r\n            background-color: var(--#{$prefix}tertiary-bg) !important;\r\n        }\r\n    }\r\n\r\n    .tox-tbtn {\r\n        &:hover {\r\n            background-color: var(--#{$prefix}tertiary-bg) !important;\r\n        }\r\n    }\r\n\r\n    .tox-toolbar__primary {\r\n        border-color: var(--#{$prefix}border-color) !important;\r\n    }\r\n\r\n    .tox-toolbar,\r\n    .tox-toolbar__overflow,\r\n    .tox-toolbar__primary {\r\n        background: var(--#{$prefix}tertiary-bg) !important;\r\n    }\r\n\r\n    .tox-tbtn {\r\n        color: var(--#{$prefix}body-color) !important;\r\n\r\n        svg {\r\n            fill: var(--#{$prefix}body-color) !important;\r\n        }\r\n    }\r\n\r\n    .tox-statusbar a,\r\n    .tox-statusbar__path-item,\r\n    .tox-statusbar__wordcount {\r\n        color: var(--#{$prefix}body-color) !important;\r\n    }\r\n\r\n    &:not([dir=\"rtl\"]) .tox-toolbar__group:not(:last-of-type) {\r\n        border-right: 1px solid var(--#{$prefix}border-color) !important;\r\n    }\r\n}\r\n.tox-tinymce-aux {\r\n    z-index: 1050 !important;\r\n}\r\n\r\n.tox .tox-toolbar-overlord {\r\n    background-color: var(--#{$prefix}tertiary-bg) !important;\r\n}\r\n", "\r\n//\r\n// Form-Upload\r\n//\r\n\r\n/* Dropzone */\r\n.dropzone {\r\n  min-height: 230px;\r\n  border: 2px dashed var(--#{$prefix}border-color);\r\n  background: $card-bg;\r\n  border-radius: 6px;\r\n\r\n  .dz-message {\r\n    font-size: 24px;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.sp-colorize-container{\r\n  border: none !important;\r\n}", "//\r\n// Form Wizard\r\n//\r\n\r\n// twitter-bs-wizard\r\n\r\n.twitter-bs-wizard {\r\n\r\n    .twitter-bs-wizard-nav {\r\n        position: relative;\r\n\r\n        &:before {\r\n            content: \"\";\r\n            width: 100%;\r\n            height: 2px;\r\n            background-color:var(--#{$prefix}border-color);\r\n            position: absolute;\r\n            left: 0;\r\n            top: 26px;\r\n        }\r\n\r\n        .step-number {\r\n            display: inline-block;\r\n            width: 38px;\r\n            height: 38px;\r\n            line-height: 34px;\r\n            border: 2px solid $primary;\r\n            color: $primary;\r\n            text-align: center;\r\n            border-radius: 50%;\r\n            position: relative;\r\n            background-color: $card-bg;\r\n\r\n            @media (max-width: 991.98px) {\r\n                display: block;\r\n                margin: 0 auto 8px !important;\r\n            }\r\n        }\r\n\r\n        .nav-link {\r\n            .step-title {\r\n                display: block;\r\n                margin-top: 8px;\r\n                font-weight: $font-weight-bold;\r\n\r\n                @media (max-width: 575.98px) {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            &.active {\r\n                background-color: transparent;\r\n                color: var(--#{$prefix}body-color);\r\n\r\n                .step-number {\r\n                    background-color: $primary;\r\n                    color: $white;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .twitter-bs-wizard-pager-link {\r\n        padding-top: 24px;\r\n        padding-left: 0;\r\n        list-style: none;\r\n        margin-bottom: 0;\r\n\r\n        li {\r\n            display: inline-block;\r\n\r\n            a {\r\n                display: inline-block;\r\n                padding: .47rem .75rem;\r\n                background-color: $primary;\r\n                color: $white;\r\n                border-radius: .25rem;\r\n            }\r\n\r\n            &.disabled {\r\n                a {\r\n                    cursor: not-allowed;\r\n                    background-color: lighten($primary, 8%);\r\n                }\r\n            }\r\n\r\n            &.next {\r\n                float: right;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.twitter-bs-wizard-tab-content {\r\n    padding-top: 24px;\r\n    min-height: 262px;\r\n}", "\r\n//\r\n// Responsive Table\r\n//\r\n\r\n.table-rep-plugin {\r\n  .btn-toolbar {\r\n    display: block;\r\n  }\r\n  .table-responsive {\r\n    border: none !important;\r\n  }\r\n  .btn-group{\r\n    .btn-default {\r\n      background-color: $light;\r\n      color: $dark;\r\n      border: 1px solid darken($light, 2%);\r\n      &.btn-primary {\r\n          background-color: $primary;\r\n          border-color: $primary;\r\n          color: $white;\r\n      }\r\n  }\r\n    &.pull-right {\r\n      float: right;\r\n      .dropdown-menu {\r\n        right: 0;\r\n        transform: none !important;\r\n        top: 100% !important;\r\n      }\r\n    }\r\n  }\r\n  tbody {\r\n    th {\r\n      font-size: 14px;\r\n      font-weight: normal;\r\n    }\r\n  }\r\n\r\n  .checkbox-row {\r\n    padding-left: 40px;\r\n    color: $dropdown-color !important;\r\n\r\n    &:hover{\r\n      background-color: lighten($gray-200, 2%) !important;\r\n    }\r\n\r\n    label {\r\n      display: inline-block;\r\n      padding-left: 5px;\r\n      position: relative;\r\n      &::before {\r\n        -o-transition: 0.3s ease-in-out;\r\n        -webkit-transition: 0.3s ease-in-out;\r\n        background-color: $white;\r\n        border-radius: 3px;\r\n        border: 1px solid $gray-300;\r\n        content: \"\";\r\n        display: inline-block;\r\n        height: 17px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        position: absolute;\r\n        transition: 0.3s ease-in-out;\r\n        width: 17px;\r\n        outline: none !important;\r\n      }\r\n      &::after {\r\n        color: $gray-200;\r\n        display: inline-block;\r\n        font-size: 11px;\r\n        height: 16px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        padding-left: 3px;\r\n        padding-top: 1px;\r\n        position: absolute;\r\n        top: -1px;\r\n        width: 16px;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"] {\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      z-index: 1;\r\n      outline: none !important;\r\n\r\n      &:disabled + label {\r\n        opacity: 0.65;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:focus + label {\r\n      &::before {\r\n        outline-offset: -2px;\r\n        outline: none;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::after {\r\n        content: \"\\f00c\";\r\n        font-family: 'Font Awesome 5 Free';\r\n        font-weight: 900;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:disabled + label {\r\n      &::before {\r\n        background-color: $gray-100;\r\n        cursor: not-allowed;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::before {\r\n        background-color: $primary;\r\n        border-color: $primary;\r\n      }\r\n      &::after {\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  .fixed-solution {\r\n    .sticky-table-header{\r\n      top: $header-height !important;\r\n      background-color: $primary;\r\n      table{\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n  @media (min-width: 992px) {\r\n    .fixed-solution {\r\n      .sticky-table-header{\r\n        top: $header-height + 50px !important;;\r\n      }\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Table editable\r\n//\r\n\r\n.table-edits{\r\n  input, select{\r\n    height: $input-height-sm;\r\n    padding: $input-padding-y-sm $input-padding-x-sm;\r\n    border: 1px solid $input-border-color;\r\n    background-color: $input-bg;\r\n    color: $input-color;\r\n    border-radius: $input-border-radius;\r\n    &:focus{\r\n      outline: none;\r\n      border-color: $input-focus-border-color;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// apexcharts.scss\r\n//\r\n.apex-charts {\r\n    min-height: 10px !important;\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n    .apexcharts-canvas {\r\n        margin: 0 auto;\r\n    }\r\n}\r\n\r\n.apexcharts-tooltip{\r\n    background-color: var(--#{$prefix}card-bg) !important;\r\n    border: 1px solid var(--#{$prefix}border-color) !important;\r\n}\r\n.apexcharts-tooltip-title{\r\n    background-color: var(--#{$prefix}body-bg) !important;\r\n    border-bottom: 1px solid var(--#{$prefix}border-color) !important;\r\n}\r\n.apexcharts-tooltip-title,\r\n.apexcharts-tooltip-text {\r\n    font-family: $font-family-base !important;\r\n}\r\n\r\n.apexcharts-legend-series {\r\n    font-weight: $font-weight-medium;\r\n}\r\n\r\n.apexcharts-gridline {\r\n    pointer-events: none;\r\n    stroke: $apex-grid-color;\r\n}\r\n\r\n.apexcharts-legend-text {\r\n    color: $gray-600 !important;\r\n    font-family: $font-family-base !important;\r\n    font-size: 13px !important;\r\n}\r\n\r\n.apexcharts-pie-label {\r\n    fill: $white !important;\r\n}\r\n\r\n.apexcharts-yaxis,\r\n.apexcharts-xaxis {\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n}\r\n\r\n.apexcharts-grid-row{\r\n    stroke: var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.apexcharts-gridline{\r\n    stroke: var(--#{$prefix}border-color) !important;\r\n}", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue:    #0d6efd !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #d63384 !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #198754 !default;\n$teal:    #20c997 !default;\n$cyan:    #0dcaf0 !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"black\":      $black,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $black !default;\n$color-contrast-light:     $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n) !default;\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n) !default;\n\n$purples: (\n  \"purple-100\": $purple-100,\n  \"purple-200\": $purple-200,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n) !default;\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n) !default;\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n) !default;\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n) !default;\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n) !default;\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n) !default;\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n) !default;\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n) !default;\n// fusv-enable\n\n// scss-docs-start theme-color-variables\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-900 !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-text-variables\n$primary-text-emphasis:   shade-color($primary, 60%) !default;\n$secondary-text-emphasis: shade-color($secondary, 60%) !default;\n$success-text-emphasis:   shade-color($success, 60%) !default;\n$info-text-emphasis:      shade-color($info, 60%) !default;\n$warning-text-emphasis:   shade-color($warning, 60%) !default;\n$danger-text-emphasis:    shade-color($danger, 60%) !default;\n$light-text-emphasis:     $gray-700 !default;\n$dark-text-emphasis:      $gray-700 !default;\n// scss-docs-end theme-text-variables\n\n// scss-docs-start theme-bg-subtle-variables\n$primary-bg-subtle:       tint-color($primary, 80%) !default;\n$secondary-bg-subtle:     tint-color($secondary, 80%) !default;\n$success-bg-subtle:       tint-color($success, 80%) !default;\n$info-bg-subtle:          tint-color($info, 80%) !default;\n$warning-bg-subtle:       tint-color($warning, 80%) !default;\n$danger-bg-subtle:        tint-color($danger, 80%) !default;\n$light-bg-subtle:         mix($gray-100, $white) !default;\n$dark-bg-subtle:          $gray-400 !default;\n// scss-docs-end theme-bg-subtle-variables\n\n// scss-docs-start theme-border-subtle-variables\n$primary-border-subtle:   tint-color($primary, 60%) !default;\n$secondary-border-subtle: tint-color($secondary, 60%) !default;\n$success-border-subtle:   tint-color($success, 60%) !default;\n$info-border-subtle:      tint-color($info, 60%) !default;\n$warning-border-subtle:   tint-color($warning, 60%) !default;\n$danger-border-subtle:    tint-color($danger, 60%) !default;\n$light-border-subtle:     $gray-200 !default;\n$dark-border-subtle:      $gray-500 !default;\n// scss-docs-end theme-border-subtle-variables\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              true !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-container-classes:    true !default;\n$enable-cssgrid:              false !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  true !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities:  true !default;\n\n$enable-dark-mode:            true !default;\n$color-mode-type:             data !default; // `data` or `media-query`\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\n$prefix:                      $variable-prefix !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem !default;\n$spacers: (\n  0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-text-align:           null !default;\n$body-color:                $gray-900 !default;\n$body-bg:                   $white !default;\n\n$body-secondary-color:      rgba($body-color, .75) !default;\n$body-secondary-bg:         $gray-200 !default;\n\n$body-tertiary-color:       rgba($body-color, .5) !default;\n$body-tertiary-bg:          $gray-100 !default;\n\n$body-emphasis-color:       $black !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $primary !default;\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Icon links\n// scss-docs-start icon-link-variables\n$icon-link-gap:               .375rem !default;\n$icon-link-underline-offset:  .25em !default;\n$icon-link-icon-size:         1em !default;\n$icon-link-icon-transition:   .2s ease-in-out transform !default;\n$icon-link-icon-transform:    translate3d(.25em, 0, 0) !default;\n// scss-docs-end icon-link-variables\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           1.5rem !default;\n$grid-row-columns:            6 !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                1px !default;\n$border-widths: (\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n) !default;\n$border-style:                solid !default;\n$border-color:                $gray-300 !default;\n$border-color-translucent:    rgba($black, .175) !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .375rem !default;\n$border-radius-sm:            .25rem !default;\n$border-radius-lg:            .5rem !default;\n$border-radius-xl:            1rem !default;\n$border-radius-xxl:           2rem !default;\n$border-radius-pill:          50rem !default;\n// scss-docs-end border-radius-variables\n// fusv-disable\n$border-radius-2xl:           $border-radius-xxl !default; // Deprecated in v5.3.0\n// fusv-enable\n\n// scss-docs-start box-shadow-variables\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075) !default;\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $white !default;\n$component-active-bg:         $primary !default;\n\n// scss-docs-start focus-ring-variables\n$focus-ring-width:      .25rem !default;\n$focus-ring-opacity:    .25 !default;\n$focus-ring-color:      rgba($primary, $focus-ring-opacity) !default;\n$focus-ring-blur:       0 !default;\n$focus-ring-box-shadow: 0 0 $focus-ring-blur $focus-ring-width $focus-ring-color !default;\n// scss-docs-end focus-ring-variables\n\n// scss-docs-start caret-variables\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n// scss-docs-end caret-variables\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease !default;\n$transition-collapse-width:   width .35s ease !default;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$prefix}font-monospace) !default;\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\n// $font-size-base affects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;\n$font-size-lg:                $font-size-base * 1.25 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-medium:          500 !default;\n$font-weight-semibold:        600 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n$line-height-base:            1.5 !default;\n$line-height-sm:              1.25 !default;\n$line-height-lg:              2 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer * .5 !default;\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n) !default;\n\n$display-font-family: null !default;\n$display-font-style:  null !default;\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             .875em !default;\n\n$sub-sup-font-size:           .75em !default;\n\n// fusv-disable\n$text-muted:                  var(--#{$prefix}secondary-color) !default; // Deprecated in 5.3.0\n// fusv-enable\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n$blockquote-footer-color:     $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n\n// fusv-disable\n$hr-bg-color:                 null !default; // Deprecated in v5.2.0\n$hr-height:                   null !default; // Deprecated in v5.2.0\n// fusv-enable\n\n$hr-border-color:             null !default; // Allows for inherited colors\n$hr-border-width:             var(--#{$prefix}border-width) !default;\n$hr-opacity:                  .25 !default;\n\n// scss-docs-start vr-variables\n$vr-border-width:             var(--#{$prefix}border-width) !default;\n// scss-docs-end vr-variables\n\n$legend-margin-bottom:        .5rem !default;\n$legend-font-size:            1.5rem !default;\n$legend-font-weight:          null !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-padding:                .1875em !default;\n$mark-color:                  $body-color !default;\n$mark-bg:                     $yellow-100 !default;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .5rem !default;\n$table-cell-padding-x:        .5rem !default;\n$table-cell-padding-y-sm:     .25rem !default;\n$table-cell-padding-x-sm:     .25rem !default;\n\n$table-cell-vertical-align:   top !default;\n\n$table-color:                 var(--#{$prefix}emphasis-color) !default;\n$table-bg:                    var(--#{$prefix}body-bg) !default;\n$table-accent-bg:             transparent !default;\n\n$table-th-font-weight:        null !default;\n\n$table-striped-color:         $table-color !default;\n$table-striped-bg-factor:     .05 !default;\n$table-striped-bg:            rgba(var(--#{$prefix}emphasis-color-rgb), $table-striped-bg-factor) !default;\n\n$table-active-color:          $table-color !default;\n$table-active-bg-factor:      .1 !default;\n$table-active-bg:             rgba(var(--#{$prefix}emphasis-color-rgb), $table-active-bg-factor) !default;\n\n$table-hover-color:           $table-color !default;\n$table-hover-bg-factor:       .075 !default;\n$table-hover-bg:              rgba(var(--#{$prefix}emphasis-color-rgb), $table-hover-bg-factor) !default;\n\n$table-border-factor:         .2 !default;\n$table-border-width:          var(--#{$prefix}border-width) !default;\n$table-border-color:          var(--#{$prefix}border-color) !default;\n\n$table-striped-order:         odd !default;\n$table-striped-columns-order: even !default;\n\n$table-group-separator-color: currentcolor !default;\n\n$table-caption-color:         var(--#{$prefix}secondary-color) !default;\n\n$table-bg-scale:              -80% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:         $focus-ring-width !default;\n$input-btn-focus-color-opacity: $focus-ring-opacity !default;\n$input-btn-focus-color:         $focus-ring-color !default;\n$input-btn-focus-blur:          $focus-ring-blur !default;\n$input-btn-focus-box-shadow:    $focus-ring-box-shadow !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      var(--#{$prefix}border-width) !default;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-color:                   var(--#{$prefix}body-color) !default;\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color:              var(--#{$prefix}link-color) !default;\n$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;\n$btn-link-disabled-color:     $gray-600 !default;\n$btn-link-focus-shadow-rgb:   to-rgb(mix(color-contrast($link-color), $link-color, 15%)) !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           var(--#{$prefix}border-radius) !default;\n$btn-border-radius-sm:        var(--#{$prefix}border-radius-sm) !default;\n$btn-border-radius-lg:        var(--#{$prefix}border-radius-lg) !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$btn-hover-bg-shade-amount:       15% !default;\n$btn-hover-bg-tint-amount:        15% !default;\n$btn-hover-border-shade-amount:   20% !default;\n$btn-hover-border-tint-amount:    10% !default;\n$btn-active-bg-shade-amount:      20% !default;\n$btn-active-bg-tint-amount:       20% !default;\n$btn-active-border-shade-amount:  25% !default;\n$btn-active-border-tint-amount:   10% !default;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .25rem !default;\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 null !default;\n$form-text-color:                       var(--#{$prefix}secondary-color) !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem !default;\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                null !default;\n$form-label-color:                      null !default;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              var(--#{$prefix}body-bg) !default;\n$input-disabled-color:                  null !default;\n$input-disabled-bg:                     var(--#{$prefix}secondary-bg) !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           var(--#{$prefix}body-color) !default;\n$input-border-color:                    var(--#{$prefix}border-color) !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      var(--#{$prefix}box-shadow-inset) !default;\n\n$input-border-radius:                   var(--#{$prefix}border-radius) !default;\n$input-border-radius-sm:                var(--#{$prefix}border-radius-sm) !default;\n$input-border-radius-lg:                var(--#{$prefix}border-radius-lg) !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               var(--#{$prefix}secondary-color) !default;\n$input-plaintext-color:                 var(--#{$prefix}body-color) !default;\n\n$input-height-border:                   calc(#{$input-border-width} * 2) !default; // stylelint-disable-line function-disallowed-list\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * .5) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-color-width:                      3rem !default;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\n$form-check-padding-start:                $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   null !default;\n\n$form-check-input-active-filter:          brightness(90%) !default;\n\n$form-check-input-bg:                     $input-bg !default;\n$form-check-input-border:                 var(--#{$prefix}border-width) solid var(--#{$prefix}border-color) !default;\n$form-check-input-border-radius:          .25em !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           $input-focus-border-color !default;\n$form-check-input-focus-box-shadow:       $focus-ring-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $component-active-color !default;\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        .5 !default;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\n\n$form-check-inline-margin-end:    1rem !default;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color:               rgba($black, .25) !default;\n$form-switch-width:               2em !default;\n$form-switch-padding-start:       $form-switch-width + .5em !default;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius:       $form-switch-width !default;\n$form-switch-transition:          background-position .15s ease-in-out !default;\n\n$form-switch-focus-color:         $input-focus-border-color !default;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color:       $component-active-color !default;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  var(--#{$prefix}tertiary-bg) !default;\n$input-group-addon-border-color:        $input-border-color !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-color:        null !default;\n$form-select-disabled-bg:           $input-disabled-bg !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x center !default;\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800 !default;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $input-border-radius !default;\n$form-select-box-shadow:          var(--#{$prefix}box-shadow-inset) !default;\n\n$form-select-focus-border-color:  $input-focus-border-color !default;\n$form-select-focus-width:         $input-focus-width !default;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n$form-select-border-radius-sm:    $input-border-radius-sm !default;\n\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n$form-select-border-radius-lg:    $input-border-radius-lg !default;\n\n$form-select-transition:          $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .5rem !default;\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             var(--#{$prefix}secondary-bg) !default;\n$form-range-track-border-radius:  1rem !default;\n$form-range-track-box-shadow:     var(--#{$prefix}box-shadow-inset) !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      $component-active-bg !default;\n$form-range-thumb-border:                  0 !default;\n$form-range-thumb-border-radius:           1rem !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg:             var(--#{$prefix}secondary-color) !default;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             var(--#{$prefix}tertiary-bg) !default;\n$form-file-button-hover-bg:       var(--#{$prefix}secondary-bg) !default;\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:                  add(3.5rem, $input-height-border) !default;\n$form-floating-line-height:             1.25 !default;\n$form-floating-padding-x:               $input-padding-x !default;\n$form-floating-padding-y:               1rem !default;\n$form-floating-input-padding-t:         1.625rem !default;\n$form-floating-input-padding-b:         .625rem !default;\n$form-floating-label-height:            1.5em !default;\n$form-floating-label-opacity:           .65 !default;\n$form-floating-label-transform:         scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-label-disabled-color:    $gray-600 !default;\n$form-floating-transition:              opacity .1s ease-in-out, transform .1s ease-in-out !default;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $form-text-font-size !default;\n$form-feedback-font-style:          $form-text-font-style !default;\n$form-feedback-valid-color:         $success !default;\n$form-feedback-invalid-color:       $danger !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-colors\n$form-valid-color:                  $form-feedback-valid-color !default;\n$form-valid-border-color:           $form-feedback-valid-color !default;\n$form-invalid-color:                $form-feedback-invalid-color !default;\n$form-invalid-border-color:         $form-feedback-invalid-color !default;\n// scss-docs-end form-validation-colors\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": var(--#{$prefix}form-valid-color),\n    \"icon\": $form-feedback-icon-valid,\n    \"tooltip-color\": #fff,\n    \"tooltip-bg-color\": var(--#{$prefix}success),\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}success-rgb), $input-btn-focus-color-opacity),\n    \"border-color\": var(--#{$prefix}form-valid-border-color),\n  ),\n  \"invalid\": (\n    \"color\": var(--#{$prefix}form-invalid-color),\n    \"icon\": $form-feedback-icon-invalid,\n    \"tooltip-color\": #fff,\n    \"tooltip-bg-color\": var(--#{$prefix}danger),\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}danger-rgb), $input-btn-focus-color-opacity),\n    \"border-color\": var(--#{$prefix}form-invalid-border-color),\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-offcanvas-backdrop:         1040 !default;\n$zindex-offcanvas:                  1045 !default;\n$zindex-modal-backdrop:             1050 !default;\n$zindex-modal:                      1055 !default;\n$zindex-popover:                    1070 !default;\n$zindex-tooltip:                    1080 !default;\n$zindex-toast:                      1090 !default;\n// scss-docs-end zindex-stack\n\n// scss-docs-start zindex-levels-map\n$zindex-levels: (\n  n1: -1,\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3\n) !default;\n// scss-docs-end zindex-levels-map\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              null !default;\n$nav-link-color:                    var(--#{$prefix}link-color) !default;\n$nav-link-hover-color:              var(--#{$prefix}link-hover-color) !default;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\n$nav-link-disabled-color:           var(--#{$prefix}secondary-color) !default;\n$nav-link-focus-box-shadow:         $focus-ring-box-shadow !default;\n\n$nav-tabs-border-color:             var(--#{$prefix}border-color) !default;\n$nav-tabs-border-width:             var(--#{$prefix}border-width) !default;\n$nav-tabs-border-radius:            var(--#{$prefix}border-radius) !default;\n$nav-tabs-link-hover-border-color:  var(--#{$prefix}secondary-bg) var(--#{$prefix}secondary-bg) $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        var(--#{$prefix}emphasis-color) !default;\n$nav-tabs-link-active-bg:           var(--#{$prefix}body-bg) !default;\n$nav-tabs-link-active-border-color: var(--#{$prefix}border-color) var(--#{$prefix}border-color) $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           var(--#{$prefix}border-radius) !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-underline-gap:                 1rem !default;\n$nav-underline-border-width:        .125rem !default;\n$nav-underline-link-active-color:   var(--#{$prefix}emphasis-color) !default;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                  $spacer * .5 !default;\n$navbar-padding-x:                  null !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\n$navbar-brand-margin-end:           1rem !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n$navbar-toggler-focus-width:        $btn-focus-width !default;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\n\n$navbar-light-color:                rgba(var(--#{$prefix}emphasis-color-rgb), .65) !default;\n$navbar-light-hover-color:          rgba(var(--#{$prefix}emphasis-color-rgb), .8) !default;\n$navbar-light-active-color:         rgba(var(--#{$prefix}emphasis-color-rgb), 1) !default;\n$navbar-light-disabled-color:       rgba(var(--#{$prefix}emphasis-color-rgb), .3) !default;\n$navbar-light-icon-color:           rgba($body-color, .75) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-icon-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba(var(--#{$prefix}emphasis-color-rgb), .15) !default;\n$navbar-light-brand-color:          $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:    $navbar-light-active-color !default;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-dark-variables\n$navbar-dark-color:                 rgba($white, .55) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-icon-color:            $navbar-dark-color !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-icon-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n$navbar-dark-brand-color:           $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:     $navbar-dark-active-color !default;\n// scss-docs-end navbar-dark-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                0 !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    var(--#{$prefix}body-color) !default;\n$dropdown-bg:                       var(--#{$prefix}body-bg) !default;\n$dropdown-border-color:             var(--#{$prefix}border-color-translucent) !default;\n$dropdown-border-radius:            var(--#{$prefix}border-radius) !default;\n$dropdown-border-width:             var(--#{$prefix}border-width) !default;\n$dropdown-inner-border-radius:      calc(#{$dropdown-border-radius} - #{$dropdown-border-width}) !default; // stylelint-disable-line function-disallowed-list\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer * .5 !default;\n$dropdown-box-shadow:               var(--#{$prefix}box-shadow) !default;\n\n$dropdown-link-color:               var(--#{$prefix}body-color) !default;\n$dropdown-link-hover-color:         $dropdown-link-color !default;\n$dropdown-link-hover-bg:            var(--#{$prefix}tertiary-bg) !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      var(--#{$prefix}tertiary-color) !default;\n\n$dropdown-item-padding-y:           $spacer * .25 !default;\n$dropdown-item-padding-x:           $spacer !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding-x:         $dropdown-item-padding-x !default;\n$dropdown-header-padding-y:         $dropdown-padding-y !default;\n// fusv-disable\n$dropdown-header-padding:           $dropdown-header-padding-y $dropdown-header-padding-x !default; // Deprecated in v5.2.0\n// fusv-enable\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:               $gray-300 !default;\n$dropdown-dark-bg:                  $gray-800 !default;\n$dropdown-dark-border-color:        $dropdown-border-color !default;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color:        $gray-500 !default;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              .375rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n\n$pagination-font-size:              $font-size-base !default;\n\n$pagination-color:                  var(--#{$prefix}link-color) !default;\n$pagination-bg:                     var(--#{$prefix}body-bg) !default;\n$pagination-border-radius:          var(--#{$prefix}border-radius) !default;\n$pagination-border-width:           var(--#{$prefix}border-width) !default;\n$pagination-margin-start:           calc(#{$pagination-border-width} * -1) !default; // stylelint-disable-line function-disallowed-list\n$pagination-border-color:           var(--#{$prefix}border-color) !default;\n\n$pagination-focus-color:            var(--#{$prefix}link-hover-color) !default;\n$pagination-focus-bg:               var(--#{$prefix}secondary-bg) !default;\n$pagination-focus-box-shadow:       $focus-ring-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            var(--#{$prefix}link-hover-color) !default;\n$pagination-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\n$pagination-hover-border-color:     var(--#{$prefix}border-color) !default; // Todo in v6: remove this?\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $component-active-bg !default;\n\n$pagination-disabled-color:         var(--#{$prefix}secondary-color) !default;\n$pagination-disabled-bg:            var(--#{$prefix}secondary-bg) !default;\n$pagination-disabled-border-color:  var(--#{$prefix}border-color) !default;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$pagination-border-radius-sm:       var(--#{$prefix}border-radius-sm) !default;\n$pagination-border-radius-lg:       var(--#{$prefix}border-radius-lg) !default;\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5 !default;\n$placeholder-opacity-min:           .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y:                     $spacer !default;\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer * .5 !default;\n$card-title-color:                  null !default;\n$card-subtitle-color:               null !default;\n$card-border-width:                 var(--#{$prefix}border-width) !default;\n$card-border-color:                 var(--#{$prefix}border-color-translucent) !default;\n$card-border-radius:                var(--#{$prefix}border-radius) !default;\n$card-box-shadow:                   null !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-y * .5 !default;\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       rgba(var(--#{$prefix}body-color-rgb), .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           var(--#{$prefix}body-bg) !default;\n$card-img-overlay-padding:          $spacer !default;\n$card-group-margin:                 $grid-gutter-width * .5 !default;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     1rem !default;\n$accordion-padding-x:                     1.25rem !default;\n$accordion-color:                         var(--#{$prefix}body-color) !default;\n$accordion-bg:                            var(--#{$prefix}body-bg) !default;\n$accordion-border-width:                  var(--#{$prefix}border-width) !default;\n$accordion-border-color:                  var(--#{$prefix}border-color) !default;\n$accordion-border-radius:                 var(--#{$prefix}border-radius) !default;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\n\n$accordion-body-padding-y:                $accordion-padding-y !default;\n$accordion-body-padding-x:                $accordion-padding-x !default;\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  var(--#{$prefix}body-color) !default;\n$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-active-bg:              var(--#{$prefix}primary-bg-subtle) !default;\n$accordion-button-active-color:           var(--#{$prefix}primary-text-emphasis) !default;\n\n// fusv-disable\n$accordion-button-focus-border-color:     $input-focus-border-color !default; // Deprecated in v5.3.3\n// fusv-enable\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\n\n$accordion-icon-width:                    1.25rem !default;\n$accordion-icon-color:                    $body-color !default;\n$accordion-icon-active-color:             $primary-text-emphasis !default;\n$accordion-icon-transition:               transform .2s ease-in-out !default;\n$accordion-icon-transform:                rotate(-180deg) !default;\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='#{$accordion-icon-color}' stroke-linecap='round' stroke-linejoin='round'><path d='M2 5L8 11L14 5'/></svg>\") !default;\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='#{$accordion-icon-active-color}' stroke-linecap='round' stroke-linejoin='round'><path d='M2 5L8 11L14 5'/></svg>\") !default;\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     var(--#{$prefix}body-bg) !default;\n$tooltip-bg:                        var(--#{$prefix}emphasis-color) !default;\n$tooltip-border-radius:             var(--#{$prefix}border-radius) !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 $spacer * .25 !default;\n$tooltip-padding-x:                 $spacer * .5 !default;\n$tooltip-margin:                    null !default; // TODO: remove this in v6\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n// fusv-disable\n$tooltip-arrow-color:               null !default; // Deprecated in Bootstrap 5.2.0 for CSS variables\n// fusv-enable\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   null !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n// scss-docs-end tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        var(--#{$prefix}body-bg) !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              var(--#{$prefix}border-width) !default;\n$popover-border-color:              var(--#{$prefix}border-color-translucent) !default;\n$popover-border-radius:             var(--#{$prefix}border-radius-lg) !default;\n$popover-inner-border-radius:       calc(#{$popover-border-radius} - #{$popover-border-width}) !default; // stylelint-disable-line function-disallowed-list\n$popover-box-shadow:                var(--#{$prefix}box-shadow) !default;\n\n$popover-header-font-size:          $font-size-base !default;\n$popover-header-bg:                 var(--#{$prefix}secondary-bg) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          $spacer !default;\n\n$popover-body-color:                var(--#{$prefix}body-color) !default;\n$popover-body-padding-y:            $spacer !default;\n$popover-body-padding-x:            $spacer !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n// scss-docs-end popover-variables\n\n// fusv-disable\n// Deprecated in Bootstrap 5.2.0 for CSS variables\n$popover-arrow-color:               $popover-bg !default;\n$popover-arrow-outer-color:         var(--#{$prefix}border-color-translucent) !default;\n// fusv-enable\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .5rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-border-width:                var(--#{$prefix}border-width) !default;\n$toast-border-color:                var(--#{$prefix}border-color-translucent) !default;\n$toast-border-radius:               var(--#{$prefix}border-radius) !default;\n$toast-box-shadow:                  var(--#{$prefix}box-shadow) !default;\n$toast-spacing:                     $container-padding-x !default;\n\n$toast-header-color:                var(--#{$prefix}secondary-color) !default;\n$toast-header-background-color:     rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-header-border-color:         $toast-border-color !default;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               var(--#{$prefix}border-radius) !default;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer !default;\n\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  var(--#{$prefix}body-bg) !default;\n$modal-content-border-color:        var(--#{$prefix}border-color-translucent) !default;\n$modal-content-border-width:        var(--#{$prefix}border-width) !default;\n$modal-content-border-radius:       var(--#{$prefix}border-radius-lg) !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       var(--#{$prefix}box-shadow-sm) !default;\n$modal-content-box-shadow-sm-up:    var(--#{$prefix}box-shadow) !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n\n$modal-header-border-color:         var(--#{$prefix}border-color) !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-header-padding-y:            $modal-inner-padding !default;\n$modal-header-padding-x:            $modal-inner-padding !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-footer-bg:                   null !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n\n$modal-sm:                          300px !default;\n$modal-md:                          500px !default;\n$modal-lg:                          800px !default;\n$modal-xl:                          1140px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:               $spacer !default;\n$alert-padding-x:               $spacer !default;\n$alert-margin-bottom:           1rem !default;\n$alert-border-radius:           var(--#{$prefix}border-radius) !default;\n$alert-link-font-weight:        $font-weight-bold !default;\n$alert-border-width:            var(--#{$prefix}border-width) !default;\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n// fusv-disable\n$alert-bg-scale:                -80% !default; // Deprecated in v5.2.0, to be removed in v6\n$alert-border-scale:            -70% !default; // Deprecated in v5.2.0, to be removed in v6\n$alert-color-scale:             40% !default; // Deprecated in v5.2.0, to be removed in v6\n// fusv-enable\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       var(--#{$prefix}secondary-bg) !default;\n$progress-border-radius:            var(--#{$prefix}border-radius) !default;\n$progress-box-shadow:               var(--#{$prefix}box-shadow-inset) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   $primary !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color:                  var(--#{$prefix}body-color) !default;\n$list-group-bg:                     var(--#{$prefix}body-bg) !default;\n$list-group-border-color:           var(--#{$prefix}border-color) !default;\n$list-group-border-width:           var(--#{$prefix}border-width) !default;\n$list-group-border-radius:          var(--#{$prefix}border-radius) !default;\n\n$list-group-item-padding-y:         $spacer * .5 !default;\n$list-group-item-padding-x:         $spacer !default;\n// fusv-disable\n$list-group-item-bg-scale:          -80% !default; // Deprecated in v5.3.0\n$list-group-item-color-scale:       40% !default; // Deprecated in v5.3.0\n// fusv-enable\n\n$list-group-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         var(--#{$prefix}secondary-color) !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           var(--#{$prefix}secondary-color) !default;\n$list-group-action-hover-color:     var(--#{$prefix}emphasis-color) !default;\n\n$list-group-action-active-color:    var(--#{$prefix}body-color) !default;\n$list-group-action-active-bg:       var(--#{$prefix}secondary-bg) !default;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      var(--#{$prefix}body-bg) !default;\n$thumbnail-border-width:            var(--#{$prefix}border-width) !default;\n$thumbnail-border-color:            var(--#{$prefix}border-color) !default;\n$thumbnail-border-radius:           var(--#{$prefix}border-radius) !default;\n$thumbnail-box-shadow:              var(--#{$prefix}box-shadow-sm) !default;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              var(--#{$prefix}secondary-color) !default;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              null !default;\n$breadcrumb-padding-y:              0 !default;\n$breadcrumb-padding-x:              0 !default;\n$breadcrumb-item-padding-x:         .5rem !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-bg:                     null !default;\n$breadcrumb-divider-color:          var(--#{$prefix}secondary-color) !default;\n$breadcrumb-active-color:           var(--#{$prefix}secondary-color) !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\n$breadcrumb-border-radius:          null !default;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-opacity:         .5 !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  1 !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n$carousel-caption-padding-y:         1.25rem !default;\n$carousel-caption-spacer:            1.25rem !default;\n\n$carousel-control-icon-width:        2rem !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n// scss-docs-end carousel-variables\n\n// scss-docs-start carousel-dark-variables\n$carousel-dark-indicator-active-bg:  $black !default;\n$carousel-dark-caption-color:        $black !default;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\n// scss-docs-end carousel-dark-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width:           2rem !default;\n$spinner-height:          $spinner-width !default;\n$spinner-vertical-align:  -.125em !default;\n$spinner-border-width:    .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:            1em !default;\n$btn-close-height:           $btn-close-width !default;\n$btn-close-padding-x:        .25em !default;\n$btn-close-padding-y:        $btn-close-padding-x !default;\n$btn-close-color:            $black !default;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow:     $focus-ring-box-shadow !default;\n$btn-close-opacity:          .5 !default;\n$btn-close-hover-opacity:    .75 !default;\n$btn-close-focus-opacity:    1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding !default;\n$offcanvas-padding-x:               $modal-inner-padding !default;\n$offcanvas-horizontal-width:        400px !default;\n$offcanvas-vertical-height:         30vh !default;\n$offcanvas-transition-duration:     .3s !default;\n$offcanvas-border-color:            $modal-content-border-color !default;\n$offcanvas-border-width:            $modal-content-border-width !default;\n$offcanvas-title-line-height:       $modal-title-line-height !default;\n$offcanvas-bg-color:                var(--#{$prefix}body-bg) !default;\n$offcanvas-color:                   var(--#{$prefix}body-color) !default;\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size:                    $small-font-size !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .1875rem !default;\n$kbd-padding-x:                     .375rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         var(--#{$prefix}body-bg) !default;\n$kbd-bg:                            var(--#{$prefix}body-color) !default;\n$nested-kbd-font-weight:            null !default; // Deprecated in v5.2.0, removing in v6\n\n$pre-color:                         null !default;\n\n@import \"variables-dark\"; // TODO: can be removed safely in v6, only here to avoid breaking changes in v5.3\n", "\r\n\r\n/* Flot chart */\r\n.flot-charts-height {\r\n  height: 320px;\r\n}\r\n\r\n.flotTip {\r\n  padding: 8px 12px;\r\n  background-color: rgba($dark, 0.9);\r\n  z-index: 100;\r\n  color: $gray-100;\r\n  box-shadow: $box-shadow;\r\n  border-radius: 4px;\r\n}\r\n\r\n.legendLabel{\r\n  color: $gray-500;\r\n}", "//\r\n// sparkline.scss\r\n//\r\n\r\n.jqstooltip {\r\n  box-sizing: content-box;\r\n  width: auto !important;\r\n  height: auto !important;\r\n  background-color: $gray-800 !important;\r\n  box-shadow: $box-shadow-lg;\r\n  padding: 5px 10px !important;\r\n  border-radius: 3px;\r\n  border-color: $gray-900 !important;\r\n}\r\n\r\n.jqsfield {\r\n  color: $gray-200 !important;\r\n  font-size: 12px !important;\r\n  line-height: 18px !important;\r\n  font-family: $font-family-base !important;\r\n  font-weight: $font-weight-medium !important;\r\n}\r\n", "\r\n//\r\n// Google map\r\n//\r\n\r\n.gmaps, .gmaps-panaroma {\r\n  height: 300px;\r\n  background: $gray-100;\r\n  border-radius: 3px;\r\n}\r\n\r\n.gmaps-overlay {\r\n  display: block;\r\n  text-align: center;\r\n  color: $white;\r\n  font-size: 16px;\r\n  line-height: 40px;\r\n  background: $primary;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.gmaps-overlay_arrow {\r\n  left: 50%;\r\n  margin-left: -16px;\r\n  width: 0;\r\n  height: 0;\r\n  position: absolute;\r\n  &.above {\r\n    bottom: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-top: 16px solid $primary;\r\n  }\r\n  &.below {\r\n    top: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-bottom: 16px solid $primary;\r\n  }\r\n  \r\n}", "//\r\n// vector-maps.scss\r\n//\r\n\r\n.jvectormap-label {\r\n    border: none;\r\n    background: $gray-800;\r\n    color: $gray-100;\r\n    font-family: $font-family-base;\r\n    font-size: $font-size-base;\r\n    padding: 5px 8px;\r\n}", "//\r\n// x editable.scss\r\n//\r\n\r\n.editable-input{\r\n    .form-control{\r\n      display: inline-block;\r\n    }\r\n  }\r\n  \r\n  .editable-buttons{\r\n    margin-left: 7px;\r\n    .editable-cancel{\r\n      margin-left: 7px;\r\n    }\r\n  }", "//\r\n// authentication.scss\r\n//\r\n\r\n.auth-body-bg {\r\n    background-color: $card-bg;\r\n}\r\n\r\n.authentication-bg {\r\n    background-image: url(\"../images/authentication-bg.jpg\");\r\n    height: 100vh;\r\n    background-size: cover;\r\n    background-position: center;\r\n    .bg-overlay {\r\n        background-color: #292626;\r\n    }\r\n\r\n    @media (max-width: 991px) {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.authentication-page-content {\r\n    height: 100vh;\r\n    display: flex;\r\n}\r\n\r\n.auth-form-group-custom {\r\n    position: relative;\r\n    .form-control {\r\n        height: 60px;\r\n        padding-top: 28px;\r\n        padding-left: 60px;\r\n    }\r\n\r\n    label {\r\n        position: absolute;\r\n        top: 7px;\r\n        left: 60px;\r\n    }\r\n\r\n    .auti-custom-input-icon {\r\n        position: absolute;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        left: 19px;\r\n        font-size: 24px;\r\n        color: $primary;\r\n    }\r\n}\r\n\r\n.auth-logo {\r\n    &.logo-light {\r\n        display: $display-none;\r\n    }\r\n    &.logo-dark {\r\n        display: $display-block;\r\n    }\r\n}\r\n\r\n[data-bs-theme=\"dark\"] {\r\n    .authentication-logo {\r\n        .logo-light {\r\n            display: block;\r\n        }\r\n        .logo-dark {\r\n            display: none;\r\n        }\r\n    }\r\n}\r\n", "// \r\n// ecommerce.scss\r\n//\r\n\r\n// product\r\n\r\n.search-box{\r\n    .form-control{\r\n        border-radius: 30px;\r\n        padding-left: 40px;\r\n    }\r\n    .search-icon{\r\n        font-size: 16px;    \r\n        position: absolute;\r\n        left: 13px;\r\n        top: 0;\r\n        line-height: 38px;\r\n    }\r\n}\r\n\r\n\r\n.ecommerce {\r\n\r\n    .accordion-item {\r\n        border-bottom: 1px solid var(--#{$prefix}border-color);\r\n\r\n\r\n        .accordion-collapse {\r\n            border: 0;\r\n        }\r\n    }\r\n\r\n    button {\r\n        border: 0;\r\n        display: block;\r\n        color: var(--#{$prefix}body-color);\r\n        font-weight: $font-weight-medium;\r\n        padding: 8px 16px;\r\n\r\n        \r\n    }\r\n\r\n\r\n    button[data-bs-toggle=collapse].collapsed::after {\r\n        content: '\\F0415';\r\n    }\r\n\r\n    button[data-bs-toggle=collapse]::after {\r\n        content: '\\F0374';\r\n        display: block;\r\n        font-family: 'Material Design Icons';\r\n        font-size: 16px;\r\n        position: absolute;\r\n        right: 20px;\r\n        font-weight: $font-weight-medium;\r\n        top: 50%;\r\n        background-image: none;\r\n        transform: translateY(-50%);\r\n    }\r\n\r\n    .accordion-body {\r\n        padding: 8px 0;\r\n        border: 0;\r\n\r\n        li {\r\n            a {\r\n                display: block;\r\n                padding: 4px 16px;\r\n                color: var(--#{$prefix}body-color);\r\n            }\r\n\r\n            &.active {\r\n                a {\r\n                    color: $primary;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n\r\n\r\n// Product Details\r\n\r\n.product-detai-imgs{\r\n    .nav{\r\n        .nav-link{\r\n            margin: 7px 0px;\r\n\r\n            &.active{\r\n                background-color: var(--#{$prefix}tertiary-bg);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-color{\r\n    a{\r\n        display: inline-block;\r\n        text-align: center;\r\n        color: var(--#{$prefix}body-color);\r\n\r\n        .product-color-item{\r\n            margin: 7px;\r\n            border: 2px solid var(--#{$prefix}border-color);\r\n            border-radius: 4px;\r\n        }\r\n        &.active, &:hover{\r\n            color: $primary;\r\n            .product-color-item{\r\n                border-color: $primary !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.ecommerce-sortby-list{\r\n    li{\r\n        color: var(--#{$prefix}body-color);\r\n        a{\r\n            color: var(--#{$prefix}body-color);\r\n            padding: 4px;\r\n        }\r\n\r\n        &.active{\r\n            a{\r\n                color: $primary;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-box{\r\n    padding: 24px;\r\n    border: 1px solid var(--#{$prefix}border-color);\r\n    transition: all 0.4s;\r\n    \r\n    &:hover{\r\n        box-shadow: $box-shadow;\r\n    }\r\n}\r\n\r\n.product-img{\r\n    position: relative;\r\n    \r\n    .product-ribbon{\r\n        position: absolute;\r\n        top: 0;\r\n        left: -24px;\r\n        padding: 6px 8px;\r\n        border-radius: 0px 30px 30px 0px;\r\n    }\r\n\r\n    .product-like{\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        a{\r\n            display: inline-block;\r\n            width: 40px;\r\n            height: 40px;\r\n            border: 2px solid var(--#{$prefix}border-color);\r\n            line-height: 38px;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            color: $gray-500;\r\n        }\r\n    }\r\n}\r\n\r\n.product-detail{\r\n    .nav-pills{\r\n        .nav-link{\r\n            margin-bottom: 7px;\r\n            &.active{\r\n                background-color: var(--#{$prefix}tertiary-bg);\r\n            }\r\n\r\n\r\n            .tab-img{\r\n                width: 5rem;\r\n            }\r\n        }\r\n    }\r\n\r\n    .product-img{\r\n        border: 1px solid var(--#{$prefix}border-color);\r\n        padding: 24px;\r\n    }\r\n}\r\n\r\n.product-desc-list{\r\n    li{\r\n        padding: 4px 0px;\r\n    }\r\n}\r\n\r\n.product-review-link{\r\n    .list-inline-item{\r\n        a{\r\n            color: $gray-600;\r\n        }\r\n        &:not(:last-child){\r\n            margin-right: 14px;\r\n        }\r\n    }           \r\n}\r\n\r\n// ecommerce cart\r\n\r\n.product-cart-touchspin{\r\n    border: 1px solid var(--#{$prefix}border-color);\r\n    background-color: $input-bg;\r\n    border-radius: $border-radius;\r\n    .form-control{\r\n        border-color: transparent;\r\n        height: 32px\r\n    }\r\n    \r\n    .input-group-btn .btn{\r\n        background-color: transparent !important;\r\n        border-color: transparent !important;\r\n        color: $primary !important;\r\n        font-size: 16px;\r\n        padding: 3px 12px;\r\n        box-shadow: none;\r\n    }\r\n\r\n}\r\n\r\n// ecommerce checkout\r\n\r\n.shipping-address{\r\n    box-shadow: none;\r\n    &.active{\r\n        border-color: $primary !important;\r\n    }\r\n}", "/* ==============\r\n  Email\r\n===================*/\r\n.email-leftbar {\r\n  width: 236px;\r\n  float: left;\r\n  padding: 20px;\r\n  border-radius: 5px;\r\n}\r\n\r\n.email-rightbar {\r\n  margin-left: 260px;\r\n}\r\n\r\n.chat-user-box {\r\n  p.user-title {\r\n    color: var(--#{$prefix}body-color);\r\n    font-weight: 600;\r\n  }\r\n  p {\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 767px) {\r\n  .email-leftbar {\r\n    float: none;\r\n    width: 100%;\r\n  }\r\n  .email-rightbar {\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n\r\n.mail-list {\r\n  a {\r\n    display: block;\r\n    color: $gray-600;\r\n    line-height: 24px;\r\n    padding: 8px 5px;\r\n    &.active {\r\n      color: $danger;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n.message-list {\r\n  display: block;\r\n  padding-left: 0;\r\n\r\n  li {\r\n    position: relative;\r\n    display: block;\r\n    height: 50px;\r\n    line-height: 50px;\r\n    cursor: default;\r\n    transition-duration: .3s;\r\n\r\n    a{\r\n      color: $gray-600;\r\n    }\r\n\r\n    &:hover {\r\n      background: var(--#{$prefix}tertiary-bg);\r\n      transition-duration: .05s;\r\n    }\r\n\r\n    .col-mail {\r\n      float: left;\r\n      position: relative;\r\n    }\r\n\r\n    .col-mail-1 {\r\n      width: 320px;\r\n\r\n      .star-toggle,\r\n      .checkbox-wrapper-mail,\r\n      .dot {\r\n        display: block;\r\n        float: left;\r\n      }\r\n\r\n      .dot {\r\n        border: 4px solid transparent;\r\n        border-radius: 100px;\r\n        margin: 22px 26px 0;\r\n        height: 0;\r\n        width: 0;\r\n        line-height: 0;\r\n        font-size: 0;\r\n      }\r\n\r\n      .checkbox-wrapper-mail {\r\n        margin: 15px 10px 0 20px;\r\n      }\r\n\r\n      .star-toggle {\r\n        margin-top: 18px;\r\n        margin-left: 5px;\r\n      }\r\n\r\n      .title {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 110px;\r\n        right: 0;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .col-mail-2 {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 320px;\r\n      right: 0;\r\n      bottom: 0;\r\n\r\n      .subject,\r\n      .date {\r\n        position: absolute;\r\n        top: 0;\r\n      }\r\n\r\n      .subject {\r\n        left: 0;\r\n        right: 200px;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .date {\r\n        right: 0;\r\n        width: 170px;\r\n        padding-left: 80px;\r\n      }\r\n    }\r\n\r\n    &.active,\r\n    &.active:hover {\r\n      box-shadow: inset 3px 0 0 $primary;\r\n    }\r\n\r\n    \r\n  &.unread  {\r\n    background-color: var(--#{$prefix}tertiary-bg);\r\n    font-weight: 500;\r\n    color: var(--#{$prefix}body-color);\r\n      a{\r\n        color:var(--#{$prefix}body-color);\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .checkbox-wrapper-mail {\r\n    cursor: pointer;\r\n    height: 20px;\r\n    width: 20px;\r\n    position: relative;\r\n    display: inline-block;\r\n    box-shadow: inset 0 0 0 1px $gray-400;\r\n    border-radius: 1px;\r\n\r\n    input {\r\n      opacity: 0;\r\n      cursor: pointer;\r\n    }\r\n    input:checked ~ label {\r\n      opacity: 1;\r\n    }\r\n\r\n    label {\r\n      position: absolute;\r\n      height: 20px;\r\n      width: 20px;\r\n      left: 0;\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      margin-bottom: 0;\r\n      transition-duration: .05s;\r\n      top: 0;\r\n      &:before {\r\n        content: \"\\F012C\";\r\n        font-family: \"Material Design Icons\";\r\n        top: 0;\r\n        height: 20px;\r\n        color: darken($dark,5%);\r\n        width: 20px;\r\n        position: absolute;\r\n        margin-top: -16px;\r\n        left: 4px;\r\n        font-size: 13px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 575.98px) { \r\n  .message-list li .col-mail-1 {\r\n      width: 200px;\r\n  }\r\n}", "// \r\n// Chat.scss\r\n//\r\n\r\n.chat-leftsidebar {\r\n    background-color: $card-bg;\r\n    border-radius: $border-radius 0 0 $border-radius;\r\n    box-shadow: $box-shadow;\r\n\r\n    @media (min-width: 992px) {\r\n        min-width: 360px;\r\n    }\r\n\r\n\r\n    .chat-leftsidebar-nav {\r\n        .nav {\r\n            background-color: var(--#{$prefix}tertiary-bg);\r\n\r\n            .nav-link {\r\n                &.active {\r\n                    background-color: $card-bg;\r\n                    color: $primary;\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n.chat-noti-dropdown {\r\n    &.active {\r\n        &:before {\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 8px;\r\n            height: 8px;\r\n            background-color: $danger;\r\n            border-radius: 50%;\r\n            right: 0;\r\n        }\r\n    }\r\n\r\n    .btn {\r\n        padding: 0px;\r\n        box-shadow: none;\r\n        font-size: 16px;\r\n    }\r\n}\r\n\r\n.chat-search-box {\r\n    .form-control {\r\n        border: 0;\r\n    }\r\n}\r\n\r\n.chat-list {\r\n    margin: 0;\r\n\r\n    li {\r\n        &.active {\r\n            a {\r\n                background-color: var(--#{$prefix}tertiary-bg);\r\n            }\r\n        }\r\n\r\n        a {\r\n            display: block;\r\n            padding: 14px 16px;\r\n            color: $gray-600;\r\n            transition: all 0.4s;\r\n            border-top: 1px solid var(--#{$prefix}border-color);\r\n            border-radius: 4px;\r\n\r\n            &:hover {\r\n                background-color: rgba(var(--#{$prefix}tertiary-bg), 0.7);\r\n            }\r\n        }\r\n\r\n        .user-img {\r\n            position: relative;\r\n\r\n            .user-status {\r\n                width: 10px;\r\n                height: 10px;\r\n                background-color: $gray-500;\r\n                border-radius: 50%;\r\n                border: 2px solid var(--#{$prefix}border-color);\r\n                position: absolute;\r\n                right: 0;\r\n                bottom: 0;\r\n\r\n\r\n            }\r\n\r\n            &.online {\r\n                .user-status {\r\n                    background-color: $success;\r\n                }\r\n            }\r\n\r\n            &.away {\r\n                .user-status {\r\n                    background-color: $warning;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.user-chat {\r\n    background-color: $card-bg;\r\n    box-shadow: $box-shadow;\r\n    .user-chat-border {\r\n        border-bottom: 1px solid var(--#{$prefix}border-color);\r\n    }\r\n}\r\n\r\n.user-chat-nav {\r\n    .dropdown {\r\n        .nav-btn {\r\n            height: 36px;\r\n            width: 36px;\r\n            line-height: 36px;\r\n            box-shadow: none;\r\n            padding: 0;\r\n            font-size: 20px;\r\n            border-radius: 50%;\r\n        }\r\n\r\n        .dropdown-menu {\r\n            box-shadow: $box-shadow;\r\n            border: 1px solid var(--#{$prefix}border-color);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.chat-conversation {\r\n    li {\r\n        clear: both;\r\n    }\r\n\r\n    .chat-avatar {\r\n        float: left;\r\n        margin-right: 8px;\r\n\r\n        img {\r\n            width: 36px;\r\n            height: 36px;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n\r\n    .chat-day-title {\r\n        position: relative;\r\n        text-align: center;\r\n        margin-bottom: 24px;\r\n        margin-top: 12px;\r\n\r\n        .title {\r\n            background-color: var(--#{$prefix}tertiary-bg);\r\n            position: relative;\r\n            z-index: 1;\r\n            padding: 3px 16px;\r\n            border-radius: 30px;\r\n        }\r\n\r\n        &:before {\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 1px;\r\n            left: 0;\r\n            right: 0;\r\n            background-color: var(--#{$prefix}border-color);\r\n            top: 10px;\r\n        }\r\n\r\n        .badge {\r\n            font-size: 12px;\r\n        }\r\n    }\r\n\r\n    .conversation-list {\r\n        margin-bottom: 24px;\r\n        display: inline-block;\r\n        position: relative;\r\n\r\n        .ctext-wrap {\r\n            overflow: hidden;\r\n\r\n            .conversation-name {\r\n                font-weight: $font-weight-semibold;\r\n                margin-bottom: 7px;\r\n            }\r\n        }\r\n\r\n        .ctext-wrap-content {\r\n            padding: 12px 16px;\r\n            background-color: $primary;\r\n            border-radius: $border-radius;\r\n            color: $white;\r\n        }\r\n\r\n        .chat-time {\r\n            margin-top: 7px;\r\n            font-size: 12px;\r\n            text-align: right;\r\n        }\r\n    }\r\n\r\n    .right {\r\n        .conversation-list {\r\n            float: right;\r\n\r\n            .conversation-name {\r\n                text-align: right;\r\n            }\r\n\r\n            .ctext-wrap-content {\r\n                background-color: var(--#{$prefix}tertiary-bg);\r\n                text-align: right;\r\n                color: $gray-600;\r\n            }\r\n\r\n            .chat-time {\r\n                text-align: left;\r\n            }\r\n\r\n        }\r\n\r\n    }\r\n\r\n}\r\n\r\n.chat-input-section {\r\n    background-color: $card-bg;\r\n    border-radius: $border-radius;\r\n}\r\n\r\n.chat-input {\r\n    background-color: var(--#{$prefix}tertiary-bg) !important;\r\n    border-color: var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.chat-input-links {\r\n    position: absolute;\r\n    left: 16px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n\r\n    li {\r\n        a {\r\n            font-size: 16px;\r\n            line-height: 36px;\r\n            padding: 0px 4px;\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.chat-send {\r\n    @media (max-width: 575.98px) {\r\n        min-width: auto;\r\n    }\r\n}", "// \r\n// Task.scss\r\n//\r\n\r\n.task-box{\r\n  border: 1px solid var(--#{$prefix}border-color);\r\n  box-shadow: none;\r\n\r\n  .team{\r\n    .team-member{\r\n      margin-right: 6px;\r\n    }\r\n  }\r\n}\r\n\r\n.gu-transit {\r\n    border: 1px dashed var(--#{$prefix}border-color) !important;\r\n    background-color: $gray-200 !important;\r\n}\r\n", "// \r\n// coming-soon.scss\r\n//\r\n\r\n.counter-number {\r\n    font-size: 24px;\r\n    font-weight: $font-weight-bold;\r\n    font-family: $font-family-secondary;\r\n    text-align: center;\r\n    display: flex;\r\n    span {\r\n        font-size: 14px;\r\n        font-weight: $font-weight-normal;\r\n        display: block;\r\n        padding-top: 5px;\r\n    }\r\n}\r\n\r\n.coming-box {\r\n    width: 25%;\r\n}\r\n\r\n.comingsoon-bg{\r\n    background-image: url(\"../images/comingsoon-bg.jpg\");\r\n}", "// \r\n// timeline.scss\r\n//\r\n/************** vertical timeline **************/ \r\n\r\n.timeline{\r\n    position: relative;\r\n    margin-bottom: 30px;\r\n    &:before {\r\n        content: \"\";\r\n        position: absolute;\r\n        width: 3px;\r\n        top: 30px;\r\n        left: 0;\r\n        bottom: 0;\r\n        background-color: var(--#{$prefix}tertiary-bg);\r\n    }\r\n\r\n    .timeline-item{\r\n        display: flex;\r\n    }\r\n\r\n    .timeline-block{\r\n        width: 100%;\r\n    }\r\n\r\n    .time-show-btn{\r\n        margin-bottom: 30px;\r\n    }\r\n\r\n    .timeline-box{\r\n        margin: 20px 0;\r\n        position: relative;\r\n        margin-left: 45px;\r\n    }\r\n\r\n    .timeline-date{\r\n        display: inline-block;\r\n        padding: 4px 16px 4px 8px;\r\n        border-radius: 0px 30px 30px 0px;\r\n        background-color: $primary;\r\n        color: $white;\r\n        position: relative;\r\n        left: -30px;\r\n        \r\n        .circle-dot{\r\n            margin-right: 8px;\r\n        }\r\n    }\r\n\r\n    .timeline-icon {\r\n        position: absolute;\r\n        width: 10px;\r\n        height: 10px;\r\n        background: $card-bg;\r\n        border-radius: 50%;\r\n        display: block;\r\n        border: 2px solid $primary;\r\n        left: -48px;\r\n        text-align: center;\r\n        top: 27px;\r\n        z-index: 9;\r\n        &:before {\r\n            content: \"\";\r\n            position: absolute;\r\n            height: 3px;\r\n            width: 20px;\r\n            background-color: var(--#{$prefix}tertiary-bg);\r\n            left: 12px;\r\n            top: 3px;\r\n        }\r\n    }\r\n\r\n    .timeline-album {\r\n        margin-top: 16px;\r\n        a {\r\n            display: inline-block;\r\n            margin-right: 5px;\r\n        }\r\n        img {\r\n            height: 40px;\r\n            width: auto;\r\n            border-radius: 4px;\r\n        }\r\n    }\r\n\r\n}\r\n\r\n@media (min-width: 768px) {\r\n\r\n    .timeline{\r\n\r\n        .timeline-block{\r\n            width: 50%;\r\n        }\r\n\r\n        &:before {\r\n            left: 50%;\r\n        }\r\n        .time-show-btn{\r\n            position: relative;\r\n            left: 67px;\r\n            text-align: right;\r\n        }\r\n    }\r\n\r\n    .timeline-item{\r\n        &::before {\r\n            content: \"\";\r\n            display: block;\r\n            width: 50%;\r\n        }\r\n\r\n        &.timeline-left{\r\n            text-align: right;\r\n            &::after {\r\n                content: \"\";\r\n                display: block;\r\n                width: 50%;\r\n            }\r\n\r\n            &::before {\r\n                display: none;\r\n            }\r\n        }\r\n\r\n        &.timeline-left{\r\n            .timeline-box{\r\n                margin-left: 0;\r\n                margin-right: 45px;\r\n            }\r\n\r\n            .timeline-date {\r\n                padding: 4px 8px 4px 16px;\r\n                border-radius: 30px 0px 0px 30px;\r\n                left: auto;\r\n                right: -30px;\r\n            \r\n                .circle-dot{\r\n                    float: right;\r\n                    margin-right: 0px;\r\n                    margin-left: 8px;\r\n                }\r\n            }\r\n\r\n            .timeline-icon{\r\n                left: auto;\r\n                right: -52px;\r\n                &::before{\r\n                    left: auto;\r\n                    right: 12px;\r\n                }\r\n            }\r\n\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.left-timeline{\r\n    &::before{\r\n        left: 3px;\r\n    }\r\n    .timeline-item::before{\r\n        display: none;\r\n    }\r\n\r\n    .timeline-block{\r\n        width: 100%;\r\n    }\r\n\r\n    .timeline-icon{\r\n        left: -45px;\r\n    }\r\n}", "// \r\n// Extras pages.scss\r\n//\r\n\r\n\r\n// pricing\r\n\r\n.pricing-nav-tabs{\r\n    display: inline-block;\r\n    background-color: $card-bg;\r\n    box-shadow: $box-shadow;\r\n    padding: 4px;\r\n    border-radius: 7px;\r\n    li{\r\n        display: inline-block;\r\n    }\r\n}\r\n\r\n\r\n.pricing-box{\r\n    .plan-features{\r\n        li{\r\n            padding: 7px 0px;\r\n        }\r\n    }\r\n}\r\n\r\n/*********************\r\n    Faqs\r\n**********************/ \r\n\r\n.faq-nav-tabs{\r\n    .nav-item{\r\n        margin: 0px 8px;\r\n    }\r\n    .nav-link{\r\n        text-align: center;\r\n        margin-bottom: 8px;\r\n        border: 2px solid var(--#{$prefix}border-color);\r\n        color: var(--#{$prefix}body-color);\r\n        .nav-icon{\r\n            font-size: 40px;\r\n            margin-bottom: 8px;\r\n            display: block;\r\n        }\r\n\r\n        &.active{\r\n            border-color: $primary;\r\n            background-color: transparent;\r\n            color: var(--#{$prefix}body-color);\r\n\r\n            .nav-icon{\r\n                color: $primary;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.text-error{\r\n    font-size: 120px;\r\n    @media (max-width: 575.98px) {\r\n        font-size: 86px;\r\n    }\r\n}\r\n\r\n.error-text{\r\n    color: $danger;\r\n    position: relative;\r\n\r\n    .error-img{\r\n        position: absolute;\r\n        width: 120px;\r\n        left: -15px;\r\n        right: 0;\r\n        bottom: 47px;\r\n\r\n        @media (max-width: 575.98px) {\r\n            width: 86px;\r\n            left: -12px;\r\n            bottom: 38px;\r\n        }\r\n    }\r\n}"]}