!function(a){"use strict";function e(){}e.prototype.init=function(){a(".select2").select2(),a(".select2-limiting").select2({maximumSelectionLength:2}),a(".select2-search-disable").select2({minimumResultsForSearch:1/0}),a(".select2-ajax").select2({ajax:{url:"https://api.github.com/search/repositories",dataType:"json",delay:250,data:function(e){return{q:e.term,page:e.page}},processResults:function(e,t){return t.page=t.page||1,{results:e.items,pagination:{more:30*t.page<e.total_count}}},cache:!0},placeholder:"Search for a repository",minimumInputLength:1,templateResult:function(e){if(e.loading)return e.text;var t=a("<div class='select2-result-repository clearfix'><div class='select2-result-repository__avatar'><img src='"+e.owner.avatar_url+"' /></div><div class='select2-result-repository__meta'><div class='select2-result-repository__title'></div><div class='select2-result-repository__description'></div><div class='select2-result-repository__statistics'><div class='select2-result-repository__forks'><i class='fa fa-flash'></i> </div><div class='select2-result-repository__stargazers'><i class='fa fa-star'></i> </div><div class='select2-result-repository__watchers'><i class='fa fa-eye'></i> </div></div></div></div>");return t.find(".select2-result-repository__title").text(e.full_name),t.find(".select2-result-repository__description").text(e.description),t.find(".select2-result-repository__forks").append(e.forks_count+" Forks"),t.find(".select2-result-repository__stargazers").append(e.stargazers_count+" Stars"),t.find(".select2-result-repository__watchers").append(e.watchers_count+" Watchers"),t},templateSelection:function(e){return e.full_name||e.text}}),a(".select2-templating").select2({templateResult:function(e){return e.id?a('<span><img src="/assets/images/flags/select2/'+e.element.value.toLowerCase()+'.png" class="img-flag" /> '+e.text+"</span>"):e.text}}),a("#colorpicker-default").spectrum(),a("#colorpicker-showalpha").spectrum({showAlpha:!0}),a("#colorpicker-showpaletteonly").spectrum({showPaletteOnly:!0,showPalette:!0,color:"#34c38f",palette:[["#556ee6","white","#34c38f","rgb(255, 128, 0);","#50a5f1"],["red","yellow","green","blue","violet"]]}),a("#colorpicker-togglepaletteonly").spectrum({showPaletteOnly:!0,togglePaletteOnly:!0,togglePaletteMoreText:"more",togglePaletteLessText:"less",color:"#556ee6",palette:[["#000","#444","#666","#999","#ccc","#eee","#f3f3f3","#fff"],["#f00","#f90","#ff0","#0f0","#0ff","#00f","#90f","#f0f"],["#f4cccc","#fce5cd","#fff2cc","#d9ead3","#d0e0e3","#cfe2f3","#d9d2e9","#ead1dc"],["#ea9999","#f9cb9c","#ffe599","#b6d7a8","#a2c4c9","#9fc5e8","#b4a7d6","#d5a6bd"],["#e06666","#f6b26b","#ffd966","#93c47d","#76a5af","#6fa8dc","#8e7cc3","#c27ba0"],["#c00","#e69138","#f1c232","#6aa84f","#45818e","#3d85c6","#674ea7","#a64d79"],["#900","#b45f06","#bf9000","#38761d","#134f5c","#0b5394","#351c75","#741b47"],["#600","#783f04","#7f6000","#274e13","#0c343d","#073763","#20124d","#4c1130"]]}),a("#colorpicker-showintial").spectrum({showInitial:!0}),a("#colorpicker-showinput-intial").spectrum({showInitial:!0,showInput:!0});a(".vertical-spin").TouchSpin({verticalbuttons:!0,verticalupclass:"ion-plus-round",verticaldownclass:"ion-minus-round",buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary"}),a("input[name='demo1']").TouchSpin({min:0,max:100,step:.1,decimals:2,boostat:5,maxboostedstep:10,postfix:"%",buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary"}),a("input[name='demo2']").TouchSpin({min:-1e9,max:1e9,stepinterval:50,maxboostedstep:1e7,prefix:"$",buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary"}),a("input[name='demo3']").TouchSpin({initval:40,buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary"}),a("input[name='demo3_21']").TouchSpin({initval:40,buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary"}),a("input[name='demo3_22']").TouchSpin({initval:40,buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary"}),a("input[name='demo5']").TouchSpin({initval:40,prefix:"pre",postfix:"post",buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary"}),a("input[name='demo0']").TouchSpin({initval:40,buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary"}),a("input[name='demo_vertical']").TouchSpin({initval:40,verticalbuttons:!0}),a("input#defaultconfig").maxlength({warningClass:"badge bg-info",limitReachedClass:"badge bg-warning"}),a("input#thresholdconfig").maxlength({threshold:20,warningClass:"badge bg-info",limitReachedClass:"badge bg-warning"}),a("input#moreoptions").maxlength({alwaysShow:!0,warningClass:"badge bg-success",limitReachedClass:"badge bg-danger"}),a("input#alloptions").maxlength({alwaysShow:!0,warningClass:"badge bg-success",limitReachedClass:"badge bg-danger",separator:" out of ",preText:"You typed ",postText:" chars available.",validate:!0}),a("textarea#textarea").maxlength({alwaysShow:!0,warningClass:"badge bg-info",limitReachedClass:"badge bg-warning"}),a("input#placement").maxlength({alwaysShow:!0,placement:"top-left",warningClass:"badge bg-info",limitReachedClass:"badge bg-warning"})},a.AdvancedForm=new e,a.AdvancedForm.Constructor=e}(window.jQuery),function(){"use strict";window.jQuery.AdvancedForm.init()}(),$(function(){"use strict";var i=$(".docs-date"),o=$(".docs-datepicker-container"),c=$(".docs-datepicker-trigger"),r={show:function(e){console.log(e.type,e.namespace)},hide:function(e){console.log(e.type,e.namespace)},pick:function(e){console.log(e.type,e.namespace,e.view)}};i.on({"show.datepicker":function(e){console.log(e.type,e.namespace)},"hide.datepicker":function(e){console.log(e.type,e.namespace)},"pick.datepicker":function(e){console.log(e.type,e.namespace,e.view)}}).datepicker(r),$(".docs-options, .docs-toggles").on("change",function(e){var t,e=e.target,a=$(e),s=a.attr("name"),n="checkbox"===e.type?e.checked:a.val();switch(s){case"container":n?(n=o).show():o.hide();break;case"trigger":n?(n=c).prop("disabled",!1):c.prop("disabled",!0);break;case"inline":(t=$('input[name="container"]')).prop("checked")||t.click();break;case"language":$('input[name="format"]').val($.fn.datepicker.languages[n].format)}r[s]=n,i.datepicker("reset").datepicker("destroy").datepicker(r)}),$(".docs-actions").on("click","button",function(e){var t=$(this).data(),a=t.arguments||[];e.stopPropagation(),t.method&&(t.source?i.datepicker(t.method,$(t.source).val()):(e=i.datepicker(t.method,a[0],a[1],a[2]))&&t.target&&$(t.target).val(e))})});