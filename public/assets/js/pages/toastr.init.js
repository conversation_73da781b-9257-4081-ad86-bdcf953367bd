$(function(){var k,v=-1,m=0;$("#closeButton").click(function(){$(this).is(":checked")?$("#addBehaviorOnToastCloseClick").prop("disabled",!1):($("#addBehaviorOnToastCloseClick").prop("disabled",!0),$("#addBehaviorOnToastCloseClick").prop("checked",!1))}),$("#showtoast").click(function(){var t=$("#toastTypeGroup input:radio:checked").val(),o=$("#message").val(),e=$("#title").val()||"",a=$("#showDuration"),n=$("#hideDuration"),s=$("#timeOut"),i=$("#extendedTimeOut"),r=$("#showEasing"),l=$("#hideEasing"),c=$("#showMethod"),p=$("#hideMethod"),d=m++,h=$("#addClear").prop("checked"),u=(toastr.options={closeButton:$("#closeButton").prop("checked"),debug:$("#debugInfo").prop("checked"),newestOnTop:$("#newestOnTop").prop("checked"),progressBar:$("#progressBar").prop("checked"),rtl:$("#rtl").prop("checked"),positionClass:$("#positionGroup input:radio:checked").val()||"toast-top-right",preventDuplicates:$("#preventDuplicates").prop("checked"),onclick:null},$("#addBehaviorOnToastClick").prop("checked")&&(toastr.options.onclick=function(){alert("You can perform some custom action after a toast goes away")}),$("#addBehaviorOnToastCloseClick").prop("checked")&&(toastr.options.onCloseClick=function(){alert("You can perform some custom action when the close button is clicked")}),a.val().length&&(toastr.options.showDuration=parseInt(a.val())),n.val().length&&(toastr.options.hideDuration=parseInt(n.val())),s.val().length&&(toastr.options.timeOut=h?0:parseInt(s.val())),i.val().length&&(toastr.options.extendedTimeOut=h?0:parseInt(i.val())),r.val().length&&(toastr.options.showEasing=r.val()),l.val().length&&(toastr.options.hideEasing=l.val()),c.val().length&&(toastr.options.showMethod=c.val()),p.val().length&&(toastr.options.hideMethod=p.val()),h&&(a=(a=o)||"Clear itself?",o=a+='<br /><br /><button type="button" class="btn-primary btn clear">Yes</button>',toastr.options.tapToDismiss=!1),o=o||(n=["My name is Inigo Montoya. You killed my father. Prepare to die!",'<div><input class="input-small form-control form-control-sm mb-2" placeholder="textbox"/>&nbsp;<a href="" target="_blank">This is a hyperlink</a></div><div><button type="button" id="okBtn" class="btn btn-primary mt-2">Close me</button><button type="button" id="surpriseBtn" class="btn text-white  mt-2" style="margin: 0 8px 0 8px">Surprise me</button></div>',"Are you the six fingered man?","Inconceivable!","I do not think that means what you think it means.","Have fun storming the castle!"])[v=++v===n.length?0:v],$("#toastrOptions").text('Command: toastr["'+t+'"]("'+o+(e?'", "'+e:"")+'")\n\ntoastr.options = '+JSON.stringify(toastr.options,null,2)),toastr[t](o,e));void 0!==(k=u)&&(u.find("#okBtn").length&&u.delegate("#okBtn","click",function(){alert("you clicked me. i was toast #"+d+". goodbye!"),u.remove()}),u.find("#surpriseBtn").length&&u.delegate("#surpriseBtn","click",function(){alert("Surprise! you clicked me. i was toast #"+d+". You could perform an action here.")}),u.find(".clear").length)&&u.delegate(".clear","click",function(){toastr.clear(u,{force:!0})})}),$("#clearlasttoast").click(function(){toastr.clear(k)}),$("#cleartoasts").click(function(){toastr.clear()})});