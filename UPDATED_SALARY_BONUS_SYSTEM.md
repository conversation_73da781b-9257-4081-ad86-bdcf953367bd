# Updated Salary and Bonus System

## Overview
The system has been updated to implement a label-based target system where each label has its own target amount that needs to be achieved to earn bonuses.

## How It Works

### Label Structure
Each label now has:
- **Target Amount**: The amount that needs to be achieved to earn this label's bonus
- **Bonus Amount**: The bonus amount given when the target is achieved

### Example Setup
```
Label 1: Target $100 → Bonus $50
Label 2: Target $200 → Bonus $100  
Label 3: Target $300 → Bonus $150
```

### Team Member Example
- **Team Member**: John
- **Main Target**: $1000 (for salary)
- **Base Salary**: $500
- **Achieved Amount**: $1150

### Calculation Logic
When <PERSON> achieves $1150:

1. **Salary**: He gets his base salary ($500) because he achieved his main target ($1000)

2. **Label Bonuses**: The system checks all labels:
   - Label 1 ($100 target): ✅ Achieved ($1150 ≥ $100) → Gets $50 bonus
   - Label 2 ($200 target): ✅ Achieved ($1150 ≥ $200) → Gets $100 bonus  
   - Label 3 ($300 target): ✅ Achieved ($1150 ≥ $300) → Gets $150 bonus

3. **Total**: $500 (salary) + $50 + $100 + $150 = $800

## Database Changes

### Labels Table
```sql
- id (primary key)
- name (label name)
- bonus_target (target amount to achieve)
- bonus_amount (bonus amount when target achieved)
- description (optional description)
- is_active (active/inactive status)
- timestamps
```

### Updated Logic
- **bonus_percent** → **bonus_target** (renamed column)
- Labels now have target amounts instead of percentages
- Bonus calculation checks if achieved amount meets label targets

## Key Features

### 1. Multiple Label Achievement
- Team members can achieve multiple labels simultaneously
- Each achieved label adds its bonus amount
- System automatically calculates total bonus

### 2. Flexible Target System
- Each label has its own target amount
- Labels can be activated/deactivated
- Easy to add new labels with different targets

### 3. Real-time Calculation
- Bonuses calculated automatically when achievements are recorded
- Dashboard shows which labels have been achieved
- Performance tracking with achievement percentages

## Usage Examples

### Creating Labels
1. Go to Labels section
2. Create labels with target amounts:
   - Label 1: Target $100, Bonus $50
   - Label 2: Target $200, Bonus $100
   - Label 3: Target $300, Bonus $150

### Recording Achievements
1. Go to Team Achievements
2. Select team member and label
3. Enter achieved amount (e.g., $1150)
4. System automatically calculates total bonus from all achieved labels

### Viewing Results
- **Bonus Dashboard**: Shows team performance and achieved labels
- **Team Performance**: Displays which labels each member has achieved
- **Achievement Tracking**: Real-time bonus calculations

## Example Scenarios

### Scenario 1: Partial Achievement
- Team member achieves $150
- Gets base salary (if main target achieved)
- Achieves Label 1 ($100 target) → Gets $50 bonus
- Total: Base salary + $50

### Scenario 2: Multiple Labels
- Team member achieves $250
- Gets base salary (if main target achieved)
- Achieves Label 1 ($100) → $50 bonus
- Achieves Label 2 ($200) → $100 bonus
- Total: Base salary + $150

### Scenario 3: All Labels
- Team member achieves $400
- Gets base salary (if main target achieved)
- Achieves all labels → $300 total bonus
- Total: Base salary + $300

## Benefits

1. **Clear Target Structure**: Each label has a specific target amount
2. **Multiple Bonuses**: Team members can earn multiple label bonuses
3. **Flexible System**: Easy to add new labels with different targets
4. **Transparent Calculation**: Clear visibility into which labels are achieved
5. **Motivational**: Progressive bonus system encourages higher achievements

## Technical Implementation

### Models Updated
- **Label**: New bonus calculation logic based on target achievement
- **Team**: Methods to calculate current bonus and get achieved labels
- **TeamAchievement**: Updated to calculate total bonus from all labels

### Controllers Updated
- **LabelController**: Validation for bonus_target field
- **TeamAchievementController**: New bonus calculation method
- **SalaryController**: Updated dashboard with label achievement display

### Views Updated
- **Labels**: Forms show target amounts instead of percentages
- **Team Achievements**: Display target and bonus information
- **Bonus Dashboard**: Show achieved labels and total bonuses

This updated system provides a more structured and transparent approach to bonus calculation, making it clear what targets need to be achieved to earn specific bonuses.
