<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\LoanController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\TeamController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\LabelController;
use App\Http\Controllers\SalaryController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\FrontendController;
use App\Http\Controllers\DutyShiftController;
use App\Http\Controllers\AttendanceController;
use App\Http\Controllers\DesignationController;
use App\Http\Controllers\BlogCategoryController;
use App\Http\Controllers\LeaveRequestController;
use App\Http\Controllers\SpecialProjectController;
use App\Http\Controllers\TeamAchievementController;
use App\Http\Controllers\ProjectManagementController;
use App\Http\Controllers\MarketplaceAccountController;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use App\Http\Controllers\MarketplaceAccountCategoryController;
use App\Http\Controllers\AuditLogController;

// Default route - redirect to login
Route::get('/', function () {
    return redirect()->route('login');
})->name('home');
Route::get('/contact', [FrontendController::class, 'contact'])->name('contact');
Route::get('/about', [FrontendController::class, 'about'])->name('about');
Route::get('/blog', [FrontendController::class, 'blog'])->name('blog');
Route::get('/blog/{slug}', [FrontendController::class, 'blogDetails'])->name('blogDetails');
Route::get('/service', [FrontendController::class, 'service'])->name('service');
Route::get('/project', [FrontendController::class, 'project'])->name('project');
Route::get('/project/{slug}', [FrontendController::class, 'projectDetails'])->name('projectDetails');
Route::get('/service/{slug}', [FrontendController::class, 'serviceDetails'])->name('serviceDetails');
Route::get('team', [FrontendController::class, 'team'])->name('team');

// Auth routes with verification
Auth::routes(['verify' => true]);

// Verification routes (if you need to customize them)
Route::get('/email/verify', function () { return view('auth.verify');})->middleware('auth')->name('verification.notice');

Route::get('/email/verify/{id}/{hash}', function (EmailVerificationRequest $request) { $request->fulfill();
return redirect('/dashboard')->with('success', 'Your email has been successfully verified! Welcome aboard!');
})->middleware(['auth', 'signed'])->name('verification.verify');

Route::post('/email/verification-notification', function (Request $request) {$request->user()->sendEmailVerificationNotification();return back()->with('message', 'Verification link sent!');})->middleware(['auth', 'throttle:6,1'])->name('verification.send');

// Dashboard - accessible to all authenticated users
Route::get('/dashboard', [HomeController::class, 'index'])->name('dashboard')->middleware('auth');
Route::get('/dashboard/user', [HomeController::class, 'index'])->name('dashboard.user')->middleware('auth');

// Content Management Routes - Granular Permissions
Route::middleware(['auth'])->group(function () {
    // Blog Management
    Route::middleware(['permission:show blog'])->group(function () {
        Route::get('blogs', [BlogController::class, 'index'])->name('blogs.index');
        Route::get('blog-categories', [BlogCategoryController::class, 'index'])->name('blog-categories.index');
    });

    Route::middleware(['permission:manage blog'])->group(function () {
        Route::resource('blogs', BlogController::class)->except(['index']);
        Route::resource('blog-categories', BlogCategoryController::class)->except(['index']);
    });

    // Service Management
    Route::middleware(['permission:show service'])->group(function () {
        Route::get('services', [ServiceController::class, 'index'])->name('services.index');
    });

    Route::middleware(['permission:manage service'])->group(function () {
        Route::resource('services', ServiceController::class)->except(['index']);
    });

    // Contact Management
    Route::middleware(['permission:show contact'])->group(function () {
        Route::resource('contacts', ContactController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage contact'])->group(function () {
        Route::resource('contacts', ContactController::class)->except(['index', 'show']);
        Route::get('/contacts/{contact}/reply', [ContactController::class, 'reply'])->name('contacts.reply');
        Route::post('/contacts/{contact}/reply', [ContactController::class, 'sendReply'])->name('contacts.sendReply');
    });

    Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

    // Project Management
    Route::middleware(['permission:show project'])->group(function () {
        Route::resource('projects', ProjectController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage project'])->group(function () {
        Route::resource('projects', ProjectController::class)->except(['index', 'show']);
    });

    // Project Management (Team Projects)
    Route::middleware(['permission:show project management'])->group(function () {
        Route::resource('project-managements', ProjectManagementController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage project management'])->group(function () {
        Route::resource('project-managements', ProjectManagementController::class)->except(['index', 'show']);
    });

    // Marketplace Account Management
    Route::middleware(['permission:show marketplace account'])->group(function () {
        Route::resource('marketplace_accounts', MarketplaceAccountController::class)->only(['index', 'show']);
        Route::resource('marketplace-account-categories', MarketplaceAccountCategoryController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage marketplace account'])->group(function () {
        Route::resource('marketplace_accounts', MarketplaceAccountController::class)->except(['index', 'show']);
        Route::resource('marketplace-account-categories', MarketplaceAccountCategoryController::class)->except(['index', 'show']);
    });

    // Loan Management
    Route::middleware(['permission:show loan'])->group(function () {
        Route::resource('loans', LoanController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage loan'])->group(function () {
        Route::resource('loans', LoanController::class)->except(['index', 'show']);
        Route::patch('loans/{loan}/status', [LoanController::class, 'updateStatus'])->name('loans.update-status');
        Route::patch('loans/{loan}/mark-paid', [LoanController::class, 'markAsPaid'])->name('loans.mark-paid');
        Route::get('loans/{loan}/deduction', [LoanController::class, 'showDeductionForm'])->name('loans.deduction-form');
        Route::post('loans/{loan}/deduction', [LoanController::class, 'addDeduction'])->name('loans.add-deduction');
    });
});

// HR Management Routes - Granular Permissions
Route::middleware(['auth'])->group(function () {
    // User Management
    Route::middleware(['permission:show user'])->group(function () {
        Route::resource('users', UserController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage user'])->group(function () {
        Route::resource('users', UserController::class)->except(['index', 'show']);
    });

    // Team Management
    Route::middleware(['permission:show team'])->group(function () {
        Route::resource('teams', TeamController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage team'])->group(function () {
        Route::resource('teams', TeamController::class)->except(['index', 'show']);
    });



    // Salary Management
    Route::middleware(['permission:show salary'])->group(function () {
        Route::resource('salaries', SalaryController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage salary'])->group(function () {
        Route::resource('salaries', SalaryController::class)->except(['index', 'show']);
    });

    Route::middleware(['permission:view bonus dashboard'])->group(function () {
        Route::get('salaries/bonus-dashboard', [SalaryController::class, 'bonusDashboard'])->name('salaries.bonus-dashboard');
    });

    // Designation Management
    Route::middleware(['permission:show designation'])->group(function () {
        Route::resource('designations', DesignationController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage designation'])->group(function () {
        Route::resource('designations', DesignationController::class)->except(['index', 'show']);
    });

    // Label Management
    Route::middleware(['permission:show label'])->group(function () {
        Route::resource('labels', LabelController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage label'])->group(function () {
        Route::resource('labels', LabelController::class)->except(['index', 'show']);
    });

    // Team Achievement Management
    Route::middleware(['permission:show team achievement'])->group(function () {
        Route::resource('team-achievements', TeamAchievementController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage team achievement'])->group(function () {
        Route::resource('team-achievements', TeamAchievementController::class)->except(['index', 'show']);
        Route::patch('/team-achievements/{teamAchievement}/mark-paid', [TeamAchievementController::class, 'markAsPaid'])->name('team-achievements.mark-paid');
        Route::patch('/team-achievements/{teamAchievement}/mark-unpaid', [TeamAchievementController::class, 'markAsUnpaid'])->name('team-achievements.mark-unpaid');
    });

    // Leave Request Management
    Route::middleware(['permission:show leave request'])->group(function () {
        Route::resource('leave_requests', LeaveRequestController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage leave request'])->group(function () {
        Route::resource('leave_requests', LeaveRequestController::class)->except(['index', 'show']);
        Route::patch('/leave-requests/{leaveRequest}/approve', [LeaveRequestController::class, 'approve'])->name('leave_requests.approve');
        Route::patch('/leave-requests/{leaveRequest}/reject', [LeaveRequestController::class, 'reject'])->name('leave_requests.reject');
    });
});
// Additional HR and Financial Routes - Granular Permissions
Route::middleware(['auth'])->group(function () {
    // Duty Shift Management
    Route::middleware(['permission:show duty shift'])->group(function () {
        Route::resource('duty_shift', DutyShiftController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage duty shift'])->group(function () {
        Route::resource('duty_shift', DutyShiftController::class)->except(['index', 'show']);
    });

    // Attendance Management
    Route::middleware(['permission:show attendance'])->group(function () {
        Route::resource('attendance', AttendanceController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage attendance'])->group(function () {
        Route::resource('attendance', AttendanceController::class)->except(['index', 'show']);
    });

    // Expense Management
    Route::middleware(['permission:show expense'])->group(function () {
        Route::resource('expenses', ExpenseController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage expense'])->group(function () {
        Route::resource('expenses', ExpenseController::class)->except(['index', 'show']);
        Route::patch('/expenses/{expense}/update-status', [ExpenseController::class, 'updateStatus'])->name('expenses.update-status');
    });

    // Special Project Management
    Route::middleware(['permission:show special project'])->group(function () {
        Route::resource('special-projects', SpecialProjectController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage special project'])->group(function () {
        Route::resource('special-projects', SpecialProjectController::class)->except(['index', 'show']);
        Route::patch('/special-projects/{specialProject}/update-status', [SpecialProjectController::class, 'updateStatus'])->name('special-projects.update-status');
    });

    // Salary Components API
    Route::get('/get-salary-components/{team_id}', [SalaryController::class, 'getSalaryComponents']);
});

// Personal/My Routes - User-specific permissions
Route::middleware(['auth'])->group(function () {
    // My Payslips
    Route::middleware(['permission:view my payslips'])->group(function () {
        Route::get('/my/payslips', [SalaryController::class, 'myPayslips'])->name('salaries.my');
        Route::get('/my/payslips/{year}/{month}', [SalaryController::class, 'myPayslip'])->name('salaries.my.show');
    });

    // My Attendance
    Route::middleware(['permission:view my attendance'])->group(function () {
        Route::get('/my/attendance', [AttendanceController::class, 'myIndex'])->name('attendance.my');
        Route::post('/my/attendance', [AttendanceController::class, 'myStore'])->name('attendance.my.store');
    });

    // Check In/Out
    Route::middleware(['permission:check in,check out'])->group(function () {
        Route::post('/my/attendance/check-in', [AttendanceController::class, 'checkIn'])->name('attendance.check-in');
        Route::post('/my/attendance/check-out', [AttendanceController::class, 'checkOut'])->name('attendance.check-out');
        Route::get('/my/attendance/status', [AttendanceController::class, 'getCurrentStatus'])->name('attendance.status');
    });

    // My Duty Schedule
    Route::middleware(['permission:view my duty schedule'])->group(function () {
        Route::get('/my/duty-schedule', [DutyShiftController::class, 'mySchedule'])->name('duty_shift.my.schedule');
    });

    // My Projects
    Route::middleware(['permission:view my projects'])->group(function () {
        Route::get('/my/projects', [ProjectManagementController::class, 'myProjects'])->name('project-managements.my');
    });

    // My Target Achievement
    Route::middleware(['permission:view target achievement'])->group(function () {
        Route::get('/my/target-achievement', [SalaryController::class, 'myTargetAchievement'])->name('salaries.target-achievement');
    });

    // My Expenses
    Route::middleware(['permission:view my expenses'])->group(function () {
        Route::get('/my/expenses', [ExpenseController::class, 'myExpenses'])->name('expenses.my');
    });

    // My Special Projects
    Route::middleware(['permission:view my special projects'])->group(function () {
        Route::get('/my/special-projects', [SpecialProjectController::class, 'myProjects'])->name('special-projects.my');
    });

    // Profit Table Routes
    Route::middleware(['permission:view profit table'])->group(function () {
        Route::get('/profits', [App\Http\Controllers\ProfitController::class, 'index'])->name('profits.index');
    });
});

// Admin-only Attendance Management (Legacy routes for backward compatibility)
Route::middleware(['auth', 'permission:manage attendance'])->group(function () {
    Route::get('/attendance', [AttendanceController::class, 'index'])->name('attendance.index');
    Route::get('/attendance/{attendance}/edit', [AttendanceController::class, 'edit'])->name('attendance.edit');
    Route::put('/attendance/{attendance}', [AttendanceController::class, 'update'])->name('attendance.update');
    Route::delete('/attendance/{attendance}', [AttendanceController::class, 'destroy'])->name('attendance.destroy');
});

// Permission and Role Management Routes - Granular Permissions
Route::middleware(['auth'])->group(function () {
    // Permission Management
    Route::middleware(['permission:show permission'])->group(function () {
        Route::resource('permissions', PermissionController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage permission'])->group(function () {
        Route::resource('permissions', PermissionController::class)->except(['index', 'show']);
        Route::post('permissions/assign-to-role', [PermissionController::class, 'assignToRole'])->name('permissions.assign-to-role');
        Route::post('permissions/remove-from-role', [PermissionController::class, 'removeFromRole'])->name('permissions.remove-from-role');
        Route::post('permissions/assign-to-user', [PermissionController::class, 'assignToUser'])->name('permissions.assign-to-user');
        Route::post('permissions/remove-from-user', [PermissionController::class, 'removeFromUser'])->name('permissions.remove-from-user');
    });

    // Role Management
    Route::middleware(['permission:show role'])->group(function () {
        Route::resource('roles', RoleController::class)->only(['index', 'show']);
    });

    Route::middleware(['permission:manage role'])->group(function () {
        Route::resource('roles', RoleController::class)->except(['index', 'show']);
        Route::post('roles/assign-to-user', [RoleController::class, 'assignToUser'])->name('roles.assign-to-user');
        Route::post('roles/remove-from-user', [RoleController::class, 'removeFromUser'])->name('roles.remove-from-user');
        Route::post('roles/{role}/sync-permissions', [RoleController::class, 'syncPermissions'])->name('roles.sync-permissions');
    });
});

// User profile routes - Profile permissions
Route::middleware(['auth'])->group(function () {
    Route::middleware(['permission:view profile'])->group(function () {
        Route::get('/profile', [UserController::class, 'profile'])->name('profile');
    });

    Route::middleware(['permission:edit profile'])->group(function () {
        Route::post('/profile/update', [UserController::class, 'updateProfile'])->name('profile.update');
    });
});

// Audit Log Management Routes
Route::middleware(['auth'])->group(function () {
    Route::middleware(['permission:show audit log'])->group(function () {
        Route::get('audit-logs', [AuditLogController::class, 'index'])->name('audit-logs.index');
        Route::get('audit-logs/{auditLog}', [AuditLogController::class, 'show'])->name('audit-logs.show');
        Route::get('audit-logs-statistics', [AuditLogController::class, 'statistics'])->name('audit-logs.statistics');
    });

    Route::middleware(['permission:manage audit log'])->group(function () {
        Route::get('audit-logs-export', [AuditLogController::class, 'export'])->name('audit-logs.export');
        Route::post('audit-logs-clean', [AuditLogController::class, 'clean'])->name('audit-logs.clean');
    });

    // Prevent deletion of audit logs
    Route::delete('audit-logs/{auditLog}', [AuditLogController::class, 'destroy'])->name('audit-logs.destroy');

    // Test route for debugging
    Route::middleware(['permission:manage team'])->get('test-teams-create', function() {
        return 'Test route works!';
    })->name('test-teams-create');

    // Simple teams create route for testing
    Route::middleware(['permission:manage team'])->get('simple-teams-create', [TeamController::class, 'create'])->name('simple-teams-create');
});


